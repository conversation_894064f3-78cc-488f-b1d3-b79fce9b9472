/************F25Basic.h*******************/
#ifndef __F25_BASIC_H
#define __F25_BASIC_H
#include "stm32l1xx.h"
#include "ThisDevice.h"
#include "Timer.h"
#include "Display.h"
#include "RF_BasicFun.h"
#include "SysBasic.h"
#include "AD_Input.h"
#include "KeyAndMenu.h"
/* Extern variables ----------------------------------------------------------*/

//-----------------------------------------------------------------------

void  Init108_F25Mode(void);//Init the 38108 board(main environment,like:STM32L15x )for Fyf25 mode

void  InitTIM9_F25(void ) ;

void  InitTIM11_F25(void ) ;

void  TIM9_ISR_F25(void) ;

void  RfTickTimer_ISR_F25(void ) ; 

void  TIM11_ISR_F25(void) ;

void  SysTickIsr_F25(void) ;

#endif   //__F25_BASIC_H

