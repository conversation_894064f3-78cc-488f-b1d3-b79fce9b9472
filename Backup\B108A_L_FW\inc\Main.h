/**
  ******************************************************************************
  * @file    /inc/Main.h 
  * <AUTHOR>
  * @version V1.0.0
  * @date    01-April-2011
  * @brief   Header for DeviceMain.c module
  ******************************************************************************
  */  

/* Define to prevent recursive inclusion -------------------------------------*/
#ifndef __MAIN_H
#define __MAIN_H

/* Includes ------------------------------------------------------------------*/
#include "stm32l1xx.h"

#include "ThisDevice.h"
#include "Timer.h"
#include "SysBasic.h"

#include "Display.h"
#include "RF_BasicFun.h"
#include "AD_Input.h"
#include "KeyAndMenu.h"

#include "F25Main.h"
#include "F35Main.h"
#include "S30Main_01.h"
#include "S30Main_021.h"
/* Exported types ------------------------------------------------------------*/

/* Exported constants --------------------------------------------------------*/

/* Exported macro ------------------------------------------------------------*/

/* Exported functions ------------------------------------------------------- */

#endif /* __MAIN_H */

/*****END OF FILE****/
