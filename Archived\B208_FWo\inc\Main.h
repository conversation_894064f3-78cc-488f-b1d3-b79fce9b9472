/**
  ******************************************************************************
  * @file    /inc/Main.h 
  * <AUTHOR>
  * @version V1.0.0
  * @date    01-April-2011
  * @brief   Header for DeviceMain.c module
  ******************************************************************************
  */  

/* Define to prevent recursive inclusion -------------------------------------*/
#ifndef __MAIN_H
#define __MAIN_H

/* Includes ------------------------------------------------------------------*/
#include "stm32f10x.h"
#include "SysBasic.h"
#include "SysConfig.h"
#include "ThisDevice.h"
#include "Timer.h"
#include "UartFun_A.h"
#include "ModbusMaster.h"
#include "CAN_bx.h"
#include "RF_BasicFun.h"
#include "RF_Test_021.h"
#include "RF_Test_01.h"
#include "IC021_Main.h"
#include "JF01_Main.h"

/* Exported types ------------------------------------------------------------*/

/* Exported constants --------------------------------------------------------*/

/* Exported macro ------------------------------------------------------------*/

/* Exported functions ------------------------------------------------------- */

#endif /* __MAIN_H */

/*****END OF FILE****/
