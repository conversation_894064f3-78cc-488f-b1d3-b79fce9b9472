# Unicode码值与字库点阵互转工具

这是一个专门用于Unicode码值和12x12字库点阵数据之间双向转换的Python工具。

## 🎯 主要功能

### 1. Unicode → 汉字转换
- 输入：`0x1154`
- 输出：对应的汉字字符
- 显示：UTF-8编码、十进制值等信息

### 2. 字库点阵数据解析
- 输入：完整的字库点阵数据
- 输出：12x12可视化预览
- 分析：字节数据和点阵图案

### 3. 双向验证
- 验证Unicode码值与字库数据的对应关系
- 检查点阵数据格式是否正确
- 生成完整的C语言代码

### 4. 批量处理
- 批量转换多个Unicode码值
- 一次性查看多个字符信息

## 🚀 快速开始

### 安装依赖
```bash
pip install tkinter
```

### 运行工具
```bash
python unicode_converter.py
```
或双击 `run_converter.bat`

## 📖 使用方法

### 方法1：Unicode码值转换
1. 在左侧"Unicode码值输入"区域输入：`0x1154`
2. 点击"转换为汉字"
3. 查看显示的汉字和相关信息
4. 点击"查找字库数据"获取查找指导

### 方法2：字库数据解析
1. 在右侧"字库点阵数据输入"区域粘贴完整的字库数据
2. 点击"解析点阵数据"
3. 查看左下角的12x12点阵预览
4. 查看右下角的详细分析信息

### 方法3：完整验证
1. 同时输入Unicode码值和字库数据
2. 验证两者是否匹配
3. 点击"生成C代码"获取完整的代码

## 📊 支持的数据格式

### Unicode码值格式
```
0x1154
0X1154
1154 (自动识别为十六进制)
```

### 字库数据格式
```c
{{12, 12},{
0x00,    /*  ........  */
0x0C,    /*  ....@@..  */
0x0E,    /*  ....@@@.  */
0xE1,    /*  @@@....@  */
0xF1,    /*  @@@@...@  */
0x1E,    /*  ...@@@@.  */
0x0C,    /*  ....@@..  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x00,    /*  ........  */

0x00,    /*  ........  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x06,    /*  .....@@.  */
0x06,    /*  .....@@.  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x00     /*  ........  */
}}
```

## 🔧 实用功能

### 1. 示例数据
点击"示例数据"按钮加载预设的测试数据，快速了解工具功能。

### 2. 批量转换
点击"批量转换"按钮，可以一次性转换多个Unicode码值：
```
0x1154
0x5E95
0x5EA7
```

### 3. 生成C代码
点击"生成C代码"按钮，自动生成可直接使用的C语言代码：
```c
// CodePage12.c 条目:
{ 0x1154, 0x1154, 0xXXXX },/* Segment XXX, 0x0001 Symbols */

// Unicode12.c 条目:
{{12, 12},{
// ... 完整的点阵数据
}}
```

### 4. 复制功能
- "复制信息"：复制分析结果到剪贴板
- 代码窗口中的"复制代码"：复制生成的C代码

## 🎨 界面说明

### 左上角：Unicode码值输入
- 输入框：输入Unicode码值
- 转换按钮：转换为对应汉字
- 汉字显示：显示转换结果
- 查找按钮：提供字库查找指导

### 右上角：字库点阵数据输入
- 文本框：粘贴完整的字库数据
- 解析按钮：解析并预览点阵

### 下方：预览和信息区域
- 左侧：12x12点阵可视化预览
- 右侧：详细的分析信息和数据

### 底部：控制按钮
- 复制信息、生成C代码、批量转换、清空、示例数据

## 🔍 应用场景

### 1. 字库开发
- 验证新添加字符的Unicode码值
- 检查字库点阵数据格式
- 生成标准的C代码格式

### 2. 调试分析
- 分析现有字库中的字符数据
- 验证字符显示异常的原因
- 对比不同字符的点阵差异

### 3. 批量处理
- 批量查看多个字符的信息
- 快速转换Unicode码值列表
- 生成批量的字库代码

## 💡 使用技巧

1. **快速验证**：输入Unicode码值后按回车键快速转换
2. **格式兼容**：支持多种Unicode码值输入格式
3. **可视化预览**：通过点阵预览快速判断字符形状
4. **代码生成**：直接生成可用的C代码，减少手工编写错误

## 🐛 故障排除

### 问题1：无法显示汉字
- 检查Unicode码值格式是否正确
- 确认系统支持该Unicode字符

### 问题2：点阵解析失败
- 检查字库数据格式是否完整
- 确认包含24个十六进制字节数据

### 问题3：预览显示异常
- 检查点阵数据的字节顺序
- 验证高字节和低字节的位排列

---
*工具版本: 1.0*  
*适用于: 12x12点阵字库系统*
