/************JF01_Main.h*******************/
#ifndef __JF01_MAIN_H
#define __JF01_MAIN_H
#include "stm32f10x.h"
#include "SysBasic.h"
#include "SysConfig.h"
#include "ThisDevice.h"
#include "Timer.h"
#include "UartFun_A.h"
#include "ModbusMaster.h"
#include "RF_BasicFun.h"
//-------------
extern  timer_t  RfcTimer[RFC_TIMER_NUM] ;//system ISR process

extern  timer_t  RfcTimer10[RFC_TIMER10_NUM] ;//user level timer(1 tick=10ms),RFC_TIMER10_NUM==2

extern  SysConfigB_t    APP_PB ;//Application parameter Group B

extern  uint16_t  RssiDisSta ;

extern  UARTStruct   UB ;//

extern  ModStruct  ModB ;

extern  uint32_t  ADC_STA ;

//--------------
void  __JF01_Main(void) __attribute__ ((noreturn));

void  Dat2HostProc_01(void); 

void  Proc10mS_JF01(void); //Timed process at frequency=100Hz

void  Proc100mS_JF01(void);//Timed process at frequency=10Hz

void  Proc1S_JF01(void);//Timed process at frequency=1Hz

void  Proc1H_JF01(void); //Timed process at interval 1 hour

#endif   //__JF01_MAIN_H

