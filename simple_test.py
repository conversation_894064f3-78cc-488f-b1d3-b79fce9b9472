#!/usr/bin/env python3
# -*- coding: utf-8 -*-

# "主"字的数据
left_bytes = [0x00, 0x88, 0x88, 0x88, 0x89, 0xFA, 0x88, 0x88, 0x88, 0x88, 0x00, 0x00]
right_bytes = [0x08, 0x08, 0x08, 0x08, 0x08, 0x0F, 0x08, 0x08, 0x08, 0x08, 0x08, 0x00]

print("主字的12x12点阵:")
for row in range(12):
    line = ""
    # 左8位
    for bit in range(8):
        line += "█" if (left_bytes[row] & (1 << (7-bit))) else "·"
    # 右4位
    for bit in range(4):
        line += "█" if (right_bytes[row] & (1 << (7-bit))) else "·"
    print(line)

print("\n字库格式验证:")
print("{{12, 12},{")
for i in range(12):
    pattern = ""
    for bit in range(8):
        pattern += "@" if (left_bytes[i] & (1 << (7-bit))) else "."
    print(f"0x{left_bytes[i]:02X},    /*  {pattern}  */")

print()
for i in range(12):
    pattern = ""
    for bit in range(4):
        pattern += "@" if (right_bytes[i] & (1 << (7-bit))) else "."
    pattern += "    "
    if i == 11:
        print(f"0x{right_bytes[i]:02X}     /*  {pattern}  */")
    else:
        print(f"0x{right_bytes[i]:02X},    /*  {pattern}  */")
print("}}")
