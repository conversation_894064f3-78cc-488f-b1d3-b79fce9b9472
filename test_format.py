#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试字库格式的正确性
"""

def test_font_format():
    """测试你提供的字库数据格式"""
    
    # 你提供的测试数据
    test_data = [
        0x00, 0xFE, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0xFE, 0x00, 0x00,  # 前12字节
        0x00, 0x0F, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x0F, 0x00, 0x00   # 后12字节
    ]
    
    print("测试字库数据格式解析:")
    print("=" * 50)
    
    # 解析为12x12位图
    bitmap = []
    for row in range(12):
        row_data = []
        
        # 前8位（来自前12字节）
        front_byte = test_data[row]
        for bit in range(8):
            row_data.append(1 if (front_byte & (1 << (7-bit))) else 0)
        
        # 后4位（来自后12字节）
        back_byte = test_data[row + 12]
        for bit in range(4):
            row_data.append(1 if (back_byte & (1 << (7-bit))) else 0)
        
        bitmap.append(row_data)
    
    # 显示点阵图案
    print("12x12点阵图案:")
    for i, row in enumerate(bitmap):
        pattern = ""
        for pixel in row:
            pattern += "█" if pixel else "·"
        print(f"行{i:2d}: {pattern}")
    
    print("\n字节数据分析:")
    print("前12字节 (每行前8位):")
    for i in range(12):
        byte_val = test_data[i]
        pattern = ""
        for bit in range(8):
            pattern += "@" if (byte_val & (1 << (7-bit))) else "."
        print(f"  0x{byte_val:02X}    /*  {pattern}  */")
    
    print("\n后12字节 (每行后4位):")
    for i in range(12, 24):
        byte_val = test_data[i]
        pattern = ""
        for bit in range(4):
            pattern += "@" if (byte_val & (1 << (7-bit))) else "."
        pattern += "    "  # 补齐显示
        print(f"  0x{byte_val:02X}    /*  {pattern}  */")
    
    print("\n完整的C代码格式:")
    print("{{12, 12},{")
    
    # 前12字节
    for i in range(12):
        byte_val = test_data[i]
        pattern = ""
        for bit in range(8):
            pattern += "@" if (byte_val & (1 << (7-bit))) else "."
        print(f"0x{byte_val:02X},    /*  {pattern}  */")
    
    print()
    
    # 后12字节
    for i in range(12, 24):
        byte_val = test_data[i]
        pattern = ""
        for bit in range(4):
            pattern += "@" if (byte_val & (1 << (7-bit))) else "."
        pattern += "    "
        if i == 23:
            print(f"0x{byte_val:02X}     /*  {pattern}  */")
        else:
            print(f"0x{byte_val:02X},    /*  {pattern}  */")
    
    print("}}")

if __name__ == "__main__":
    test_format()
