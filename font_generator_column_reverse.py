#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
列行式逆向取模汉字取模工具
格式：前12字节(12列×8位，逆向) + 后12字节(12列×4位，逆向)
逆向取模：第0行→bit0, 第1行→bit1, ..., 第7行→bit7
"""

import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox, filedialog
from PIL import Image, ImageDraw, ImageFont
import numpy as np
import os

class FontGeneratorColumnReverse:
    def __init__(self, root):
        self.root = root
        self.root.title("列行式逆向取模汉字取模工具 v1.0")
        self.root.geometry("1400x1000")
        
        self.font_path = None
        self.font_size = 12
        self.create_widgets()
        self.load_default_font()
    
    def create_widgets(self):
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 标题
        title_label = ttk.Label(main_frame, text="列行式逆向取模汉字取模工具 (12×12 宋体)", 
                               font=("SimSun", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=4, pady=(0, 20))
        
        # 字体设置区域
        font_frame = ttk.LabelFrame(main_frame, text="字体设置", padding="10")
        font_frame.grid(row=1, column=0, columnspan=4, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Button(font_frame, text="选择宋体字体", command=self.select_font).pack(side=tk.LEFT, padx=(0, 10))
        self.font_label = ttk.Label(font_frame, text="使用默认宋体")
        self.font_label.pack(side=tk.LEFT, padx=(0, 20))
        
        ttk.Label(font_frame, text="字体大小:").pack(side=tk.LEFT, padx=(0, 5))
        self.size_var = tk.StringVar(value="12")
        size_combo = ttk.Combobox(font_frame, textvariable=self.size_var, values=["10", "11", "12", "13", "14", "15", "16"], width=5)
        size_combo.pack(side=tk.LEFT, padx=(0, 10))
        size_combo.bind('<<ComboboxSelected>>', self.on_size_change)
        
        # 输入区域
        input_frame = ttk.LabelFrame(main_frame, text="字符输入", padding="10")
        input_frame.grid(row=2, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 5))
        
        ttk.Label(input_frame, text="输入汉字:").grid(row=0, column=0, sticky=tk.W, pady=(0, 5))
        self.char_entry = ttk.Entry(input_frame, width=15, font=("SimSun", 16))
        self.char_entry.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        self.char_entry.bind('<Return>', lambda e: self.generate_font())
        self.char_entry.bind('<KeyRelease>', self.on_char_change)
        
        ttk.Button(input_frame, text="生成取模", command=self.generate_font).grid(row=2, column=0, pady=(0, 10))
        ttk.Button(input_frame, text="批量取模", command=self.batch_generate).grid(row=3, column=0, pady=(0, 5))
        
        # 预览区域
        preview_frame = ttk.LabelFrame(input_frame, text="12×12预览", padding="5")
        preview_frame.grid(row=4, column=0, sticky=(tk.W, tk.E), pady=(10, 0))
        
        self.canvas = tk.Canvas(preview_frame, width=360, height=360, bg='white')
        self.canvas.grid(row=0, column=0)
        
        # 列数据分析区域
        column_frame = ttk.LabelFrame(main_frame, text="列数据分析（逆向取模）", padding="10")
        column_frame.grid(row=2, column=1, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(5, 5))
        
        self.column_text = scrolledtext.ScrolledText(column_frame, width=45, height=30, font=("Consolas", 9))
        self.column_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 取模对比区域
        compare_frame = ttk.LabelFrame(main_frame, text="顺向vs逆向对比", padding="10")
        compare_frame.grid(row=2, column=2, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(5, 5))
        
        self.compare_text = scrolledtext.ScrolledText(compare_frame, width=45, height=30, font=("Consolas", 9))
        self.compare_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 输出区域
        output_frame = ttk.LabelFrame(main_frame, text="生成的C代码", padding="10")
        output_frame.grid(row=2, column=3, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(5, 0))
        
        self.output_text = scrolledtext.ScrolledText(output_frame, width=55, height=30, font=("Consolas", 9))
        self.output_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 控制按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=3, column=0, columnspan=4, pady=(10, 0))
        
        ttk.Button(button_frame, text="复制C代码", command=self.copy_code).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="复制列分析", command=self.copy_column).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="保存到文件", command=self.save_to_file).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="清空", command=self.clear_all).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="示例字符", command=self.load_examples).pack(side=tk.LEFT, padx=(0, 5))
        
        # 配置网格权重
        main_frame.columnconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.columnconfigure(2, weight=1)
        main_frame.columnconfigure(3, weight=1)
        main_frame.rowconfigure(2, weight=1)
        input_frame.columnconfigure(0, weight=1)
        input_frame.rowconfigure(4, weight=1)
        column_frame.columnconfigure(0, weight=1)
        column_frame.rowconfigure(0, weight=1)
        compare_frame.columnconfigure(0, weight=1)
        compare_frame.rowconfigure(0, weight=1)
        output_frame.columnconfigure(0, weight=1)
        output_frame.rowconfigure(0, weight=1)
    
    def load_default_font(self):
        """加载默认宋体字体"""
        # 尝试加载系统宋体
        font_paths = [
            "C:/Windows/Fonts/simsun.ttc",
            "C:/Windows/Fonts/simsun.ttf",
            "/System/Library/Fonts/STSong.ttc",  # macOS
            "/usr/share/fonts/truetype/arphic/uming.ttc",  # Linux
        ]
        
        for path in font_paths:
            if os.path.exists(path):
                self.font_path = path
                self.font_label.config(text=f"使用系统宋体: {os.path.basename(path)}")
                return
        
        self.font_label.config(text="使用默认字体（建议选择宋体）")
    
    def select_font(self):
        """选择字体文件"""
        font_path = filedialog.askopenfilename(
            title="选择宋体字体文件",
            filetypes=[("字体文件", "*.ttf *.ttc *.otf"), ("所有文件", "*.*")]
        )
        if font_path:
            self.font_path = font_path
            self.font_label.config(text=f"已选择: {os.path.basename(font_path)}")
    
    def on_size_change(self, event=None):
        """字体大小改变"""
        self.font_size = int(self.size_var.get())
        # 如果有字符，重新生成
        if self.char_entry.get().strip():
            self.generate_font()
    
    def on_char_change(self, event=None):
        """字符改变时自动生成"""
        char = self.char_entry.get().strip()
        if len(char) == 1:
            self.root.after(500, self.generate_font)  # 延迟500ms自动生成
    
    def char_to_bitmap(self, char):
        """将字符转换为12x12位图"""
        try:
            # 创建16x16图像用于渲染
            img = Image.new('L', (16, 16), 255)
            draw = ImageDraw.Draw(img)
            
            # 选择字体
            if self.font_path and os.path.exists(self.font_path):
                font = ImageFont.truetype(self.font_path, self.font_size)
            else:
                # 使用默认字体
                font = ImageFont.load_default()
            
            # 获取文字尺寸
            bbox = draw.textbbox((0, 0), char, font=font)
            text_width = bbox[2] - bbox[0]
            text_height = bbox[3] - bbox[1]
            
            # 计算居中位置
            x = (16 - text_width) // 2
            y = (16 - text_height) // 2 - bbox[1]
            
            # 绘制字符
            draw.text((x, y), char, font=font, fill=0)
            
            # 裁剪到12x12
            img = img.crop((2, 2, 14, 14))
            
            # 转换为二值图像
            img_array = np.array(img)
            bitmap = (img_array < 128).astype(np.uint8)
            
            return bitmap
            
        except Exception as e:
            messagebox.showerror("错误", f"生成字符位图失败: {str(e)}")
            return None
    
    def bitmap_to_bytes_column_reverse(self, bitmap):
        """将12x12位图转换为列行式逆向取模字节数组"""
        bytes_data = []
        
        # 前12字节：12列，每列前8行（逆向取模）
        for col in range(12):
            byte_val = 0
            for row in range(8):  # 前8行
                if bitmap[row][col]:
                    byte_val |= (1 << row)  # 逆向：第0行对应bit0
            bytes_data.append(byte_val)
        
        # 后12字节：12列，每列后4行（逆向取模）
        for col in range(12):
            byte_val = 0
            for row in range(8, 12):  # 后4行
                if bitmap[row][col]:
                    byte_val |= (1 << (row - 8))  # 逆向：第8行对应bit0
            bytes_data.append(byte_val)
        
        return bytes_data
    
    def bitmap_to_bytes_column_forward(self, bitmap):
        """将12x12位图转换为列行式顺向取模字节数组（用于对比）"""
        bytes_data = []
        
        # 前12字节：12列，每列前8行（顺向取模）
        for col in range(12):
            byte_val = 0
            for row in range(8):  # 前8行
                if bitmap[row][col]:
                    byte_val |= (1 << (7 - row))  # 顺向：第0行对应bit7
            bytes_data.append(byte_val)
        
        # 后12字节：12列，每列后4行（顺向取模）
        for col in range(12):
            byte_val = 0
            for row in range(8, 12):  # 后4行
                if bitmap[row][col]:
                    byte_val |= (1 << (7 - (row - 8)))  # 顺向：第8行对应bit7
            bytes_data.append(byte_val)
        
        return bytes_data

    def analyze_column_data(self, bitmap, bytes_data_reverse, bytes_data_forward):
        """分析列数据（逆向取模）"""
        analysis = "列行式逆向取模数据分析:\n"
        analysis += "=" * 50 + "\n\n"

        # 分析前12字节（前8行）
        analysis += "前12字节（12列×前8行，逆向取模）:\n"
        for col in range(12):
            byte_val = bytes_data_reverse[col]
            analysis += f"列{col:2d}: 0x{byte_val:02X} = "

            # 显示该列的8位数据（逆向）
            col_pattern = ""
            for row in range(8):
                if bitmap[row][col]:
                    col_pattern += "█"
                else:
                    col_pattern += "·"
            analysis += f"{col_pattern} (bit0→行0)\n"

        analysis += "\n后12字节（12列×后4行，逆向取模）:\n"
        for col in range(12):
            byte_val = bytes_data_reverse[col + 12]
            analysis += f"列{col:2d}: 0x{byte_val:02X} = "

            # 显示该列的4位数据（逆向）
            col_pattern = ""
            for row in range(8, 12):
                if bitmap[row][col]:
                    col_pattern += "█"
                else:
                    col_pattern += "·"
            col_pattern += "    "  # 补齐显示
            analysis += f"{col_pattern} (bit0→行8)\n"

        analysis += "\n逆向取模方式说明:\n"
        analysis += "- 逆向取模：第0行对应字节的最低位(bit0)\n"
        analysis += "- 第1行对应bit1，第2行对应bit2，以此类推\n"
        analysis += "- 第7行对应bit7\n"
        analysis += "- 每列独立存储，共12列\n"
        analysis += "- 前8行存储在前12字节，后4行存储在后12字节\n"

        return analysis

    def generate_compare_data(self, bitmap, bytes_data_reverse, bytes_data_forward):
        """生成顺向vs逆向对比数据"""
        compare = "顺向取模 vs 逆向取模对比:\n"
        compare += "=" * 50 + "\n\n"

        compare += "前12字节对比（前8行）:\n"
        compare += "列号  顺向取模  逆向取模  差异\n"
        compare += "-" * 40 + "\n"

        for col in range(12):
            forward_val = bytes_data_forward[col]
            reverse_val = bytes_data_reverse[col]
            diff = "相同" if forward_val == reverse_val else "不同"
            compare += f"{col:2d}    0x{forward_val:02X}     0x{reverse_val:02X}     {diff}\n"

        compare += "\n后12字节对比（后4行）:\n"
        compare += "列号  顺向取模  逆向取模  差异\n"
        compare += "-" * 40 + "\n"

        for col in range(12):
            forward_val = bytes_data_forward[col + 12]
            reverse_val = bytes_data_reverse[col + 12]
            diff = "相同" if forward_val == reverse_val else "不同"
            compare += f"{col:2d}    0x{forward_val:02X}     0x{reverse_val:02X}     {diff}\n"

        compare += "\n取模方式对比:\n"
        compare += "顺向取模: 行0→bit7, 行1→bit6, ..., 行7→bit0\n"
        compare += "逆向取模: 行0→bit0, 行1→bit1, ..., 行7→bit7\n"
        compare += "\n应用场景:\n"
        compare += "- 顺向取模：传统LCD显示，从高位开始\n"
        compare += "- 逆向取模：某些特殊硬件，从低位开始\n"

        return compare

    def generate_font(self):
        """生成字库数据"""
        char = self.char_entry.get().strip()
        if not char:
            messagebox.showwarning("警告", "请输入汉字")
            return

        if len(char) != 1:
            messagebox.showwarning("警告", "请输入单个汉字")
            return

        # 生成位图
        bitmap = self.char_to_bitmap(char)
        if bitmap is None:
            return

        # 显示预览
        self.show_bitmap_preview(bitmap)

        # 转换为字节数组（逆向和顺向）
        bytes_data_reverse = self.bitmap_to_bytes_column_reverse(bitmap)
        bytes_data_forward = self.bitmap_to_bytes_column_forward(bitmap)

        # 分析列数据
        column_analysis = self.analyze_column_data(bitmap, bytes_data_reverse, bytes_data_forward)
        self.column_text.delete(1.0, tk.END)
        self.column_text.insert(tk.END, column_analysis)

        # 生成对比数据
        compare_data = self.generate_compare_data(bitmap, bytes_data_reverse, bytes_data_forward)
        self.compare_text.delete(1.0, tk.END)
        self.compare_text.insert(tk.END, compare_data)

        # 生成代码
        unicode_val = ord(char)
        code = self.generate_code(char, unicode_val, bytes_data_reverse, bitmap)

        # 显示代码
        self.output_text.delete(1.0, tk.END)
        self.output_text.insert(tk.END, code)

    def generate_code(self, char, unicode_val, bytes_data, bitmap):
        """生成C代码"""
        code = f"// 字符: {char} (Unicode: 0x{unicode_val:04X}) - 列行式逆向取模\n"
        code += f"// 字体: 宋体 {self.font_size}pt, 12×12点阵\n\n"

        # CodePage条目
        code += f"// CodePage条目 (需要插入到CodePage12.c中):\n"
        code += f"{{ 0x{unicode_val:04X}, 0x{unicode_val:04X}, 0xXXXX }}/* Segment XXX, 0x0001 Symbols */\n\n"

        # Unicode12数组条目
        code += f"// Unicode12数组条目 (需要插入到Unicode12.c中):\n"
        code += f"{{{{12, 12}},{{\n"

        # 前12字节（12列×前8行，逆向取模）
        for i in range(12):
            col_byte = bytes_data[i]

            # 生成注释（显示该列前8行的图案）
            pattern = ""
            for row in range(8):
                pattern += "@" if (col_byte & (1 << row)) else "."

            if i == 11:
                code += f"0x{col_byte:02X},    /*  {pattern}  */\n\n"
            else:
                code += f"0x{col_byte:02X},    /*  {pattern}  */\n"

        # 后12字节（12列×后4行，逆向取模）
        for i in range(12, 24):
            col_byte = bytes_data[i]

            # 生成注释（显示该列后4行的图案）
            pattern = ""
            for row in range(4):
                pattern += "@" if (col_byte & (1 << row)) else "."
            pattern += "    "  # 补齐显示

            if i == 23:
                code += f"0x{col_byte:02X}     /*  {pattern}  */\n"
            else:
                code += f"0x{col_byte:02X},    /*  {pattern}  */\n"

        code += f"}}}}\n\n"

        # 添加格式说明
        code += f"// 列行式逆向取模格式说明:\n"
        code += f"// 前12字节: 12列，每列前8行（逆向取模）\n"
        code += f"// 后12字节: 12列，每列后4行（逆向取模）\n"
        code += f"// 逆向取模: 第0行→bit0, 第1行→bit1, ..., 第7行→bit7\n\n"

        # 添加完整12x12预览
        code += f"// 完整12×12点阵预览:\n"
        for row in range(12):
            pattern = ""
            for col in range(12):
                pattern += "@" if bitmap[row][col] else "."
            code += f"// 行{row:2d}: {pattern}\n"

        return code

    def show_bitmap_preview(self, bitmap):
        """显示位图预览"""
        self.canvas.delete("all")

        cell_size = 30

        # 绘制网格
        for i in range(13):
            self.canvas.create_line(i*cell_size, 0, i*cell_size, 12*cell_size, fill="lightgray")
            self.canvas.create_line(0, i*cell_size, 12*cell_size, i*cell_size, fill="lightgray")

        # 绘制像素
        for row in range(12):
            for col in range(12):
                if bitmap[row][col]:
                    x1, y1 = col*cell_size, row*cell_size
                    x2, y2 = x1+cell_size, y1+cell_size
                    self.canvas.create_rectangle(x1, y1, x2, y2, fill="black", outline="gray")

        # 添加行列标号
        for i in range(12):
            # 列标号
            self.canvas.create_text(i*cell_size + cell_size//2, -15, text=str(i), font=("Arial", 10), fill="blue")
            # 行标号
            self.canvas.create_text(-20, i*cell_size + cell_size//2, text=str(i), font=("Arial", 10), fill="red")

    def batch_generate(self):
        """批量生成取模"""
        batch_window = tk.Toplevel(self.root)
        batch_window.title("批量汉字取模")
        batch_window.geometry("800x600")

        ttk.Label(batch_window, text="输入多个汉字 (每行一个或连续输入):").pack(pady=10)

        input_text = scrolledtext.ScrolledText(batch_window, width=60, height=8)
        input_text.pack(pady=10, padx=20, fill=tk.BOTH)

        result_text = scrolledtext.ScrolledText(batch_window, width=60, height=20)
        result_text.pack(pady=10, padx=20, fill=tk.BOTH, expand=True)

        def do_batch_generate():
            input_data = input_text.get(1.0, tk.END).strip()
            if not input_data:
                messagebox.showwarning("警告", "请输入汉字")
                return

            # 提取所有汉字
            chars = []
            for line in input_data.split('\n'):
                for char in line.strip():
                    if char and ord(char) > 127:  # 只处理中文字符
                        chars.append(char)

            if not chars:
                messagebox.showwarning("警告", "没有找到有效的汉字")
                return

            results = []
            for char in chars:
                try:
                    bitmap = self.char_to_bitmap(char)
                    if bitmap is not None:
                        bytes_data = self.bitmap_to_bytes_column_reverse(bitmap)
                        unicode_val = ord(char)
                        code = self.generate_code(char, unicode_val, bytes_data, bitmap)
                        results.append(code)
                        results.append("\n" + "="*80 + "\n")
                except Exception as e:
                    results.append(f"// 错误: 字符 '{char}' 生成失败: {str(e)}\n")

            result_text.delete(1.0, tk.END)
            result_text.insert(tk.END, '\n'.join(results))

        def copy_batch_result():
            result = result_text.get(1.0, tk.END).strip()
            if result:
                batch_window.clipboard_clear()
                batch_window.clipboard_append(result)
                messagebox.showinfo("成功", "批量结果已复制到剪贴板")

        button_frame = ttk.Frame(batch_window)
        button_frame.pack(pady=10)
        ttk.Button(button_frame, text="开始批量生成", command=do_batch_generate).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="复制结果", command=copy_batch_result).pack(side=tk.LEFT)

    def load_examples(self):
        """加载示例字符"""
        examples = ["底", "座", "主", "切", "启", "动", "停", "止"]
        example_window = tk.Toplevel(self.root)
        example_window.title("示例字符")
        example_window.geometry("400x300")

        ttk.Label(example_window, text="点击字符进行取模:").pack(pady=10)

        button_frame = ttk.Frame(example_window)
        button_frame.pack(pady=20)

        for i, char in enumerate(examples):
            def load_char(c=char):
                self.char_entry.delete(0, tk.END)
                self.char_entry.insert(0, c)
                self.generate_font()
                example_window.destroy()

            ttk.Button(button_frame, text=char, command=load_char, width=4).grid(
                row=i//4, column=i%4, padx=5, pady=5)

    def copy_code(self):
        """复制代码到剪贴板"""
        code = self.output_text.get(1.0, tk.END).strip()
        if code:
            self.root.clipboard_clear()
            self.root.clipboard_append(code)
            messagebox.showinfo("成功", "C代码已复制到剪贴板")
        else:
            messagebox.showwarning("警告", "没有可复制的代码")

    def copy_column(self):
        """复制列分析到剪贴板"""
        analysis = self.column_text.get(1.0, tk.END).strip()
        if analysis:
            self.root.clipboard_clear()
            self.root.clipboard_append(analysis)
            messagebox.showinfo("成功", "列分析已复制到剪贴板")
        else:
            messagebox.showwarning("警告", "没有可复制的列分析")

    def save_to_file(self):
        """保存到文件"""
        char = self.char_entry.get().strip()
        if not char:
            messagebox.showwarning("警告", "请先生成字符数据")
            return

        filename = filedialog.asksaveasfilename(
            title="保存字库数据",
            defaultextension=".c",
            filetypes=[("C文件", "*.c"), ("文本文件", "*.txt"), ("所有文件", "*.*")],
            initialname=f"font_{char}_{ord(char):04X}.c"
        )

        if filename:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(self.output_text.get(1.0, tk.END))
                messagebox.showinfo("成功", f"已保存到: {filename}")
            except Exception as e:
                messagebox.showerror("错误", f"保存失败: {str(e)}")

    def clear_all(self):
        """清空所有内容"""
        self.char_entry.delete(0, tk.END)
        self.output_text.delete(1.0, tk.END)
        self.column_text.delete(1.0, tk.END)
        self.compare_text.delete(1.0, tk.END)
        self.canvas.delete("all")

def main():
    root = tk.Tk()
    app = FontGeneratorColumnReverse(root)
    root.mainloop()

if __name__ == "__main__":
    main()
