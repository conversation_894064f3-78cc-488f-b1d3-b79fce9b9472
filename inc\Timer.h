/************TimerCounter.h*******************/
#ifndef __TIMER_H
#define __TIMER_H
#include "stm32l1xx.h"
/*timer  related const define */

#define  TIM_NUM         2 /*Number of timers in the system  */
#define  TIM10ms_NUM     3 /*Number of timer in 10ms,these timers be processed in user mode*/

//#define  TIM1000_NUM     1 /*Number of timer in 1000ms,these timers be processed in user mode*/

//------------------------------------------------------
#define  OPC_TIMER       0 //Timer10 for for keyboard operation indicator or RUN indicator
#define  LCD_TIMER	     1 //Timer10 for LCD operation
#define  CM_TIMER        2 //Timer10 for RS485 communication monitor

/*********timer and conter function related data define **************/
typedef struct csbitTag
      {
        uint16_t   flag: 1;//when a timer or counter being used set to 1
        uint16_t    tid: 8;
        uint16_t   trig: 1;
        uint16_t unhold: 1;//when set timer or contuer continue to run to TC_MAX even cv>=pv
        uint16_t   rev1: 1;//
        uint16_t      q: 1;//Output status bit
        uint16_t   preq: 1;//Previous output stauts
        uint16_t     en: 1;//enable the timer or counter
        uint16_t   rev2: 1;//
       } csbits;
typedef union tc_csunionTag
{    csbits      csbit ;
     uint16_t   csword ;
  } tc_cs;
typedef struct tim_countTag
 {
    tc_cs              csr;
    uint16_t                 pv;
    uint16_t                 cv;
 }  tim_count ;


/*******************************/
extern  tim_count Timer[TIM_NUM]  ;

extern  tim_count Timer10[TIM10ms_NUM]  ;//user defined timer(1 tick=10ms)

/*begin to define timer function*/
void TBM_ISR(void) ;
void InitSysTimer(void) ;
void InitTimer(void) ;
void TimerProc(void) ;//user mode timer proc
//-------------------------------------------------------------------------
#endif   //__TIMER_H

