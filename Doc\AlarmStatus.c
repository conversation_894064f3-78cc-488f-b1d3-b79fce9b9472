
unsigned char rawData[320] =
{
    {0x216A, 0x0F5F, 0x1AFF, 0x2560, 0x5C50, 0x2000, 0x2000, 0x2000},//0-模式：急停    
    {0x216A, 0x0F5F, 0x1AFF, 0xDC8F, 0x0B7A, 0xA763, 0x3652, 0x2000},//1-模式：远程控制  
    {0x216A, 0x0F5F, 0x1AFF, 0x665B, 0x604E, 0x9F52, 0xFD80, 0x2000},//2-模式：学习功能  
    {0x216A, 0x0F5F, 0x1AFF, 0xEA81, 0xA852, 0x9F52, 0xFD80, 0x2000},//3-模式：自动功能 
	
    {0x216A, 0x0F5F, 0x1AFF, 0x2C67, 0x3057, 0xA763, 0x3652, 0x2000},//4-模式：本地控制  
    {0x216A, 0x0F5F, 0x1AFF, 0xDD4F, 0xA462, 0x5C50, 0x3A67, 0x2000},//5-模式：保护停机 
    {0x6590, 0xA763, 0xE14F, 0xF753, 0x2A59, 0x315F, 0xA562, 0x668B}, 
    {0xA763, 0x3652, 0xFB7C, 0xDF7E, 0x025F, 0x385E, 0xA562, 0x668B},
	
    {0xE65D, 0xD853, 0x9198, 0x6856, 0xA562, 0x668B, 0x2000, 0x2000}, 
    {0xF353, 0xD853, 0x9198, 0x6856, 0xA562, 0x668B, 0x2000, 0x2000}, 
    {0x7572, 0x155F, 0xE890, 0x2A67, 0x0A4E, 0x3575, 0xD063, 0x3A79}, 
    {0xDB8F, 0x6551, 0x5096, 0x1F90, 0x3A53, 0xDF57, 0xD063, 0x3A79},
	
    {0xBA4E, 0xE55D, 0xA763, 0x3652, 0xB672, 0x0160, 0xD063, 0x3A79}, 
    {0xC78F, 0x0B7A, 0xB08B, 0x555F, 0xB672, 0x0160, 0xD063, 0x3A79}, 
    {0xEA81, 0xA852, 0xA763, 0x3652, 0xB672, 0x0160, 0xD063, 0x3A79}, 
	{0xBA4E, 0xE55D, 0xEE4F, 0x636B, 0xB672, 0x0160, 0xD063, 0x3A79} 
} ;
