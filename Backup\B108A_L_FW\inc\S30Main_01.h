#ifndef __S30_MAIN01_H
#define __S30_MAIN01_H
#include "S30Basic.h"
//---------------------
//Following system status define uded in FYS30 mode
#define  DUPLEX_RX_STA        (uint16_t)0x0000
#define  DUPLEX_TX_STA        (uint16_t)0x0002
#define  UNI_RX_STA           (uint16_t)0x0004
#define  DUPLEX_INIT_STA      (uint16_t)0x0006
#define  UNI_INIT_STA         (uint16_t)0x0008
#define  WAIT_RFIC_READY_STA  (uint16_t)0x000a
//---------------------
extern  UARTStruct   UB ;//

extern  ModStruct  ModB ;
//---------------------
void  __S30Main_01(void) __attribute__ ((noreturn));

void  Proc10mS_S30_01(void); //Timed process at frequency=100Hz

void  Proc100mS_S30_01(void);//Timed process at frequency=10Hz
void  Proc1S_S30_01(void); //Timed process at frequency=1Hz
void  Proc1H_S30_01(void); //Timed process at interval 1 hour


#endif   //__S30_MAIN01_H
