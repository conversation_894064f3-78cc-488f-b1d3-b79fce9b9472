#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证字库格式的正确性
"""

def verify_main_char():
    """验证'主'字的字库数据"""
    
    # "主"字的实际数据
    main_data_left = [0x00, 0x88, 0x88, 0x88, 0x89, 0xFA, 0x88, 0x88, 0x88, 0x88, 0x00, 0x00]  # 左8位
    main_data_right = [0x08, 0x08, 0x08, 0x08, 0x08, 0x0F, 0x08, 0x08, 0x08, 0x08, 0x08, 0x00]  # 右4位
    
    print("验证'主'字的字库格式")
    print("=" * 40)
    
    # 显示12x12点阵
    print("12x12点阵图案:")
    for row in range(12):
        pattern = ""
        
        # 左8位
        left_byte = main_data_left[row]
        for bit in range(8):
            pattern += "█" if (left_byte & (1 << (7-bit))) else "·"
        
        # 右4位（从高4位取）
        right_byte = main_data_right[row]
        for bit in range(4):
            pattern += "█" if (right_byte & (1 << (7-bit))) else "·"
        
        print(f"行{row:2d}: {pattern}")
    
    print("\n字库数据格式:")
    print("前12字节（左8位）:")
    for i, byte_val in enumerate(main_data_left):
        pattern = ""
        for bit in range(8):
            pattern += "@" if (byte_val & (1 << (7-bit))) else "."
        print(f"0x{byte_val:02X},    /*  {pattern}  */")
    
    print("\n后12字节（右4位）:")
    for i, byte_val in enumerate(main_data_right):
        pattern = ""
        for bit in range(4):
            pattern += "@" if (byte_val & (1 << (7-bit))) else "."
        pattern += "    "  # 补齐显示
        if i == 11:
            print(f"0x{byte_val:02X}     /*  {pattern}  */")
        else:
            print(f"0x{byte_val:02X},    /*  {pattern}  */")

def test_bitmap_conversion():
    """测试位图转换函数"""
    
    # 创建一个简单的测试位图（一个十字）
    test_bitmap = []
    for row in range(12):
        row_data = []
        for col in range(12):
            # 创建一个十字图案
            if row == 6 or col == 6:
                row_data.append(1)
            else:
                row_data.append(0)
        test_bitmap.append(row_data)
    
    print("\n测试位图转换")
    print("=" * 40)
    
    # 显示测试位图
    print("测试位图（十字图案）:")
    for row in range(12):
        pattern = ""
        for col in range(12):
            pattern += "█" if test_bitmap[row][col] else "·"
        print(f"行{row:2d}: {pattern}")
    
    # 转换为字节数组
    bytes_data = []
    
    # 前12字节：每行的左8位
    for row in test_bitmap:
        byte_val = 0
        for col in range(8):
            if row[col]:
                byte_val |= (1 << (7 - col))
        bytes_data.append(byte_val)
    
    # 后12字节：每行的右4位（存储在字节的高4位）
    for row in test_bitmap:
        byte_val = 0
        for col in range(8, 12):
            if row[col]:
                byte_val |= (1 << (7 - (col - 8)))
        bytes_data.append(byte_val)
    
    print("\n转换后的字库格式:")
    print("{{12, 12},{")
    
    # 前12字节
    for i in range(12):
        byte_val = bytes_data[i]
        pattern = ""
        for bit in range(8):
            pattern += "@" if (byte_val & (1 << (7-bit))) else "."
        print(f"0x{byte_val:02X},    /*  {pattern}  */")
    
    print()
    
    # 后12字节
    for i in range(12, 24):
        byte_val = bytes_data[i]
        pattern = ""
        for bit in range(4):
            pattern += "@" if (byte_val & (1 << (7-bit))) else "."
        pattern += "    "
        if i == 23:
            print(f"0x{byte_val:02X}     /*  {pattern}  */")
        else:
            print(f"0x{byte_val:02X},    /*  {pattern}  */")
    
    print("}}")

if __name__ == "__main__":
    verify_main_char()
    test_bitmap_conversion()
