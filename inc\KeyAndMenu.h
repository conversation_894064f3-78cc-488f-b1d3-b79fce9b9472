#ifndef __KEY_AND_MENU_H
#define __KEY_AND_MENU_H
#include  "stm32l1xx.h"
#include  "stdlib.h"
#include  "string.h"
#include  "stdio.h"
#include  "ThisDevice.h"
/*Key board function defintion*/
  #define  MODIFY_KEY  0x2000   //Use "F2"
#ifdef  SH_FQ_MODE
  #define  NEXT_KEY    0x0200   //Increase haulage speed
  #define  BACK_KEY    0x4000   //Decrease haulage speed
  #define  ESC_KEY     0x1000   //All motor stop
  #define  OK_KEY      0x0800   //Automatic cut

#else
  #define  NEXT_KEY    0x8000   //Increase haulage speed
  #define  BACK_KEY    0x1000   //Decrease haulage speed
  #define  ESC_KEY     0x0800   //All motor stop
  #define  OK_KEY      0x4000   //Automatic cut
#endif
#define  F1_KEY      0x0400   //
#define  F2_KEY      0x2000

#define  HOTKEY_CHECK_TIME  30   //Allow 3 second to confirm hotkey operation
#define  SETUP_ACTIVE_TIME  100   //Allow 10 second to wait

#define  MENU_LEVEL1_MAX    2   //4

#define  BINARY_LIMIT_VAR1  15   //For user setting var1 max setting is 15+1 bits
#define  MSB_LIMIT_AVR1     0x8000 //In according with BINARY_LIMIT_VAR1

#define  BINARY_LIMIT_VAR2  4   //For user setting var1 max setting is 4+1 bits
#define  MSB_LIMIT_AVR2   0x0010 //In according with BINARY_LIMIT_VAR1

void  SetMainMenuScreen(void) ;

void  MainMenuFun(void) ;

void  MonitorVar1Fun(void) ;

void  MonitorVar2Fun(void) ;

void  BinaryVar1Fun(void) ;

void  BinaryVar2Fun(void) ;

void  HotKeyCheck(void) ;

void  Shr_GetKeyID(void) ;

void  S160Tx_GetKeyID(void) ;

void  S260T1_GetKeyID(void) ;

void  S260T2_GetKeyID(void) ;

extern  void WaitSomeTime(uint16_t delay) ;

#endif  //__KEY_AND_MENU_H

