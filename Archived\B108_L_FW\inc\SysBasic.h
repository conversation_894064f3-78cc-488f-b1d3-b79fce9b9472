/**
  ******************************************************************************
  * @file    SysBasic.h
  * <AUTHOR>
  * @version V1.0.0
  * @date    20-December-2011
  * @brief   This file contains basic functions prototypes for the 38108
  *          application  device.
  ******************************************************************************  
  */ 

/* Define to prevent recursive inclusion -------------------------------------*/
#ifndef __SYS_BASIC_H
#define __SYS_BASIC_H
/* Includes ------------------------------------------------------------------*/
#include "stm32l1xx.h"

//-----------------
#define  DEVICE_MODE_MASK   (uint16_t)0xf000

#define  IC_SEL_CONFIG_BIT	(uint16_t)0x8000

#define  FYF35_SHR_MODE     (uint16_t)0x0000

#define  FYF35_TUN_MODE  	(uint16_t)0x1000

#define  FYF25_MODE  	    (uint16_t)0x8000

#define  FYS30_021_MODE    	(uint16_t)0x4000

#define  FYS30_01_MODE    	(uint16_t)0xc000

#define  FYS30_OPS_MODE     (uint16_t)0x5000

#define  HEART_BEAT_EN_BIT 	(uint16_t)0x0800

#define  NO_AUTO_SHUTOFF    (uint16_t)0x0400

#define  LNA_EXIST_01       (uint16_t)0x0100 

#define  MACHINE_TYPE_MASK  (uint16_t)0x000f

#define  MACHINE_TYPE_S260T1  (uint16_t)0x0000

#define  MACHINE_TYPE_S260T2  (uint16_t)0x0001
/*
I use:
       GPIOB->ODR  bit14 use as RF function enter RX_ING status flag
       GPIOB->ODR  bit15 use as RF function enter TX_ING status flag

       GPIOC->ODR  bit0 use as ADC convertion request flag
	   GPIOC->ODR  bit1 use as IC021 RSSI check request(or JF01 RSSI data avaliable) flag
	   GPIOC->ODR  bit2 use as Rx/Tx clock Synchronizing operation OK flag

	   GPIOC->ODR  bit12 use as Real Time Process entry lock flag
*/
#define  PARA_GA_MEM_BASE  0x08080000
#define  PARA_GB_MEM_BASE  0x08080080
#define  PARA_GC_MEM_BASE  0x08080100
#define  PARA_GD_MEM_BASE  0x08080200

#define  BootDats ((SysBootStruct *)0x20003ff0) //Need to change according to on chip RAM configuration
//#define  Display_p1 ((uint16_t *)0x20003fe0) //Need to change according to on chip RAM configuration
//#define  Display_p2 ((uint16_t *)0x20003fe2) //Need to change according to on chip RAM configuration
//-------------------------Define some devices Modbus address
#define ID_HOST_PLC  0x05    //Node ID for host control PLC
#define ID_OPS_RX    0x05    //Node ID for FYS28N work as OPS signal receiver station
#define ID_OPS_L     0x06    //Node ID for FYS30 work as left side OPS 
#define ID_OPS_R     0x07    //Node ID for FYS30 work as right side OPS

//-------------------------Defination of IsrFlag bits may be used in ISR routine ----------------------------------------
#define  IFG_TO_BE_SHUTOFF    (uint16_t)0x4000
//#define  IFG_        (uint16_t)0x0000
#define  IFG_FS_RUN_REQ       (uint16_t)0x1000

#define  IFG_FLASH_EN         (uint16_t)0x0800
#define  IFG_HEART_BEAT_EN    (uint16_t)0x0400
#define  IFG_KB_SCAN_EN       (uint16_t)0x0200
#define  IFG_GTOKEN_EN        (uint16_t)0x0100
//--
#define  IFG_KB_CHANGED       (uint16_t)0x0080
#define  IFG_JF01_INT_ERR     (uint16_t)0x0040
#define  IFG_JF01_TX_SYNC     (uint16_t)0x0020
#define  IFG_JF01_FIFO_ALARM  (uint16_t)0x0010

#define  IFG_RSSI_D_OK_R      (uint16_t)0x0008//Alias
#define  IFG_JF01_RSSI_OK_R   (uint16_t)0x0008
#define  IFG_RSSI_D_OK_L      (uint16_t)0x0004//Alias
#define  IFG_JF01_RSSI_OK_L   (uint16_t)0x0004
#define  IFG_JF01_RSSI_OK     (uint16_t)0x0004 //Alias
#define  IFG_JF01_RX_FINISHED (uint16_t)0x0002
#define  IFG_JF01_RX_SYNC     (uint16_t)0x0001
				
//-------------------------Control Flag Bit Define----------------------------------------
//#define  CB_   (uint32_t)0x00002000
#define  CB_TUN_A8_PRINT_REQ  (uint32_t)0x00004000
#define  CB_RX_CRC_OK         (uint32_t)0x00002000

#define  CB_JF01_RD_RSSI      (uint32_t)0x00001000
//--
#define  CB_MDM_COM_OK      (uint32_t)0x00000800   //Modbus master communication OK
#define  CB_MDS_COM_OK      (uint32_t)0x00000400   //Modbus slaver communication OK
#define  CB_T_ONLINE_R      (uint32_t)0x00000200
#define  CB_T_ONLINE_L      (uint32_t)0x00000100
//--
#define  CB_RX_OK_R         (uint32_t)0x00000080
#define  CB_RX_OK_L         (uint32_t)0x00000040
#define  CB_RX_OK           (uint32_t)0x00000040 //alias
//#define  CB_RX_ING          (uint32_t)0x00000020
//#define  CB_TX_ING          (uint32_t)0x00000010
//--
#define  CB_DO_LCD_EN       (uint32_t)0x00000004
#define  CB_KB_HOLD_ON      (uint32_t)0x00000002
#define  CB_KB_CHANGED      (uint32_t)0x00000001

//---------------------------------------------------------------------------
#define  GF_LOW_SPEED_RUN    (uint32_t)0x80000000
#define  GF_IN_SETUP_STA     (uint32_t)0x40000000  
#define  GF_SYS_INIT_OK      (uint32_t)0x20000000
#define  GF_SYS_NOTIFY_OK    (uint32_t)0x10000000

//--
#define  GF_CALIBRATION_OK   (uint32_t)0x00400000
#define  GF_SYNC_OK          (uint32_t)0x00200000
#define  GF_KB_ERROR         (uint32_t)0x00100000

#define  GF_TEST_TX_OK       (uint32_t)0x00020000
#define  GF_TEST_RX_OK       (uint32_t)0x00010000
//--
#define  GF_CMD_CONFIRMED    (uint32_t)0x00004000
#define  GF_RSSI_GOOD_R      (uint32_t)0x00002000
#define  GF_RSSI_GOOD_L      (uint32_t)0x00001000
//--
#define  GF_ALARM_DIS_ON     (uint32_t)0x00000400
#define  GF_LCD_A6_MASK      (uint32_t)0x00000200   //Fixed Key command indicator print control
#define  GF_BAT_ALARM        (uint32_t)0x00000100
//--
#define  GF_MAREA2_V_U_OK    (uint32_t)0x00000080
#define  GF_MAREA1_V_U_OK    (uint32_t)0x00000040  
#define  GF_MAREA2_TAG_OK    (uint32_t)0x00000020
#define  GF_MAREA1_TAG_OK    (uint32_t)0x00000010
//--
#define  GF_BATV_REFRESH     (uint32_t)0x00000002
#define  GF_RSSI_REFRESH     (uint32_t)0x00000001
//---------------------------------------
//---------------------------------------------------------------------------------------
#define  SetRfTxEnPin       GPIOB->BSRRL = (uint16_t)0x0080  
#define  ResetRfTxEnPin     GPIOB->BSRRH = (uint16_t)0x0080

#define  SetRfRxEnPin       GPIOB->BSRRL = (uint16_t)0x0060
#define  ResetRfRxEnPin     GPIOB->BSRRH = (uint16_t)0x0060

#define  EnableJF01Chip     GPIOE->BSRRH = (uint16_t)0x1000
#define  DisableJF01Chip    GPIOE->BSRRL = (uint16_t)0x1000

//--
#define  SetRx_ingFlag      GPIOB->BSRRL = (uint16_t)0x4000
#define  ClearRx_ingFlag    GPIOB->BSRRH = (uint16_t)0x4000
#define  Rx_ingFlag             GPIOB->ODR&(uint16_t)0x4000 

#define  SetTx_ingFlag      GPIOB->BSRRL = (uint16_t)0x8000
#define  ClearTx_ingFlag    GPIOB->BSRRH = (uint16_t)0x8000
#define  Tx_ingFlag             GPIOB->ODR&(uint16_t)0x8000 

#define  SetStartAdcFlag     GPIOC->BSRRL = (uint16_t)0x0001
#define  ClearStartAdcFlag   GPIOC->BSRRH = (uint16_t)0x0001
#define  StartAdcFlag            GPIOC->ODR&(uint16_t)0x0001 

#define  SetRssiCheckFlag    GPIOC->BSRRL = (uint16_t)0x0002
#define  ClearRssiCheckFlag  GPIOC->BSRRH = (uint16_t)0x0002
#define  RssiCheckFlag           GPIOC->ODR&(uint16_t)0x0002 

#define  SetSyncOpOkFlag     GPIOC->BSRRL = (uint16_t)0x0004
#define  ClearSyncOpOkFlag   GPIOC->BSRRH = (uint16_t)0x0004
#define  SyncOpOkFlag            GPIOC->ODR&(uint16_t)0x0004 

#define  SetLockFlag     GPIOC->BSRRL = (uint16_t)0x1000
#define  ClearLockFlag   GPIOC->BSRRH = (uint16_t)0x1000
#define  LockFlagSta         GPIOC->ODR&(uint16_t)0x1000 

#define  Led_1_off    GPIOB->BSRRH = (uint16_t)0x2000 
#define  Led_1_on     GPIOB->BSRRL = (uint16_t)0x2000 
#define  Led_1_sta        GPIOB->ODR&(uint16_t)0x2000 

#define  Led_2_off    GPIOB->BSRRH = (uint16_t)0x1000 
#define  Led_2_on     GPIOB->BSRRL = (uint16_t)0x1000
#define  Led_2_sta        GPIOB->ODR&(uint16_t)0x1000 
 
#define  Led_3_off    GPIOC->BSRRH = (uint16_t)0x0400 
#define  Led_3_on     GPIOC->BSRRL = (uint16_t)0x0400 
#define  Led_3_sta        GPIOC->ODR&(uint16_t)0x0400 

#define  Led_4_off    GPIOC->BSRRH = (uint16_t)0x0800 
#define  Led_4_on     GPIOC->BSRRL = (uint16_t)0x0800 
#define  Led_4_sta        GPIOC->ODR&(uint16_t)0x0800 
 
/*** Constant Definitions ***/
#define  F_KEY_FLAG       (uint32_t)0xaa55bc3d  //define first check key
#define  S_KEY_FLAG       (uint32_t)0x55aae1c6  //define second check key
#define  SWITCHED_ON_ID   (uint32_t)0xd3cb6c1e  //define device be switched ON indentifier
#define  SWITCH_OFF_ID	  (uint32_t)0xed13cc6b  //define a flag that will guide the device switch off operation
#define  HOTBOOT_FLAG     (uint16_t)0x5a00  //define hot boot flag
//-----------------
#define  SW1    (uint16_t)0x0004
#define  SW2    (uint16_t)0x0004
#define  SW3    (uint16_t)0x0020

#define  SW4    (uint16_t)0x0002
#define  SW5    (uint16_t)0x0008
#define  SW6    (uint16_t)0x0040

#define  SW7    (uint16_t)0x0001
#define  SW8    (uint16_t)0x0010
#define  SW9    (uint16_t)0x0080

#define  SW10   (uint16_t)0x0100
#define  SW11   (uint16_t)0x1000
#define  SW12   (uint16_t)0x8000

#define  SW13   (uint16_t)0x0200
#define  SW14   (uint16_t)0x0800
#define  SW15   (uint16_t)0x4000

#define  SW16   (uint16_t)0x0400
#define  SW17   (uint16_t)0x2000

//-------Normal Parameter Operation-------
#define  ENC_MASK        (uint16_t)0x2040
#define  FIXED_FLAG      (uint16_t)0x0080	//fixed bit - bit 7 in PanSettingW must be set to 1 
#define  TEST_KEY_ID_A   (uint16_t)0x5a0f
#define  TEST_KEY_ID_B   (uint16_t)0xf0a5
#define  COMMAND_KEY     (uint16_t)0xaa55

#define  LEFT_CRC_SEED   (uint16_t)0xfa5f
#define  RIGHT_CRC_SEED  (uint16_t)0xf5af
#define  MACH_CRC_SEED   (uint16_t)0x5ffa
//--
#define  READ_PARA_REQ      (uint16_t)0x0003   //request to read a parameter
#define  WRITE_PARA_REQ     (uint16_t)0x0010   //request to write a parameter
#define  CLEAR_PARA_REQ     (uint16_t)0x0055   //request to clear a parameter
#define  EXIT_PARA_REQ      (uint16_t)0x00aa   //request to exit the parameter access operation
#define  READ_FULFILLED     (uint16_t)0x0300   //read parameter operation fulfilled
#define  READ_INVALIDED     (uint16_t)0x0083   //invalid read operation request no operation executed
#define  WRITE_FULFILLED    (uint16_t)0x1000   //write parameter operation finish without error flag
#define  WRITE_DENY         (uint16_t)0x0090   //write parameter operation was denied for password error
#define  CLEAR_FULFILLED    (uint16_t)0x5500   //clear parameter operation finish without error flag

//-------System run control varible status-----------------------------------------------

#define  POWER_DOWN_STA      (uint16_t)0x0000
#define  LP_WAIT_STA         (uint16_t)0x0002
#define  SETUP_LP_STA        (uint16_t)0x0004
#define  LCD_REFRESH_STA     (uint16_t)0x0006
#define  DO_RX_STA           (uint16_t)0x0008  //Rx status-do RX or wait for the poll
#define  DO_TX_STA           (uint16_t)0x000a  //Tx status-async or syanc TX sta
#define  SETUP_RX_STA        (uint16_t)0x000c
#define  SETUP_TX_STA        (uint16_t)0x000e  
#define  SETUP_TEST_STA      (uint16_t)0x0010
#define  TEST_STA            (uint16_t)0x0012
#define  PREPARE_PD_STA      (uint16_t)0x0014
#define  SYS_NOTIFY_STA      (uint16_t)0x0016
#define  POWER_ON_INIT_STA   (uint16_t)0x0018
//------
#define  TOKEN_UINT     1   //Define the minimum token number that will generated at every token generate action
#define  TOKENPOOLS     5   //Define max token pool number in system --must not change

#define  MAX_DIS_INS_BUF           16    //Define max display instruction buffer length

//---------------------------------------
typedef struct TagPAccessReqDS_t
{
 uint16_t req   ;//parameter access request field 
 uint16_t rev_w ;//reserved word for future use
 uint16_t pid   ;//Gloabal defined parameter identifier
 uint16_t ack   ;//parameter access acknowledge field
                 //(it also indicate parameter processor's internal operation status)
}PAccessReqDS_t ; 

typedef struct TagPAccessSta_t
{
 uint16_t   sta    ;//parameter access status
 uint16_t   reg_t  ;//Register used for temp storage
 uint16_t   pid    ;//current be accessed parameter's identifier
 uint16_t   len    ;//lenth of the current be accessed parameter structure
 
 uint16_t   ack     ;//Acknowdge status for parameter access
 uint16_t   rev_w   ;//reserved area
 uint32_t   UIDCode ;//Unique ID for authentication
 } PAccessSta_t ;

//---------------------
typedef struct TagSysBootStruct
{
  volatile uint32_t  f_key ;//first key for detect hot boot
  volatile uint32_t  s_key ;//second key for detect hot boot
  volatile uint32_t  switch_id ;//switch	ON/OFF identifier
  volatile uint16_t  rst_count ;//used to count the system hot boot times
  volatile uint16_t  bt_flag ;//flag used to guide firmware switch to designed run mode
} SysBootStruct ;

typedef union
{
  uint16_t  SettingW ;
  struct
  {
    uint16_t  UnidirOpCtrl        : 1 ;//='1' request to use unidirection operation mode,Not recommend
    uint16_t  AdjFreqToHi         : 1 ;//='1' request to use high frequecy than normal setting
    uint16_t  AllowOthersideCtrl  : 1 ;//='1' allow the output to control other side operation(both left and right)
    uint16_t  ExtendBL_AS_T       : 1 ;//='1' request to extend black light automatic shutoff time in idle status
    uint16_t  ExtendPan_AS_T      : 1 ;//='1' request to extend the pannel automatic shutoff time in idle status
    uint16_t  rev_bits            : 11 ;//reserved bits
   };
 } SYS_SETTING_VAR;
//---------MAC define-------
typedef struct
{
  uint16_t  Interval ; //Time interval beteewn two  output Token(in RF tick)
  int16_t   TokenNum ; //Totoal token number in current token pool,if it=-1 ,means have infinite token
 } TPoolStruct ;
typedef struct
{//+12
 uint16_t        SyncTimeSet ;//Px0117,Reset value for RF tick timer,when synchronization signal be captured
 uint16_t      RfTickTimerPV ;//Px0118,Preload counter cycle value for the RF tick timer(determine the RF tick long)
                              //       for Fyf35 use 3275(10.005Hz),for Fyf25 use 1536(21.33Hz)	
 TPoolStruct  TokenPool[TOKENPOOLS] ;//Px0105-112,TOKENPOOLS --fixed to 5, +10
} RfTickTokenCtrl_t ;
//-----------------------------------
//Modbus access control function
#define  NO_ACCESS   (uint16_t) 0
#define  READ_ONLY   (uint16_t) 1
#define  WRITE_ONLY  (uint16_t) 2
#define  READ_WRITE  (uint16_t) 3

typedef struct TagSegCtrlStruct
{
  uint16_t Lo_Add ;//Lowest address of the controlled data segment
  uint16_t Hi_Add ;//highest address of the controlled data segment
  uint16_t Attr   ;//Access attribute control word for the data segment length
	          //bit0-3 =0 access not allowed,=1 read only,=2 write only ,=3 read and write
	          //bit7=1 when a access performd ,call a hook function with access type
} SegCtrlStruct ;

#define ACC_CTRL_SEG_NUM  4
typedef struct TagAccessCtrlStruct
{

  SegCtrlStruct   SCtrl[ACC_CTRL_SEG_NUM] ;

} AccessCtrlStruct ;
//-------------------------------------------------------
typedef struct TagUARTPortConfig
{
  uint8_t    mode      ;//work mode of the port:=0xff port be disbled,=0 modbus slaver without access contorl,
                   // =1 modbus master, =0x80 modbus slaver with access control table
  uint8_t    mod_add   ;//used to store slaver node address of modbus protocol

  uint16_t   sci_speed ;//SCI communication speed (baud rate,allow:2400,4800,9600,14400,19200,28800,38400,default 19200)

  uint8_t    parity    ;//char length always be 8 bits,=00 no parity ,01 odd parity,11 even parity (default 01 odd parity)
  uint8_t    tx_delay  ;//in ms,tx delay time of slaver reponse(default 20 ms),minimal value=2ms

  uint8_t    rx_t_out  ;//in ms,when act as master the max wait time(in ms) for slaver respone,minimal value=40ms
  uint8_t    retry ;//retry number  when there are no response form slaver
}UARTPortConfig ;
//-------------------------------------------
typedef struct TagAIProcPara
{
  int16_t       offset ;//calculation offset,abs of this value
  int16_t        scale ;//calculation scale,abs of this value
  int16_t          min ;//allowed min value
  int16_t          max ;//allowed max value or rated value(CT rated value)
 } AIProcPara ; //analog channel raw data process parameter structure

//-----------------------------------
typedef struct TagComModOpStruct
{
  uint16_t  sta ;
  uint16_t  sub_sta ;
  uint16_t  Packages ;
  uint16_t  norp_err ; //no response received from slaver
  uint16_t  ot_err ; //time out error there have been occured
  uint16_t  crc_err  ;//received data frame have CRC error
} ComModOpStruct ; //TDECS bus operation object
//-----------------------------------------------------------------------------------
typedef struct TagCANComStatistics
{ //total 9 words
 uint16_t   CAN_ESR_L  ;//Store current CAN module Status Register value ,bit0-15,
 uint16_t   CAN_ESR_H  ;//Store current CAN module Status Register value ,bit16-31
 uint16_t   StuffError ;//Stuff Error counter
 uint16_t   FormError  ;//Form Error counter
 uint16_t   AckError   ;//Transmittion acknowledge error counter
 uint16_t   Bit1Error  ;//Bus hold to recessive '1' error counter
 uint16_t   Bit0Error  ;//Bus hold to dominant '0' error counter
 uint16_t   CrcError   ;//total CRC error
 uint16_t   BusOff ;//Bus Off status counter
} CANComStatistics ;
//-----------------------------------------------------------------------------------
typedef struct TagMyWord
{
 uint8_t L_Byte: 8 ;//define low byte
 uint8_t H_Byte: 8 ;//define high byte
} MyWord ;

typedef union  TagKeyGroup{
                              uint16_t      cw ;
                              MyWord        cb ;
                           }  KeyGroup ;

typedef union TagWordByte{
  uint8_t  bytes[2] ;
  uint16_t word ;
} WordByte ;

typedef struct TagLongByte
{
  uint8_t ch1 ;
  uint8_t ch2 ;
  uint8_t ch3 ;
  uint8_t ch4 ;
} LongByte ;

typedef union TagLongWordByte
{ //used to get byte value of a long word when calculate FREQ word
  uint32_t        w32 ;
  LongByte    b8 ;
  uint8_t      str[4] ;
 } LongWordByte ;

typedef union TagLongWord
{
  uint32_t  w32 ;
  uint16_t  w16[2] ;
  uint8_t   b8[4] ;
} LongWord ;
//-------------------------------------------------------------------
#define SPI1Pins_GPIO_CONFIG  0x44000000
#define SPI1Pins_SPI_CONFIG   0xa8000000

//------------------------------------------------------------------------
typedef struct TagIC021Registers
{//total 15*4=60 bytes
  uint32_t  R0_N       ;//R0--N Register PLL 8-bit Integer-N 15-Bit Fractional-N
  uint32_t  R1_VCO_OSC ;//R1--VCO/Oscillator Register
  uint32_t  R2_Tx_Mod  ;//R2--Transmit Modulation register
  uint32_t  R3_TxRx_Clk  ;//R3--Transmit/Receive clock register
  uint32_t  R4_Demod     ;//R4--Demodulator setup register
  uint32_t  R5_IF_Filter ;//R5--IF filter setup register
  uint32_t  R6_IF_Cal ;//R6--IF fine calibration setup register
  uint32_t  R8_PDT    ;//Power down test register
  uint32_t  R9_AGC    ;//R9--AGC setup register
  uint32_t  R10_AFC   ;//R10--AFC setup register
  uint32_t  R11_SYNC  ;//R11--SYNC word detect register
  uint32_t  R12_SWD   ;//R12--SWD/Threshold setup register
  uint32_t  R13_3FSK  ;//R13--3FSK Demod register
  uint32_t  R14_DAC   ;//R14--test DAC register setup
  uint32_t  R15_TEST  ;//R15--Test mode register
}IC021Registers ;

typedef struct TagIC021RxSwitchReg
{//2*4=8 bytes
 uint32_t  R0_N_Rx ;//Switch register for Rx operation @ specfied frequncy
 uint32_t  R1_VCO_OSC_Rx ;//R1--VCO/Oscillator Register
}IC021RxSwitchReg ;

typedef struct TagIC021TxSwitchReg
{//2*4=8 bytes
 uint32_t  R0_N_Tx ;//Switch register for Tx operation @ specfied frequncy
 uint32_t  R1_VCO_OSC_Tx ;//R1--VCO/Oscillator Register
}IC021TxSwitchReg ;
//-------------------------
typedef struct TagJF01Registers
{
  uint8_t  iocfg2    ;  // GDO2 output pin configuration
  uint8_t  iocfg1    ;  // GDO1 output pin configuration
  uint8_t  iocfg0    ;  // GDO0 output pin configuration
  uint8_t  fifothr   ;  // RX FIFO and TX FIFO thresholds
  uint8_t  sync1     ;  // Sync word, high byte
  uint8_t  sync0     ;  // Sync word, low byte
  uint8_t  pktlen    ;  // Packet length
  uint8_t  pktctrl1  ;  // Packet automation control,PQT,CRC_AUTOFLUSH,APPEND_STATUS,ADR_CHK ,+4

  uint8_t  pktctrl0  ;  // Packet automation control,WHITE_DATA,PKT_FORMAT,CRC_EN,LENGTH_CONFIG
  uint8_t  addr      ;  // Device address
  uint8_t  channr    ;  // Channel number
  uint8_t  fsctrl1   ;  // Frequency synthesizer control,FREQ_IF
  uint8_t  fsctrl0   ;  // Frequency synthesizer control,FREQOFF
  uint8_t  freq2     ;  // Frequency control word, high byte
  uint8_t  freq1     ;  // Frequency control word, middle byte
  uint8_t  freq0     ;  // Frequency control word, low byte	  ,+4

  uint8_t  mdmcfg4   ;  // Modem configuration,CHANBW_E,CHANBW_M,DRATE_E
  uint8_t  mdmcfg3   ;  // Modem configuration,DRATE_M
  uint8_t  mdmcfg2   ;  // Modem configuration,DEM_DCFILT_OFF,MOD_FORMAT,MANCHESTER_EN,SYNC_MODE
  uint8_t  mdmcfg1   ;  // Modem configuration,FEC_EN,NUM_PREAMBLE,CHANSPC_E
  uint8_t  mdmcfg0   ;  // Modem configuration,CHANSPC_M
  uint8_t  deviatn   ;  // Modem deviation setting,DEVIATION_E,DEVIATION_M
  uint8_t  mcsm2     ;  // Main Radio Cntrl State Machine config,RX_TIME_RSSI,RX_TIME_QUAL,RX_TIME
  uint8_t  mcsm1     ;  // Main Radio Cntrl State Machine config,CCA_MODE,RXOFF_MODE,TXOFF_MODE	,+4

  uint8_t  mcsm0     ;  // Main Radio Cntrl State Machine config,FS_AUTOCAL,PO_TIMEOUT,PIN_CTRL_EN,XOSC_FORCE_ON
  uint8_t  foccfg    ;  // Frequency Offset Compensation config,FOC_BS_CS_GATE,FOC_PRE_K,FOC_POST_K,FOC_LIMIT
  uint8_t  bscfg     ;  // Bit Synchronization configuration,BS_PRE_KI,BS_PRE_KP,BS_POST_KI,BS_POST_KP,BS_LIMIT
  uint8_t  agcctrl2  ;  // AGC control,MAX_DVGA_GAIN,MAX_LNA_GAIN,MAGN_TARGET
  uint8_t  agcctrl1  ;  // AGC control,AGC_LNA_PRIORITY,CARRIER_SENSE_REL_THR,CARRIER_SENSE_ABS_THR
  uint8_t  agcctrl0  ;  // AGC control,HYST_LEVEL,WAIT_TIME,AGC_FREEZE,FILTER_LENGTH
  uint8_t  worevt1   ;  // High byte Event 0 timeout
  uint8_t  worevt0   ;  // Low byte Event 0 timeout	  ,+4

  uint8_t  worctrl   ;  // Wake On Radio control,RC_PD,EVENT1,RC_CAL,WOR_RES
  uint8_t  frend1    ;  // Front end RX configuration,LNA_CURRENT,LNA2MIX_CURRENT,LODIV_BUF_CURRENT_RX,MIX_CURRENT
  uint8_t  frend0    ;  // Front end TX configuration,LODIV_BUF_CURRENT_TX,PA_POWER
  uint8_t  fscal3    ;  // Frequency synthesizer calibration,FSCAL3[7:6],CHP_CURR_CAL_EN,FSCAL3[3:0]
  uint8_t  fscal2    ;  // Frequency synthesizer calibration,VCO_CORE_H_EN,FSCAL2
  uint8_t  fscal1    ;  // Frequency synthesizer calibration,FSCAL1
  uint8_t  fscal0    ;  // Frequency synthesizer calibration,FSCAL0	,+4
  uint8_t  rcctrl1   ;  // RC oscillator configuration

  uint8_t  rcctrl0   ;  // RC oscillator configuration
  uint8_t  fstest    ;  // Frequency synthesizer cal control,For test only.Do not write to this register
  uint8_t  ptest     ;  // Production test,Write 0xbf for on-chip temperature in the IDLE state,otherwise use default value 0x7f
  uint8_t  agctest   ;  // AGC test,Do not write to this register
  uint8_t  test2     ;  // Various test settings
  uint8_t  test1     ;  // Various test settings
  uint8_t  test0     ;  // Various test settings,TEST0[7:2],VCO_SEL_CAL_EN,TEST0[0]	,+4
  uint8_t  rev_byte  ;
} JF01Registers ;

typedef struct TagJF01SwitchReg
{
  uint8_t  freq2     ;  // Frequency control word, high byte
  uint8_t  freq1     ;  // Frequency control word, middle byte
  uint8_t  freq0     ;  // Frequency control word, low byte
  uint8_t  test0     ;  // Various test settings,TEST0[7:2],VCO_SEL_CAL_EN,TEST0[0]
} JF01SwitchReg ;

typedef __packed struct TagCC01Ctrl
{
  volatile uint16_t  OpReq ;//Input for Initalization request
  volatile uint16_t  OpSta ;//Hold current Operation status
  volatile int16_t  CalTemp ;//used to store environment temperature when perform the calibration
                //if more than 12 celsuis degree changed,need to recalibration CC01
  volatile uint8_t  RB_FSCAL3 ;//FS calibration result FSCAL3
  volatile uint8_t  RB_FSCAL2 ;//FS calibration result FSCAL2
  volatile uint8_t  RB_FSCAL1 ;//FS calibration result FSCAL1
  volatile uint8_t  rev_B ;
} CC01InitStruct ;

/*****************define a control structure for IC021Init**************************/
typedef  struct TagADF7021Ctrl
{
  uint16_t  OpReq ;//Input for Initalization request
  uint16_t  OpSta ;//Hold current Operation status
  int16_t   CalTemp ;//used to store environment temperature when perform the calibration
                    //if more than 12 celsuis degree changed,need to recalibration IC021
  uint16_t  R5_IR_Cal_I ;//store IR calibration adjust value for I channel(GAIN,PHASE)
  uint16_t  R5_IR_Cal_Q ;//store IR calibration adjust value for Q channel(GAIN,PHASE)
  uint16_t  FilterCalRaw ;
  uint32_t  FilterCalReadback ;//used to store read back IF Filter fine calibration result
} IC021InitStruct ;
//----------------------
typedef union{
               uint16_t  SettingW ;//Current setting word of the panel
               struct {
                        uint16_t  DatGID_1        : 5 ;//Request to display Dat_1 GID
                        uint16_t  UniDirCtrl      : 1 ;//Panel run in uni-direction control mode flag
                        uint16_t  Enc_1           : 1 ;//Data encrpt code bit1
                        uint16_t  FixedBit        : 1 ;//Fixed data bit must be 1 for valid control data frame

                        uint16_t  DatGID_2        : 5 ;//bit8-12,Request to display Dat_2 GID
                        uint16_t  Enc_2           : 1 ;//Data encrpt code bit2
                        uint16_t  rev_bit1        : 1 ;//Must be zero in control data frame
                        uint16_t  rev_bit2        : 1 ;//Must be zero in control data frame
				       } ;
              } PanSetting_t ;
typedef union{
               uint16_t  StaW ;//Status word of the panel
               struct {
                        uint16_t   InGroupId     : 2 ;//Panel's in group ID number
                        uint16_t  GroupIdNum     : 6 ;//Panel's group ID number

                        uint16_t  rev_sta_bit1   : 1 ;//
                        uint16_t  RxDatOk        : 1 ;//=1 indicate panel have received valid data from WCR just before
                        uint16_t  RFLinkAlarm    : 1 ;//=1 indicate panel feel may be lost link with receiver station
                        uint16_t  InquiryPackage : 1 ;//bit11=1 indicate this Package TX frame for machine receiver status inquery only
                                                      //no append control code should be used for host machine control opertion
                        uint16_t  rev_sta_bit2   : 1 ;//
                        uint16_t  ToBeShutoff    : 1 ;//=1 indicate the panel to be enter shutoff status
                        uint16_t  BatteryAlarm   : 1 ;//=1 indicate the Battery low voltage alarm occured
                        uint16_t  KeyBoardError  : 1 ;//Key Board error flag bit
                       } ;
              } PanStatus_t ;

//---------------------------------------------------------------------------------------------
typedef  struct{
				 PanSetting_t PSet ;
				 PanStatus_t  PSta ;
                 uint16_t KeyCode ;//16 bits current key status
                } PANEL_DAT;
//----
typedef  struct {
                  PANEL_DAT  Payload ;
                  uint16_t   CRC_app ;//CRC value that will append to the data frame
                } PANEL_TX_DAT;
//----------------------------------------------------------------------------------------------------------------
//---------------------------------------------------------------------------------------------
typedef  struct{
                 union {
                        uint16_t  PanSettingW ;
                        struct {
                                 uint16_t  DatGID_1     : 5 ;//Request to display Dat_1 GID
                                 uint16_t  rev_b1       : 1 ;//reserved bit
                                 uint16_t  Enc_1        : 1 ;//Data encrpt code bit1
                                 uint16_t  Enc_2        : 1 ;//Data encrpt code bit2
                                 uint16_t  DatGID_2     : 5 ;//Request to display Dat_2 GID
                                 uint16_t  rev_b2       : 1 ;//reserved bit
                                 uint16_t  FixedFlag    : 2 ;//=3 ,This field must be 3 for valid RF TX frame
                                } ;
                       };
                union {
                        uint16_t  PanStaW ;//common status word of the receiver
                        struct {
                                 uint16_t  InquiryPackage  : 1 ;//=1 indicate this Package TX frame for Receiver status inquery only
                                                                //no append control code should be used for host machine control opertion
                                 uint16_t  RxDatOk         : 1 ;//=1 indicate panel have received valid data from Rx Station
                                 uint16_t  LowPowerWait    : 1 ;//=1 Panel enter low power wait mode
                                 uint16_t  ToBeSleep       : 1 ;//=1 indicate the panel to be enter sleep status
                                 
                                 uint16_t  Rev_bits          : 8 ;//Reserved bit flieds
                                 uint16_t  UnidirectionMode  : 1 ;//Key Board error flag bit
                                 uint16_t  KeyBoardError   : 1 ;//Key Board error flag bit
                                 uint16_t  RFLinkAlarm     : 1 ;//=1 indicate panelfeel may be lost link with receiver statin
                                 uint16_t  BatteryAlarm    : 1 ;//=1 indicate the Battery low voltage alarm occured
                                } ;
                       } ;
                uint16_t KeyCode ;//16 bits current key status
                } PANEL_OLD_DAT;
typedef  struct {
                PANEL_OLD_DAT  Payload ;
                uint16_t   CRC_app ;//CRC value that will append to the data frame
                } PANEL_OLD_TX_DAT;
		
//----------------------------------------------------------------------------------------------------------------
typedef struct{
                union {
                        uint16_t WcrStaW ;//Wireless control reveiver status word
                        struct {
                                 uint16_t  DatGID_1     : 5 ;//Dat_1 GID
                                 uint16_t  DatGID_2     : 5 ;//Dat_2 GID
                                 uint16_t  ToggleBit    : 1 ;//This bit will taggle with current access slot number(tick number -odd or even)
                                 uint16_t  DatValid     : 1 ;//=1 indicate Dat_1,Dat_2 are valid data
                                 uint16_t  RSSI_Ok_L    : 1 ;//=1 indicate Rx Station dectect left panel RSSI ok(>-95dBm)
                                 uint16_t  RSSI_Ok_R    : 1 ;//=1 indicate Rx Station dectect right panel RSSI ok(>-95dBm)
                                 uint16_t  LeftLinkOk   : 1 ;//=1 indicate Rx Station received valid OPC from left panel
                                 uint16_t  RightLinkOk  : 1 ;//=1 indicate Rx Station received valid OPC from right panel
                                } ;
                       } ;
                 uint16_t   MachSta ;//machine operation status word
                 uint16_t   Dat_1 ;//First data to the panel
                 uint16_t   Dat_2 ;//Second one
                 uint16_t   CRC_app ;//CRC value that will append to the data frame
                } SHR_TX_DAT ; //Shearer control receiver transmitted data meassage----no change from 38107 to 38108

typedef struct{
                union {
                        uint16_t WcrStaW ;//Wireless control reveiver status word
                        struct {
                                 uint16_t  DatGID_1     : 5 ;//Dat_1 GID
                                 uint16_t  DatGID_2     : 5 ;//Dat_2 GID
                                 uint16_t  rev          : 1 ;//reserved bit
                                 uint16_t  DatValid     : 1 ;//=1 indicate Dat_1,Dat_2 are valid data
                                 uint16_t  RSSI_Ok_L    : 1 ;//=1 indicate Rx Station dectect left panel RSSI ok(>-95dBm)
                                 uint16_t  RSSI_Ok_R    : 1 ;//=1 indicate Rx Station dectect right panel RSSI ok(>-95dBm)
                                 uint16_t  LeftLinkOk   : 1 ;//=1 indicate Rx Station received valid OPC from left panel
                                 uint16_t  RightLinkOk  : 1 ;//=1 indicate Rx Station received valid OPC from right panel
                                } ;
                        } ;
                 uint16_t MachSta ;//machine operation status word
                 uint16_t FB_CMD_D1 ;//Feedback received command form D1 transmiter
                 uint16_t FB_CMD_D2 ;//Feedback received command form D2 transmiter
                 uint16_t MachSpeed ;//Feedback device speed setting
                 uint16_t   CRC_app ;//CRC value that will append to the data frame
                 } TUN_TX_DAT ;//Tunneller machine control receiver transmitted data meassage

//----------------------------------------------------------------------------------------------------------------
typedef struct {
                union {
                        uint16_t OpsStaW ;//common status word of the OPS
                        struct {
                                 uint16_t  DatGID_1     : 5 ;//Bit0-4->Request to display Dat_1 GID
                                 uint16_t  rev_bits1    : 3 ;//reserved bits
                                 uint16_t  DatGID_2     : 5 ;//Request to display Dat_2 GID
                                 uint16_t  rev_bits2    : 3 ;//reserved bit
                                } ;
                       } ;
                uint16_t KeyCode ;//16 bits current key status
                uint16_t RandomNum ;//internal use random number--main for encryp meassage
                uint16_t CRC_app ;//CRC value that will append to the data frame
                } OPS_TX_DAT;//4

typedef struct {
                union {
                        uint16_t  LinkCtrlStaW ;//OPS link control and status word
                        struct {
                                 uint16_t  DatGID_1     : 5 ;//Dat_1 GID
                                 uint16_t  DatGID_2     : 5 ;//Dat_2 GID
                                 uint16_t  rev          : 1 ;//reserved bit
                                 uint16_t  DatValid     : 1 ;//=1 indicate Dat_1,Dat_2 are valid data
                                 uint16_t  rev_bits     : 2 ;//reserved bits
                                 uint16_t  LeftLinkOk   : 1 ;//=1 indicate left OPS communication linkage OK
                                 uint16_t  RightLinkOk  : 1 ;//=1 indicate right OPS communication linkage OK
                                } ;
                       } ;
                uint16_t MachSta ;//machine run status word
                uint16_t Dat_1 ;//First data to the panel
                uint16_t Dat_2 ;//Second one
                } TX_TO_OPS_DAT ;//4 --Wired OPS Receiver Tx(Output to ops) data frame structure
//--------------------------
typedef struct TagTPDOStruct {
                uint16_t  KeyCode_R ;//Received Key Status word for Right side Tx panel
                uint16_t  KeyCode_L ;//Received Key Status word for left side Tx panel
                uint8_t   PanSta_R ;//Pay attention to the byte order ,STR7xx use litte endian mode
                uint8_t   PanSta_L ;//Extent panel status byte
                   int8_t   RSSI_R ;//
                   int8_t   RSSI_L ;//
                } TPDOStruct;
//------------------------------------
typedef struct TagTPDO2Struct {
                uint16_t  KeyCode_L ;//Recieved Key Status word for left side OPS
                uint16_t  KeyCode_R ;//Recieved Key Status word for right side OPS
                uint16_t  OpsStaW_L ;//Recieved Status word for left side OPS
                uint16_t  OpsStaW_R ;//Recieved Status word for right side OPS
                } TPDO2Struct;//For OPS control

/* Extern variables ----------------------------------------------------------*/

extern  uint32_t  SystemCoreClock  ;// 
extern  uint32_t  APB1_Clock ;//defined in "system_stm32L10xc"
extern  uint32_t  APB2_Clock ;//defined in "system_stm32L10xc"

extern  volatile  uint32_t   OpCtrl ;

extern  volatile  uint32_t   GFlag  ;
extern  volatile  uint32_t   GFlag_Pre ;

extern  volatile  uint16_t   SYS_STA ;
extern  volatile  int16_t    TxToken ;
extern  volatile  uint16_t   IsrFlag  ;//Special flag for used in ISR	,Begin with IFG_

extern  volatile  uint16_t  TestCtrlW ;

extern  volatile  SYS_SETTING_VAR  SysSetting ;
				
extern  uint16_t  InstructionBuf[MAX_DIS_INS_BUF] ;

extern  uint16_t  DoBufIndex,EmptyBufIndex ;

extern  volatile  uint16_t  Def_CRC_Seed ;//default CRC seed for left or right side operation

extern  RfTickTokenCtrl_t *RfTT_p ;//define a pointer for current RF tick and token setting
//extern volatile  uint32_t   CK_HSI_Flag ; //='1' Flag to indicate current use HSI clock,maybe HSE failed to run

/* Extern Function pointer ----------------------------------------------------------*/
extern void (*AppMain_fp)(void) __attribute__ ((noreturn));//Define a function pointer for particular application main funtion 

extern void (*Exi15_10_isr_fp)(void) ;

extern void (*SysTick_isr_fp)(void) ;

extern void (*Tim9_isr_fp)(void ) ;

extern void (*Tim10_isr_fp)(void ) ;

extern void (*Tim11_isr_fp)(void ) ;

extern void (*TxToken_fp)(void ) ;

extern void (*Proc10mS_fp)(void) ;//Define function pointer for periodical called timer function--10mS
extern void (*Proc100mS_fp)(void) ;
extern void (*Proc1S_fp)(void) ;
extern void (*Proc1H_fp)(void) ;

extern void (*PanelDatTxLoad_fp)(void) ;
extern void (*MachDatRx_fp)(void) ;
extern void (*PanelLcdPrint_fp)(void) ;
extern void (*GetKeyID_fp)(void) ;

//-------------------------------------------
extern void SetSysClockHSE_8MHz(void) ;

extern void SetSysClockHSI_12MHz(void) ;

extern void SetSysClockMSI_4MHz(void) ;
//-------------------------------------------
uint16_t CRC16 (const uint16_t *nData, uint16_t wLength) ;

uint16_t CRC16WithSeed (uint16_t seed,const uint16_t *nData, uint16_t wLength) ;//Gerate 16 bit CRC value from series words

uint32_t CRC32(const uint32_t *nData, uint32_t wLength) ;

uint32_t CalReqCode(void) ;

uint32_t GenAuthCode(uint32_t ReqCode) ;

uint32_t VerifyAuthCode(uint32_t AuthCode) ;

void  ClrObj(uint8_t *obj,uint16_t size) ;

void  ClrObj16(uint16_t *obj,uint16_t size) ;

void  ClrObj32(uint32_t *obj,uint16_t size) ;

void  CopyObj(uint8_t *scr,uint8_t *den,uint16_t size) ;

void  CopyObj16(uint16_t *scr,uint16_t *den,uint16_t size) ;

void  CopyObj32(uint32_t *scr,uint32_t *den,uint16_t size) ;

void  InitWatchDog(void) ;

void  KickWatchDog(void) ;

int32_t get_lock(void) ;

void release_lock(void) ;

void  DisableTIM9(void) ;

void  DisableRfTickTimer(void) ;

void  DisableTIM11(void) ; 

void  EnableTIM11(void)	;

void  ShutDownSys01(void) ;//Routine use to shutdown RF01(FYF25) Transmitter system 

void  ShutDownSys021(void) ;//Routine use to shutdown RF021(FYF35) Transmitter system

void  InitSTM32Lxx(void) ;

void  InitRfTickTimer(void) ;

void  AdjustRfTickTimer(uint16_t NewCountValue) ;

__inline void ReadKeyBoard(void) ;

void  GetKeySta(void) ;//Read all normal function key input status

void  TxTokenGenProc_Bi(void) ;//Generate TxToken according to keyboard operation status in bidirection mode

void  TxTokenGenProc_Uni(void) ;//Generate TxToken according to keyboard operation status in unidirection mode

void  GetSysRstKeySta(void) ;//Read System Reset Key input status,under some circumstance

void  TurnOffRfSys_01(void) ;

void  TurnOffRfSys_021(void) ;

void  SetupLowSpeedRunMode(void) ;//Set the device to run about 262kHz--MSI range 2

void  ExitLowSpeedRunMode(void) ;//Exit low speed runstatus and enter normal run status

void  EnStandby_LCD_021(void) ;//Enter Standby mode (shutoff mode) put LCD to sleep status
void  EnStandby_NoLCD_01(void);//Enter Standby mode (shutoff mode) ,no LCD operation(For FYF25 ,etc..)

void  __Fyf25Main(void)	;

void  __Fyf35Main(void)	;

void  __Fys30Main(void)	;

void  __TestMain(void) ;

void  NoOperation(void) ;
//-------------------
void  Led_1x2_on(void) ;//Extended function(may changed for led1 and led2) for output on led 1
void  Led_1x2_off(void) ;
uint32_t Led_1x2_sta(void) ;
void  Led_2x1_on(void) ;
void  Led_2x1_off(void) ;
uint32_t Led_2x1_sta(void) ;

#endif  //__SYS_BASIC_H
