#ifndef __THIS_DEVICE_H
#define __THIS_DEVICE_H

#include "SysBasic.h"

/*** Constant Definitions ***/
//--
#define  TIME_50uS_V    44     //delay const @CPU=14.7456MHz(HSE)
#define  TIME_40uS_V    35     //
#define  TIME_20uS_V    16     //

//---------------------------------
#define  RF_BUF_LEN_MAX   10	//Define maxim buffer length for normal RF Tx/Rx operation

//----------------------------

#pragma  anon_unions

//define a big structure for application data
typedef struct TagUserAreaStruct
{
//---------------------------------MD Address 1-4
union {
 struct {
          PANEL_DAT    PaDat_L ;//received valid data from left side panel
          int16_t       RSSI_L ;//used to store RF part RSSI in dBm
         } ;
 uint16_t   PanelArea_L[4] ;
       } ;
//---------------------------------MD Address 5-8
union {
 struct {
          PANEL_DAT    PaDat_R ;//received valid data from left side panel
          int16_t       RSSI_R ;//used to store RF part RSSI in dBm
         } ;
 uint16_t  PanelArea_R[4] ;
      } ;
//--------------------------------MD Address 9-16
  //following two union must be  allocated series in memory for proper output to Host------------
  union 
  {
    TPDOStruct       RF_TPDO ;//MD 9-12,Machine receiver output PDO
    uint16_t    RF_PDODat[4] ;
   } ;
  union 
  {
    TPDO2Struct      OPS_TPDO ;//Reserve OPS data area
    uint16_t    OPS_PDODat[4] ;////MD=13-16,Local connected OPS data,build to PDO from
   } ;
//--------------------------------MD Address 17-36
  uint16_t    RevArray0[32] ;//MD17-48
  uint16_t     RevArray1[2] ;//MD49-50
  //---------------------------------------------------------------------
  uint16_t   DisplayDatBuf[32] ;//MD51-82 ,used to store current 32 monitor data
  //---------------------------------------------------------------------------------------------------
  uint16_t    RevArray2[1] ;//MD83 
  uint16_t    SweepTimeMax ;//MD84,
  uint16_t      SweepTimeC ;//MD85,
  uint16_t     BootCounter ;//MD86,
//------------------------
  uint16_t     WiredComSta ;//MD87,used to record wired com port error,=1 OK ,=0 Connection error or not used (data invalid)
    		                //bit0 - Left side OPS, bit1 -right side OPS
			                //bit2 - N.A., bit3 - N.A.
			                //bit4 - CANOpen PDO received OK, bit5 - N.A.
			                //bit7 - External receiver station communication OK
			                //bit8 - Can Dev 0(Main HOST) ,bit9-Can dev1 ,bit10-can dev2 ,bit11-can dev3
			                //bit12 - Can Dev 4 ,bit13-Can dev5 ,bit14-can dev6 ,bit15-can dev7
  uint16_t        DatReq ;//MD88,used to record display request from  any side wireless transmitter
  uint16_t      DatReq_L ;//MD89,used to record display request from left side panel
  uint16_t      DatReq_R ;//MD90------------
  uint16_t     DatReq_CP ;//MD91,Current Output data pointer for one display request
  uint16_t      DatGID_C ;//MD92,Current display data GID
  uint16_t       WcrStaW ;//MD93,
  uint16_t      MachStaW ;//MD94,Received Current machine status word
  uint16_t     Counter10ms ;//MD95
  uint16_t      HW_Version ;//MD96,Device Hardware version version number
  uint16_t      SW_Version ;//MD97,Firmware version number
  uint16_t             Err ;//MD98,Module run error 
  uint16_t       TestCtrlW ;//MD99,Used to control to do RF test
  union {
          uint16_t  TestCommandW ;//MD100,Used to control to do RF test
          uint16_t   AreaFlag100 ;//MD100,used set flag for debug
         } ;
//------------------------------
  union{
         PANEL_TX_DAT    PanelDat_RB0_L ;//Receive 0 buffer for wireless panel--in WCR mode 
           SHR_TX_DAT  ShrMachDat_RB0_L ;//Receive buffer 0 for shearer machine receiver--in wireless control transmitter mode
		   TUN_TX_DAT  TunMachDat_RB0_L ;//Receive buffer 0 for tunneller machine receiver-- in wireless control transmitter mode
		    uint16_t   RfRxBuf0_L[RF_BUF_LEN_MAX] ;//MD101-110,Universal RF Receive buffer 0
		     uint8_t   RfRxChBuf0_L[RF_BUF_LEN_MAX*2] ;//MD101-110,Universal RF Receive buffer 0--access in char mode
		 } ;
  union{
         PANEL_TX_DAT    PanelDat_RB1_L ;//Receive 1 buffer for wireless panel--in WCR mode 
           SHR_TX_DAT  ShrMachDat_RB1_L ;//Receive buffer 1 for shearer machine receiver--in wireless control transmitter mode
		   TUN_TX_DAT  TunMachDat_RB1_L ;//Receive buffer 1 for tunneller machine receiver-- in wireless control transmitter mode 
		     uint16_t  RfRxBuf1_L[RF_BUF_LEN_MAX] ;//MD111-120,Universal RF Receive buffer 1
		      uint8_t  RfRxChBuf1_L[RF_BUF_LEN_MAX*2] ;//MD111-120,Universal RF Receive buffer 1--access in char mode
	    } ;
//------------------------------
  union{
         PANEL_TX_DAT    PanelDat_RB0_R ;//Receive 0 buffer for wireless panel--in WCR mode 
           SHR_TX_DAT  ShrMachDat_RB0_R ;//Receive buffer 0 for shearer machine receiver--in wireless control transmitter mode
		   TUN_TX_DAT  TunMachDat_RB0_R ;//Receive buffer 0 for tunneller machine receiver-- in wireless control transmitter mode
		    uint16_t   RfRxBuf0_R[RF_BUF_LEN_MAX] ;//MD121-130,Universal RF Receive buffer 0
		     uint8_t   RfRxChBuf0_R[RF_BUF_LEN_MAX*2] ;//MD121-130,Universal RF Receive buffer 0--access in char mode
		 } ;
  union{
         PANEL_TX_DAT    PanelDat_RB1_R ;//Receive 1 buffer for wireless panel--in WCR mode 
           SHR_TX_DAT  ShrMachDat_RB1_R ;//Receive buffer 1 for shearer machine receiver--in wireless control transmitter mode
		   TUN_TX_DAT  TunMachDat_RB1_R ;//Receive buffer 1 for tunneller machine receiver-- in wireless control transmitter mode 
		     uint16_t  RfRxBuf1_R[RF_BUF_LEN_MAX] ;//MD131-140,Universal RF Receive buffer 1
		      uint8_t  RfRxChBuf1_R[RF_BUF_LEN_MAX*2] ;//MD131-140,Universal RF Receive buffer 1--access in char mode
	    } ;
//------------------------------
  union{
         PANEL_TX_DAT    PanelDat_TB0 ;//Transmitter buffer 0 for wireless panel(in wireless control transmitter mode )
           SHR_TX_DAT  ShrMachDat_TB0 ;//Transmitter buffer 0 for shearer machine receiver( in WCR mode	)
		   TUN_TX_DAT  TunMachDat_TB0 ;//Transmitter buffer 0 for tunneller machine receiver( in WCR mode ) 
		     uint16_t     RfTxBuf0[RF_BUF_LEN_MAX] ;//MD141-150,Universal RF Transmitter buffer 0
		      uint8_t   RfTxChBuf0[RF_BUF_LEN_MAX*2] ;//MD141-150,Universal RF Transmitter buffer 0
		 } ;
//----
  union{
         PANEL_TX_DAT       PanelDat ;//panel data that will be transmitted(in wireless control transmitter mode )
         SHR_TX_DAT       ShrMachDat ;//Received shearer machine data--in panel mode ,or dat that will copy to ShrMachDat_TB0 to transmitte--in WCR mode
		 TUN_TX_DAT       TunMachDat ;//Received  tunneller  machine data--in panel mode 
	     uint16_t        RfDatAsm[RF_BUF_LEN_MAX] ;//MD151-160,Universal RF(Rx/Tx) panel or machine data assemble area
	    } ;
//-------------------------------
  uint16_t  CANOpenID ;//MD=161
  uint16_t  TPDO_Ctrl ;//MD=162
  uint16_t  SYNC_Interval ;//MD=163,Store CANopen SYNC interval time in ms(default=5ms)
  uint16_t  PDO_Interval ;//MD=164
  uint16_t  NodeIDSim ;//MD=165
  uint16_t  CAN_TCounter ;//MD=166
  //-------------
  CANComStatistics  CAN_Sta ;//MD167-175,Used for CAN bus status recorder ,+9
  ComModOpStruct    U1ModOp ;//MD176-181,Uart1 Modbus RTU Master operation recorder object, +6
//--------------
  uint16_t    RevArray4[16] ;//MD182-197 
  uint16_t      RemoteKey ;//MD198,External or remote receiver(FYS30,FYS25) out put operation code
  uint16_t	      KeyComb ;//MD199,combined operation key code for wireless and local input--FYS30 OPS and WCR mode
  uint16_t    AreaFlag200 ;//MD200 , used to set flag for debug
//---------------------------------------------------------------------------------------------------
  uint16_t    Ultra_BUF_L[64]  ;//MD201-264
  uint16_t    Ultra_BUF_R[64]  ;//MD265-328
  uint16_t    Rev_W2 ;//MD329
  uint16_t   RevSpace_1 ;//MD330		
  RPDOStruct  RPDO_ID1[4] ;//MD331-346--Data will send to FYS35 through CAN bus
  RPDOStruct  RPDO_ID2[4] ;//MD347-362
  TPDOStruct  TPDO_RxRF  ;//MD363-366--Used for FYS35 receiver output test or verify
  TPDOStruct  TPDO_RxOPS ;//MD367-370--Used for FYS35 receiver output test or verify
  uint16_t   RevSpace_2 ;//MD371			
} UserAreaStruct ;
//------------------------------------------------------------------------------

typedef struct TagSysAreaStruct
{ //total  bytes
  int16_t   EStop_Timer ;//  
  uint16_t  SwallowArea[128] ;//used for output useless data
} SysAreaStruct ;

//------------------------------------------------------------

void SystemReset(void) ;

void InitTimer3_1uS(void) ;//Init TIM for CPU sweep measure

__inline void CpuSweepTime(uint16_t *MaxTp,uint16_t *CurTp) ;//Get the sweep time info

#endif // __THIS_DEVICE_H
/******************* END OF FILE****/
