#include "JF01_Main.h"
extern volatile   int16_t    daemon_lock ;

/*******************************************************************************
* Function Name  : __JF01_Main
* Description    : This is main function entry for JF01 type receiver
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void __JF01_Main(void) 
{
 InitSPI1_01() ;//Init the SPI1 for  JF01  operation
 InitRfEnvironment();//
 USART_Config() ;//Initialize the uart and modbus RTU function 
 if((APP_PB.CAN_Mode!=0)&&(APP_PB.CAN_Mode<0x0100))
   InitCanOpen() ;	 
 //----------------
 Timer10[RUN_IND_TIMER].pv=95 ;
 Timer10[RUN_IND_TIMER].cv=0 ;
 Timer10[RUN_IND_TIMER].csr.csword=0x4000 ; //start Timer10[RUN_IND_TIMER]
 Timer10[CM_TIMER].pv=APP_PB.U1Config.rx_t_out ;//Setup modbus slaver mode communication monitor
 Timer10[CM_TIMER].cv=0 ;
 Timer10[CM_TIMER].csr.csword=0x4000 ; //start Timer10[CM_TIMER]
 //----------------
 InitWatchDog() ;//Time out value about 10ms
 //------------------------
 InitTimer3_1uS() ;//Init TIM3 for CPU sweep measure(1=1uS)
 U_Area.AreaFlag100=0xF100 ;//MD0100
 SYS_STA=POWER_ON_INIT_STA ;
 while(1)
 {
  TimerProc() ;//user defined timer proccess
  //--------------------------------------
  switch(SYS_STA)
  {
  case DUPLEX_INIT_STA :
    RfDs.JF01Init_L.OpReq =SET_CODE+SET_UP_RX ;//Request to do Rx setup operation
    RfDs.JF01Init_R.OpReq =SET_CODE+SET_UP_RX ;//Request to do Rx setup operation
    RfDs.JF01Init_L.OpSta =NOT_INIT_STA;//
    RfDs.JF01Init_R.OpSta =NOT_INIT_STA;//
    SYS_STA=DUPLEX_RX_STA ;
    break ;
  case DUPLEX_RX_STA :
    if((IsrFlag&IFG_GTOKEN_EN))
    {
	  if(RfDs.RF_TxState_R==TX_IDLE)
	  {
        __disable_irq();//Disable IRQ
        IsrFlag &=~IFG_GTOKEN_EN ;
        RfDs.RF_State_R=RF_IDLE_STATUS ;//Set to Idle state
        __enable_irq();//Enable IRQ
//        Led_2_on ;//--debug
        if(RfDs.JF01Init_R.OpSta!=IN_RX_STA)
        {
          RfDs.JF01Init_R.OpSta=NOT_INIT_STA ;
          RfDs.JF01Init_R.OpReq=SET_CODE+SET_UP_TX ;
         }
        else
          RfDs.JF01Init_R.OpReq=SET_UP_TX ;//
        SYS_STA=DUPLEX_TX_STA ;
	   }
     }
    break ;
  case DUPLEX_TX_STA :
    if(RfDs.RF_TxState_R==TX_FINISHED)
    {
//      Led_2_off ;//--debug
      RfDs.RF_State_R=RF_IDLE_STATUS ;//Set to Idle state
      RfDs.RF_TxState_R=TX_IDLE ;
      if(RfDs.JF01Init_R.OpSta!=IN_TX_STA)
      {
        RfDs.JF01Init_R.OpSta=NOT_INIT_STA ;
        RfDs.JF01Init_R.OpReq=SET_CODE+SET_UP_RX ;//
       }
      else
        RfDs.JF01Init_R.OpReq=SET_UP_RX ;//
      SYS_STA=DUPLEX_RX_STA ;
     }
    break ;
  case UNI_INIT_STA :
	RfDs.JF01Init_L.OpReq =SET_CODE+SET_UP_RX ;//Request to do Rx setup operation
	RfDs.JF01Init_R.OpReq =SET_CODE+SET_UP_RX ;//Request to do Rx setup operation
    RfDs.JF01Init_L.OpSta=NOT_INIT_STA;//
    RfDs.JF01Init_R.OpSta=NOT_INIT_STA;//
    SYS_STA=UNI_RX_STA ;
    break ;
  case UNI_RX_STA :
    /*
	if(RfDs.JF01Init_L.OpSta==IN_RX_STA)
	{//The RF chip may be in wrong status
       RfDs.JF01Init_L.OpSta=NOT_INIT_IDLE ;
       RfDs.JF01Init_L.OpReq=SET_CODE+SET_UP_RX ;
	 }
	if(RfDs.JF01Init_R.OpSta==IN_RX_STA)
	{//The RF chip may be in wrong status
       RfDs.JF01Init_R.OpSta=NOT_INIT_IDLE ;
       RfDs.JF01Init_R.OpReq=SET_CODE+SET_UP_RX ;
	 }
	 */
    break ;
  case POWER_ON_INIT_STA :
    SYS_STA=UNI_INIT_STA;//DUPLEX_INIT_STA ;//Enter Duplex RF init status
    //-----------
    break ;
  case WAIT_RFIC_READY_STA:

    break ;
  default :
    SYS_STA=POWER_ON_INIT_STA ;
  }
//----------------------------------
  ModProcA(&UA,&ModA) ;//Modbus daemon  using channel A
  UXTxP() ;//
  RfDaemon_01_L() ;
  RfDaemon_01_R() ;
  RfRxDaemon_01_L() ;
  RfRxDaemon_01_R() ;
  if(Timer10[CM_TIMER].csr.csbit.q==1)
  {
    OpCtrl&=~CB_MDS_COM_OK ;//Wired communication lost error
   }
  if((U_Area.PaDat_L.KeyCode!=0)||(U_Area.PaDat_R.KeyCode!=0))
  {
    Led_1_on ;//if PA.0='0',set it to '1'
    Timer10[RUN_IND_TIMER].csr.csbit.q=1 ;
   }
  else if(Timer10[RUN_IND_TIMER].csr.csbit.q==1)
  {
    if(Led_1_sta)
    {
      if((OpCtrl&(CB_MDM_COM_OK+CB_MDS_COM_OK))||(U_Area.WiredComSta&0x0010))//Modbus Master or Slaver communication OK,or CANOpen PDO received normally
        Timer10[RUN_IND_TIMER].pv=5 ;
      else
        Timer10[RUN_IND_TIMER].pv=95 ;
      Led_1_off ;//if PA.0='1',reset it to '0'
     }
    else
    {
      Timer10[RUN_IND_TIMER].pv=5 ;
      Led_1_on ;//if PA.0='0',set it to '1'
     } 
    Timer10[RUN_IND_TIMER].cv=0 ;
    Timer10[RUN_IND_TIMER].csr.csword=0x4000 ; //start Timer10[RUN_IND_TIMER]
   }
  RSSI_Filter_01() ;//Get and calculate the RSSI of JF01
   
  if((APP_PB.CAN_Mode!=0)&&(APP_PB.CAN_Mode<0x0100))
  {  
 	CANOpen_Proc() ;  
   }

 // ActiveAntenna() ;//For SC_GN shearer
   
  if((U_Area.TestCtrlW==TEST_KEY_ID_A)&&((U_Area.TestCommandW&0x00ff)==0x00a5))
  {//MD99=0x5a0f ,MD100=0xx0a5
    U_Area.TestCtrlW    =0x0101 ;
	U_Area.TestCommandW &=0xff00 ;
    TestCtrlW=U_Area.TestCommandW ;	//MD100
	RfTestEntry_01() ;//This call nerver return
   }
  if(U_Area.AreaFlag200==0x1234)//MD20356,==0x1234
  {
    U_Area.AreaFlag200=0x4321 ;
    JF01_B_Read_L(JF01_IOCFG2,(uint8_t *) &U_Area.Ultra_BUF_L[0],47) ;//---MD20001
    JF01_B_Read_R(JF01_IOCFG2,(uint8_t *) &U_Area.Ultra_BUF_L[24],47) ;//---MD20025
   }
  if(U_Area.AreaFlag200==0x2345)//MD20356,==0x2345
  {
    U_Area.AreaFlag200=0x5432 ;
    JF01_B_Read_L(JF01_MARCSTATE,(uint8_t *) &U_Area.Ultra_BUF_L[48],1) ;//---MD20049HB
    JF01_B_Read_R(JF01_MARCSTATE,(uint8_t *) &U_Area.Ultra_BUF_L[49],1) ;//---MD20050HB
   }
//----------------------------------------
  SysConfigDaemon() ;//For parameter change IAP
  KickWatchDog();//kick the watchdog
  CpuSweepTime(&U_Area.SweepTimeMax,&U_Area.SweepTimeC);
  if((CK_HSI_Flag==0)&&(RCC->CR&0x00020000)==0)//system init set to use HSE,but HSE not ready
  {
   SCB->AIRCR = 0x05fa0001;//do system & core reset
   }
 }
}
//------------------------------------

void  Dat2HostProc_01(void)
{
//------------------------------------------------------------------- 
 U_Area.RF_TPDO.KeyCode_L=U_Area.PaDat_L.KeyCode ;
 U_Area.RF_TPDO.KeyCode_R=U_Area.PaDat_R.KeyCode ;
 //--
 U_Area.RF_TPDO.PanSta_L=0x00 ;
 if(U_Area.PaDat_L.PSta.BatteryAlarm) U_Area.RF_TPDO.PanSta_L|=0x80 ;//Battery alarm
 if(U_Area.RSSI_L>=(RSSI_LIMIT+5)) U_Area.RF_TPDO.PanSta_L|=0x40 ;//RSSI good flag
 if(U_Area.PaDat_L.PSta.KeyBoardError) U_Area.RF_TPDO.PanSta_L|=0x20 ;//Keyboard error alarm
 if(OpCtrl&CB_T_ONLINE_L) U_Area.RF_TPDO.PanSta_L|=0x10 ;//Transsmiter online flag
 //--
 U_Area.RF_TPDO.PanSta_R=0x00 ;
 if(U_Area.PaDat_R.PSta.BatteryAlarm) U_Area.RF_TPDO.PanSta_R|=0x80 ;//Battery alarm
 if(U_Area.RSSI_R>=(RSSI_LIMIT+5)) U_Area.RF_TPDO.PanSta_R|=0x40 ;//RSSI good flag
 if(U_Area.PaDat_R.PSta.KeyBoardError) U_Area.RF_TPDO.PanSta_R|=0x20 ;//Keyboard error alarm
 if(OpCtrl&CB_T_ONLINE_R) U_Area.RF_TPDO.PanSta_R|=0x10 ;//Transsmiter online flag
 //
 U_Area.RF_TPDO.RSSI_L=(s8) U_Area.RSSI_L ;
 U_Area.RF_TPDO.RSSI_R=(s8) U_Area.RSSI_R ;
//--------------------------------
 U_Area.KeyComb =U_Area.RF_TPDO.KeyCode_L|U_Area.RF_TPDO.KeyCode_R ;
	
}
//-------------------
void Proc10mS_JF01(void) //Timed process at frequency=100Hz
{
  ;
}

void Proc100mS_JF01(void)
{
  if(APP_PB.CAN_Mode!=0)
  {
	CANErrMonitor() ;  
	Dat2HostProc_01();
   }
}

void Proc1S_JF01(void) //Timed process at frequency=1Hz
{
  ;
}

void Proc1H_JF01(void) //Timed process at interval 1 hour
{
  ;
}


