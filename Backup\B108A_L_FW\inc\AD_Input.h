#ifndef __AD_INPUT_H
#define __AD_INPUT_H
#include "ThisDevice.h"
#include "SysConfig.h"
//--------------------------------------------------------------------------
#define  ADC_CH_NUM     2     
//ADC clock set to 4MHz(HSI freqency 16MHz)
#define  ADC_INIT_STA          0
#define  ADC_POWER_ON_INIT     1
#define  ADC_WAIT_FOR_START    2
#define  ADC_CONVERTING        3
#define  ADC_GET_RESULT        4
#define  ADC_POWER_DOWN_STA    5

void  InitOnchipADC(void) ;

void  ADC12Daemon(void) ;

void  ADC_DMA_ISR(void) ;

void  CalBatVoltage(uint16_t  rawv) ;

void  CalSysTemp(uint16_t  rawv) ;

#endif  //__AD_INPUT_H

