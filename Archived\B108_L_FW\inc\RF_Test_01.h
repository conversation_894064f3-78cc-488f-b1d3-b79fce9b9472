#ifndef __RF_TEST_01_H
#define __RF_TEST_01_H
//-----------------------
#include  "stm32l1xx.h"
#include  "ThisDevice.h"
#include  "RF_BasicFun.h"
#include  "Timer.h"
#include  "AD_Input.h"
#include  "UartFun_A.h"
#include  "KeyAndMenu.h"
//-----------------------
extern  UARTStruct   UB ;//

extern  ModStruct  ModB ;

extern  uint32_t  ADC_STA ;
//-----------------------
void     SetupTestRegs_01(void) ;
void     RxTestDaemon_01(void) ;
void     TxTestDaemon_01(void) ;
void     RSSI_Test_Filter_01(void) ;
uint8_t  WriteFIFO_Const_01(uint8_t Data ,uint8_t Len) ;
int16_t  ReadRSSITest_01(void) ;
__inline void  EnterTxTestMode_01(void) ;
__inline void  EnterRxTestMode_01(void) ;

#endif  //__RF_TEST_01_H
