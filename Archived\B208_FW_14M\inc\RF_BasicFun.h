#ifndef __RF_COM_FUN_H
#define __RF_COM_FUN_H

#include  "stm32f10x.h"
#include  "Timer.h"
#include  "SysBasic.h"
#include  "ThisDevice.h"
#include  "SysConfig.h"
//-------------Rfc Timer related define------------------
#define  RFC_TIMER_NUM    2
#define  RFC_TIMER10_NUM  4

#define  RF_TIMER_L    0 //dispatch a RfcTimer act as RF chip operation monitor timer
#define  RF_TIMER_R    1 //dispatch a RfcTimer act as RF chip operation monitor timer

#define  RF_OP_TIMER_L   0 //dispacth a RfcTimer10 for left side RF Operation monitor timer
#define  RF_OP_TIMER_R   1 //dispatch a RfcTimer10 for right side RF Operation  monitor timer
#define  AIR_TIMER_L     2 //dispacth a RfcTimer10 for left side AIR linkage monitor timer
#define  AIR_TIMER_R     3 //dispatch a RfcTimer10 for right side AIR linkage monitor timer

//Define for IC021 Value of time-out timer during calibration
#define  OSC_TIMEOUT        3
#define  IF_CAL_FINE_TIME   8
#define  MIN_TIMEOUT        2
//-------------------------------------------------------------------------
#define  TEST_KEY_ID_A     0x5a0f
#define  TEST_KEY_ID_B     0xf0a5
#define  COMMAND_KEY       0xaa55

#define  LEFT_CRC_SEED     0xfa5f
#define  RIGHT_CRC_SEED    0xf5af
#define  MACHINE_CRC_SEED  0x5ffa
#define  ENC_MASK          0x0c00

#define  SOF_ID1 0xcc		/* First byte of unique SOF identifier */
#define  SOF_ID2 0x33		/* Second byte of unique SOF identifier */

#define  RF_ACK_NUM         4
#define  RSSI_LIMIT        -105
#define  PREAMBLE_REQ   10    /* Number of bits required in addition to */
                              /* the initial 8 bits for the preamble to be */
                              /* accepted */
//--------------------------------------------------------------------------
#define  RFIC_OSC_UNKNOW   0x0000
#define  RFIC_OSC_READY	   0x5aa5
//--------------------------------------------------------------------------
#define  RF_IDLE_STATUS    0x0000
#define  TX_STATUS         0x0002
#define  RX_STATUS         0x0004
//---------------------------------------
#define  TX_IDLE                 0x0000
#define  TX_FINISHED             0x0002
#define  TX_1SEND_PREAMBLE_SEG   0x0004
#define  TX_1SEND_SOF_ID1        0x0006
#define  TX_1SEND_SOF_ID2        0x0008
#define  TX_1SEND_DP             0x000a
#define  TX_WAIT_FOR_END         0x000c
#define  TX_WAIT_FOR_END2        0x000e
#define  TX_WAIT_FOR_END3        0x0010
#define  TX_RF_TEST              0x0012
//--
#define  TX_SENDING              0x0004
#define  TX_READY                0x0006

//----------------------------------------
#define  RX_NO_INIT_IDLE         0x0000
#define  RX_DP_FINISHED          0x0002
#define  RX_SEARCH_PREAMBLE      0x0004
#define  RX_SEARCH_SOF1          0x0006
#define  RX_SEARCH_SOF2          0x0008
#define  RX_RECIEVE_DP           0x000a
#define  RX_RF_TEST_INIT         0x000c
#define  RX_RF_TEST              0x000e
//--
#define  RX_RECEIVING            0x0004
#define  RX_READY                0x0006

//-----------------------------------------------------------------------------
#define  SET_CODE          0xa500

#define  NOT_INIT_STA      0x0000
#define  IN_TX_STA         0x0002
#define  IN_RX_STA         0x0004
#define  SET_UP_RX         0x0006
#define  SET_UP_RX_WAIT0   0x0008
#define  SET_UP_RX_WAIT1   0x000a
#define  SET_UP_RX_WAIT2   0x000c
#define  SET_UP_RX_WAIT3   0x000e
#define  SET_UP_TX         0x0010
#define  SET_UP_TX_WAIT0   0x0012
#define  SET_UP_TX_WAIT1   0x0014
#define  SET_UP_TX_WAIT2   0x0016
#define  TX_TEST_STA       0x0018
#define  RX_TEST_STA       0x001a
#define  SET_UP_TEST       0x001c
#define  IR_CAL_REQ        0x001e
#define  IN_SLEEP_STA      0x0020
//---------------------------------------------------------------------------
#define  PA_TAB_LEN     5			 //Only use for JF01
//-----------------------------------------------------------------------------
// Configuration Registers
#define  JF01_IOCFG2           (uint8_t) 0x00    // GDO2 output pin configuration
#define  JF01_IOCFG1           (uint8_t) 0x01    // GDO1 output pin configuration
#define  JF01_IOCFG0           (uint8_t) 0x02    // GDO0 output pin configuration
#define  JF01_FIFOTHR          (uint8_t) 0x03    // RX FIFO and TX FIFO thresholds
#define  JF01_SYNC1            (uint8_t) 0x04    // Sync word, high byte
#define  JF01_SYNC0            (uint8_t) 0x05    // Sync word, low byte
#define  JF01_PKTLEN           (uint8_t) 0x06    // Packet length
#define  JF01_PKTCTRL1         (uint8_t) 0x07    // Packet automation control
#define  JF01_PKTCTRL0         (uint8_t) 0x08    // Packet automation control
#define  JF01_ADDR             (uint8_t) 0x09    // Device address
#define  JF01_CHANNR           (uint8_t) 0x0A    // Channel number
#define  JF01_FSCTRL1          (uint8_t) 0x0B    // Frequency synthesizer control
#define  JF01_FSCTRL0          (uint8_t) 0x0C    // Frequency synthesizer control
#define  JF01_FREQ2            (uint8_t) 0x0D    // Frequency control word, high byte
#define  JF01_FREQ1            (uint8_t) 0x0E    // Frequency control word, middle byte
#define  JF01_FREQ0            (uint8_t) 0x0F    // Frequency control word, low byte
#define  JF01_MDMCFG4          (uint8_t) 0x10    // Modem configuration
#define  JF01_MDMCFG3          (uint8_t) 0x11    // Modem configuration
#define  JF01_MDMCFG2          (uint8_t) 0x12    // Modem configuration
#define  JF01_MDMCFG1          (uint8_t) 0x13    // Modem configuration
#define  JF01_MDMCFG0          (uint8_t) 0x14    // Modem configuration
#define  JF01_DEVIATN          (uint8_t) 0x15    // Modem deviation setting
#define  JF01_MCSM2            (uint8_t) 0x16    // Main Radio Cntrl State Machine config
#define  JF01_MCSM1            (uint8_t) 0x17    // Main Radio Cntrl State Machine config
#define  JF01_MCSM0            (uint8_t) 0x18    // Main Radio Cntrl State Machine config
#define  JF01_FOCCFG           (uint8_t) 0x19    // Frequency Offset Compensation config
#define  JF01_BSCFG            (uint8_t) 0x1A    // Bit Synchronization configuration
#define  JF01_AGCCTRL2         (uint8_t) 0x1B    // AGC control
#define  JF01_AGCCTRL1         (uint8_t) 0x1C    // AGC control
#define  JF01_AGCCTRL0         (uint8_t) 0x1D    // AGC control
#define  JF01_WOREVT1          (uint8_t) 0x1E    // High byte Event 0 timeout
#define  JF01_WOREVT0          (uint8_t) 0x1F    // Low byte Event 0 timeout
#define  JF01_WORCTRL          (uint8_t) 0x20    // Wake On Radio control
#define  JF01_FREND1           (uint8_t) 0x21    // Front end RX configuration
#define  JF01_FREND0           (uint8_t) 0x22    // Front end TX configuration
#define  JF01_FSCAL3           (uint8_t) 0x23    // Frequency synthesizer calibration
#define  JF01_FSCAL2           (uint8_t) 0x24    // Frequency synthesizer calibration
#define  JF01_FSCAL1           (uint8_t) 0x25    // Frequency synthesizer calibration
#define  JF01_FSCAL0           (uint8_t) 0x26    // Frequency synthesizer calibration
#define  JF01_RCCTRL1          (uint8_t) 0x27    // RC oscillator configuration
#define  JF01_RCCTRL0          (uint8_t) 0x28    // RC oscillator configuration
#define  JF01_FSTEST           (uint8_t) 0x29    // Frequency synthesizer cal control
#define  JF01_PTEST            (uint8_t) 0x2A    // Production test
#define  JF01_AGCTEST          (uint8_t) 0x2B    // AGC test
#define  JF01_TEST2            (uint8_t) 0x2C    // Various test settings
#define  JF01_TEST1            (uint8_t) 0x2D    // Various test settings
#define  JF01_TEST0            (uint8_t) 0x2E    // Various test settings

// Status registers
#define JF01_PARTNUM          (uint8_t) 0x30     // Part number
#define JF01_VERSION          (uint8_t) 0x31     // Current version number
#define JF01_FREQEST          (uint8_t) 0x32     // Frequency offset estimate
#define JF01_LQI              (uint8_t) 0x33     // Demodulator estimate for link quality
#define JF01_RSSI             (uint8_t) 0x34     // Received signal strength indication
#define JF01_MARCSTATE        (uint8_t) 0x35     // Control state machine state
#define JF01_WORTIME1         (uint8_t) 0x36     // High byte of WOR timer
#define JF01_WORTIME0         (uint8_t) 0x37     // Low byte of WOR timer
#define JF01_PKTSTATUS        (uint8_t) 0x38     // Current GDOx status and packet status
#define JF01_VCO_VC_DAC       (uint8_t) 0x39     // Current setting from PLL cal module
#define JF01_TXBYTES          (uint8_t) 0x3A     // Underflow and # of bytes in TXFIFO
#define JF01_RXBYTES          (uint8_t) 0x3B     // Overflow and # of bytes in RXFIFO

// Multi byte memory locations
#define JF01_PATABLE          (uint8_t) 0x3E
#define JF01_TXFIFO           (uint8_t) 0x3F
#define JF01_RXFIFO           (uint8_t) 0x3F

// Definitions for burst/single access to registers
#define JF01_WRITE_BURST      (uint8_t) 0x40
#define JF01_READ_SINGLE      (uint8_t) 0x80
#define JF01_READ_BURST       (uint8_t) 0xC0

// Strobe commands
#define JF01_SRES             (uint8_t) 0x30     // Reset chip.
#define JF01_SFSTXON          (uint8_t) 0x31     // Enable and calibrate frequency synthesizer (if MCSM0.FS_AUTOCAL=1).
                                                 // If in RX/TX: Go to a wait state where only the synthesizer is
                                                 // running (for quick RX / TX turnaround).
#define JF01_SXOFF            (uint8_t) 0x32     // Turn off crystal oscillator.
#define JF01_SCAL             (uint8_t) 0x33     // Calibrate frequency synthesizer and turn it off
                                                 // (enables quick start).
#define JF01_SRX              (uint8_t) 0x34     // Enable RX. Perform calibration first if coming from IDLE and
                                                 // MCSM0.FS_AUTOCAL=1.
#define JF01_STX              (uint8_t) 0x35     // In IDLE state: Enable TX. Perform calibration first if
                                                 // MCSM0.FS_AUTOCAL=1. If in RX state and CCA is enabled:
                                                 // Only go to TX if channel is clear.
#define JF01_SIDLE            (uint8_t) 0x36     // Exit RX / TX, turn off frequency synthesizer and exit
                                                 // Wake-On-Radio mode if applicable.
#define JF01_SAFC             (uint8_t) 0x37     // Perform AFC adjustment of the frequency synthesizer
#define JF01_SWOR             (uint8_t) 0x38     // Start automatic RX polling sequence (Wake-on-Radio)
#define JF01_SPWD             (uint8_t) 0x39     // Enter power down mode when CSn goes high.
#define JF01_SFRX             (uint8_t) 0x3A     // Flush the RX FIFO buffer.
#define JF01_SFTX             (uint8_t) 0x3B     // Flush the TX FIFO buffer.
#define JF01_SWORRST          (uint8_t) 0x3C     // Reset real time clock.
#define JF01_SNOP             (uint8_t) 0x3D     // No operation. May be used to pad strobe commands to two
                                                 // bytes for simpler software.
//----------------------------------------------------------------------------------
// Chip Status Byte
//----------------------------------------------------------------------------------

// Bit fields in the chip status byte
#define JF01_STATUS_CHIP_RDYn_BM             (uint8_t) 0x80
#define JF01_STATUS_STATE_BM                 (uint8_t) 0x70
#define JF01_STATUS_FIFO_BYTES_AVAILABLE_BM  (uint8_t) 0x0F

// Chip states
#define JF01_STATE_IDLE                      (uint8_t) 0x00
#define JF01_STATE_RX                        (uint8_t) 0x10
#define JF01_STATE_TX                        (uint8_t) 0x20
#define JF01_STATE_FSTXON                    (uint8_t) 0x30
#define JF01_STATE_CALIBRATE                 (uint8_t) 0x40
#define JF01_STATE_SETTLING                  (uint8_t) 0x50
#define JF01_STATE_RX_OVERFLOW               (uint8_t) 0x60
#define JF01_STATE_TX_UNDERFLOW              (uint8_t) 0x70

//-------------------------------------------
// Other register bit fields
//-------------------------------------------
#define JF01_LQI_CRC_OK_BM    (uint8_t) 0x80
#define JF01_LQI_EST_BM       (uint8_t) 0x7F
#define RSSI_OFFSET                       74   //RSSI offset value for 315MHz

#define FIFO_THRESHOLD        (uint8_t) 0x07
#define FIFO_THRESHOLD_BYTES  (uint8_t)   32
#define FIFO_SIZE             (uint8_t)   64
//-------------------------------------
#define  PA_TAB_LEN     5
//-------------------------------------

/******************************************************************************
         Define Dedicate Structure used for RF Control
*******************************************************************************/
#pragma anon_unions
typedef  struct TagRFC_WorkDS_t{
//------------------MD Address 20001-20006
  volatile uint16_t    RF_State_L ; //Total RF part operation state varible
  volatile uint16_t    RF_TxState_L ; //State varible for TX operation
  volatile uint16_t    RF_RxState_L ; //used to switch RX processs status
  volatile uint16_t    RF_State_R ; //Total RF part operation state varible
  volatile uint16_t    RF_TxState_R ; //State varible for TX operation
  volatile uint16_t    RF_RxState_R ; //used to switch RX processs status
//------------------MD Address 20007-20023
  union
  {
    IC021InitStruct   IC021Init_L ;//MD20007-20014(16 bytes)for SRD IC021 initializtion control & status,+8
	//  uint16_t  OpReq ;//Input for Initalization request
	//  uint16_t  OpSta ;//Hold current Operation status
	//  int16_t  CalTemp ;//used to store environment temperature when perform the calibration
	                //if more than 12 celsuis degree changed,need to recalibration IC021
	//  uint16_t  R5_IR_Cal_I ;//store IR calibration adjust value for I channel(GAIN,PHASE)
	//  uint16_t  R5_IR_Cal_Q ;//store IR calibration adjust value for Q channel(GAIN,PHASE)
	//  uint16_t  FilterCalRaw ;
	//  uint32_t  FilterCalReadback ;//used to store read back IF Filter fine calibration result
    CC01InitStruct  JF01Init_L ;//MD20007-20011(10 bytes)for  JF01 initializtion control & status ,+5
	//  volatile uint16_t  OpReq ;//Input for Initalization request
	//  volatile uint16_t  OpSta ;//Hold current Operation status
	//  volatile int16_t  CalTemp ;//used to store environment temperature when perform the calibration
	                //if more than 12 celsuis degree changed,need to recalibration CC01
	//  volatile uint8_t  RB_FSCAL3 ;//FS calibration result FSCAL3
	//  volatile uint8_t  RB_FSCAL2 ;//FS calibration result FSCAL2
	//  volatile uint8_t  RB_FSCAL1 ;//FS calibration result FSCAL1
	//  volatile uint8_t  rev_B ;
	} ;
  union
  {
    IC021InitStruct   IC021Init_R ;//MD20015-20022(16 bytes)for SRD IC021 initializtion control & status ,+8
	//  uint16_t  OpReq ;//Input for Initalization request
	//  uint16_t  OpSta ;//Hold current Operation status
	//  int16_t  CalTemp ;//used to store environment temperature when perform the calibration
	                //if more than 12 celsuis degree changed,need to recalibration IC021
	//  uint16_t  R5_IR_Cal_I ;//store IR calibration adjust value for I channel(GAIN,PHASE)
	//  uint16_t  R5_IR_Cal_Q ;//store IR calibration adjust value for Q channel(GAIN,PHASE)
	//  uint16_t  FilterCalRaw ;
	//  uint32_t  FilterCalReadback ;//used to store read back IF Filter fine calibration result
    CC01InitStruct  JF01Init_R ;//MD20015-20019(10 bytes)for  JF01 initializtion control & status ,+5
	//  volatile uint16_t  OpReq ;//Input for Initalization request
	//  volatile uint16_t  OpSta ;//Hold current Operation status
	//  volatile int16_t  CalTemp ;//used to store environment temperature when perform the calibration
	                //if more than 12 celsuis degree changed,need to recalibration CC01
	//  volatile uint8_t  RB_FSCAL3 ;//FS calibration result FSCAL3
	//  volatile uint8_t  RB_FSCAL2 ;//FS calibration result FSCAL2
	//  volatile uint8_t  RB_FSCAL1 ;//FS calibration result FSCAL1
	//  volatile uint8_t  rev_B ;
   } ;
//--------------
  uint8_t    TxPackageLen ;//MD20023,Tx pachage length in current mode
  uint8_t    RxPackageLen ;//Rx pachage length in current mode
  uint16_t       AreaFlag024 ;//MD20024,==0xaaaa
//------------------MD Address 20025-20030
  uint16_t    RF_TxPackage_L ;//MD20025,transmitted package number through RF linkage to the Pannel
  uint16_t    RF_RxPackage_L ;//MD20026,received left side package number through RF linkage
  uint16_t        RF_RxErr_L ;//MD20027,Total left side CRC error ocurred in received package
  uint16_t    RF_TxPackage_R ;//MD20028,reserved postion
  uint16_t    RF_RxPackage_R ;//MD20029,received right side package number through RF linkage
  uint16_t        RF_RxErr_R ;//MD20030,Total right side CRC error ocurred in received package
//------------------MD Address 20031-20034
  uint8_t     flag_L ;//MD20031 ,
  uint8_t     flag_R ;
//-----
  uint8_t    RF_OpcReq  ; //MD20032 ,Flag for RF TX or RX operation request
  uint8_t    RF_TimeAct ; //used to record partner response time in Bidirection mode
//-----
  uint8_t    RF_ACK_Count_L ;//MD20033 ,Counter for leftside Pannel meassage acknowledge
  uint8_t    RF_ACK_Count_R ;//Counter for rightside Pannel meassage acknowledge
//-----
  volatile  uint8_t  RBuf_StaL ;//MD20034 ,used to reflect receiver buffer status,bit0-1=01 means left channel bufffer0 inused by ISR ,
                      //=11 means data in Lbuffer0 OK ,bit2-3=01 means channel buffer1 inused by ISR,=11 data OK
  volatile  uint8_t  RBuf_StaR ;//used to reflect receiver buffer status,bit0-1=01 means right channel bufffer0 inused by ISR ,
                      //=11 means data in Lbuffer0 OK ,bit2-3=01 means channel buffer1 inused by ISR,=11 data OK
//------------------MD Address 20035-20048
int16_t     RSSI_L ;//MD Address 20035,used to store RF part RSSI in dBm
int16_t     RSSI_R ;//MD Address 20036,used to store RF part RSSI in dBm
uint16_t    RSSI_BUF_L[4] ;//MD Address 20037-20040,
uint16_t    RSSI_BUF_R[4] ;//MD Address 20041-20044,
int16_t     RSSI_D_L ;//MD Address 20045,used to store direct read RF part RSSI in dBm
int16_t     RSSI_D_R ;//MD Address 20046,used to store direct read RF part RSSI in dBm

//------------------MD Address 20047-20048
uint16_t    char_count_L ;
uint16_t    char_count_R ;
//------------------MD Address 20049-20054
uint16_t    TestCtrl ;//MD Address 20049
uint16_t    rev_w0 ;//MD Address 20050

uint16_t    DClkCnt_L ;	// MD Address 20051
uint16_t    DClkCnt_R ;	// MD Address 20052
uint16_t    DClkCntChk_L ;// MD Address 20053
uint16_t    DClkCntChk_R ;// MD Address 20054
//------------------MD Address 20055-20072
uint16_t    EntTxCnt_L ; //MD20055
uint16_t    EntRxCnt_L ; //MD20056
uint16_t    EntTxCnt_R ; //MD20057
uint16_t    EntRxCnt_R ; //MD20058
uint16_t    RxAttenFlag_L ;//MD20059,Used for JF01
uint16_t    RxAttenFlag_R ;//MD20060,Used for JF01
uint16_t    Rev_A[38] ;//MD20061--20098,Change the array lenght to allow following varible locade at fixed modbus address
uint16_t    RfPollNum ;//MD20099--RF channel poll access number-even or odd will set the ToggleBit in Mach Tx data frame
uint16_t    AreaFlag100;//Fixd MD 20100 address flag
//-------------------------- 
uint16_t  PARA_M_BUF[64] ;//MD Address 20101--20164,Reserved
//--------------------------
union
{ 
  PAccessReqDS_t  PAccDs ;//Parameter access request input
  uint16_t  PAccArr[4] ;//MD 20165-20168
 } ;
union
{
  struct
  {//MD 20169- ,
	 uint16_t   RfChannelChangeReq ;//
	 uint16_t   RfChannelNew ; //
	 uint16_t   RfChannelChangeAck ;//
   } ;
  uint16_t  RfChannelPara[16] ;//MD20169-20184 ,Reserved
};
union
{ 
  PAccessSta_t  PAccStaS ;//Parameter access function status info
  uint16_t  PAccSArr[8] ;//MD 20185-20192,
 } ;
uint16_t TArray[8] ;//MD 20193-20200
} RFC_WorkDS_t ;

//---------------Extern Varible declare---------------------------------
extern  uint32_t APB1_Clock ;//defined in "system_stm32f10xc"
extern  uint32_t APB2_Clock ;//defined in "system_stm32f10xc"

extern  timer_t Timer[TIM_NUM]  ;
extern  timer_t Timer10[TIM10ms_NUM]  ;//user defined timer(1 tick=10ms)

extern  timer_t RfcTimer[RFC_TIMER_NUM]  ;//system ISR process
extern  timer_t RfcTimer10[RFC_TIMER10_NUM]  ;//user level timer(1 tick=10ms),RFC_TIMER10_NUM==2

extern  const uint16_t wCRCTable[] ;

extern  volatile  uint32_t   OpCtrl ;
extern  volatile  uint32_t   GFlag  ;
extern  volatile  uint32_t   GFlag_Pre ;

extern  volatile   uint16_t   IsrFlag  ;//Special flag for used in ISR	,Begin with IFG_
extern  volatile  uint16_t   TestKeyA, TestKeyB, TestCtrlW ;

extern  SysAreaStruct    S_Area ;//Extern defined an object for system data --in file ".h"
extern  UserAreaStruct   U_Area ;//Extern defined an object for user data

extern  SysConfigA_t      APP_PA ;//Device parameter Group A, ID=0x0081

extern  SysConfigB_t      APP_PB ;//Device parameter Group B, ID=0x0082

extern  SysConfigC_t   *APP_PC_p ;//Device parameter pointer Group C, ID=0x0083

extern  SysConfigD_t   *APP_PD_p ;//Device parameter pointer Group D, ID=0x0084

//-----------------------------------------------------------------------------
extern  uint8_t  DefPATable[] ;

extern  RFC_WorkDS_t   RfDs ;//Main data struct used for RF related function

extern IC021RxSwitchReg   *Rx021_Ch_p_L ;//A pointer to Left side Rx work channel register of IC021
extern IC021TxSwitchReg   *Tx021_Ch_p_L ;//A pointer to Left side Tx work channel register of IC021
extern IC021RxSwitchReg   *Rx021_Ch_p_R ;//A pointer to Right side Rx work channel register of IC021
extern IC021TxSwitchReg   *Tx021_Ch_p_R ;//A pointer to Right side Tx work channel register of IC021

extern JF01SwitchReg       *Rx01_Ch_p_L ;//A pointer to Left side Rx work channel register of JF01
extern JF01SwitchReg       *Tx01_Ch_p_L ;//A pointer to Left side Tx work channel register of JF01
extern JF01SwitchReg       *Rx01_Ch_p_R ;//A pointer to Right side Rx work channel register of JF01
extern JF01SwitchReg       *Tx01_Ch_p_R ;//A pointer to Right side Tx work channel register of JF01

extern uint8_t   *RfDat_rp_L  ;//pointer used for Left channel RF receiveing  data
extern uint8_t   *RfDat_tp_L  ;//pointer used for Left channel RF Transmitte data 
extern uint8_t   *RfDat_rp_R  ;//pointer used for Right channel RF receiveing  data
extern uint8_t   *RfDat_tp_R  ;//pointer used for Right channel RF Transmitte data 

extern uint8_t   *test_p_L ;//pointer used to store current test output poistion of RX data

extern uint8_t   *test_p_R ;//pointer used to store current testoutput poistion of RX data

//-----------------------------------------------------------------------------
extern  volatile  uint16_t   RSSI_STA_L,RSSI_STA_R,RSSI_BufIndex_L,RSSI_BufIndex_R ;

extern  volatile uint16_t  RFChipOscSta  ;

extern  __IO uint32_t  GFlag ;

extern  uint8_t  ByteC_1,ByteC_2 ;

//-----------------------------------------------------------------------------------

//-------------------FUNCTION DECLARE-----------------------------------
extern void    InitRfcTimer(void) ;//Timer function for RF Control operation 
extern void    RfcTimerFun(void) ;//Timer function for RF Control,Processed in System tick ISR
extern void    WaitSomeTime(uint16_t delay) ;
uint16_t       CRC16WithSeed (uint16_t seed,const uint16_t *nData, uint16_t wLength) ;//Gerate 16 bit CRC value from series words

void InitRfTickTimer(void);
void InitRfTickTimer_HSI(void);

void  InitRfEnvironment(void) ;

void  RfSetupTestEntry(__IO uint8_t *Data) ;

//--------------------------------------------------------------
void            InitSPI1_021(void) ;
void            WriteReg_021_L(uint32_t *regv_p) ;
uint16_t        ReadReg_021_L(uint8_t readback_config) ;//in gpio mode
uint16_t        Read_AFC_021_L(void);
int16_t         Read_RSSI_021_L(void);
uint16_t        ReadFilterCal_021_L(void);
uint16_t        ReadVersion_021_L(void);
int16_t         ReadTemperature_021_L(void);
__inline void   EnterRxMode_021_L(void) ;

//--------------------------------------------------------------
void            WriteReg_021_R(uint32_t *regv_p) ;
uint16_t        ReadReg_021_R(uint8_t readback_config) ;//in gpio mode
int16_t         Read_RSSI_021_R(void);
uint16_t        ReadFilterCal_021_R(void);
uint16_t        ReadVersion_021_R(void);
int16_t         ReadTemperature_021_R(void) ;

__inline  uint16_t   Read_AFC_021_R(void);

__inline  void   EnterTxMode_021_R(void) ;
__inline  void   EnterRxMode_021_R(void) ;

void  EXTI3_ISR_021(void) ;
void  EXTI9_5_ISR_021(void) ;

//---------------------------------------------------
void  RSSI_Filter_021(void) ;
void  RfRxDaemon_021_L(void) ;
void  RfDaemon_021_L(void) ;
void  RfRxTestDaemon_021_L(void) ;

void  RfRxDaemon_021_R(void) ;
void  RfDaemon_021_R(void) ;
void  RfRxTestDaemon_021_R(void) ;
//---------------------------------------------------
void  ShrMachDatTxLoad_021(void) ;
void  TunMachDatTxLoad_021(void) ;

void  PanelDatRx_021_L(void) ;
void  PanelDatRx_021_R(void) ;

void  RfTestEntry_021(void) __attribute__ ((noreturn)) ;

//----------------------------------------------------
void  InitSPI1_01(void);
void  ResetJF01_L(void) ;
uint8_t  JF01_B_Read_L(uint8_t Add, uint8_t *Data_p,uint8_t Len) ;
uint8_t  JF01ReadReg_L(uint8_t Add, uint8_t *Data_p);
uint8_t  JF01ReadRSSIReg_L(void);
uint8_t  JF01ReadStaReg_L(uint8_t Add);
uint8_t  JF01GetRxSta_L(void);
uint8_t  JF01GetTxSta_L(void);
uint8_t  JF01_B_Write_L(uint8_t Add, uint8_t *Data_p,uint8_t Len);
void  JF01WriteReg_L(uint8_t Add, uint8_t Data);
uint8_t  JF01CmdStrobe_L(uint8_t cmd);
uint8_t  JF01ReadFIFO_L(uint8_t *Data_p ,uint8_t Len);
uint8_t  JF01WriteFIFO_L(uint8_t *Data_p ,uint8_t Len);
uint8_t  JF01ReadPATable_L(uint8_t *Data_p ,uint8_t Len);
uint8_t  JF01WritePATable_L(uint8_t *Data_p ,uint8_t Len) ;
void  SetupJF01PD_L(void);
uint8_t  ReadJF01FSCal_L(uint8_t *Data_p);
void  JF01RegConfig_L(JF01Registers  *rfConfig);
void  RfDaemon_01_L(void);
int16_t  Read_RSSI_01_L(void) ;

__inline void  JF01EnterTxMode_L(void);
__inline void  JF01EnterRxMode_L(void);

//---------------------------------
void  ResetJF01_R(void) ;
uint8_t  JF01_B_Read_R(uint8_t Add, uint8_t *Data_p,uint8_t Len) ;
uint8_t  JF01ReadReg_R(uint8_t Add, uint8_t *Data_p);
uint8_t  JF01ReadRSSIReg_R(void) ;
uint8_t  JF01ReadStaReg_R(uint8_t Add);
uint8_t  JF01GetRxSta_R(void);
uint8_t  JF01GetTxSta_R(void);
uint8_t  JF01_B_Write_R(uint8_t Add, uint8_t *Data_p,uint8_t Len);
void  JF01WriteReg_R(uint8_t Add, uint8_t Data);
uint8_t  JF01CmdStrobe_R(uint8_t cmd);
uint8_t  JF01ReadFIFO_R(uint8_t *Data_p ,uint8_t Len);
uint8_t  JF01WriteFIFO_R(uint8_t *Data_p ,uint8_t Len);
uint8_t  JF01ReadPATable_R(uint8_t *Data_p ,uint8_t Len);
uint8_t  JF01WritePATable_R(uint8_t *Data_p ,uint8_t Len) ;
void  SetupJF01PD_R(void);
uint8_t  ReadJF01FSCal_R(uint8_t *Data_p);
void  JF01RegConfig_R(JF01Registers  *rfConfig);
void  RfDaemon_01_R(void);
int16_t  Read_RSSI_01_R(void) ;

__inline void  JF01EnterTxMode_R(void);
__inline void  JF01EnterRxMode_R(void);

//---------
void  EXTI2_ISR_01(void) ;
void  EXTI3_ISR_01(void) ;
void  EXTI9_5_ISR_01(void) ;

//---------

void  RfRxDaemon_01_L(void) ;
void  RfDaemon_01_L(void) ;
void  RfRxTestDaemon_01_L(void) ;

void  RfRxDaemon_01_R(void) ;
void  RfDaemon_01_R(void) ;
void  RfRxTestDaemon_01_R(void) ;
void  RSSI_Filter_01(void) ;

//---------------------------------------------------
void  ShrMachDatTxLoad_01(void) ;
void  TunMachDatTxLoad_01(void) ;

void  PanelDatRx_01_L(void) ;
void  PanelDatRx_01_R(void) ;

void  RfTestEntry_01(void) __attribute__ ((noreturn)) ;

#endif   //RF_COM_FUN_H
//---------
