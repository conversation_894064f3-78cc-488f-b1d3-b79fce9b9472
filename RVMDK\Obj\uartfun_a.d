.\obj\uartfun_a.o: ..\src\UartFun_A.c
.\obj\uartfun_a.o: ..\inc\Timer.h
.\obj\uartfun_a.o: ..\Libraries\CMSIS\CM3\DeviceSupport\ST\STM32L1xx\stm32l1xx.h
.\obj\uartfun_a.o: ..\Libraries\CMSIS\CM3\CoreSupport\core_cm3.h
.\obj\uartfun_a.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\stdint.h
.\obj\uartfun_a.o: ..\Libraries\CMSIS\CM3\DeviceSupport\ST\STM32L1xx\system_stm32l1xx.h
.\obj\uartfun_a.o: ..\inc\stm32l1xx_conf.h
.\obj\uartfun_a.o: ..\Libraries\STM32L1xx_StdPeriph_Driver\inc\stm32l1xx_flash.h
.\obj\uartfun_a.o: ..\Libraries\CMSIS\CM3\DeviceSupport\ST\STM32L1xx\stm32l1xx.h
.\obj\uartfun_a.o: ..\Libraries\STM32L1xx_StdPeriph_Driver\inc\stm32l1xx_rcc.h
.\obj\uartfun_a.o: ..\Libraries\STM32L1xx_StdPeriph_Driver\inc\misc.h
.\obj\uartfun_a.o: ..\inc\UartFun_A.h
.\obj\uartfun_a.o: ..\inc\SysConfig.h
.\obj\uartfun_a.o: ..\inc\SysBasic.h
.\obj\uartfun_a.o: ..\inc\ThisDevice.h
.\obj\uartfun_a.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\stdlib.h
.\obj\uartfun_a.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\stdio.h
.\obj\uartfun_a.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\string.h
.\obj\uartfun_a.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\math.h
.\obj\uartfun_a.o: ..\inc\Display.h
