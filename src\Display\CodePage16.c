/*W  CodePage16.c    LCDIcon FileDescriptor: Do not edit or move */
#include  "Display.h"

const G<PERSON><PERSON>PAGE16 CodePage16={
{61,0x003c},{ /*  First number is number of ranges, last number is default Character index  */
{ 0x000A, 0x000A, 0x0000 },/* Segment 0, 0x0001 Symbols */
{ 0x000D, 0x000D, 0x0001 },/* Segment 1, 0x0001 Symbols */
{ 0x0021, 0x0021, 0x0002 },/* Segment 2, 0x0001 Symbols */
{ 0x2605, 0x2605, 0x0003 },/* Segment 3, 0x0001 Symbols */
{ 0x4E45, 0x4E45, 0x0004 },/* Segment 4, 0x0001 Symbols */
{ 0x4F5C, 0x4F5C, 0x0005 },/* Segment 5, 0x0001 Symbols */
{ 0x4F7F, 0x4F7F, 0x0006 },/* Segment 6, 0x0001 Symbols */
{ 0x516C, 0x516C, 0x0007 },/* Segment 7, 0x0001 Symbols */
{ 0x5173, 0x5173, 0x0008 },/* Segment 8, 0x0001 Symbols */
{ 0x5236, 0x5236, 0x0009 },/* Segment 9, 0x0001 Symbols */
{ 0x52A8, 0x52A8, 0x000A },/* Segment 10, 0x0001 Symbols */
{ 0x5373, 0x5373, 0x000B },/* Segment 11, 0x0001 Symbols */
{ 0x53D1, 0x53D1, 0x000C },/* Segment 12, 0x0001 Symbols */
{ 0x53EF, 0x53EF, 0x000D },/* Segment 13, 0x0001 Symbols */
{ 0x53F8, 0x53F8, 0x000E },/* Segment 14, 0x0001 Symbols */
{ 0x5668, 0x5668, 0x000F },/* Segment 15, 0x0001 Symbols */
{ 0x56FA, 0x56FA, 0x0010 },/* Segment 16, 0x0001 Symbols */
{ 0x5B9A, 0x5B9A, 0x0011 },/* Segment 17, 0x0001 Symbols */
{ 0x5C04, 0x5C04, 0x0012 },/* Segment 18, 0x0001 Symbols */
{ 0x5C06, 0x5C06, 0x0013 },/* Segment 19, 0x0001 Symbols */
{ 0x5C71, 0x5C71, 0x0014 },/* Segment 20, 0x0001 Symbols */
{ 0x6001, 0x6001, 0x0015 },/* Segment 21, 0x0001 Symbols */
{ 0x6377, 0x6377, 0x0016 },/* Segment 22, 0x0001 Symbols */
{ 0x6398, 0x6398, 0x0017 },/* Segment 23, 0x0001 Symbols */
{ 0x63A5, 0x63A5, 0x0018 },/* Segment 24, 0x0001 Symbols */
{ 0x63A7, 0x63A7, 0x0019 },/* Segment 25, 0x0001 Symbols */
{ 0x64CD, 0x64CD, 0x001A },/* Segment 26, 0x0001 Symbols */
{ 0x6536, 0x6536, 0x001B },/* Segment 27, 0x0001 Symbols */
{ 0x65E0, 0x65E0, 0x001C },/* Segment 28, 0x0001 Symbols */
{ 0x6606, 0x6606, 0x001D },/* Segment 29, 0x0001 Symbols */
{ 0x6709, 0x6709, 0x001E },/* Segment 30, 0x0001 Symbols */
{ 0x673A, 0x673A, 0x001F },/* Segment 31, 0x0001 Symbols */
{ 0x6746, 0x6746, 0x0020 },/* Segment 32, 0x0001 Symbols */
{ 0x6B22, 0x6B22, 0x0021 },/* Segment 33, 0x0001 Symbols */
{ 0x6C14, 0x6C14, 0x0022 },/* Segment 34, 0x0001 Symbols */
{ 0x6C38, 0x6C38, 0x0023 },/* Segment 35, 0x0001 Symbols */
{ 0x6F14, 0x6F14, 0x0024 },/* Segment 36, 0x0001 Symbols */
{ 0x7164, 0x7164, 0x0025 },/* Segment 37, 0x0001 Symbols */
{ 0x72B6, 0x72B6, 0x0026 },/* Segment 38, 0x0001 Symbols */
{ 0x7528, 0x7528, 0x0027 },/* Segment 39, 0x0001 Symbols */
{ 0x7535, 0x7535, 0x0028 },/* Segment 40, 0x0001 Symbols */
{ 0x76D8, 0x76D8, 0x0029 },/* Segment 41, 0x0001 Symbols */
{ 0x793A, 0x793A, 0x002A },/* Segment 42, 0x0001 Symbols */
{ 0x7EBF, 0x7EBF, 0x002B },/* Segment 43, 0x0001 Symbols */
{ 0x7EED, 0x7EED, 0x002C },/* Segment 44, 0x0001 Symbols */
{ 0x81EA, 0x81EA, 0x002D },/* Segment 45, 0x0001 Symbols */
{ 0x89E3, 0x89E3, 0x002E },/* Segment 46, 0x0001 Symbols */
{ 0x8BB8, 0x8BB8, 0x002F },/* Segment 47, 0x0001 Symbols */
{ 0x8BC1, 0x8BC1, 0x0030 },/* Segment 48, 0x0001 Symbols */
{ 0x8FCE, 0x8FCE, 0x0031 },/* Segment 49, 0x0001 Symbols */
{ 0x8FDB, 0x8FDB, 0x0032 },/* Segment 50, 0x0001 Symbols */
{ 0x8FDE, 0x8FDE, 0x0033 },/* Segment 51, 0x0001 Symbols */
{ 0x901A, 0x901A, 0x0034 },/* Segment 52, 0x0001 Symbols */
{ 0x9065, 0x9065, 0x0035 },/* Segment 53, 0x0001 Symbols */
{ 0x91C7, 0x91C7, 0x0036 },/* Segment 54, 0x0001 Symbols */
{ 0x9501, 0x9501, 0x0037 },/* Segment 55, 0x0001 Symbols */
{ 0x951A, 0x951A, 0x0038 },/* Segment 56, 0x0001 Symbols */
{ 0x952E, 0x952E, 0x0039 },/* Segment 57, 0x0001 Symbols */
{ 0x9650, 0x9650, 0x003A },/* Segment 58, 0x0001 Symbols */
{ 0x9664, 0x9664, 0x003B },/* Segment 59, 0x0001 Symbols */
{ 0xFEFF, 0xFEFF, 0x003C }/* Segment 60, 0x0001 Symbols */
}
};
