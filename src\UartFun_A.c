/*********Function related to use UART  communication ******/
//define status varible
#include "Timer.h"
#include "UartFun_A.h"

/* Extern variables ----------------------------------------------------------*/
extern  uint32_t   APB1_Clock ;//defined in "system_stm32L10xc"
extern  uint32_t   APB2_Clock ;//defined in "system_stm32L10xc"

extern const   uint16_t  wCRCTable[] ;

extern  SysConfigA_t    APP_PA ;//System application parameter Group A, ID=0x0081

extern UserAreaStruct   U_Area ;//define an object for user data

//---------------------------------------------
uint8_t  U2_RX_BUF0[U_DB_MAX]  ;
uint8_t  U2_RX_BUF1[U_DB_MAX]  ;

union {
       uint8_t  U2_TX_BUF[U_DB_MAX]   ;
       uint8_t  U2_MOD_BUF[U_DB_MAX]  ;
      } TxModBuf1 ;

//---------------------------------------------
tim_count U_Timer[U_TIM_NUM]  ;

UARTStruct  UB,*UX  ;//

ModStruct  ModB,*ModX  ;

//---------------------------------------------
void InitUartTimer(void)
{
  unsigned i ;
  for(i=0;i<U_TIM_NUM ;i++)
  {
    U_Timer[i].pv=0 ;
    U_Timer[i].cv=0 ;
    U_Timer[i].csr.csword=0 ;
  }
}

void UartTimerFun(void)//Processed in System tick ISR
{
 unsigned  i ;
 for(i=0;i<U_TIM_NUM;i++)
 {
   if(U_Timer[i].csr.csbit.en==1)
   {
     if(U_Timer[i].cv<65535) ++(U_Timer[i].cv) ;
     if(U_Timer[i].cv>=U_Timer[i].pv)
     {
       U_Timer[i].csr.csbit.q=1 ;
      }
    }
  }
}

//---------------------------------------------
void USART1_BaudRateConfig(USART_TypeDef *UARTx, uint16_t BaudRate)
{
   uint32_t IntegerDivider = 0;
   uint32_t FractionalDivider = 0;
   uint32_t tempreg=0 ;
   if((BaudRate>57600)||(BaudRate<1200)||(BaudRate%1200!=0))
   {//Set to default 19200bps
     /* Determine the integer part */
     if(APB2_Clock<=36000000)
     {
       IntegerDivider =100 ;
       IntegerDivider *=APB2_Clock ;
       IntegerDivider /=16 ;
       IntegerDivider /=19200 ;
       tempreg=IntegerDivider / 100;
      }
     else
     {
       IntegerDivider =APB2_Clock ;
       IntegerDivider /=16 ;
       IntegerDivider *=100 ;
       IntegerDivider /=19200 ;
       tempreg=IntegerDivider / 100;
      }
     /* Determine the fractional part */
     FractionalDivider = IntegerDivider - (100 * tempreg);
     IntegerDivider=((((FractionalDivider * 16) + 50) / 100))&((uint8_t)0x0f);
     UARTx->BRR =(tempreg<<4)+IntegerDivider;
   }
   else
   {
     /* Determine the integer part */
     if(APB2_Clock<=36000000)
     {
       IntegerDivider =100 ;
       IntegerDivider *=APB2_Clock ;
       IntegerDivider /=16 ;
       IntegerDivider /=BaudRate ;
       tempreg=IntegerDivider / 100;
      }
     else
     {
       IntegerDivider =APB2_Clock ;
       IntegerDivider /=16 ;
       IntegerDivider *=100 ;
       IntegerDivider /=BaudRate ;
       tempreg=IntegerDivider / 100;
      }
     /* Determine the fractional part */
     FractionalDivider = IntegerDivider - (100 * tempreg);
     IntegerDivider=((((FractionalDivider * 16) + 50) / 100))&((uint8_t)0x0f);
     UARTx->BRR =(tempreg<<4)+IntegerDivider;
    }
}
void UART_BaudRateConfig(USART_TypeDef *UARTx, uint16_t BaudRate)
{
   uint32_t IntegerDivider = 0;
   uint32_t FractionalDivider = 0;
   uint32_t tempreg=0 ;
   if((BaudRate>57600)||(BaudRate<1200)||(BaudRate%1200!=0))
   {//Set to default 19200bps
     /* Determine the integer part */
     IntegerDivider =100 ;
     IntegerDivider *=APB1_Clock ;//The APB1_Clock must not great than 36MHz
     IntegerDivider /=16 ;
     IntegerDivider /=19200 ;
     tempreg=IntegerDivider / 100;
     /* Determine the fractional part */
     FractionalDivider = IntegerDivider - (100 * tempreg);
     IntegerDivider=((((FractionalDivider * 16) + 50) / 100))&((uint8_t)0x0f);
     UARTx->BRR =(tempreg<<4)+IntegerDivider;
   }
   else
   {
     /* Determine the integer part */
     IntegerDivider =100 ;
     IntegerDivider *=APB1_Clock ;
     IntegerDivider /=16 ;
     IntegerDivider /=BaudRate ;
     tempreg=IntegerDivider / 100;
     /* Determine the fractional part */
     FractionalDivider = IntegerDivider - (100 * tempreg);
     IntegerDivider=((((FractionalDivider * 16) + 50) / 100))&((uint8_t)0x0f);
     UARTx->BRR =(tempreg<<4)+IntegerDivider;
    }
}
//---------------------------------------
void USART_Config(void)
{
  RCC_APB1PeriphClockCmd(RCC_APB1Periph_USART2, ENABLE);
  RCC_APB1PeriphResetCmd(RCC_APB1Periph_USART2, ENABLE);//Reset USART 2
  RCC_APB1PeriphResetCmd(RCC_APB1Periph_USART2, DISABLE);
  NVIC->ICER[(USART2_IRQn>> 0x05)] =(uint32_t)0x01 << (USART2_IRQn& (uint8_t)0x1F);//Disable the USART2 IRQ channel for interrupt
  USART2->CR1 =0x00002000 ;//enable USART1 to function
// Configure the GPIO pins
  GPIOA->BSRR =0x0002000c ;//PA.1(TX_EN)=0,PA.2,3(U2_TX,U2_RX)=1
//UART peripheral configuration -------------------------------------------
  USART2->CR2   =0x00000000 ;//
  USART2->CR3   =0x00000000 ;//Disable DMA Enabled for TX ,Error interrupt enabled in DMA mode
  /*Configure the UART X */
  if(APP_PB.U2Config.parity==0)
    USART2->CR1=0x00002020 ;//UART_NO_PARITY, 8 bit data,enable TXE, RXNE
  else if(APP_PB.U2Config.parity==2)
    USART2->CR1=0x00003410 ;//UART_EVEN_PARITY, 9 bits data(one for parity),enable IDLE INT
  else
    USART2->CR1=0x00003610 ;//UART_ODD_PARITY, 9 bits data(one for parity),enable IDLE INT
  /* Configure the BaudRate --------------------------------------------------*/
  UART_BaudRateConfig(USART2, (uint16_t) APP_PB.U2Config.sci_speed) ;
    
//--------------------------------------------------------------------------
  UB.RxBuf0=U2_RX_BUF0 ;
  UB.RxBuf1=U2_RX_BUF1 ;
  UB.TxBuf=TxModBuf1.U2_TX_BUF ;
  UB.tdp=UB.TxBuf ;
  UB.rdp=UB.RxBuf0 ;
  UB.rxd_sta=0x08 ;//set first receive buffer data  is invalid ,second is empty,0x08--data valid
                   //0x00-- buffer empty
  UB.rdl=0 ;
  UB.RxDL0=0 ;
  UB.RxDL1=0 ;
  UB.tcs=0 ;
  UB.tdl=0 ;
//--------------------------------------------------------------------------
  UB.TimerID=UB_TIMER ;
  if(APP_PB.U2Config.rx_t_out>=40)
    UB.RWtime=APP_PB.U2Config.rx_t_out ;
  else
    UB.RWtime=200 ;
  if((APP_PB.U2Config.tx_delay>1)&&(APP_PB.U2Config.tx_delay<51))
    UB.TDtime=APP_PB.U2Config.tx_delay ;
  else
    UB.TDtime=2 ;
   
  UB.RxFlag=0 ;
//--------------------------------------------------------------------------
  UB.TxPkCount=0 ;
  UB.RxPkCount=0 ;
//--------------------------------------------------------------------------
  ModB.NodeAdd=APP_PB.U2Config.mod_add ;//change according to node configuration--debug test
  ModB.Opc=0x00 ;//change according to node configuration
  ModB.Sta=0x00 ;
  ModB.RTryHold=APP_PB.U2Config.retry ;
  ModB.MBuf=(uint8_t *) TxModBuf1.U2_MOD_BUF ;
  ModB.Err= 0;
  if(APP_PB.AccSegCtrlW)
     ModB.AccTable=(AccessCtrlStruct *)&APP_PB.Acc_SCtrl_0 ;//Set access control table for port A(=0,no control)
  else
     ModB.AccTable=0x0000 ;//Set access control table for port A(=0,no control)
  ModB.TXPort=Tx_UB ;
  ModB.DModNotify=DModNotify_UB ;//Salver data area have been modified Notify function
// --------------------Configure the DMA1 channel 6 for Rx
  DMA1_Channel6->CPAR=(uint32_t)(&(USART2->DR)) ;
  DMA1_Channel6->CMAR=(uint32_t) (UB.rdp) ;
  DMA1_Channel6->CNDTR=(uint32_t) U_DB_MAX ;//set DMA RX trasction length
  DMA1_Channel6->CCR=0x000020a0 ;//DMA_DIR_PeripheralSCR|DMA_Circular|DMA_MemoryInc_Enable|DMA_Priority_high
  DMA1_Channel6->CCR|=0x00000001 ;//Enable the DMA channel
  USART2->CR1 |=((uint16_t)0x0004) ;//Enable USART2 for RX operation
  USART2->CR3 |=0x00c0 ;//Enable DMA1 for Tx & Rx
  NVIC->ICPR[(USART2_IRQn>> 0x05)] =(uint32_t)0x01 << (USART2_IRQn& (uint8_t)0x1F);//Clear pending USART2 IRQ
  NVIC->ISER[(USART2_IRQn>> 0x05)] =(uint32_t)0x01 << (USART2_IRQn& (uint8_t)0x1F);//Enable the USART2 IRQ channel for interrupt
}
void OpenUBTx(void)
{
  GPIOA->BSRRL=0x0002 ;//Set PA.1(TxCtrl) to '1',enable SN75LBC184 for TX
}
void CloseUBTx(void)
{
  GPIOA->BSRRH=0x0002 ;//Set PA.1(TxCtrl) to '0',enable SN75LBC184 for RX
}


////////////////////////////////
uint8_t   Tx_UB(__IO uint8_t *Tx_UA_BF,int16_t TxdLen)
{
   if((UB.tcs!=U_TX_IDLE)||(TxdLen==0))  return UB.tcs ;
   if(TxdLen >(uint16_t)U_DB_MAX ) TxdLen=U_DB_MAX ;
   ////////////////////////////
   UB.tdp= Tx_UA_BF ;//load data information
   UB.tdl= TxdLen ;
   if(USART2->SR&0x0080)//TXE=1 Transmit Data Register Empty
    {
      UB.tcs=U_TX_WAIT_FOR_SEND;
      U_Timer[UB.TimerID].pv=UB.TDtime ;
      U_Timer[UB.TimerID].cv=0 ;
      U_Timer[UB.TimerID].csr.csword=0x4000 ;//enable U_Timer[0]
      return 0 ;  //
     }
   return 0xff ;  //
}
/*******************************************************************************
* Function Name  :  DModNotify_UB.
* Description    :  Access Notification for modbus function
* Input          :  Add, current access address begin.
                        Len, access area length
* Return         :  =0,access recognized normally
*******************************************************************************/

uint8_t DModNotify_UB(uint16_t Add ,uint16_t Len)
{
 return 0 ;
}

//--------------------------------------------
void UXTxP(void)
{
//For USART2
 switch (UB.tcs)
  {
   case U_TX_IDLE : {//#define U_TX_IDLE 0x0000
                     break ;}
   case U_TX_WAIT_FOR_IDLE :
                     UB.tcs=U_TX_IDLE ;
					 UB.TxPkCount++ ;
                     CloseUBTx() ;
	                 break ;
   case U_TX_WAIT_FOR_SEND : {//#define U_TX_WAIT_FOR_SEND 0x0002
			         if(U_Timer[UB.TimerID].csr.csbit.q==1)
			         {
                       if(UB.tdl>1)
		                UB.tcs=U_TX_CONTINUE_SEND ;//
                       else
		                UB.tcs=U_TX_WAIT_FOR_FINISH ;//
			           U_Timer[UB.TimerID].csr.csword=0x0000 ;
			           U_Timer[UB.TimerID].cv=0 ;
                       OpenUBTx() ;//open USART2 port's tx
                       DMA1_Channel7->CPAR=(uint32_t)(&(USART2->DR)) ;
                       DMA1_Channel7->CMAR=(uint32_t) (UB.tdp) ;
                       DMA1_Channel7->CNDTR=(uint32_t) (UB.tdl) ;//set DMA TX trasction length
                       DMA1_Channel7->CCR=0x00001093 ;//DMA_DIR_PeripheralDST|DMA_MemoryInc_Enable|DMA_Priority_Medium|DMA_IT_TC
                       DMA1_Channel7->CCR|=0x00000001 ;//Enable the DMA channel
                       USART2->CR1 |=((uint16_t)0x0008) ;//USART2 TX enable
			          }
			         break ;}
  case U_TX_CONTINUE_SEND   ://#define U_TX_CONTINUE_SEND   0x04
                     break ;
  case U_TX_WAIT_FOR_FINISH :
                     if(USART2->SR&0x0040)//Tx data register empty & Transmission complete
                     {
                       USART2->SR&=0xffbf ;//Clear Transmission complete flag
                       UB.tcs=U_TX_WAIT_FOR_IDLE ;
                      }
                     break ;
  default : UB.tcs=U_TX_IDLE ;
  }  
}

//-----------------------------------------------
extern  void USART2_ISR(void)
{
  uint16_t l_tmp ;
  uint16_t s_tmp ;
  l_tmp=USART2->SR ;
  s_tmp=USART2->DR ;//clear the flag ;
  s_tmp=s_tmp ;
  if(l_tmp &0x0010 ) //Idle line received
  {
    ++UB.RxPkCount ;
    DMA1_Channel6->CCR=0x00000000 ;//disable the DMA Channel6
    DMA1_Channel6->CPAR=(uint32_t)(&(USART2->DR)) ;
    if((UB.rxd_sta & 0x88)==0x08)
    {
       UB.RxDL0=(uint8_t)(U_DB_MAX-(uint8_t)DMA1_Channel6->CNDTR) ;
       UB.rxd_sta=0x87 ;//received new data ,old receiving buffer invalid
	 		   	        //set new data life cycle to 7f
       DMA1_Channel6->CMAR=(uint32_t)UB.RxBuf1 ;
     }
    else
    {
      UB.RxDL1=( uint8_t)(U_DB_MAX-(uint8_t)DMA1_Channel6->CNDTR) ;
      UB.rxd_sta=0x78 ;//received new data ,old receiving buffer invalid
   		  	           //set new data life cycle to 7f
      DMA1_Channel6->CMAR=(uint32_t)UB.RxBuf0 ;
     }
    UB.rdl=0 ;
    DMA1_Channel6->CNDTR=(uint32_t) U_DB_MAX ;//set DMA RX trasction length
    DMA1_Channel6->CCR=0x000020a0 ;//DMA_DIR_PeripheralSCR|DMA_Circular|DMA_MemoryInc_Enable|DMA_Priority_high
    DMA1_Channel6->CCR|=0x00000001 ;//Enable the DMA channel
   }
}
//-----------------------------------------------

void USART2_DMA_ISR(void)
{
  if(DMA1->ISR&0x02000000)
  {
    DMA1->IFCR=0x07000000 ;
    UB.tcs=U_TX_WAIT_FOR_FINISH ;//
    DMA1->IFCR=0x00000000 ;
    DMA1_Channel7->CCR&=0xfffffffe ;//Disable the DMA Channel7
  }
}

//--------------------End of file
