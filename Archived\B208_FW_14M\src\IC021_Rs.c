#include "SysBasic.h"
#include "Timer.h"
#include "RF_BasicFun.h"
//----------------------------

extern  timer_t  RfcTimer[RFC_TIMER_NUM]  ;//system ISR process

extern  timer_t  RfcTimer10[RFC_TIMER10_NUM]  ;//user level timer(1 tick=10ms),RFC_TIMER10_NUM==2

extern  uint8_t   ShiftReg_R ;
extern  uint8_t   BitCounter_R;
extern  uint8_t   PreambleCount_R;
extern  uint8_t   PreambleError_R;
extern  uint8_t   ByteCounter_R;
extern  LongWord  IC021Reg ;

extern  uint8_t gain_correction[] ;

//--------
uint32_t      TestReg_R=0x0000010f ;

//----------------------------------------------------------------------------------------
#ifdef  USE_OLD_FRAME
void RfRxDaemon_021_R(void)
{
 uint16_t tmp,*tp1 ;
 uint16_t *tp2 ;
 if(RfcTimer10[RF_OP_TIMER_R].csr.csbit.q==1)
 {
   U_Area.PaDat_R.PanSettingW&=0x3f1f ;//reset main the pannel setting status
   U_Area.PaDat_R.PanStaW =0x0000 ;//reset the pannel status
   U_Area.PaDat_R.KeyCode =0 ;//reset the key operation status
   OpCtrl &=~CB_RX_OK_R ;//Turn off the right side RF operation received OK flag
  }
 if(RfcTimer10[AIR_TIMER_R].csr.csbit.q==1)
 {
   OpCtrl &=~CB_T_ONLINE_R ;//Turn off the right side linkage OK flag
   U_Area.RSSI_R=-128 ;//RfDs.RSSI_R ;
  }
 if(RfDs.RBuf_StaR&0x02)//begine to check and process RF receiver buffer 0
  {
    if(CRC16WithSeed(RIGHT_CRC_SEED,(const uint16_t *) &U_Area.PanelDat_RB0_R,RfDs.RxPackageLen/2)==0)
    {
      tp1=(uint16_t *) &U_Area.PanelDat_RB0_R ;
      tp2=(uint16_t *) &U_Area.PaDat_R ;
      for(tmp=0 ;tmp<RfDs.RxPackageLen/2;tmp++)
      {
       *tp2++=*tp1++ ;//copy valid data to output area
       }
      if((U_Area.PaDat_R.InquiryPackage!=0)||(U_Area.PaDat_R.FixedFlag!=0x0003))
        U_Area.PaDat_R.KeyCode=0x0000 ;//reset the operation status
      // set air linkage valid flag
      RfDs.RF_ACK_Count_R=RF_ACK_NUM ;
      U_Area.DatReq_R=U_Area.PaDat_R.PanSettingW ;//Display setting
      OpCtrl |=CB_RX_OK_R+CB_T_ONLINE_R ;//Turn on the right side linkage OK flag
	  U_Area.RSSI_R=RfDs.RSSI_R ;
      if(RfDs.RSSI_R>=RSSI_LIMIT)
        GFlag |=GF_RSSI_GOOD_R ;
      else
        GFlag &=~GF_RSSI_GOOD_R ;
      //
	  if(U_Area.PaDat_R.InquiryPackage==0)
	  {
        RfcTimer10[RF_OP_TIMER_R].cv=0 ;
        RfcTimer10[RF_OP_TIMER_R].csr.csword=0x4000 ; //start RfcTimer10[RF_OP_TIMER_R]
	   }
      RfcTimer10[AIR_TIMER_R].csr.csword=0x4000 ;//reset the monitor timer
      RfcTimer10[AIR_TIMER_R].cv=0 ;
     }
    else
      RfDs.RF_RxErr_R++ ;//record the CRC error
    __disable_irq();//Disable IRQ
    RfDs.RBuf_StaR &=0xfd ;//reset the buffer to empty status
    __enable_irq();//Enable IRQ
  }
 if(RfDs.RBuf_StaR&0x08)//begine to check and process RF receiver buffer 0
  {
    if(CRC16WithSeed(RIGHT_CRC_SEED,(const uint16_t *) &U_Area.PanelDat_RB1_R,RfDs.RxPackageLen/2)==0)
    {
      tp1=(uint16_t *) &U_Area.PanelDat_RB1_R ;
      tp2=(uint16_t *) &U_Area.PaDat_R ;
      for(tmp=0 ;tmp<RfDs.RxPackageLen/2;tmp++)
      {
       *tp2++=*tp1++ ;//copy valid data to output area
       }
      if((U_Area.PaDat_R.InquiryPackage!=0)||(U_Area.PaDat_R.FixedFlag!=0x0003))
        U_Area.PaDat_R.KeyCode=0x0000 ;//reset the operation status
      // set air linkage valid flag
      RfDs.RF_ACK_Count_R=RF_ACK_NUM ;
      U_Area.DatReq_R=U_Area.PaDat_R.PanSettingW ;//Display setting
      OpCtrl |=CB_RX_OK_R+CB_T_ONLINE_R ;//Turn on the right side linkage OK flag
      U_Area.RSSI_R=RfDs.RSSI_R ;
      if(RfDs.RSSI_R>=RSSI_LIMIT)
        GFlag |=GF_RSSI_GOOD_R ;
      else
        GFlag &=~GF_RSSI_GOOD_R ;
      //
	  if(U_Area.PaDat_R.InquiryPackage==0)
	  {
        RfcTimer10[RF_OP_TIMER_R].cv=0 ;
        RfcTimer10[RF_OP_TIMER_R].csr.csword=0x4000 ; //start RfcTimer10[RF_OP_TIMER_R]
	   }
      RfcTimer10[AIR_TIMER_R].csr.csword=0x4000 ;//reset the monitor timer
      RfcTimer10[AIR_TIMER_R].cv=0 ;
     }
    else
      RfDs.RF_RxErr_R++ ;//record the CRC error
    __disable_irq();//Disable IRQ
    RfDs.RBuf_StaR &=0xf7 ;//reset the buffer to empty status
    __enable_irq();//Enable IRQ
  }
}
#else
void RfRxDaemon_021_R(void)
{
 uint16_t tmp,*tp1 ;
 uint16_t *tp2 ;
 if(RfcTimer10[RF_OP_TIMER_R].csr.csbit.q==1)
 {
   U_Area.PaDat_R.PSet.SettingW&=0xdf3f ;//reset main the pannel setting status
   U_Area.PaDat_R.PSta.StaW &=0xf1ff ;//reset the pannel status
   U_Area.PaDat_R.KeyCode =0 ;//reset the key operation status
   OpCtrl &=~CB_RX_OK_R ;//Turn off the right side RF operation received OK flag
  }
 if(RfcTimer10[AIR_TIMER_R].csr.csbit.q==1)
 {
   OpCtrl &=~CB_T_ONLINE_R ;//Turn off the right side linkage OK flag
   U_Area.RSSI_R=-128 ;//RfDs.RSSI_R ;
  }
 if(RfDs.RBuf_StaR&0x02)//begine to check and process RF receiver buffer 0
  {
    if(CRC16WithSeed(RIGHT_CRC_SEED,(const uint16_t *) &U_Area.PanelDat_RB0_R,RfDs.RxPackageLen/2)==0)
    {
      tp1=(uint16_t *) &U_Area.PanelDat_RB0_R ;
      tp2=(uint16_t *) &U_Area.PaDat_R ;
      for(tmp=0 ;tmp<RfDs.RxPackageLen/2;tmp++)
      {
       *tp2++=*tp1++ ;//copy valid data to output area
       }
      if((U_Area.PaDat_R.PSta.InquiryPackage!=0)||(U_Area.PaDat_R.PSet.FixedBit!=0x0001))
        U_Area.PaDat_R.KeyCode=0x0000 ;//reset the operation status
      // set air linkage valid flag
      RfDs.RF_ACK_Count_R=RF_ACK_NUM ;
      U_Area.DatReq_R=U_Area.PaDat_R.PSet.SettingW ;//Display setting
      OpCtrl |=CB_RX_OK_R+CB_T_ONLINE_R ;//Turn on the right side linkage OK flag
	  U_Area.RSSI_R=RfDs.RSSI_R ;
      if(RfDs.RSSI_R>=RSSI_LIMIT)
        GFlag |=GF_RSSI_GOOD_R ;
      else
        GFlag &=~GF_RSSI_GOOD_R ;
      //
	  if(U_Area.PaDat_R.PSta.InquiryPackage==0)
	  {
        RfcTimer10[RF_OP_TIMER_R].cv=0 ;
        RfcTimer10[RF_OP_TIMER_R].csr.csword=0x4000 ; //start RfcTimer10[RF_OP_TIMER_R]
	   }
      RfcTimer10[AIR_TIMER_R].csr.csword=0x4000 ;//reset the monitor timer
      RfcTimer10[AIR_TIMER_R].cv=0 ;
     }
    else
      RfDs.RF_RxErr_R++ ;//record the CRC error
    __disable_irq();//Disable IRQ
    RfDs.RBuf_StaR &=0xfd ;//reset the buffer to empty status
    __enable_irq();//Enable IRQ
  }
 if(RfDs.RBuf_StaR&0x08)//begine to check and process RF receiver buffer 0
  {
    if(CRC16WithSeed(RIGHT_CRC_SEED,(const uint16_t *) &U_Area.PanelDat_RB1_R,RfDs.RxPackageLen/2)==0)
    {
      tp1=(uint16_t *) &U_Area.PanelDat_RB1_R ;
      tp2=(uint16_t *) &U_Area.PaDat_R ;
      for(tmp=0 ;tmp<RfDs.RxPackageLen/2;tmp++)
      {
       *tp2++=*tp1++ ;//copy valid data to output area
       }
      if((U_Area.PaDat_R.PSta.InquiryPackage!=0)||(U_Area.PaDat_R.PSet.FixedBit!=0x0001))
        U_Area.PaDat_R.KeyCode=0x0000 ;//reset the operation status
      // set air linkage valid flag
      RfDs.RF_ACK_Count_R=RF_ACK_NUM ;
      U_Area.DatReq_R=U_Area.PaDat_R.PSet.SettingW ;//Display setting
      OpCtrl |=CB_RX_OK_R+CB_T_ONLINE_R ;//Turn on the right side linkage OK flag
      U_Area.RSSI_R=RfDs.RSSI_R ;
      if(RfDs.RSSI_R>=RSSI_LIMIT)
        GFlag |=GF_RSSI_GOOD_R ;
      else
        GFlag &=~GF_RSSI_GOOD_R ;
      //
	  if(U_Area.PaDat_R.PSta.InquiryPackage==0)
	  {
        RfcTimer10[RF_OP_TIMER_R].cv=0 ;
        RfcTimer10[RF_OP_TIMER_R].csr.csword=0x4000 ; //start RfcTimer10[RF_OP_TIMER_R]
	   }
      RfcTimer10[AIR_TIMER_R].csr.csword=0x4000 ;//reset the monitor timer
      RfcTimer10[AIR_TIMER_R].cv=0 ;
     }
    else
      RfDs.RF_RxErr_R++ ;//record the CRC error
    __disable_irq();//Disable IRQ
    RfDs.RBuf_StaR &=0xf7 ;//reset the buffer to empty status
    __enable_irq();//Enable IRQ
  }
}
#endif
//----------------------------------------------------------------------------------------
void RfRxTestDaemon_021_R(void)
{
  uint16_t tmp,*tp1 ;
  uint16_t tmp3 ;
  //check the received data ==0x55 ==0xaa or not
  if((RfDs.char_count_R>64)&(RfDs.flag_R==0))
    RfDs.flag_R=1 ;
  if((RfDs.char_count_R<64)&(RfDs.flag_R==2))
    RfDs.flag_R=0 ;
  if(RfDs.flag_R==1)
  {
    tp1=U_Area.Ultra_BUF_R ;
    RfDs.flag_R=2 ;
    tmp3=0 ;
    for(tmp=0 ;tmp<32 ;tmp++)
    {
     tmp3 |=*tp1++ ;
     }
    if((tmp3==0x5555)||(tmp3==0xaaaa))
      OpCtrl |=CB_RX_OK_R ;//Turn on the right side linkage OK flag
    else
      OpCtrl &=~CB_RX_OK_R ;//Turn off the right side linkage OK flag
   }
  return ;
}
//-------------------------------------
void RfDaemon_021_R(void)
{
//Process IC021 Initalization operation
 switch (RfDs.IC021Init_R.OpSta)
 {
  case NOT_INIT_STA :
    if((RfDs.IC021Init_R.OpReq&0xff00)==SET_CODE )
	{
      RfDs.IC021Init_R.OpSta =RfDs.IC021Init_R.OpReq&0x00ff ;
	  RfDs.IC021Init_R.OpReq=0;
	  __disable_irq();
      RfcTimer[RF_TIMER_R].pv=OSC_TIMEOUT ;
      RfcTimer[RF_TIMER_R].cv=0 ;
      RfcTimer[RF_TIMER_R].csr.csword=0x4000 ;//enable RfcTimer[RF_TIMER_R]
      __enable_irq();
	 }
    break ;
  case IN_RX_STA :
    if(RfDs.IC021Init_R.OpReq==SET_UP_TX)
    {
      RfDs.IC021Init_R.OpReq=0x0000 ;
	  __disable_irq();
      RfcTimer[RF_TIMER_R].pv=MIN_TIMEOUT ;
      RfcTimer[RF_TIMER_R].cv=0 ;
      RfcTimer[RF_TIMER_R].csr.csword=0x4000 ;//enable RfcTimer[RF_TIMER_R]
      __enable_irq();
 	  //write R0, switch RX to TX and change LO
	  WriteReg_021_R(&(Tx021_Ch_p_R->R0_N_Tx));
      RfDs.IC021Init_R.OpSta=SET_UP_TX_WAIT2 ;
     }
    break ;
  case IN_TX_STA :
    if(RfDs.IC021Init_R.OpReq==SET_UP_RX)
    {
      RfDs.IC021Init_R.OpReq=0x0000 ;
	  __disable_irq();
      RfcTimer[RF_TIMER_R].pv=MIN_TIMEOUT ;
      RfcTimer[RF_TIMER_R].cv=0 ;
      RfcTimer[RF_TIMER_R].csr.csword=0x4000 ;//enable RfcTimer[RF_TIMER_R]
      __enable_irq();
      //write R0, switch TX to RX and change LO
	  WriteReg_021_R(&(Rx021_Ch_p_R->R0_N_Rx));
      RfDs.IC021Init_R.OpSta=SET_UP_RX_WAIT3 ;
     }
    break ;
  case SET_UP_RX :
    if(RfcTimer[RF_TIMER_R].csr.csbit.q==1)
    {
	  __disable_irq();
      RfcTimer[RF_TIMER_R].pv=MIN_TIMEOUT ;
      RfcTimer[RF_TIMER_R].cv=0 ;
      RfcTimer[RF_TIMER_R].csr.csword=0x4000 ;//enable RfcTimer[RF_TIMER_R]
      __enable_irq();
	  WriteReg_021_R(&(Rx021_Ch_p_R->R1_VCO_OSC_Rx));//write R1, turn on VCO
      RfDs.IC021Init_R.OpSta=SET_UP_RX_WAIT1 ;
	 }
  case SET_UP_RX_WAIT1 :
    if(RfcTimer[RF_TIMER_R].csr.csbit.q==1)
    {
	  //write R3, turn on TX/RX clocks
	  WriteReg_021_R(&APP_PC_p->IC021Def.R3_TxRx_Clk);
      if(GFlag&GF_RFIC_CAL_OK_R)
      {
        WriteReg_021_R(&RfDs.IC021Init_R.FilterCalReadback);//write R5 to start
        RfcTimer[RF_TIMER_R].csr.csword=0x5000 ;//enable Timer[RFIC_TIMER] and .q=1
       }
      else if(((GFlag&GF_RFIC_CAL_OK_R)==0)&&(APP_PC_p->IC021Def.R6_IF_Cal&0x00000010))
      { //Configured require IF Filter Fine calibration
	    __disable_irq();
        RfcTimer[RF_TIMER_R].pv=IF_CAL_FINE_TIME ;
        RfcTimer[RF_TIMER_R].cv=0 ;
        RfcTimer[RF_TIMER_R].csr.csword=0x4000 ;//enable RfcTimer[RF_TIMER_R]
        __enable_irq();
        WriteReg_021_R(&APP_PC_p->IC021Def.R6_IF_Cal);//write R6 here, if fine IF filter cal is wanted
	    WriteReg_021_R(&APP_PC_p->IC021Def.R5_IF_Filter);//write R5 to start IF filter cal
       }
      else
      {//Do coarse calibration
	    __disable_irq();
        RfcTimer[RF_TIMER_R].pv=MIN_TIMEOUT ;
        RfcTimer[RF_TIMER_R].cv=0 ;
        RfcTimer[RF_TIMER_R].csr.csword=0x4000 ;//enable RfcTimer[RF_TIMER_R]
        __enable_irq();
        WriteReg_021_R(&APP_PC_p->IC021Def.R5_IF_Filter);//write R5 to start IF filter cal
       }
      RfDs.IC021Init_R.OpSta=SET_UP_RX_WAIT2 ;
    }
    break ;
  case SET_UP_RX_WAIT2 :
    if(RfcTimer[RF_TIMER_R].csr.csbit.q==1)
    {
      if(((GFlag&GF_RFIC_CAL_OK_R)==0)&&(APP_PC_p->IC021Def.R6_IF_Cal&0x00000010))
      { //Configured require IF Filter Fine calibration
        GFlag|=GF_RFIC_CAL_OK_R ;
        RfDs.IC021Init_R.FilterCalRaw=ReadFilterCal_021_R() ;
        RfDs.IC021Init_R.CalTemp=ReadTemperature_021_R() ;
       }
	  __disable_irq();
      RfcTimer[RF_TIMER_R].pv=MIN_TIMEOUT ;
      RfcTimer[RF_TIMER_R].cv=0 ;
      RfcTimer[RF_TIMER_R].csr.csword=0x4000 ;//enable RfcTimer[RF_TIMER_R]
      __enable_irq();
	  //write R8, configure power down test register
	  WriteReg_021_R(&APP_PC_p->IC021Def.R8_PDT);//Set internal Rx/Tx switch ;
	  //write R11, configure sync word detect
	  WriteReg_021_R(&APP_PC_p->IC021Def.R11_SYNC);//sync word = ;
 	  //write R12, start sync word detect
	  WriteReg_021_R(&APP_PC_p->IC021Def.R12_SWD);//for sync word detect;
   	  //write R0, turn on PLL
	  WriteReg_021_R(&(Rx021_Ch_p_R->R0_N_Rx));
      RfDs.IC021Init_R.OpSta=SET_UP_RX_WAIT3 ;
    }
    break ;
  case SET_UP_RX_WAIT3 :
    if(RfcTimer[RF_TIMER_R].csr.csbit.q==1)
    {
	  __disable_irq();
	  RfcTimer[RF_TIMER_R].csr.csword=0x0000 ;//disable timer
	  RfcTimer[RF_TIMER_R].cv=0 ;
      __enable_irq();
      //write R4, turn on demodulation
	  WriteReg_021_R(&APP_PC_p->IC021Def.R4_Demod);
  	  if (APP_PC_p->IC021Def.R10_AFC&0x00000010)//Required to use AFC
	  {
		//write R10, turn AFC on
		WriteReg_021_R(&APP_PC_p->IC021Def.R10_AFC);
	   }
      if((TestCtrlW&0xff00)==0x8000)
        RfDs.IC021Init_R.OpSta=RX_TEST_STA ;//Set RX test status
      else
        RfDs.IC021Init_R.OpSta=IN_RX_STA ;//Set rx setup ok status
      RfDs.IC021Init_R.OpReq=0 ;
      //----
      EnterRxMode_021_R() ;
    }
    break ;
  case SET_UP_TX :
    if(RfcTimer[RF_TIMER_R].csr.csbit.q==1)
    {
      //write R1, turn on VCO
	  WriteReg_021_R(&(Tx021_Ch_p_R->R1_VCO_OSC_Tx));//
      RfcTimer[RF_TIMER_R].pv=MIN_TIMEOUT ;//delay about 800us
      RfcTimer[RF_TIMER_R].cv=0 ;
      RfcTimer[RF_TIMER_R].csr.csword=0x4000 ;//enable RfcTimer[RF_TIMER_R]
      __enable_irq();
      RfDs.IC021Init_R.OpSta=SET_UP_TX_WAIT1 ;
	 }
  case SET_UP_TX_WAIT1 :
    if(RfcTimer[RF_TIMER_R].csr.csbit.q==1)
    {
	  __disable_irq();
      RfcTimer[RF_TIMER_R].pv=MIN_TIMEOUT ;//require to delay about 40us
      RfcTimer[RF_TIMER_R].cv=0 ;
      RfcTimer[RF_TIMER_R].csr.csword=0x4000 ;//enable RfcTimer[RF_TIMER_R]
      __enable_irq();
	  //write R8, configure power down test register
	  WriteReg_021_R(&APP_PC_p->IC021Def.R8_PDT);//Set internal Rx/Tx switch off ;
	  WriteReg_021_R(&APP_PC_p->IC021Def.R3_TxRx_Clk);//write R3, turn on TX/RX clocks
	  WriteReg_021_R(&(Tx021_Ch_p_R->R0_N_Tx));//write R0, turn on PLL
      RfDs.IC021Init_R.OpSta=SET_UP_TX_WAIT2 ;
    }
    break ;
  case SET_UP_TX_WAIT2 :
    if(RfcTimer[RF_TIMER_R].csr.csbit.q==1)
    {
	  __disable_irq();
	  RfcTimer[RF_TIMER_R].csr.csword=0x0000 ;//disable timer
	  RfcTimer[RF_TIMER_R].cv=0 ;
      __enable_irq();
	  WriteReg_021_R(&APP_PC_p->IC021Def.R2_Tx_Mod);//write R2, turn on PA
      if((TestCtrlW&0xfc00)==0xc000)
      {
//---------
        if((TestCtrlW&0xff00)==0xc100)//
	      WriteReg_021_R(&TestReg_R);//write Test register R15, to Output the carrier only
//---------
        RfDs.IC021Init_R.OpSta=TX_TEST_STA  ;//Enter Tx test status
        RfDs.IC021Init_R.OpReq=0 ;
        EnterTxMode_021_R() ;//Enter TX test status
        break ;
      }
      ShrMachDatTxLoad_021() ;
      RfDs.IC021Init_R.OpSta=IN_TX_STA  ;//Set tx setup ok status
      RfDs.IC021Init_R.OpReq=0 ;
      EnterTxMode_021_R() ;
    }
    break ;
  case TX_TEST_STA :
  case RX_TEST_STA :
    RfRxTestDaemon_021_R() ;
    break ;
  default ://There must have enter wrong status
    {
	  RfDs.IC021Init_R.OpReq=0 ;
      RfDs.IC021Init_R.OpSta=NOT_INIT_STA ;
      RfDs.RF_State_R=RF_IDLE_STATUS ;
	  }
 }
}
//Function related to SRD IC 021
uint16_t  ReadReg_021_R(u8 readback_config )
{
  uint32_t regv32;
  uint16_t i,regv;
/* Write readback and ADC control value */
  regv32 = ((uint32_t)readback_config & 0x0000001F) << 4;
  regv32 |=0x00000007; // Address the readback setup register
  WriteReg_021_R(&regv32) ;//After this call L_SLE=1
  __disable_irq();
  GPIOA->BRR=0x000000e0 ;//SCLK,MOSI,MISO ODR to 0
  GPIOA->CRL&=0x000fffff ;
  GPIOA->CRL|=SPI1Pins_GPIO_CONFIG ;//Set PA.5,6,7(SCLK,MISO,MOSI) to GPIO status
  //Clock in first bit and discard
  GPIOA->BSRR=0x00000020 ;//SLE=1,SDATA=0,SCLK=1,SREAD input dir
  regv = 0; // Slight pulse extend
  GPIOA->BRR=0x00000020 ;//SLE=1,SDATA=SCLK=0,SREAD input dir
  // Clock in data MSbit first
  for (i=16; i>0;i--)
  {
    GPIOA->BSRR=0x00000020 ;//SLE=1,SDATA=0,SCLK=1,SREAD input dir
    regv <<= 1; // left shift 1
    GPIOA->BRR=0x00000020 ;//SLE=1,SDATA=SCLK=0,SREAD input dir
    if (GPIOA->IDR &0x00000040)
       regv |= 1;//SREAD=1
   }
//Generate additional clock pulse
  GPIOA->BSRR=0x00000020 ;//SLE=1,SDATA=0,SCLK=1,SREAD input dir
//  GPIOB->BRR =0x00000400 ;//Reset PB.10 force SLE to '0' 	 --2011.3.20
  GPIOA->BRR =0x00000020 ;//SLE=0,SDATA=SCLK=0,SREAD input dir
  // All port lines left low ?
  GPIOA->CRL&=0x000fffff ;
  GPIOA->CRL|=SPI1Pins_SPI_CONFIG ;//Set PA.5,6,7(SCLK,MISO,MOSI) to SPI status
  __enable_irq();
  return regv;
}

void WriteReg_021_R(uint32_t *regv_p)
{//The read back data all be ignored
  uint16_t dummy ;
  IC021Reg.w32=*regv_p ;
  while((SPI1->SR&0x0080)!=0) ;//Wait the SPI1 to idle
  dummy=SPI1->DR ;
  __disable_irq();
  GPIOB->BRR =0x00000400 ;//Reset PB.10  force SLE to '0' 
  SPI1->DR=IC021Reg.w16[1]; // Write high half word to SPI1
  while ((SPI1->SR&0x0002)==0)  ;//Wait the Tx buffer to empty to idle
  SPI1->DR=IC021Reg.w16[0]; // Write low half word to SPI1
  dummy=SPI1->DR ;
  // Wait until data is written
  while ((SPI1->SR&0x0080)!=0)  ;//Wait the SPI1 to idle
  dummy=SPI1->DR ;
  GPIOB->BSRR =0x00000400 ;// Set L_PSEL(021 SLE) to High latch the data
  __enable_irq();//Enable IRQ
  dummy=dummy ;
}
__inline void EnterTxMode_021_R(void)
{
  __disable_irq();
  BitCounter_R=0;
  PreambleCount_R=0;
  RfDs.RF_State_R=TX_STATUS ;//Set to TX state
  if((TestCtrlW&0xfc00)==0xc000)
    RfDs.RF_TxState_R=TX_RF_TEST ;//Enter RF TX Test status
  else
    RfDs.RF_TxState_R=TX_1SEND_PREAMBLE_SEG ;//prepare to output preamble status
  GPIOA->CRL &=0xfffff0ff ;
  GPIOA->CRL |=0x00000200 ;//Set PA.2(L_DIO) to PP output status
  EXTI->RTSR &=0x0007fff7 ;//Disable Rising trigger
  EXTI->FTSR |=0x00000008 ;//Using Falling trigger (for TX mode,push data on low-to-high transition )
  EXTI->PR    =0x00000008 ;//clear pending ext interrupt line3# PA.3(L_DCLK)
  EXTI->IMR  |=0x00000008 ;//Allow PA.3 to generate interrupt
  RfDs.EntTxCnt_R++ ;
  __enable_irq();
}
__inline void EnterRxMode_021_R(void)
{
  __disable_irq();
  ShiftReg_R=0 ;
  BitCounter_R=0;
  RfDs.RF_State_R=RX_STATUS ;//Set to RX state
  if((TestCtrlW&0xff00)==0x8000)
    RfDs.RF_RxState_R=RX_RF_TEST_INIT ;//Prepar to enter RX TEST status
  else
    RfDs.RF_RxState_R=RX_SEARCH_PREAMBLE ;//search for a vaild preamble status
  GPIOA->CRL &=0xfffff0ff ;
  GPIOA->CRL |=0x00000400 ;//Set PA.2(L_DIO) to input float status
  EXTI->RTSR |=0x00000008 ;//using Rising trigger(for RX mode,read received data on low-to-high transition )
  EXTI->FTSR &=0x0007fff7 ;//Disable Falling trigger
  EXTI->PR    =0x00000008 ;//clear pending ext interrupt line3# PA.3(L_DCLK)
  EXTI->IMR  |=0x00000008 ;//Allow PA.3 to generate interrupt
  RfDs.EntRxCnt_R++ ;
  __enable_irq();
}
/****************************************************************************
 *                   		   readback functions                           *
 ***************************************************************************/
__inline uint16_t Read_AFC_021_R(void)
{
  if( RfDs.IC021Init_R.OpSta==IN_RX_STA)
    return ReadReg_021_R(0x10);
  else
    return 0 ;
}
int16_t  ReadTemperature_021_R(void)
{
  int32_t temp = -45;
  uint32_t tmp ;
  uint16_t TMP_value ;
  if(RfDs.IC021Init_R.OpSta==IN_TX_STA)
  {//I assume the ADC enabled in Tx mode
    TMP_value=ReadReg_021_R(0x16)&0x007f;
    TMP_value&=0x007f ;
    TMP_value *=10 ;
    temp=(684-TMP_value)*932 ;
    temp/=1000 ;
    temp-=40 ;
    return (int16_t)temp ;
  }
  else
  {
    tmp=APP_PC_p->IC021Def.R9_AGC|0x00080000 ;
	WriteReg_021_R(&tmp);//Disable(Freeze) the AGC function
    TMP_value=ReadReg_021_R(0x16)&0x007f;
	WriteReg_021_R(&APP_PC_p->IC021Def.R9_AGC);//re_enable the AGC function
    TMP_value&=0x007f ;
    TMP_value *=10 ;
    temp=(684-TMP_value)*932 ;
    temp/=1000 ;
    temp-=40 ;
    return (int16_t)temp ;
  }
}
int16_t Read_RSSI_021_R(void)
{
  int16_t rssi = 0;
  uint16_t RSSI_value;
  if( RfDs.IC021Init_R.OpSta==IN_RX_STA)
  {
	RSSI_value = ReadReg_021_R(0x14)&0x07ff;
    rssi = RSSI_value>>7 ;
    RSSI_value&=0x007f ;//only use last 7 bits
	RSSI_value += gain_correction[rssi&0x0F] ;
    rssi = RSSI_value>>1 ;//*0.5
    return (rssi-130) ;//RSSI(dBm) = rssi + 130
   }
  else
    return -128 ;//Should set to invalid value
}

uint16_t ReadFilterCal_021_R(void)
{
  uint16_t vreturn ;
  vreturn=ReadReg_021_R(0x18)&0x00ff ;
  RfDs.IC021Init_R.FilterCalReadback=vreturn-128 ;
  RfDs.IC021Init_R.FilterCalReadback<<=14 ;
  RfDs.IC021Init_R.FilterCalReadback&=0x000fc000 ;
  RfDs.IC021Init_R.FilterCalReadback|=APP_PC_p->IC021Def.R5_IF_Filter ;
  RfDs.IC021Init_R.FilterCalReadback&=0xffffffef ;//Clear calibration req bit
  return(vreturn);
}

uint16_t ReadVersion_021_R(void)
{
  //IC021 PC=0x210 ,current revision code should >=0x4
  return(ReadReg_021_R(0x1c));//So readback value >=0x2104
}
//----------------------------
void  EXTI3_ISR_021(void)
{//L_DCLK
  EXTI->PR=0x00000008 ;//Clear the interrupt
  RfDs.DClkCnt_R++  ;//MD20068
  switch (RfDs.RF_State_R) {
    case TX_STATUS:  //TX_STATE
			      if(BitCounter_R==0)
			      {
                    switch (RfDs.RF_TxState_R)
                    {
                      case TX_IDLE    : break ;//
                      case TX_1SEND_PREAMBLE_SEG : //prepare to output preamble status
                                  RfDs.RF_TxState_R=TX_1SEND_SOF_ID1 ;//switch to continue output preamble status
                                  PreambleCount_R=1 ;
                                  ShiftReg_R=(uint8_t)0x55 ;
                                  break ;
                      case TX_1SEND_SOF_ID1 : //continue to output the preamble character
                                  if(PreambleCount_R>=APP_PA.TxPreLen)
                                  {
                                    PreambleCount_R=0 ;//for debug
                                    RfDs.RF_TxState_R=TX_1SEND_SOF_ID2 ;//switch to prepare to output first SOF identifier status
                                    ShiftReg_R=(uint8_t)SOF_ID1 ;//Output first SOF identifier
                                   }
                                  else
                                  {
                                    PreambleCount_R++ ;
                                    ShiftReg_R=(uint8_t)0x55 ;//continue to output preamble
                                   }
                                  break ;
                      case TX_1SEND_SOF_ID2 : //
                                  RfDs.RF_TxState_R=TX_1SEND_DP ;//switch to prepare to output data frame status
                                  ByteCounter_R=0 ;
                                  RfDat_tp_R=(uint8_t *) U_Area.RfTxChBuf0;
                                  ShiftReg_R=(uint8_t)SOF_ID2 ;//Output second SOF identifier
                                  break ;
                      case TX_1SEND_DP : //
                                  ShiftReg_R=*(RfDat_tp_R+ByteCounter_R++);//TxBuffer[ByteCounter_R++]^0xaa ;
                                  ShiftReg_R^=(uint8_t)0xaa ;
                                  if(ByteCounter_R>=RfDs.TxPackageLen)//ByteCounter_R>=DATAFRAME_LEN)
                                  {
                                    RfDs.RF_TxState_R=TX_WAIT_FOR_END ;//switch to wait last charater send out status
                                    ByteCounter_R=0 ;                   //in this status the TX buffer can be rewrite now
                                    RfDs.RF_TxPackage_R++ ;
                                   }
                                  break ;
                      case TX_FINISHED : //finish to send all bytes
                                  ShiftReg_R=(uint8_t)0x55 ;//
                                  break ;
                      case TX_WAIT_FOR_END : 
                                  RfDs.RF_TxState_R=TX_WAIT_FOR_END2 ;//switch to Wait finish status 2
                                  ShiftReg_R=(uint8_t)0x55 ;//
                                  break ;
                      case TX_WAIT_FOR_END2 : 
                                  RfDs.RF_TxState_R=TX_WAIT_FOR_END3 ;//switch to Wait finish status 3
                                  ShiftReg_R=(uint8_t)0x55 ;//
                                  break ;
                      case TX_WAIT_FOR_END3 : 
                                  RfDs.RF_TxState_R=TX_FINISHED ;//switch to TX finish status
                                  ShiftReg_R=(uint8_t)0x55 ;//
                                  break ;
                      case TX_RF_TEST :
                                  if((TestCtrlW&0xfe00)==(u16)0xc200) //
                                  {
                                    if(TestCtrlW&(u16)0x0100)//
                                      ShiftReg_R=(uint8_t)0xff ;
                                    else
                                      ShiftReg_R=(uint8_t)0x00 ;
                                   }
                                  else
                                    ShiftReg_R=(uint8_t)0x55 ;
                                  break ;
                      default :   RfDs.RF_TxState_R=TX_IDLE ;
                     }
                   }
                  if(ShiftReg_R&(uint8_t)0x80)
                  {
                    GPIOA->BSRR =0x00000004 ;//set to high--L_DIO
                  }
                  else
                  {
                    GPIOA->BRR  =0x00000004  ;//set to low--L_DIO
                  }
			      ShiftReg_R=ShiftReg_R<<1;
			      BitCounter_R++;
			      if(BitCounter_R>=8) BitCounter_R=0 ;
			      break;
   case RX_STATUS :  //RX_STATE
                  /* Read data from IC021 */
			      ShiftReg_R=ShiftReg_R<<1;
			      if(GPIOA->IDR &0x00000004) ShiftReg_R|=(uint8_t)0x01 ;//DIO pin = high
			      BitCounter_R++;
                  switch (RfDs.RF_RxState_R) {
                    case RX_NO_INIT_IDLE : break ;
                    case RX_SEARCH_PREAMBLE : //search for a vaild preamble status
						       /* If valid preamble, increase counter */
						       if ((ShiftReg_R==(uint8_t)0x55)||(ShiftReg_R==(uint8_t)0xAA))
                                 PreambleCount_R++;
						       /* If not, reset counter */
						       else
							     PreambleCount_R=0;
						       /* If preamble requirement has been reached, declare that preamble */
						       /* has been found */
						       if (PreambleCount_R>=PREAMBLE_REQ)
						       {
								 RfDs.RF_RxState_R=RX_SEARCH_SOF1;//switch to preamble found status
								 PreambleError_R=0;
			                     IsrFlag |=IFG_RSSI_D_OK_R ;//Request to do RSSI read operation
					            }
                               break ;
                    case RX_SEARCH_SOF1 ://vailid preable be found already
                               //here must start receiving monitor timer#####################
                               //after receiving last data byte,must disable receiving monitor timer
                               //overflow of the timer,will reset the receiver deamon

				               /* Look for SOF/unique identifier */
							   if (ShiftReg_R==(uint8_t)SOF_ID1)
                               {
								// If SOF found, go into RX mode
								BitCounter_R=0 ;
								RfDs.RF_RxState_R=RX_SEARCH_SOF2;//first SOF already received status
								}
								/* Are we still receiving the preamble? */
							   else if ((ShiftReg_R==(uint8_t)0x55)||(ShiftReg_R==(uint8_t)0xAA))
                               {
								 ; /* If so, do nothing */
								}
								/* If we are not receiving a correct preamble, declare an error */
							   else if (PreambleError_R==0)
                               {
								 PreambleError_R++;
								}
							   if (PreambleError_R>0)
								/* Increase the error counter regardless of bits read if */
								/* we have found en error */
								 PreambleError_R++;
									
								/* Once an error condition has occurred, a correct SOF must be */
								/* found within 9 bits (we increment by 2 at the first bit), */
								/* otherwise we abort and start looking for the preamble again */
							   if (PreambleError_R>10)
                               {
								 RfDs.RF_RxState_R=RX_SEARCH_PREAMBLE;//switch to search for preamble status
								}
                               break ;
                    case RX_SEARCH_SOF2 : //
			                    /* Byte received? */
			                    if (BitCounter_R>=8)
			                    {
					              BitCounter_R=0;
								  if (ShiftReg_R==(uint8_t)SOF_ID2)
								  {//clear receive buffer ,reset pointer,ready to receiving datapackage
								    ByteCounter_R=0 ;
								    if((RfDs.RBuf_StaR&0x02)==0)
							        {
								      RfDat_rp_R=(uint8_t*)U_Area.RfRxChBuf0_R ;
								      RfDs.RBuf_StaR &=(uint8_t)0xfc ;//
                                      RfDs.RBuf_StaR |=(uint8_t)0x01 ;//
								     }
								    else
								    {
								      RfDat_rp_R=(uint8_t*)U_Area.RfRxChBuf1_R ;
								      RfDs.RBuf_StaR &=(uint8_t)0xf3 ;//
                                      RfDs.RBuf_StaR |=(uint8_t)0x04 ;//
								     }
								    RfDs.RF_RxState_R=RX_RECIEVE_DP;//switch to receiving data package status
								   }
								  else
								  {
								    RfDs.RF_RxState_R=RX_SEARCH_PREAMBLE;//switch to search for preamble status
								   }
								 }
                                break ;
                    case RX_RECIEVE_DP : //
			                    /* Byte received? */
			                    if (BitCounter_R>=8)
			                      {
					                 BitCounter_R=0;
					                 *(RfDat_rp_R+ByteCounter_R++)=ShiftReg_R^(uint8_t)0xaa;//RxBuffer_R[ByteCounter_R++]=ShiftReg_R^0xaa;
					                 if(ByteCounter_R>=RfDs.RxPackageLen)
					                   {
								         if(RfDat_rp_R==(uint8_t*)U_Area.RfRxChBuf0_R)
                                         {
								           RfDs.RBuf_StaR &=(uint8_t)0xfe ;
                                           RfDs.RBuf_StaR |=(uint8_t)0x02 ;//set data buffer0 valid flag
                                         }
								         else
                                         {
								           RfDs.RBuf_StaR &=(uint8_t)0xfb ;
                                           RfDs.RBuf_StaR |=(uint8_t)0x08 ;//set data buffer1 valid flag
                                         }
								         RfDs.RF_RxState_R=RX_SEARCH_PREAMBLE;//enter to search new data frame status
								                                 //the received data
								         RfDs.RF_RxPackage_R++ ;
					                    }
                                   }
                                break ;
                    case RX_DP_FINISHED:
                      
                                break ;
                    case RX_RF_TEST_INIT:
						       if ((ShiftReg_R==(uint8_t)0x55)||(ShiftReg_R==(uint8_t)0xAA))
                               {
                                 RfDs.char_count_R=0 ;
                                 RfDat_rp_R=(uint8_t*) U_Area.Ultra_BUF_R ;
		                         RfDs.RF_RxState_R=RX_RF_TEST;//switch to RX test status
	                            }
	                           break ;
                    case RX_RF_TEST:
                               if (BitCounter_R==8)
                               {
                                 BitCounter_R=0 ;
                                 *RfDat_rp_R=ShiftReg_R ;
                                 RfDat_rp_R++ ;
                                 RfDs.char_count_R++ ;
                                 if((RfDs.char_count_R&(u16)0x001f)==0)
                                   IsrFlag |=IFG_RSSI_D_OK_R ;//Request to do RSSI check operation
                                 if(RfDs.char_count_R>=96)
                                  {
                                    RfDs.char_count_R=0 ;
                                    RfDat_rp_R=(uint8_t*)  U_Area.Ultra_BUF_R ;
                                   }
                                }
                                break ;
			        default :
								RfDs.RF_RxState_R=RX_SEARCH_PREAMBLE;//enter data ready flag status,guide RX deamon to process
								                          //the received data
					}
					break ;
	default :{
	           RfDs.RF_State_R=RF_IDLE_STATUS ;//=0x0000
               EXTI->IMR  &=0xfffffff7  ; // disable DCLK signal to generate interrupt in current state
              }
  }
}
/* EOF */

