;#<FEEDBACK># ARM Linker, 5060750: Last Updated: Sat Sep 27 09:54:18 2025
;VERSION 0.2
;FILE ad_input.o
CalBatVoltage <= USED 0
CalSysTemp <= USED 0
;FILE core_cm3.o
__REV16 <= USED 0
__REVSH <= USED 0
__get_MSP <= USED 0
__get_PSP <= USED 0
__set_MSP <= USED 0
__set_PSP <= USED 0
;FILE dataview.o
SprintForceV <= USED 0
SprintInclAngleV <= USED 0
SprintOilPosV <= USED 0
SprintPowV <= USED 0
SprintVolV <= USED 0
;FILE display.o
AHAreaPrintDeamon <= USED 0
LcdContrast <= USED 0
LcdInit <= USED 0
LcdReadData <= USED 0
LcdReadStatus <= USED 0
LcdReset <= USED 0
LcdSaftyHintPrint <= USED 0
LcdSendCommand <= USED 0
LcdSendData <= USED 0
LcdWakeup <= USED 0
LcdWelcomPrint <= USED 0
S260T1_Print_A11 <= USED 0
S260T1_Print_A12 <= USED 0
S260T2_Print_A11 <= USED 0
S260T2_Print_A12 <= USED 0
S260T2_Print_A13 <= USED 0
S260T2_Print_A9 <= USED 0
SetLayoutOnLCD <= USED 0
UnicodeToIndex12 <= USED 0
UnicodeToIndex16 <= USED 0
;FILE f25basic.o
InitTIM11_F25 <= USED 0
SysTickIsr_F25 <= USED 0
;FILE f25main.o
;FILE f35basic.o
;FILE f35main.o
SyncTxSta <= USED 0
;FILE keyandmenu.o
InitSequencer <= USED 0
S160Tx_GetKeyID <= USED 0
UnlockPassWordCheck <= USED 0
__ARM_common_memclr4_8 <= USED 0
;FILE ma_certify_fun.o
MA_SendTo204B <= USED 0
;FILE main.o
;FILE mbmaster.o
ActiveAntenna <= USED 0
ModDevComMon <= USED 0
;FILE misc.o
NVIC_SetVectorTable <= USED 0
NVIC_SystemLPConfig <= USED 0
SysTick_CLKSourceConfig <= USED 0
;FILE modbusrtu_a.o
;FILE rf_basicfun.o
;FILE rf_ic_01.o
JF01GetRxSta <= USED 0
JF01GetTxSta <= USED 0
JF01ReadFIFO <= USED 0
JF01ReadPATable <= USED 0
JF01ReadRSSIReg <= USED 0
JF01ReadReg <= USED 0
JF01_B_Write <= USED 0
ReadJF01FSCal <= USED 0
Read_RSSI_01 <= USED 0
ShrMachDatRxDaemon_01 <= USED 0
SwitchTxToRx_01 <= USED 0
;FILE rf_ic_021.o
ReadFilterCal_021 <= USED 0
ReadTemperature_021 <= USED 0
RfRxTestDaemon_021 <= USED 0
SwitchRxToTx_021 <= USED 0
TunMachDatTxLoad_021 <= USED 0
;FILE rf_test_01.o
RSSI_Test_Filter_01 <= USED 0
ReadRSSITest_01 <= USED 0
TxTestDaemon_01 <= USED 0
;FILE rf_test_021.o
RSSI_Test_Filter_021 <= USED 0
;FILE s30basic.o
SysTickIsr_S30 <= USED 0
;FILE s30main_01.o
;FILE s30main_021.o
;FILE startup_stm32l1xx_md.o
;FILE stm32l1xx_flash.o
DATA_EEPROM_EraseWord <= USED 0
DATA_EEPROM_FastProgramByte <= USED 0
DATA_EEPROM_FastProgramHalfWord <= USED 0
DATA_EEPROM_FastProgramWord <= USED 0
DATA_EEPROM_FixedTimeProgramCmd <= USED 0
DATA_EEPROM_Lock <= USED 0
DATA_EEPROM_ProgramByte <= USED 0
DATA_EEPROM_ProgramHalfWord <= USED 0
DATA_EEPROM_ProgramWord <= USED 0
DATA_EEPROM_Unlock <= USED 0
FLASH_ClearFlag <= USED 0
FLASH_ErasePage <= USED 0
FLASH_FastProgramWord <= USED 0
FLASH_GetFlagStatus <= USED 0
FLASH_GetStatus <= USED 0
FLASH_ITConfig <= USED 0
FLASH_Lock <= USED 0
FLASH_OB_BORConfig <= USED 0
FLASH_OB_GetBOR <= USED 0
FLASH_OB_GetUser <= USED 0
FLASH_OB_GetWRP <= USED 0
FLASH_OB_UserConfig <= USED 0
FLASH_OB_WRPConfig <= USED 0
FLASH_PrefetchBufferCmd <= USED 0
FLASH_ReadAccess64Cmd <= USED 0
FLASH_SLEEPPowerDownCmd <= USED 0
FLASH_SetLatency <= USED 0
FLASH_Unlock <= USED 0
;FILE stm32l1xx_it.o
;FILE stm32l1xx_rcc.o
RCC_AHBPeriphClockCmd <= USED 0
RCC_AHBPeriphClockLPModeCmd <= USED 0
RCC_AHBPeriphResetCmd <= USED 0
RCC_APB1PeriphClockLPModeCmd <= USED 0
RCC_APB2PeriphClockLPModeCmd <= USED 0
RCC_AdjustHSICalibrationValue <= USED 0
RCC_AdjustMSICalibrationValue <= USED 0
RCC_ClearFlag <= USED 0
RCC_ClearITPendingBit <= USED 0
RCC_ClockSecuritySystemCmd <= USED 0
RCC_DeInit <= USED 0
RCC_GetClocksFreq <= USED 0
RCC_GetFlagStatus <= USED 0
RCC_GetITStatus <= USED 0
RCC_GetSYSCLKSource <= USED 0
RCC_HCLKConfig <= USED 0
RCC_HSEConfig <= USED 0
RCC_HSICmd <= USED 0
RCC_ITConfig <= USED 0
RCC_LSEConfig <= USED 0
RCC_LSICmd <= USED 0
RCC_MCOConfig <= USED 0
RCC_MSICmd <= USED 0
RCC_MSIRangeConfig <= USED 0
RCC_PCLK1Config <= USED 0
RCC_PCLK2Config <= USED 0
RCC_PLLCmd <= USED 0
RCC_PLLConfig <= USED 0
RCC_RTCCLKCmd <= USED 0
RCC_RTCCLKConfig <= USED 0
RCC_RTCResetCmd <= USED 0
RCC_SYSCLKConfig <= USED 0
RCC_WaitForHSEStartUp <= USED 0
;FILE sysbasic.o
CRC16 <= USED 0
CRC32 <= USED 0
ClrObj <= USED 0
ClrObj32 <= USED 0
CopyObj <= USED 0
CopyObj16 <= USED 0
CopyObj32 <= USED 0
DisableTIM9 <= USED 0
GetSysRstKeySta <= USED 0
Led_2x1_sta <= USED 0
;FILE sysconfig.o
ModifyPara <= USED 0
;FILE system_stm32l1xx.o
SetSysClockHSI_12MHz <= USED 0
;FILE timer.o
;FILE uartfun_a.o
CloseUBTx <= USED 0
OpenUBTx <= USED 0
UART_BaudRateConfig <= USED 0
USART1_BaudRateConfig <= USED 0
