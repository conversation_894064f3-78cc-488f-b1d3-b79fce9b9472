/************S30Basic.h*******************/
#ifndef __S30_BASIC_H
#define __S30_BASIC_H
#include "stm32l1xx.h"
#include "ThisDevice.h"
#include "Timer.h"
#include "Display.h"
#include "RF_BasicFun.h"
#include "SysBasic.h"
#include "AD_Input.h"
#include "KeyAndMenu.h"
#include "MA_Certify_Fun.h"
/* Extern variables ----------------------------------------------------------*/
//-----------------------------------------------------------------------

void  Init108_S30Mode(void);//Init the 38108 board(main environment,like:STM32L15x )for Fys30 mode
void  InitTIM9_S30(void ) ;
void  InitTIM11_S30(void ) ;
void  TIM9_ISR_S30(void) ;
void  RfTickTimer_ISR_S30(void ) ; 
void  TIM11_ISR_S30(void ) ;
#endif   //__S30_BASIC_H
