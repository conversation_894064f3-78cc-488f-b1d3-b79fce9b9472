//----------------------------------------------------
#include  "SysBasic.h"
#include  "RF_BasicFun.h"
#include  "UartFun_A.h"
#include  "RF_Test_01.h"

extern  UARTStruct   UA ;//

extern  ModStruct  ModA ;

//----------------------------------
void RfTestEntry_01(void)
{
  Led_1_on ;//Turn on RUN LED
  Timer10[RUN_IND_TIMER].pv=5 ;
  Timer10[RUN_IND_TIMER].csr.csword=0x4000 ; //start Timer10[RUN_IND_TIMER]
  RfcTimer10[AIR_TIMER_L].pv=12 ;//
  RfcTimer10[AIR_TIMER_L].cv=0 ;
  RfcTimer10[AIR_TIMER_L].csr.csword=0x5000 ; //start RfcTimer10[AIR_TIMER_L],set Q=1
  RfcTimer10[AIR_TIMER_R].pv=12 ;
  RfcTimer10[AIR_TIMER_R].cv=0 ;
  RfcTimer10[AIR_TIMER_R].csr.csword=0x5000 ; //start RfcTimer10[AIR_TIMER_R],set Q=1
  //-----------
  RSSI_STA_L=0x0002 ;
  RSSI_STA_R=0x0002 ;
  RSSI_BufIndex_L=0 ;
  RSSI_BufIndex_R=0 ;
 //--------------------------------------
  SYS_STA=0x0006 ;//For test mode init
  KickWatchDog();//kick the watchdog
 //-------------------------------------- 
 while(1)
 {
  if(Timer10[RUN_IND_TIMER].csr.csbit.q==1)
  {
    Timer10[RUN_IND_TIMER].csr.csword=0x4000 ;
    Timer10[RUN_IND_TIMER].cv=0 ;
    if(Led_1_sta)//LED1 in ON status
    {
      Led_1_off ;//Turn off RUN LED "D1"
     }
    else
      Led_1_on ;//Turn on RUN LED "D1"
   }
  TimerProc() ;//user defined timer proccess
  switch(SYS_STA) {
    case 0x0000:
	    if((TestCtrlW&0xf000)==0xc000) //
	    {//Enter TX test status
		   //--Use RX frequency in TX test mode(convenient for RX channel calibration)
          JF01WriteReg_L(JF01_FREQ2,    Rx01_Ch_p_L->freq2);  // Frequency control word, high byte.
          JF01WriteReg_L(JF01_FREQ1,    Rx01_Ch_p_L->freq1);  // Frequency control word, middle byte.
          JF01WriteReg_L(JF01_FREQ0,    Rx01_Ch_p_L->freq0);  // Frequency control word, low byte.
		  JF01WriteReg_R(JF01_FREQ2,    Rx01_Ch_p_R->freq2);  // Frequency control word, high byte.
		  JF01WriteReg_R(JF01_FREQ1,    Rx01_Ch_p_R->freq1);  // Frequency control word, middle byte.
		  JF01WriteReg_R(JF01_FREQ0,    Rx01_Ch_p_R->freq0);  // Frequency control word, low byte.
		  JF01WriteReg_L(JF01_IOCFG0,0x02) ;// Set GDO0 to be TX FIFO threshold ALARM signal
		  JF01WriteReg_L(JF01_IOCFG2,0x06) ;// Set GDO2 to packet sent signal
		  JF01WriteReg_R(JF01_IOCFG0,0x02) ;// Set GDO0 to be TX FIFO threshold ALARM signal
		  JF01WriteReg_R(JF01_IOCFG2,0x06) ;// Set GDO2 to packet sent signal
		  if((TestCtrlW&0xff00)==0xc100) //
		  {//Tx for 0x00 modulation
		    JF01WriteFIFO_Const_L(0x00,62);//
		    JF01WriteFIFO_Const_R(0x00,62);//
		    TestCtrlW=0xc200 ;
		   }
		  else if((TestCtrlW&0xff00)==0xc300) //"
		  {//Tx for 0xff modulation
		    JF01WriteFIFO_Const_L(0xff,62);//
		    JF01WriteFIFO_Const_R(0xff,62);//
		    TestCtrlW=0xc300 ;
		   }
		  else
		  {//Tx for 0x55 modulation
		    JF01WriteFIFO_Const_L(0x55,62);//
		    JF01WriteFIFO_Const_R(0x55,62);//
		    TestCtrlW=0xc100 ;
		   }
		  EnterTxTestMode_01_L() ;
		  JF01CmdStrobe_L(JF01_STX);//Enter TX status
		  EnterTxTestMode_01_R() ;
		  JF01CmdStrobe_R(JF01_STX);//Enter TX status
		  SYS_STA=0x0002 ;//Enter RF TX test status
		  break ;
	     }
	    if(TestCtrlW==0x8000)//Require  TestCtrlW==0x80xx ;
	    {//
          JF01WriteReg_L(JF01_FREQ2,    Rx01_Ch_p_L->freq2);  // Frequency control word, high byte.
          JF01WriteReg_L(JF01_FREQ1,    Rx01_Ch_p_L->freq1);  // Frequency control word, middle byte.
          JF01WriteReg_L(JF01_FREQ0,    Rx01_Ch_p_L->freq0);  // Frequency control word, low byte.
		  JF01WriteReg_R(JF01_FREQ2,    Rx01_Ch_p_R->freq2);  // Frequency control word, high byte.
		  JF01WriteReg_R(JF01_FREQ1,    Rx01_Ch_p_R->freq1);  // Frequency control word, middle byte.
		  JF01WriteReg_R(JF01_FREQ0,    Rx01_Ch_p_R->freq0);  // Frequency control word, low byte.
		  JF01WriteReg_L(JF01_IOCFG0,0x00) ;// Set GDO0 to be Asserts when RX FIFO is filled at or above threshold
		  JF01WriteReg_L(JF01_IOCFG2,0x06) ;// Set GDO2 to assert when sync word has been received
		  JF01WriteReg_R(JF01_IOCFG0,0x00) ;// Set GDO0 to be Asserts when RX FIFO is filled at or above threshold
		  JF01WriteReg_R(JF01_IOCFG2,0x06) ;// Set GDO2 to assert when sync word has been received
		  EnterRxTestMode_01_L() ;
		  EnterRxTestMode_01_R() ;
		  JF01CmdStrobe_L(JF01_SRX);//Enter RX status
		  JF01CmdStrobe_R(JF01_SRX);//Enter RX status
		  SYS_STA=0x0004 ;//ENter RF RX test status
	     }
	   break ;
    case 0x0002:
         TxTestDaemon_01_L() ;
         TxTestDaemon_01_R() ;
		 if(U_Area.TestCtrlW==0x5a5a) //MD40099  TestCtrlW
		 {
		   U_Area.TestCtrlW=0x0101 ;
	       U_Area.Ultra_BUF_L[0]=JF01GetTxSta_L() ;
		   U_Area.Ultra_BUF_L[1]=JF01GetTxSta_R() ;
		  }
		 if(U_Area.TestCtrlW==0x5555)//MD 40099
		 {
		   U_Area.TestCtrlW=0x0101 ;
		   JF01CmdStrobe_L(JF01_SIDLE);//Set to IDLE status
		   JF01CmdStrobe_R(JF01_SIDLE);//Set to IDLE status
		   JF01CmdStrobe_L(JF01_SFTX);//Flush TX FIFO status
		   JF01CmdStrobe_R(JF01_SFTX);//Flush TX FIFO status
		   if((TestCtrlW&0xff00)==0xc200)
		   {//Tx for 0x00 modulation
		     JF01WriteFIFO_Const_L(0x00,62);//
		     JF01WriteFIFO_Const_R(0x00,62);//
		    }
		   else if((TestCtrlW&0xff00)==0xc300)
		   {//Tx for 0xff modulation
		     JF01WriteFIFO_Const_L(0xff,62);//
		     JF01WriteFIFO_Const_R(0xff,62);//
		    }
		   else
		   {//Tx for 0x55 modulation
		     JF01WriteFIFO_Const_L(0x55,62);//
		     JF01WriteFIFO_Const_R(0x55,62);//
		    }
		   EnterTxTestMode_01_L() ;
		   JF01CmdStrobe_L(JF01_STX);//Enter TX status
		   EnterTxTestMode_01_R() ;
		   JF01CmdStrobe_R(JF01_STX);//Enter TX status
		  }
	   break ;
	case 0x0004:
         RxTestDaemon_01_L() ;
         RxTestDaemon_01_R() ;
         RSSI_Filter_Test_01() ;
		 if(OpCtrl&(CB_RX_OK_L+CB_RX_OK_R))
		   Led_2_on ;//Turn on the OK LED
		 else
		   Led_2_off ;
		 if(U_Area.TestCtrlW==0x5a5a) //MD40099  TestCtrlW
		 {
		   U_Area.TestCtrlW=0x0101 ;
		   U_Area.Ultra_BUF_L[0]=JF01GetRxSta_L() ;
		   U_Area.Ultra_BUF_L[1]=JF01GetRxSta_R() ;
		  }
		 if(U_Area.TestCtrlW==0x5aa5) //MD 40099
		 {
		   U_Area.TestCtrlW=0x0101 ;
		   JF01CmdStrobe_L(JF01_SIDLE);//Set to IDLE status
		   JF01CmdStrobe_R(JF01_SIDLE);//Set to IDLE status
		   JF01CmdStrobe_L(JF01_SFRX);//Flush RX FIFO status
		   JF01CmdStrobe_R(JF01_SFRX);//Flush RX FIFO status
		   EnterRxTestMode_01_L() ;
		   EnterRxTestMode_01_R() ;
		   JF01CmdStrobe_L(JF01_SRX);//Enter RX status
		   JF01CmdStrobe_R(JF01_SRX);//Enter RX status
		  }
	   break ;						 
    case 0x0006://Test mode Entry  init
         InitSPI1_01() ;
	     ResetJF01_R() ;//After this operation- RFChipOscSta=RFIC_OSC_READY
	     ResetJF01_L();//After this chip enter IDLE status
         SetupTestRegs_01_L() ;//
         SetupTestRegs_01_R() ;//
		 SYS_STA=0x0000 ;//For test mode select
	default: SYS_STA=0x0006 ;
	}
//------------------------------------------------  
  if(RfDs.AreaFlag100==0x1234)//MD20100,==0x1234
  {
    RfDs.AreaFlag100=0x0101 ;
    JF01_B_Read_L(JF01_IOCFG2,(uint8_t *) &U_Area.Ultra_BUF_L[0],47) ;//---MD20001
    JF01_B_Read_R(JF01_IOCFG2,(uint8_t *) &U_Area.Ultra_BUF_L[24],47) ;//---MD20025
   }
  if(RfDs.AreaFlag100==0x2345)//MD20100,==0x2345
  {
    RfDs.AreaFlag100=0x0101 ;
    JF01_B_Read_L(JF01_MARCSTATE,(uint8_t *) &U_Area.Ultra_BUF_L[48],1) ;//---MD20049HB
    JF01_B_Read_R(JF01_MARCSTATE,(uint8_t *) &U_Area.Ultra_BUF_L[49],1) ;//---MD20050HB
   }
//----------------------------------
  ModProcA(&UA,&ModA) ;//Modbus daemon  using channel A
  UXTxP() ;
//----------------------------------
  KickWatchDog();//kick the watchdog
  CpuSweepTime(&U_Area.SweepTimeMax,&U_Area.SweepTimeC); 
 }
}

void SetupTestRegs_01_L(void)
{
  ResetJF01_L();//After this chip enter IDLE status
  JF01CmdStrobe_L(JF01_SIDLE);//Enter IDLE status
  JF01WriteReg_L(JF01_FSCTRL1,  APP_PD_p->JF01Def.fsctrl1);    // Frequency synthesizer control.
  JF01WriteReg_L(JF01_FSCTRL0,  APP_PD_p->JF01Def.fsctrl0);    // Frequency synthesizer control.
  JF01WriteReg_L(JF01_MDMCFG4,  APP_PD_p->JF01Def.mdmcfg4);    // Modem configuration.
  JF01WriteReg_L(JF01_MDMCFG3,  APP_PD_p->JF01Def.mdmcfg3);    // Modem configuration.
  JF01WriteReg_L(JF01_MDMCFG2,  0x00);    // Modem configuration for no preamble/sync word in RX/TX mode(2FSK )
  JF01WriteReg_L(JF01_MDMCFG1,  APP_PD_p->JF01Def.mdmcfg1);    // Modem configuration.
  JF01WriteReg_L(JF01_MDMCFG0,  APP_PD_p->JF01Def.mdmcfg0);    // Modem configuration.
  JF01WriteReg_L(JF01_CHANNR,   APP_PD_p->JF01Def.channr);     // Channel number.
  JF01WriteReg_L(JF01_DEVIATN,  APP_PD_p->JF01Def.deviatn);    // Modem deviation setting (when FSK modulation is enabled).
  JF01WriteReg_L(JF01_FREND1,   APP_PD_p->JF01Def.frend1);     // Front end RX configuration.
  JF01WriteReg_L(JF01_FREND0,   APP_PD_p->JF01Def.frend0);     // Front end RX configuration.
  JF01WriteReg_L(JF01_MCSM0,    APP_PD_p->JF01Def.mcsm0);      // Main Radio Control State Machine configuration.
  JF01WriteReg_L(JF01_FOCCFG,   APP_PD_p->JF01Def.foccfg);     // Frequency Offset Compensation Configuration.
  JF01WriteReg_L(JF01_BSCFG,    APP_PD_p->JF01Def.bscfg);      // Bit synchronization Configuration.
  JF01WriteReg_L(JF01_AGCCTRL2, APP_PD_p->JF01Def.agcctrl2);   // AGC control.
  JF01WriteReg_L(JF01_AGCCTRL1, APP_PD_p->JF01Def.agcctrl1);   // AGC control.
  JF01WriteReg_L(JF01_AGCCTRL0, APP_PD_p->JF01Def.agcctrl0);   // AGC control.
  JF01WriteReg_L(JF01_FSCAL3,   APP_PD_p->JF01Def.fscal3);     // Frequency synthesizer calibration.
  JF01WriteReg_L(JF01_FSCAL2,   APP_PD_p->JF01Def.fscal2);     // Frequency synthesizer calibration.
  JF01WriteReg_L(JF01_FSCAL1,   APP_PD_p->JF01Def.fscal1);     // Frequency synthesizer calibration.
  JF01WriteReg_L(JF01_FSCAL0,   APP_PD_p->JF01Def.fscal0);     // Frequency synthesizer calibration.
  JF01WriteReg_L(JF01_FSTEST,   APP_PD_p->JF01Def.fstest);     // Frequency synthesizer calibration.
  JF01WriteReg_L(JF01_TEST2,    APP_PD_p->JF01Def.test2); //Various test settings ,lost in sleep status.
  JF01WriteReg_L(JF01_TEST1,    APP_PD_p->JF01Def.test1); //Various test settings ,lost in sleep status.
  JF01WriteReg_L(JF01_TEST0,    APP_PD_p->JF01Def.test0); //Various test settings ,lost in sleep status.
  JF01WriteReg_L(JF01_FIFOTHR,  0x47); //Set TX FIFO threshold 33 Bytes,Bytes in RX FIFO =32 ,Test1=0x35 ,Test2=0x81 from sleep
  JF01WriteReg_L(JF01_MCSM1,    0x00); //CCA mode=Always,RXOFF_MODE=IDLE,TXOFF_MODE=IDLE status
  JF01WriteReg_L(JF01_SYNC1,    0x55); //0xcc
  JF01WriteReg_L(JF01_SYNC0,    0x55); //0x33
  JF01WriteReg_L(JF01_PKTCTRL1, 0x00); // Packet automation control.
  JF01WriteReg_L(JF01_PKTCTRL0, 0x02); //use infinte packet mode for test.
  JF01WritePATable_L(DefPATable+3,2) ; //use DefPATable[3] normally
}

void SetupTestRegs_01_R(void) 
{
  ResetJF01_R();//After this chip enter IDLE status
  JF01CmdStrobe_R(JF01_SIDLE);//Enter IDLE status
  JF01WriteReg_R(JF01_FSCTRL1,  APP_PD_p->JF01Def.fsctrl1);    // Frequency synthesizer control.
  JF01WriteReg_R(JF01_FSCTRL0,  APP_PD_p->JF01Def.fsctrl0);    // Frequency synthesizer control.
  JF01WriteReg_R(JF01_MDMCFG4,  APP_PD_p->JF01Def.mdmcfg4);    // Modem configuration.
  JF01WriteReg_R(JF01_MDMCFG3,  APP_PD_p->JF01Def.mdmcfg3);    // Modem configuration.
  JF01WriteReg_R(JF01_MDMCFG2,  0x00);    // Modem configuration for no preamble/sync word in RX/TX mode(2FSK )
  JF01WriteReg_R(JF01_MDMCFG1,  APP_PD_p->JF01Def.mdmcfg1);    // Modem configuration.
  JF01WriteReg_R(JF01_MDMCFG0,  APP_PD_p->JF01Def.mdmcfg0);    // Modem configuration.
  JF01WriteReg_R(JF01_CHANNR,   APP_PD_p->JF01Def.channr);     // Channel number.
  JF01WriteReg_R(JF01_DEVIATN,  APP_PD_p->JF01Def.deviatn);    // Modem deviation setting (when FSK modulation is enabled).
  JF01WriteReg_R(JF01_FREND1,   APP_PD_p->JF01Def.frend1);     // Front end RX configuration.
  JF01WriteReg_R(JF01_FREND0,   APP_PD_p->JF01Def.frend0);     // Front end RX configuration.
  JF01WriteReg_R(JF01_MCSM0,    APP_PD_p->JF01Def.mcsm0);      // Main Radio Control State Machine configuration.
  JF01WriteReg_R(JF01_FOCCFG,   APP_PD_p->JF01Def.foccfg);     // Frequency Offset Compensation Configuration.
  JF01WriteReg_R(JF01_BSCFG,    APP_PD_p->JF01Def.bscfg);      // Bit synchronization Configuration.
  JF01WriteReg_R(JF01_AGCCTRL2, APP_PD_p->JF01Def.agcctrl2);   // AGC control.
  JF01WriteReg_R(JF01_AGCCTRL1, APP_PD_p->JF01Def.agcctrl1);   // AGC control.
  JF01WriteReg_R(JF01_AGCCTRL0, APP_PD_p->JF01Def.agcctrl0);   // AGC control.
  JF01WriteReg_R(JF01_FSCAL3,   APP_PD_p->JF01Def.fscal3);     // Frequency synthesizer calibration.
  JF01WriteReg_R(JF01_FSCAL2,   APP_PD_p->JF01Def.fscal2);     // Frequency synthesizer calibration.
  JF01WriteReg_R(JF01_FSCAL1,   APP_PD_p->JF01Def.fscal1);     // Frequency synthesizer calibration.
  JF01WriteReg_R(JF01_FSCAL0,   APP_PD_p->JF01Def.fscal0);     // Frequency synthesizer calibration.
  JF01WriteReg_R(JF01_FSTEST,   APP_PD_p->JF01Def.fstest);     // Frequency synthesizer calibration.
  JF01WriteReg_R(JF01_TEST2,    APP_PD_p->JF01Def.test2); //Various test settings ,lost in sleep status.
  JF01WriteReg_R(JF01_TEST1,    APP_PD_p->JF01Def.test1); //Various test settings ,lost in sleep status.
  JF01WriteReg_R(JF01_TEST0,    APP_PD_p->JF01Def.test0); //Various test settings ,lost in sleep status.
  JF01WriteReg_R(JF01_FIFOTHR,  0x47); //Set TX FIFO threshold 33 Bytes,Bytes in RX FIFO =32 ,Test1=0x35 ,Test2=0x81 from sleep
  JF01WriteReg_R(JF01_MCSM1,    0x00); //CCA mode=Always,RXOFF_MODE=IDLE,TXOFF_MODE=IDLE status
  JF01WriteReg_R(JF01_SYNC1,    0x55); //0xcc
  JF01WriteReg_R(JF01_SYNC0,    0x55); //0x33
  JF01WriteReg_R(JF01_PKTCTRL1, 0x00); // Packet automation control.
  JF01WriteReg_R(JF01_PKTCTRL0, 0x02); //use infinte packet mode for test.
  JF01WritePATable_R(DefPATable+3,2) ; //I will only use DefPATable[3] normally
}

uint8_t JF01WriteFIFO_Const_L(uint8_t Data ,uint8_t Len)
{
  uint8_t i,sta ;
  GPIOB->BRR=0x00000400 ;//nCS='0'
  while((SPI1->SR&0x0002)==0x0000) ;//Wait the Tx FIFO to empty
  while(SPI1->SR&0x0001)
  { //empty the receive FIFO
    sta=(uint8_t) SPI1->DR ;//
   } 
  while((GPIOA->IDR&0x00000040)!=0)  ;//Wait for chip to ready
  __disable_irq();//Disable all interrupt
  SPI1->DR=JF01_TXFIFO+JF01_WRITE_BURST ;//Issue the address byte
  while((SPI1->SR&0x0001)==0)  ;//Wait for the data be output
  sta=SPI1->DR ;//Get returned JF01 sta byte
  for(i=0 ;i<Len ;i++)
  {
    SPI1->DR=Data ;//Write the data
    while((SPI1->SR&0x0001)==0)  ;//Wait for the data be output
    sta=SPI1->DR ;//dummy read
   }
  GPIOB->BSRR=0x00000400;  // Set /nCS to High 
  __enable_irq();
  return sta ;
}
uint8_t JF01WriteFIFO_Const_R(uint8_t Data ,uint8_t Len)
{
  uint8_t i,sta ;
  GPIOB->BRR=0x00000200 ;//nCS='0'
  while((SPI1->SR&0x0002)==0x0000) ;//Wait the Tx FIFO to empty
  while(SPI1->SR&0x0001)
  { //empty the receive FIFO
    sta=(uint8_t) SPI1->DR ;//
   } 
  while((GPIOA->IDR&0x00000040)!=0)  ;//Wait for chip to ready
  __disable_irq();//Disable all interrupt
  SPI1->DR=JF01_TXFIFO+JF01_WRITE_BURST ;//Issue the address byte
  while((SPI1->SR&0x0001)==0)  ;//Wait for the data be output
  sta=SPI1->DR ;//Get returned JF01 sta byte
  for(i=0 ;i<Len ;i++)
  {
    SPI1->DR=Data ;//Write the data
    while((SPI1->SR&0x0001)==0)  ;//Wait for the data be output
    sta=SPI1->DR ;//dummy read
   }
  GPIOB->BSRR=0x00000200;  // Set /nCS to High 
  __enable_irq();
  return sta ;
}
__inline void EnterTxTestMode_01_L(void)
{
  __disable_irq();//Disable all interrupt//Disable IRQ
  RfDs.RF_State_L=TX_STATUS ;//Set to TX state
  RfDs.RF_RxState_L=RX_NO_INIT_IDLE ;//
  RfDs.RF_TxState_L=TX_RF_TEST ;//Set to in Tx test	status
  EXTI->RTSR &=0x0000fff7 ;//Disable Rising trigger
  EXTI->FTSR |=0x00000008 ;//generate interrupt on high-to-low transition(TX FIFO blow threshold)
  EXTI->PR   |=0x00000008 ;//clear pending ext interrupt line3# PA.3(GDO0-DCLK)
  EXTI->IMR  |=0x00000008 ;//Allow PA.3 to generate interrupt
  __enable_irq();//Enable IRQ
}
__inline void EnterRxTestMode_01_L(void)
{
//  GPIOC->BRR=0x00000200 ;//Reset nRx_Tx_EN='0'
  __disable_irq();//Disable all interrupt//Disable IRQ
  RfDs.RF_State_L=RX_STATUS ;//Set to RX state
  RfDs.RF_RxState_L=RX_RF_TEST ;//Set to in Rx test status
  RfDs.RF_TxState_L=TX_IDLE ;//
  RfDs.char_count_L=0 ;
  test_p_L=(uint8_t*) &U_Area.Ultra_BUF_L[0] ;
  EXTI->RTSR |=0x00000008 ;//generate interrupt on low-to-high transition(RX FIFO filled at or above threshold)
  EXTI->FTSR &=0x0000fff7 ;//Disable Falling trigger
  EXTI->PR   |=0x00000008 ;//clear pending ext interrupt line3# PA.3(GDO0-DCLK)
  EXTI->IMR  |=0x00000008 ;//Allow PA.3 to generate interrupt
  __enable_irq();//Enable IRQ
}
__inline void EnterTxTestMode_01_R(void)
{
//  GPIOC->BSRR=0x00000200 ;//Set nRx_Tx_EN='1'
  __disable_irq();//Disable all interrupt//Disable IRQ
  RfDs.RF_State_R=TX_STATUS ;//Set to TX state
  RfDs.RF_RxState_R=RX_NO_INIT_IDLE ;//
  RfDs.RF_TxState_R=TX_RF_TEST ;//Set to in Tx test status
  EXTI->RTSR &=0x000feff ;//Disable Rising trigger
  EXTI->FTSR |=0x0000100 ;//generate interrupt on high-to-low transition(TX FIFO blow the threshold)
  EXTI->PR  |=0x00000100 ;//clear pending ext interrupt line8# PB.8(GDO0-DCLK)
  EXTI->IMR |=0x00000100 ;//Allow PB.8 to generate interrupt
  __enable_irq();//Enable IRQ
}

__inline void EnterRxTestMode_01_R(void)
{
  __disable_irq();//Disable all interrupt//Disable IRQ
  RfDs.RF_State_R =RX_STATUS ;//Set to RX state
  RfDs.RF_RxState_R =RX_RF_TEST ;//Set to in Rx test status
  RfDs.RF_TxState_R =TX_IDLE ;//
  RfDs.char_count_R=0 ;
  test_p_R=(uint8_t*) &U_Area.Ultra_BUF_R[0] ;
  EXTI->RTSR |=0x0000100 ;//generate interrupt on low-to-high transition(RX FIFO filled at or above the threshold)
  EXTI->FTSR &=0x000feff ;//Disable Falling trigger
  EXTI->PR  |=0x00000100 ;//clear pending ext interrupt line8# PB.8(GDO0-DCLK)
  EXTI->IMR |=0x00000100 ;//Allow PB.8 to generate interrupt
  __enable_irq();//Enable IRQ
}

void RxTestDaemon_01_L(void)
{
 uint8_t i,sta,error_c ;
 if((IsrFlag & IFG_JF01_FIFO_ALARM_L)||(GPIOC->IDR&0x00000080))//GDO0 interrupt or stay at high level
 {
   error_c=0 ; 
   __disable_irq();//Disable all interrupt//Disable IRQ
   IsrFlag &=~IFG_JF01_FIFO_ALARM_L ;//Clear flag
   __enable_irq();//Enable IRQ
   GPIOB->BRR=0x00000400 ;//reset PB.10 JF01 nCS='0'
   while((SPI1->SR&0x0002)==0x0000) ;//Wait the Tx FIFO to empty
   while(SPI1->SR&0x0001)
   { //empty the receive FIFO
     sta=(uint8_t) SPI1->DR ;//
    } 
   //---------------Begin to read out data in Rx FIFO
   while((GPIOA->IDR&0x00000040)!=0)  ;//Wait for chip to ready 
   SPI1->DR=JF01_RXFIFO+JF01_READ_BURST ;//Issue the address byte
   while((SPI1->SR&0x0001)==0)  ;//Wait the SPI1 to idle
   sta=SPI1->DR ;//Get returned JF01 sta byte
   for(i=0 ;i<FIFO_THRESHOLD_BYTES-1 ;i++)
   {
      SPI1->DR=JF01_RXFIFO+JF01_READ_BURST ;//Dummy data
      while ((SPI1->SR&0x0001)==0)  ;//Wait for the data be output
     *test_p_L=SPI1->DR ;//Get the data ;
     if(((*test_p_L)!=0x55)&&((*test_p_L)!=0xaa))
       error_c++ ;
     test_p_L++ ;
     RfDs.char_count_L++ ;
     if(RfDs.char_count_L>=64)
     {
       RfDs.char_count_L=0 ;
       test_p_L=(uint8_t*) &U_Area.Ultra_BUF_L[0] ;
      }
     }
   GPIOB->BSRR=0x00000400;  // Set PB.10 JF01 nCS to High 
   sta=sta ;
   __disable_irq();//Disable all interrupt//Disable IRQ
   if(error_c==0)
     OpCtrl|=CB_RX_OK_L ;
   else
     OpCtrl&=~CB_RX_OK_L ;
   IsrFlag|=IFG_RSSI_D_OK_L ;
   __enable_irq();//Enable IRQ
   RfcTimer10[AIR_TIMER_L].csr.csword=0x4000 ;//reset the monitor timer
   RfcTimer10[AIR_TIMER_L].cv=0 ;
   RfDs.RF_RxPackage_L++ ;
  }
}
void RxTestDaemon_01_R(void)
{
 uint8_t i,sta,error_c ;
 if((IsrFlag & IFG_JF01_FIFO_ALARM_R)||(GPIOB->IDR&0x00000800))//GDO0 interrupt or stay at high level
 {
   error_c=0 ; 
   __disable_irq();//Disable all interrupt//Disable IRQ
   IsrFlag &=~IFG_JF01_FIFO_ALARM_R ;//Clear flag
   __enable_irq();//Enable IRQ
   GPIOB->BRR=0x00000200 ;//PB.09-- nCS='0'
   while((SPI1->SR&0x0002)==0x0000) ;//Wait the Tx FIFO to empty
   while(SPI1->SR&0x0001)
   { //empty the receive FIFO
     sta=(uint8_t) SPI1->DR ;//
    } 
   //---------------Begin to read out data in Rx FIFO
   while((GPIOA->IDR&0x00000040)!=0)  ;//Wait for chip to ready 
   SPI1->DR=JF01_RXFIFO+JF01_READ_BURST ;//Issue the address byte
   while((SPI1->SR&0x0001)==0)  ;//Wait the SPI1 to idle
   sta=SPI1->DR ;//Get returned JF01 sta byte
   for(i=0 ;i<FIFO_THRESHOLD_BYTES-1 ;i++)
   {
     SPI1->DR=JF01_RXFIFO+JF01_READ_BURST ;//Dummy data
     while ((SPI1->SR&0x0001)==0)  ;//Wait for the data be output
     *test_p_R=SPI1->DR ;//Get the data ;
     if(((*test_p_R)!=0x55)&&((*test_p_R)!=0xaa))
       error_c++ ;
     test_p_R++ ;
     RfDs.char_count_R++ ;
     if(RfDs.char_count_R>=64)
     {
       RfDs.char_count_R=0 ;
       test_p_R=(uint8_t*) &U_Area.Ultra_BUF_R[0] ;
      }
    }
   GPIOB->BSRR=0x00000200 ;//PB.09-- nCS='1'
   sta=sta ;
   __disable_irq();//Disable all interrupt//Disable IRQ
   if(error_c==0)
     OpCtrl |=CB_RX_OK_R ;
   else
     OpCtrl &=~CB_RX_OK_R ;
   IsrFlag|=IFG_RSSI_D_OK_R ;
   __enable_irq();//Enable IRQ
   RfcTimer10[AIR_TIMER_R].csr.csword=0x4000 ;//reset the monitor timer
   RfcTimer10[AIR_TIMER_R].cv=0 ;
   RfDs.RF_RxPackage_R++ ;
  }
}

void TxTestDaemon_01_L(void)
{
 if(IsrFlag & IFG_JF01_FIFO_ALARM_L)//||(GPIOC->IDR&0x00000080))
 {
   __disable_irq();//Disable all interrupt//Disable IRQ
   IsrFlag &=~IFG_JF01_FIFO_ALARM_L ;//Clear flag
   __enable_irq();//Enable IRQ
   if((TestCtrlW&0xff00)==0xc200)
   {//Tx for 0x00 modulation
     JF01WriteFIFO_Const_L(0x00,FIFO_THRESHOLD_BYTES-1);//
    }
   else if((TestCtrlW&0xff00)==0xc300)
   {//Tx for 0xff modulation
     JF01WriteFIFO_Const_L(0xff,FIFO_THRESHOLD_BYTES-1);//
    }
   else
   {//Tx for 0x55 modulation
     JF01WriteFIFO_Const_L(0x55,FIFO_THRESHOLD_BYTES);//
    }
   RfcTimer10[AIR_TIMER_L].csr.csword=0x4000 ;//reset the monitor timer
   RfcTimer10[AIR_TIMER_L].cv=0 ;
   RfDs.RF_TxPackage_L++ ;
 }
}

void TxTestDaemon_01_R(void)
{
 if(IsrFlag & IFG_JF01_FIFO_ALARM_R)//||(GPIOB->IDR&0x00000800))
 {
   __disable_irq();//Disable all interrupt//Disable IRQ
   IsrFlag &=~IFG_JF01_FIFO_ALARM_R ;//Clear flag
   __enable_irq();//Enable IRQ
   if((TestCtrlW&0xff00)==0xc200)
   {//Tx for 0x00 modulation
     JF01WriteFIFO_Const_R(0x00,FIFO_THRESHOLD_BYTES-1);//
    }
   else if((TestCtrlW&0xff00)==0xc300)
   {//Tx for 0xff modulation
     JF01WriteFIFO_Const_R(0xff,FIFO_THRESHOLD_BYTES-1);//
    }
   else
   {//Tx for 0x55 modulation
     JF01WriteFIFO_Const_R(0x55,FIFO_THRESHOLD_BYTES);//
    }
   RfcTimer10[AIR_TIMER_R].csr.csword=0x4000 ;//reset the monitor timer
   RfcTimer10[AIR_TIMER_R].cv=0 ;
   RfDs.RF_TxPackage_R++ ;
  }
}
void  RSSI_Filter_Test_01(void)
{
 int16_t i,acc ;
 switch(RSSI_STA_L)
 {
  case 0x0000 :

      break ;
  case 0x0002 :
    if(IsrFlag&IFG_RSSI_D_OK_L)
    {
      __disable_irq();//Disable all interrupt//Disable IRQ
      IsrFlag&=~IFG_RSSI_D_OK_L ;
      __enable_irq();//Enable IRQ
      RfDs.RSSI_BUF_L[RSSI_BufIndex_L++]=ReadRSSI_Test_01_L() ;
      if(RSSI_BufIndex_L>=4)
      {
        RSSI_BufIndex_L=0 ;
        RSSI_STA_L=0x0004 ;
       }
    }
    break ;
  case 0x0004 :
    acc=0 ;
    for(i=0 ;i<4 ;i++)
    {
     acc+=RfDs.RSSI_BUF_L[i] ;
     }
    RfDs.RSSI_L=(acc>>2) ;
    RfDs.RSSI_L+=APP_PA.RSSIOffset_L ;
    if(RfDs.RSSI_L<-128) RfDs.RSSI_L=-128 ;
    RfDs.RSSI_L=RfDs.RSSI_L ;
    RSSI_STA_L=0x0002 ;
    break ;
  default :
    RSSI_STA_L=0x0000 ;
 }
 switch(RSSI_STA_R)
 {
  case 0x0000 :

    break ;
  case 0x0002 :
    if(IsrFlag&IFG_RSSI_D_OK_R)
    {
     __disable_irq();//Disable all interrupt//Disable IRQ
     IsrFlag&=~IFG_RSSI_D_OK_R ;
     __enable_irq();//Enable IRQ
     RfDs.RSSI_BUF_R[RSSI_BufIndex_R++]=ReadRSSI_Test_01_R() ;
     if(RSSI_BufIndex_R>=4)
     {
      RSSI_BufIndex_R=0 ;
      RSSI_STA_R=0x0004 ;
      }
    }
    break ;
  case 0x0004 :
    acc=0 ;
    for(i=0 ;i<4 ;i++)
    {
     acc+=RfDs.RSSI_BUF_R[i] ;
     }
    RfDs.RSSI_D_R=(acc>>2) ;
    RfDs.RSSI_D_R+=APP_PA.RSSIOffset_R ;
    if(RfDs.RSSI_D_R<-128) RfDs.RSSI_D_R=-128 ;
    RfDs.RSSI_R=RfDs.RSSI_R ;
    RSSI_STA_R=0x0002 ;
    break ;
  default :
    RSSI_STA_R=0x0000 ;
 }
}
//------------
int16_t ReadRSSI_Test_01_L(void)
{
 uint8_t ctmp ;
 int16_t  stmp ;
 ctmp=JF01ReadStaReg_L(JF01_RSSI) ;
 if(ctmp>128)
 {
   stmp=((uint16_t)ctmp-256)>>1 ;
   stmp-=RSSI_OFFSET ;
  }
 else
 {
   stmp=(ctmp>>1)-RSSI_OFFSET ;
  }
 return stmp ;
}
int16_t ReadRSSI_Test_01_R(void)
{
 uint8_t ctmp ;
 int16_t  stmp ;
 ctmp=JF01ReadStaReg_R(JF01_RSSI) ;if(ctmp>128)
 {
   stmp=((uint16_t)ctmp-256)>>1 ;
   stmp-=RSSI_OFFSET ;
  }
 else
 {
   stmp=(ctmp>>1)-RSSI_OFFSET ;
  }
 return stmp ;
}
//------------
