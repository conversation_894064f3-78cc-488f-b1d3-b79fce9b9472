#ifndef  __SYS_CONFIG_H
#define  __SYS_CONFIG_H
#include "stdint.h"
#include "SysBasic.h"

//System application configuration parameter group A
typedef struct TagSysConfigA_t  //Parameter ID=0x0041 ,LEN=32
{ 
 uint16_t        pid ;//Px0101,Parameter's identifier
 uint16_t        len ;//Px0102,configuration data length
 uint16_t  rev_wa[2] ;//Px0103-104 ,+4

 uint16_t   work_mode ;//Px0105, work mode of this device
	          //--Bit_12-15(DEVICE_MODE, Bit15(IC_SEL_CONFIG_BIT)) 
	          //  =0x0xxx-0x7xxx->for IC021 type transmitter/receiver(0x0xxx-0x3xxx for FYF35,0x4xxx-0x7xxx for FYS30(38403B type)) 
              //  =0x8xxx-0xfxxx->for JF01 type transmitter/receiver(0x8xxx-0xbxxx for FYF25,0xcxxx-0xfxxx for FYS30(38404B type))
              //--Bit_11(HEART_BEAT_EN_BIT) ='1' use heart beat signal, ='0' not use heart beat signal
              //--Bit_10(NO_AUTO_SHUTOFF) ='1' disable auto switch off function,='0' use it
		      //--Bit_8(LNA_EXIST_01) =1 there have a LNA used in 38404B RF front board,=0 no LNA used in 38404B RF front board
              //--Bit_0-3(MACHINE_TYPE)--> =0x0 for S260T1,=0x1 for S260T2,when in FYF35 tunneller mode
 uint16_t   GroupId_InGroupId ;//Px0106,Bit0-1[0-3]set this panel  in group id,Bit2-7[0-63] group id ,When the device work as panel transmitter 
 int16_t        BLShutOffTime ;//Px0107,LCD black light automatic shut off time when idle(in second,8S)
 uint16_t	   SysActWaitTime ;//Px0108,System active status wait time before enter sleep status(in second,1800S) ,+4

 uint16_t     RFIC_L_RxChNum ; //Px0109,Left side RF IC Receive working frequecy channel number--in Panel Transmitter mode
 uint16_t     RFIC_L_TxChNum ; //Px0110,Left side RF IC Transmitte working frequecy channel number--in Panel Transmitter mode
 uint16_t     RFIC_R_RxChNum ; //Px0111,Right side RF IC Receiver working frequecy channel number--in Panel Transmitter mode
 uint16_t     RFIC_R_TxChNum ; //Px0112,Right side RF IC Transmitte working frequecy channel number--in Panel Transmitter mode ,+4

 uint16_t       AirLinkageTO ;//Px0113,time out value for air linkage rx activ, in 10 mS unit
 uint16_t        OpcKeepTime ;//Px0114, Operation key code keep time for rf command(used in receiver mode),in 10 mS
 uint16_t    WaitPollTimeOut ;//Px0115, Wait for poll status time out value in Sync mode(ms) 
 uint16_t           TxPreLen ;//Px0116, RF Tx frame preamble segment length in bytes ,+4

 int16_t         SyncTimeOut ;//Px0117,SyncTimeOut-Synchronous status keep time setting (in RF Tx Ticks)
 int16_t              rev_w1 ;//Px0118,
 int16_t   JF01_RxAttenMonTO ;//Px0119,JF01 chip Rx attenuation status monitor time out value --in mS 						   
 int16_t     LedFlashInteval ;//Px0120,Determine the FYF25 Led flash interval --in RfTick numbers 						   

 uint16_t            rev_w2	;//Px0121,
 int16_t      RF_PowerLevel ;//Px0122,RF output power level setting 10 step [0..9],this will index to power level control table for each device
 int16_t         RSSIOffset ;//Px0123,--default RSSI offset
 int16_t             rev_w3 ;//Px0124, ,+4

 int16_t    BatMeAdjCoeff ;//Px0125,Scale coefficient used to adjust the calculated battery voltage x1000
 int16_t      BatMeOffset ;//Px0126,Offset used to adjust the calculated battery output voltage
 int16_t      BatLowLimit ;//Px0127,low voltage alarm
 int16_t     BatLowLimit0 ;//Px0128,use to indicate battery end up,+4

 int16_t       TCoeff ;//Px0129,typical temperature coefficient (1.66x4095/3300)*1000
 int16_t       VTemp0 ;//Px0130,0 celsuis degree output offset 
 uint16_t      rev_w4 ;//Px0131,
 uint16_t       crc16 ;//Px0132,CRC-16 of this configuration object   +4
} SysConfigA_t ;

//System application configuration parameter group B
typedef struct TagSysConfigB_t  //Parameter ID=0x0042 ,LEN=56
{ 
 uint16_t       pid ;//Px0101,Parameter's identifier
 uint16_t       len ;//Px0102,configuration data length
 uint16_t   ACode_l ;//Px0103,module Auth code low word16
 uint16_t   ACode_h ;//Px0104,module Auth code hight word16   ,+4

 UARTPortConfig   U2Config ;   //Px0105-0108,   +4

 SegCtrlStruct    Acc_SCtrl_0 ;//Px0109-0111,Access  control segment 0 , +3
 SegCtrlStruct    Acc_SCtrl_1 ;//Px0112-0114,Access  control segment 1 , +3
 SegCtrlStruct    Acc_SCtrl_2 ;//Px0115-0117,Access  control segment 2 , +3
 SegCtrlStruct    Acc_SCtrl_3 ;//Px0118-0120,Access  control segment 3 , +3 (+12)

 uint16_t    AccSegCtrlW ;//Px0121,Access control table entry number(=0,no access control)
 uint16_t        KeyMask ;//Px0122,Globle operation key mask code-used to mask out some key operation for tranmit operation ,if allowed 
 uint16_t    LeftKeyMask ;//Px0123,Left side mode operation key mask code
 uint16_t   RightKeyMask ;//Px0124,Right side mode operation key mask  code,+4

 uint16_t     CAN_Mode ;//Px0125,CAN bus interface working mode 
 uint16_t    CAN_Speed ;//Px0126,CAN bus working speed setting 125kbps,250kbps,500kbps,1Mbps
 uint16_t    CANOpenID ;//Px0127,CANOpen mode Node ID 0-127
 uint16_t        rev_w ;//Px0128, reserved(CAN bus output message object ID setting) ,+4	

 RfTickTokenCtrl_t  BiTickToken ;//Px0129-0140,Bidirection mode RF tick and token control data struct,+12
  
 RfTickTokenCtrl_t  UniTickToken ;//Px0141-0152,Unidirection mode RF tick and token control data struct,+12
  
 uint16_t     rev_wa[2] ;//Px0153-0154,
 uint16_t     EStopCode ;//Px0155,define the application specified emergence stop key code
 uint16_t         crc16 ;//Px0156,CRC-16 of this configuration object   +4
} SysConfigB_t  ;

//System application  configuration parameter group C--Main for 021 RF IC
typedef struct TagSysConfigC_t
{//Parameter ID=0x0043 ,LEN=86
 uint16_t               pid ;//Parameter's identifier
 uint16_t               len ;//configuration data length
 uint16_t         rev_wa[2] ;// ,+4

 IC021Registers   IC021Def  ;//default configuration of IC021 registers,15 dwords, +30

 IC021RxSwitchReg   Rx_Freq_Ch[6]  ;//Rx frequecy work channel 0-5 register setting,12 dwords, +24

 IC021TxSwitchReg   Tx_Freq_Ch[6]  ;//Tx frequecy work channel 0-5 register setting,12 dwords, +24

//-----------------
 uint16_t      uid_l ;//module UID low word16
 uint16_t      uid_h ;//module UID hight word16
 uint16_t      rev_w  ;
 uint16_t      crc16  ;//CRC-16 of this configuration object   +4
} SysConfigC_t  ;

//-----------------------------------------------------------------------------------------------
typedef struct TagSysConfigD_t  //Parameter ID=0x0044 ,LEN=48 main for JF01 IC
{ 
  uint16_t         pid ;//Px0101,Parameter's identifier
  uint16_t         len ;//Px0102,configuration data length
  uint16_t   rev_wa[2] ;// ,+4

  JF01Registers         JF01Def ;//default configuration of JF01 register ,+24

  JF01SwitchReg   Rx_Freq_Ch[6] ;//Rx frequecy work channel 0-5 register setting,+9 

  JF01SwitchReg   Tx_Freq_Ch[6] ;//Tx frequecy work channel 0-5 register setting,+9 
  uint16_t	      rev_w ;
  uint16_t        crc16  ;//Px0148,CRC-16 of this configuration object   +2
} SysConfigD_t ;
//-----------------------------------------------------------------------------------------------
extern  const  uint16_t  SysConfigADef[]	;//Default application parameter group A

extern  SysConfigA_t   APP_PA ;//System application parameter Group A, ID=0x0081

extern  SysConfigB_t   APP_PB ;//EDC module  parameter Group B, ID=0x0082

extern  SysConfigC_t  *APP_PC_p ;//System application parameter Group C pointer, ID=0x0083

extern  SysConfigD_t  *APP_PD_p ;//System application parameter Group D pointer, ID=0x0084

void  ModifyPara(uint16_t PGroup,uint16_t Opc) ;//Modify specified parameter group in EEPROM area

void  SysConfigInit(void)  ;

void  SysConfigDaemon(void)  ;

#endif  //__SYS_CONFIG_H

