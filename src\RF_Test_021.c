#include  "RF_Test_021.h"
//---------------
extern  tim_count  Rfc<PERSON>imer[RFC_TIMER_NUM]  ;//system ISR process

extern  tim_count  RfcRfcTimer10[RFC_TIMER10_NUM]  ;//user level timer(1 tick=10ms),RFC_TIMER10_NUM==2

extern  uint8_t   ShiftReg ;
extern  uint8_t   BitCounter;
extern  uint8_t   PreambleCount;
extern  uint8_t   PreambleError;
extern  uint8_t   ByteCounter;
extern  uint32_t  IC021Reg ;

extern  uint8_t   gain_correction[]; //For AGC and LAN 021
extern  uint32_t  TestReg;

extern  uint8_t   *test_p ;//pointer used to store current testoutput poistion of RX data
extern  volatile  uint16_t  RSSI_STA_L,RSSI_BufIndex_L;

//-----------------

extern  volatile  uint16_t   SYS_STA ;
extern  volatile   int16_t   TxToken ;
extern  volatile  uint32_t    OpCtrl ;
extern  volatile  uint32_t     GFlag ,GFlag_Pre ;
int16_t  ch_num ;

//------------RF Test Entry
void  RfTestEntry_021(void)
{
 Led_BL_off;//turn off LCD back light
 test_p=(uint8_t *)&U_Area.Ultra_BUF ;//pointer used to store current output poistion of RX data
 TestCtrlW=0x8000 ;
 SYS_STA=0x0000  ;
 Timer10[OPC_TIMER].pv=10 ;
 Timer10[OPC_TIMER].cv=0 ;
 Timer10[OPC_TIMER].csr.csword=0x4000 ; //start RfcTimer10[OPC]
 while(1)
 {
   TimerProc() ;//user defined timer proccess
   if(Timer10[OPC_TIMER].csr.csbit.q==1)
   {
     Timer10[OPC_TIMER].csr.csword=0x4000 ;
     Timer10[OPC_TIMER].cv=0 ;
     GetKeySta() ;
    }
   switch(SYS_STA)//SYS_STA to control Test process
   {
     case 0x0000://Wait user to select Test RX or TX
       if(U_Area.FallingKeyRaw.cw==SW5)//SW5 be pressed
       {//Be required to Enter TX test mode
         RfDs.IC021Init.OpReq=SET_UP_TX+SET_CODE ;//Request to do Tx setup operation
         RfDs.IC021Init.OpSta = NOT_INIT_STA ;//
         TestCtrlW=0xC000 ;
         Led_BL_on ;//Turn on LCD back light
         SYS_STA=0x0002 ;
 	    }
       else if(U_Area.FallingKeyRaw.cw==SW4)//SW4 be pressed
       {//Be required to Enter RX test mode
         RfDs.IC021Init.OpReq=SET_UP_RX+SET_CODE ;//Request to do Rx setup operation
         RfDs.IC021Init.OpSta = NOT_INIT_STA ;//
         TestCtrlW=0x8000 ;
         Led_BL_on ;//Turn on LCD back light
         SYS_STA=0x0004 ;
	    }
       break ;
     case 0x0002://Tx Test Mode
       if(GFlag&GF_BATV_REFRESH)
          U_Area.LcdCtrl.Set.Print_A15=1 ;//Request to Print current battery voltage
       if(U_Area.FallingKeyRaw.cw==SW11)//SW11 (keycode.12)
       {//Switch test frequecy band
		 ch_num++ ;
		 if(ch_num>5) ch_num=0 ;
         Rx_021_Ch_p=(IC021RxSwitchReg *)&(APP_PC_p->Rx_Freq_Ch[ch_num]) ;
         Tx_021_Ch_p=(IC021TxSwitchReg *)&(APP_PC_p->Tx_Freq_Ch[ch_num]) ;
         RfDs.IC021Init.OpSta=NOT_INIT_STA ;
         RfDs.RF_State=RF_IDLE_STATUS ;
         RfDs.IC021Init.OpReq=SET_UP_TX+SET_CODE ;
         U_Area.FallingKeyRaw.cw=0 ;
        }
       if(U_Area.FallingKeyRaw.cw==SW7)//SW7 be pressed
       {//Required to test FSK frequecy offset
         TestCtrlW=0xC100 ;
         WriteReg_021(&TestReg);//write Test register R15, to Output the carrier only
        }
       else if(U_Area.FallingKeyRaw.cw==SW8)//SW8 be pressed
       {
         TestCtrlW=0xC000 ;
         WriteReg_021(&APP_PC_p->IC021Def.R15_TEST);//write Test register R15, to normal mode
        }
        break ;
     case 0x0004://To do RF RX test
       if(U_Area.FallingKeyRaw.cw==SW11)//SW11 (keycode.12)
       {//Switch test frequecy band
		 ch_num++ ;
		 if(ch_num>5) ch_num=0 ;
         Rx_021_Ch_p=(IC021RxSwitchReg *)&(APP_PC_p->Rx_Freq_Ch[ch_num]) ;
         Tx_021_Ch_p=(IC021TxSwitchReg *)&(APP_PC_p->Tx_Freq_Ch[ch_num]) ;
         RfDs.IC021Init.OpSta=NOT_INIT_STA ;
         RfDs.RF_State=RF_IDLE_STATUS ;
         RfDs.IC021Init.OpReq=SET_UP_RX+SET_CODE ;
         U_Area.FallingKeyRaw.cw=0 ;
        }
       if((GFlag_Pre^GFlag)&GF_TEST_RX_OK)
       {
         if(GFlag&GF_TEST_RX_OK)
         { //Set in sync status code 
           U_Area.uc_str[1][0]=0x1208 ;
           U_Area.uc_str[1][1]=0x1308 ;
		   Led_4_on ;
          }
         else
         { //Set out of sync status code
           U_Area.uc_str[1][0]=0x1408 ;
           U_Area.uc_str[1][1]=0x1508 ;
		   Led_4_off ;
          }
         U_Area.LcdCtrl.Set.Print_A1=1 ;//Draw sync status indicator
        }
       GFlag_Pre=GFlag ;
       break ;
     default :
     SYS_STA=0 ;
    }
   ADC12Daemon() ;//process ADC operation request
   RfDaemon_021() ;
   RSSI_Test_Filter_021()  ;
   Shr_GetKeyID()  ;//Get Key ID for display
   LcdModuleInitDaemon();//process LCD module initialization request
   LcdPrintDaemon();//process LCD print(display) request
   U_Area.FallingKeyRaw.cw=0x0000 ;
  }
}
//--------------------------------------
void  RSSI_Test_Filter_021(void)
{
 int16_t i,acc ;
 switch(RSSI_STA_L)
 {
  case 0x0000 :

    break ;
  case 0x0002 :
    if(RssiCheckFlag)
    {
	  ClearRssiCheckFlag ;
	  RfDs.RSSI_D=Read_RSSI_021() ;
      RfDs.RSSI_BUF[RSSI_BufIndex_L++]=RfDs.RSSI_D;
     if(RSSI_BufIndex_L>=8)
     {
      RSSI_BufIndex_L=0 ;
      RSSI_STA_L=0x0004 ;
      }
    }
    break ;
  case 0x0004 :
    acc=0 ;
    for(i=0 ;i<8 ;i++)
    {
     acc+=RfDs.RSSI_BUF[i] ;
     }
    RfDs.RSSI=(acc>>3) ;
    RfDs.RSSI+=APP_PA.RSSIOffset ;
	//-------
    GFlag|=GF_RSSI_REFRESH ;
    U_Area.DisplayDatBuf[25]=RfDs.RSSI ;//For debug
    U_Area.LcdCtrl.Set.Print_A14=1 ;//Request to Print current RSSI
	//-------
    RSSI_STA_L=0x0002 ;
    break ;
  default :
    RSSI_STA_L=0x0000 ;
 }
}

//-------
