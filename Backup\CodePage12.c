/*W  38108_Pixcp.c    LCDIcon FileDescriptor: Do not edit or move */
#include  "Display.h"

const G<PERSON><PERSON>PAGE12 CodePage12={
{427,0x0055},{ /*  First number is number of ranges, last number is default Character F.x. ASCII space  */
{ 0x000A, 0x000A, 0x0000 },/* Segment 0, 0x0001 Symbols */
{ 0x000D, 0x000D, 0x0001 },/* Segment 1, 0x0001 Symbols */
{ 0x0020, 0x0020, 0x0002 },/* Segment 2, 0x0001 Symbols */
{ 0x0025, 0x0025, 0x0003 },/* Segment 3, 0x0001 Symbols */
{ 0x002B, 0x002B, 0x0004 },/* Segment 4, 0x0001 Symbols */
{ 0x002D, 0x003A, 0x0005 },/* Segment 5, 0x000E Symbols */
{ 0x0041, 0x005B, 0x0013 },/* Segment 6, 0x001B Symbols */
{ 0x005D, 0x005D, 0x002E },/* Segment 7, 0x0001 Symbols */
{ 0x005F, 0x005F, 0x002F },/* Segment 8, 0x0001 Symbols */
{ 0x0061, 0x007A, 0x0030 },/* Segment 9, 0x001A Symbols */
{ 0x03A6, 0x03A6, 0x004A },/* Segment 10, 0x0001 Symbols */
{ 0x03A9, 0x03A9, 0x004B },/* Segment 11, 0x0001 Symbols */
{ 0x03BC, 0x03BC, 0x004C },/* Segment 12, 0x0001 Symbols */
{ 0x0800, 0x083F, 0x004D },/* Segment 13, 0x0040 Symbols */
{ 0x2014, 0x2014, 0x008D },/* Segment 14, 0x0001 Symbols */
{ 0x2018, 0x2019, 0x008E },/* Segment 15, 0x0002 Symbols */
{ 0x201C, 0x201D, 0x0090 },/* Segment 16, 0x0002 Symbols */
{ 0x2026, 0x2026, 0x0092 },/* Segment 17, 0x0001 Symbols */
{ 0x2030, 0x2030, 0x0093 },/* Segment 18, 0x0001 Symbols */
{ 0x2103, 0x2103, 0x0094 },/* Segment 19, 0x0001 Symbols */
{ 0x2116, 0x2116, 0x0095 },/* Segment 20, 0x0001 Symbols */
{ 0x2605, 0x2605, 0x0096 },/* Segment 21, 0x0001 Symbols */
{ 0x3001, 0x3002, 0x0097 },/* Segment 22, 0x0002 Symbols */
{ 0x4E00, 0x4E00, 0x0099 },/* Segment 23, 0x0001 Symbols */
{ 0x4E07, 0x4E07, 0x009A },/* Segment 24, 0x0001 Symbols */
{ 0x4E09, 0x4E0B, 0x009B },/* Segment 25, 0x0003 Symbols */
{ 0x4E0E, 0x4E0E, 0x009E },/* Segment 26, 0x0001 Symbols */
{ 0x4E13, 0x4E13, 0x009F },/* Segment 27, 0x0001 Symbols */
{ 0x4E22, 0x4E22, 0x00A0 },/* Segment 28, 0x0001 Symbols */
{ 0x4E24, 0x4E25, 0x00A1 },/* Segment 29, 0x0002 Symbols */
{ 0x4E2D, 0x4E2D, 0x00A3 },/* Segment 30, 0x0001 Symbols */
{ 0x4E3B, 0x4E3B, 0x00A4 },/* Segment 31, 0x0001 Symbols */
{ 0x4E45, 0x4E45, 0x00A5 },/* Segment 32, 0x0001 Symbols */
{ 0x4E66, 0x4E66, 0x00A6 },/* Segment 33, 0x0001 Symbols */
{ 0x4E8C, 0x4E8C, 0x00A7 },/* Segment 34, 0x0001 Symbols */
{ 0x4EBA, 0x4EBA, 0x00A8 },/* Segment 35, 0x0001 Symbols */
{ 0x4ED8, 0x4ED8, 0x00A9 },/* Segment 36, 0x0001 Symbols */
{ 0x4EE4, 0x4EE4, 0x00AA },/* Segment 37, 0x0001 Symbols */
{ 0x4EEA, 0x4EEA, 0x00AB },/* Segment 38, 0x0001 Symbols */
{ 0x4EF0, 0x4EF0, 0x00AC },/* Segment 39, 0x0001 Symbols */
{ 0x4EF6, 0x4EF6, 0x00AD },/* Segment 40, 0x0001 Symbols */
{ 0x4F0F, 0x4F0F, 0x00AE },/* Segment 41, 0x0001 Symbols */
{ 0x4F11, 0x4F11, 0x00AF },/* Segment 42, 0x0001 Symbols */
{ 0x4F20, 0x4F20, 0x00B0 },/* Segment 43, 0x0001 Symbols */
{ 0x4F38, 0x4F38, 0x00B1 },/* Segment 44, 0x0001 Symbols */
{ 0x4F4D, 0x4F4E, 0x00B2 },/* Segment 45, 0x0002 Symbols */
{ 0x4F53, 0x4F53, 0x00B4 },/* Segment 46, 0x0001 Symbols */
{ 0x4F5C, 0x4F5C, 0x00B5 },/* Segment 47, 0x0001 Symbols */
{ 0x4F7F, 0x4F7F, 0x00B6 },/* Segment 48, 0x0001 Symbols */
{ 0x4F9B, 0x4F9B, 0x00B7 },/* Segment 49, 0x0001 Symbols */
{ 0x4FA7, 0x4FA7, 0x00B8 },/* Segment 50, 0x0001 Symbols */
{ 0x4FDD, 0x4FDD, 0x00B9 },/* Segment 51, 0x0001 Symbols */
{ 0x4FE1, 0x4FE1, 0x00BA },/* Segment 52, 0x0001 Symbols */
{ 0x4FEE, 0x4FEF, 0x00BB },/* Segment 53, 0x0002 Symbols */
{ 0x503C, 0x503C, 0x00BD },/* Segment 54, 0x0001 Symbols */
{ 0x503E, 0x503E, 0x00BE },/* Segment 55, 0x0001 Symbols */
{ 0x504F, 0x504F, 0x00BF },/* Segment 56, 0x0001 Symbols */
{ 0x505C, 0x505C, 0x00C0 },/* Segment 57, 0x0001 Symbols */
{ 0x5085, 0x5085, 0x00C1 },/* Segment 58, 0x0001 Symbols */
{ 0x5145, 0x5146, 0x00C2 },/* Segment 59, 0x0002 Symbols */
{ 0x5149, 0x5149, 0x00C4 },/* Segment 60, 0x0001 Symbols */
{ 0x5165, 0x5165, 0x00C5 },/* Segment 61, 0x0001 Symbols */
{ 0x5168, 0x5168, 0x00C6 },/* Segment 62, 0x0001 Symbols */
{ 0x516C, 0x516C, 0x00C7 },/* Segment 63, 0x0001 Symbols */
{ 0x5173, 0x5173, 0x00C8 },/* Segment 64, 0x0001 Symbols */
{ 0x5185, 0x5185, 0x00C9 },/* Segment 65, 0x0001 Symbols */
{ 0x518C, 0x518C, 0x00CA },/* Segment 66, 0x0001 Symbols */
{ 0x51B5, 0x51B5, 0x00CB },/* Segment 67, 0x0001 Symbols */
{ 0x51B7, 0x51B7, 0x00CC },/* Segment 68, 0x0001 Symbols */
{ 0x51CF, 0x51CF, 0x00CD },/* Segment 69, 0x0001 Symbols */
{ 0x51FA, 0x51FA, 0x00CE },/* Segment 70, 0x0001 Symbols */
{ 0x5200, 0x5200, 0x00CF },/* Segment 71, 0x0001 Symbols */
{ 0x5206, 0x5207, 0x00D0 },/* Segment 72, 0x0002 Symbols */
{ 0x521B, 0x521B, 0x00D2 },/* Segment 73, 0x0001 Symbols */
{ 0x5229, 0x5229, 0x00D3 },/* Segment 74, 0x0001 Symbols */
{ 0x5230, 0x5230, 0x00D4 },/* Segment 75, 0x0001 Symbols */
{ 0x5236, 0x5236, 0x00D5 },/* Segment 76, 0x0001 Symbols */
{ 0x524D, 0x524D, 0x00D6 },/* Segment 77, 0x0001 Symbols */
{ 0x5272, 0x5272, 0x00D7 },/* Segment 78, 0x0001 Symbols */
{ 0x529B, 0x529B, 0x00D8 },/* Segment 79, 0x0001 Symbols */
{ 0x529F, 0x52A0, 0x00D9 },/* Segment 80, 0x0002 Symbols */
{ 0x52A8, 0x52A9, 0x00DB },/* Segment 81, 0x0002 Symbols */
{ 0x52D2, 0x52D2, 0x00DD },/* Segment 82, 0x0001 Symbols */
{ 0x5305, 0x5305, 0x00DE },/* Segment 83, 0x0001 Symbols */
{ 0x5316, 0x5316, 0x00DF },/* Segment 84, 0x0001 Symbols */
{ 0x533A, 0x533A, 0x00E0 },/* Segment 85, 0x0001 Symbols */
{ 0x5343, 0x5343, 0x00E1 },/* Segment 86, 0x0001 Symbols */
{ 0x5347, 0x5347, 0x00E2 },/* Segment 87, 0x0001 Symbols */
{ 0x5355, 0x5355, 0x00E3 },/* Segment 88, 0x0001 Symbols */
{ 0x5371, 0x5371, 0x00E4 },/* Segment 89, 0x0001 Symbols */
{ 0x5373, 0x5374, 0x00E5 },/* Segment 90, 0x0002 Symbols */
{ 0x538B, 0x538B, 0x00E7 },/* Segment 91, 0x0001 Symbols */
{ 0x5398, 0x5398, 0x00E8 },/* Segment 92, 0x0001 Symbols */
{ 0x53C2, 0x53C2, 0x00E9 },/* Segment 93, 0x0001 Symbols */
{ 0x53CC, 0x53CD, 0x00EA },/* Segment 94, 0x0002 Symbols */
{ 0x53D1, 0x53D1, 0x00EC },/* Segment 95, 0x0001 Symbols */
{ 0x53D6, 0x53D6, 0x00ED },/* Segment 96, 0x0001 Symbols */
{ 0x53D8, 0x53D8, 0x00EE },/* Segment 97, 0x0001 Symbols */
{ 0x53E6, 0x53E6, 0x00EF },/* Segment 98, 0x0001 Symbols */
{ 0x53EF, 0x53F0, 0x00F0 },/* Segment 99, 0x0002 Symbols */
{ 0x53F3, 0x53F3, 0x00F2 },/* Segment 100, 0x0001 Symbols */
{ 0x53F7, 0x53F8, 0x00F3 },/* Segment 101, 0x0002 Symbols */
{ 0x540C, 0x540C, 0x00F5 },/* Segment 102, 0x0001 Symbols */
{ 0x540E, 0x540E, 0x00F6 },/* Segment 103, 0x0001 Symbols */
{ 0x5411, 0x5411, 0x00F7 },/* Segment 104, 0x0001 Symbols */
{ 0x542F, 0x542F, 0x00F8 },/* Segment 105, 0x0001 Symbols */
{ 0x544A, 0x544A, 0x00F9 },/* Segment 106, 0x0001 Symbols */
{ 0x547D, 0x547D, 0x00FA },/* Segment 107, 0x0001 Symbols */
{ 0x552E, 0x552E, 0x00FB },/* Segment 108, 0x0001 Symbols */
{ 0x5668, 0x5668, 0x00FC },/* Segment 109, 0x0001 Symbols */
{ 0x56DE, 0x56DE, 0x00FD },/* Segment 110, 0x0001 Symbols */
{ 0x56E0, 0x56E0, 0x00FE },/* Segment 111, 0x0001 Symbols */
{ 0x56E2, 0x56E2, 0x00FF },/* Segment 112, 0x0001 Symbols */
{ 0x56FA, 0x56FA, 0x0100 },/* Segment 113, 0x0001 Symbols */
{ 0x56FD, 0x56FD, 0x0101 },/* Segment 114, 0x0001 Symbols */
{ 0x5728, 0x5728, 0x0102 },/* Segment 115, 0x0001 Symbols */
{ 0x5730, 0x5730, 0x0103 },/* Segment 116, 0x0001 Symbols */
{ 0x5740, 0x5740, 0x0104 },/* Segment 117, 0x0001 Symbols */
{ 0x574F, 0x574F, 0x0105 },/* Segment 118, 0x0001 Symbols */
{ 0x5782, 0x5782, 0x0106 },/* Segment 119, 0x0001 Symbols */
{ 0x578B, 0x578B, 0x0107 },/* Segment 120, 0x0001 Symbols */
{ 0x57DF, 0x57DF, 0x0108 },/* Segment 121, 0x0001 Symbols */
{ 0x57F9, 0x57F9, 0x0109 },/* Segment 122, 0x0001 Symbols */
{ 0x5904, 0x5904, 0x010A },/* Segment 123, 0x0001 Symbols */
{ 0x5907, 0x5907, 0x010B },/* Segment 124, 0x0001 Symbols */
{ 0x590D, 0x590D, 0x010C },/* Segment 125, 0x0001 Symbols */
{ 0x591A, 0x591A, 0x010D },/* Segment 126, 0x0001 Symbols */
{ 0x5927, 0x5927, 0x010E },/* Segment 127, 0x0001 Symbols */
{ 0x5929, 0x592A, 0x010F },/* Segment 128, 0x0002 Symbols */
{ 0x592E, 0x592E, 0x0111 },/* Segment 129, 0x0001 Symbols */
{ 0x5931, 0x5931, 0x0112 },/* Segment 130, 0x0001 Symbols */
{ 0x5934, 0x5934, 0x0113 },/* Segment 131, 0x0001 Symbols */
{ 0x59C6, 0x59C6, 0x0114 },/* Segment 132, 0x0001 Symbols */
{ 0x5B50, 0x5B50, 0x0115 },/* Segment 133, 0x0001 Symbols */
{ 0x5B57, 0x5B58, 0x0116 },/* Segment 134, 0x0002 Symbols */
{ 0x5B89, 0x5B89, 0x0118 },/* Segment 135, 0x0001 Symbols */
{ 0x5B9A, 0x5B9A, 0x0119 },/* Segment 136, 0x0001 Symbols */
{ 0x5B9E, 0x5B9E, 0x011A },/* Segment 137, 0x0001 Symbols */
{ 0x5BA4, 0x5BA4, 0x011B },/* Segment 138, 0x0001 Symbols */
{ 0x5BB6, 0x5BB6, 0x011C },/* Segment 139, 0x0001 Symbols */
{ 0x5BC6, 0x5BC6, 0x011D },/* Segment 140, 0x0001 Symbols */
{ 0x5C04, 0x5C04, 0x011E },/* Segment 141, 0x0001 Symbols */
{ 0x5C06, 0x5C06, 0x011F },/* Segment 142, 0x0001 Symbols */
{ 0x5C0F, 0x5C0F, 0x0120 },/* Segment 143, 0x0001 Symbols */
{ 0x5C3D, 0x5C3D, 0x0121 },/* Segment 144, 0x0001 Symbols */
{ 0x5C4F, 0x5C4F, 0x0122 },/* Segment 145, 0x0001 Symbols */
{ 0x5C55, 0x5C55, 0x0123 },/* Segment 146, 0x0001 Symbols */
{ 0x5C65, 0x5C65, 0x0124 },/* Segment 147, 0x0001 Symbols */
{ 0x5C71, 0x5C71, 0x0125 },/* Segment 148, 0x0001 Symbols */
{ 0x5CA9, 0x5CA9, 0x0126 },/* Segment 149, 0x0001 Symbols */
{ 0x5D4C, 0x5D4C, 0x0127 },/* Segment 150, 0x0001 Symbols */
{ 0x5DE5, 0x5DE6, 0x0128 },/* Segment 151, 0x0002 Symbols */
{ 0x5DF7, 0x5DF7, 0x012A },/* Segment 152, 0x0001 Symbols */
{ 0x5E02, 0x5E02, 0x012B },/* Segment 153, 0x0001 Symbols */
{ 0x5E08, 0x5E08, 0x012C },/* Segment 154, 0x0001 Symbols */
{ 0x5E26, 0x5E26, 0x012D },/* Segment 155, 0x0001 Symbols */
{ 0x5E2E, 0x5E2E, 0x012E },/* Segment 156, 0x0001 Symbols */
{ 0x5E38, 0x5E38, 0x012F },/* Segment 157, 0x0001 Symbols */
{ 0x5E73, 0x5E73, 0x0130 },/* Segment 158, 0x0001 Symbols */
{ 0x5E8F, 0x5E8F, 0x0131 },/* Segment 159, 0x0001 Symbols */
{ 0x5E94, 0x5E94, 0x0132 },/* Segment 160, 0x0001 Symbols */
{ 0x5EA6, 0x5EA6, 0x0133 },/* Segment 161, 0x0001 Symbols */
{ 0x5EF6, 0x5EF6, 0x0134 },/* Segment 162, 0x0001 Symbols */
{ 0x5F00, 0x5F00, 0x0135 },/* Segment 163, 0x0001 Symbols */
{ 0x5F02, 0x5F04, 0x0136 },/* Segment 164, 0x0003 Symbols */
{ 0x5F0F, 0x5F0F, 0x0139 },/* Segment 165, 0x0001 Symbols */
{ 0x5F15, 0x5F15, 0x013A },/* Segment 166, 0x0001 Symbols */
{ 0x5F31, 0x5F31, 0x013B },/* Segment 167, 0x0001 Symbols */
{ 0x5F3A, 0x5F3A, 0x013C },/* Segment 168, 0x0001 Symbols */
{ 0x5F53, 0x5F53, 0x013D },/* Segment 169, 0x0001 Symbols */
{ 0x5F55, 0x5F55, 0x013E },/* Segment 170, 0x0001 Symbols */
{ 0x5F85, 0x5F85, 0x013F },/* Segment 171, 0x0001 Symbols */
{ 0x5F8B, 0x5F8B, 0x0140 },/* Segment 172, 0x0001 Symbols */
{ 0x5FAE, 0x5FAE, 0x0141 },/* Segment 173, 0x0001 Symbols */
{ 0x5FC6, 0x5FC6, 0x0142 },/* Segment 174, 0x0001 Symbols */
{ 0x5FEB, 0x5FEB, 0x0143 },/* Segment 175, 0x0001 Symbols */
{ 0x6001, 0x6001, 0x0144 },/* Segment 176, 0x0001 Symbols */
{ 0x6025, 0x6025, 0x0145 },/* Segment 177, 0x0001 Symbols */
{ 0x603B, 0x603B, 0x0146 },/* Segment 178, 0x0001 Symbols */
{ 0x60AC, 0x60AC, 0x0147 },/* Segment 179, 0x0001 Symbols */
{ 0x610F, 0x610F, 0x0148 },/* Segment 180, 0x0001 Symbols */
{ 0x611F, 0x611F, 0x0149 },/* Segment 181, 0x0001 Symbols */
{ 0x622A, 0x622A, 0x014A },/* Segment 182, 0x0001 Symbols */
{ 0x624B, 0x624B, 0x014B },/* Segment 183, 0x0001 Symbols */
{ 0x6269, 0x6269, 0x014C },/* Segment 184, 0x0001 Symbols */
{ 0x627E, 0x6280, 0x014D },/* Segment 185, 0x0003 Symbols */
{ 0x62A4, 0x62A5, 0x0150 },/* Segment 186, 0x0002 Symbols */
{ 0x62C9, 0x62C9, 0x0152 },/* Segment 187, 0x0001 Symbols */
{ 0x62E9, 0x62E9, 0x0153 },/* Segment 188, 0x0001 Symbols */
{ 0x6307, 0x6307, 0x0154 },/* Segment 189, 0x0001 Symbols */
{ 0x6309, 0x6309, 0x0155 },/* Segment 190, 0x0001 Symbols */
{ 0x6321, 0x6321, 0x0156 },/* Segment 191, 0x0001 Symbols */
{ 0x6362, 0x6362, 0x0157 },/* Segment 192, 0x0001 Symbols */
{ 0x636E, 0x636E, 0x0158 },/* Segment 193, 0x0001 Symbols */
{ 0x6377, 0x6377, 0x0159 },/* Segment 194, 0x0001 Symbols */
{ 0x6398, 0x6398, 0x015A },/* Segment 195, 0x0001 Symbols */
{ 0x63A5, 0x63A5, 0x015B },/* Segment 196, 0x0001 Symbols */
{ 0x63A7, 0x63A7, 0x015C },/* Segment 197, 0x0001 Symbols */
{ 0x63D0, 0x63D0, 0x015D },/* Segment 198, 0x0001 Symbols */
{ 0x6446, 0x6447, 0x015E },/* Segment 199, 0x0002 Symbols */
{ 0x6491, 0x6491, 0x0160 },/* Segment 200, 0x0001 Symbols */
{ 0x64CD, 0x64CD, 0x0161 },/* Segment 201, 0x0001 Symbols */
{ 0x652F, 0x652F, 0x0162 },/* Segment 202, 0x0001 Symbols */
{ 0x6536, 0x6536, 0x0163 },/* Segment 203, 0x0001 Symbols */
{ 0x6539, 0x6539, 0x0164 },/* Segment 204, 0x0001 Symbols */
{ 0x653E, 0x653E, 0x0165 },/* Segment 205, 0x0001 Symbols */
{ 0x6545, 0x6545, 0x0166 },/* Segment 206, 0x0001 Symbols */
{ 0x6548, 0x6548, 0x0167 },/* Segment 207, 0x0001 Symbols */
{ 0x654F, 0x654F, 0x0168 },/* Segment 208, 0x0001 Symbols */
{ 0x6570, 0x6570, 0x0169 },/* Segment 209, 0x0001 Symbols */
{ 0x65A4, 0x65A4, 0x016A },/* Segment 210, 0x0001 Symbols */
{ 0x65AD, 0x65AD, 0x016B },/* Segment 211, 0x0001 Symbols */
{ 0x65AF, 0x65AF, 0x016C },/* Segment 212, 0x0001 Symbols */
{ 0x65B9, 0x65B9, 0x016D },/* Segment 213, 0x0001 Symbols */
{ 0x65CB, 0x65CB, 0x016E },/* Segment 214, 0x0001 Symbols */
{ 0x65E0, 0x65E0, 0x016F },/* Segment 215, 0x0001 Symbols */
{ 0x65E5, 0x65E5, 0x0170 },/* Segment 216, 0x0001 Symbols */
{ 0x65F6, 0x65F6, 0x0171 },/* Segment 217, 0x0001 Symbols */
{ 0x6606, 0x6606, 0x0172 },/* Segment 218, 0x0001 Symbols */
{ 0x660E, 0x660E, 0x0173 },/* Segment 219, 0x0001 Symbols */
{ 0x661F, 0x661F, 0x0174 },/* Segment 220, 0x0001 Symbols */
{ 0x663E, 0x663E, 0x0175 },/* Segment 221, 0x0001 Symbols */
{ 0x666E, 0x666E, 0x0176 },/* Segment 222, 0x0001 Symbols */
{ 0x6676, 0x6676, 0x0177 },/* Segment 223, 0x0001 Symbols */
{ 0x667A, 0x667A, 0x0178 },/* Segment 224, 0x0001 Symbols */
{ 0x66F4, 0x66F4, 0x0179 },/* Segment 225, 0x0001 Symbols */
{ 0x6709, 0x6709, 0x017A },/* Segment 226, 0x0001 Symbols */
{ 0x671F, 0x671F, 0x017B },/* Segment 227, 0x0001 Symbols */
{ 0x672A, 0x672A, 0x017C },/* Segment 228, 0x0001 Symbols */
{ 0x672F, 0x672F, 0x017D },/* Segment 229, 0x0001 Symbols */
{ 0x673A, 0x673A, 0x017E },/* Segment 230, 0x0001 Symbols */
{ 0x6746, 0x6746, 0x017F },/* Segment 231, 0x0001 Symbols */
{ 0x6761, 0x6761, 0x0180 },/* Segment 232, 0x0001 Symbols */
{ 0x6765, 0x6765, 0x0181 },/* Segment 233, 0x0001 Symbols */
{ 0x677F, 0x677F, 0x0182 },/* Segment 234, 0x0001 Symbols */
{ 0x67B6, 0x67B6, 0x0183 },/* Segment 235, 0x0001 Symbols */
{ 0x67E5, 0x67E5, 0x0184 },/* Segment 236, 0x0001 Symbols */
{ 0x68C0, 0x68C0, 0x0185 },/* Segment 237, 0x0001 Symbols */
{ 0x69FD, 0x69FD, 0x0186 },/* Segment 238, 0x0001 Symbols */
{ 0x6A21, 0x6A21, 0x0187 },/* Segment 239, 0x0001 Symbols */
{ 0x6A2A, 0x6A2A, 0x0188 },/* Segment 240, 0x0001 Symbols */
{ 0x6B22, 0x6B22, 0x0189 },/* Segment 241, 0x0001 Symbols */
{ 0x6B27, 0x6B27, 0x018A },/* Segment 242, 0x0001 Symbols */
{ 0x6B3E, 0x6B3E, 0x018B },/* Segment 243, 0x0001 Symbols */
{ 0x6B62, 0x6B63, 0x018C },/* Segment 244, 0x0002 Symbols */
{ 0x6B65, 0x6B65, 0x018E },/* Segment 245, 0x0001 Symbols */
{ 0x6BB5, 0x6BB5, 0x018F },/* Segment 246, 0x0001 Symbols */
{ 0x6BCF, 0x6BCF, 0x0190 },/* Segment 247, 0x0001 Symbols */
{ 0x6C14, 0x6C14, 0x0191 },/* Segment 248, 0x0001 Symbols */
{ 0x6C34, 0x6C34, 0x0192 },/* Segment 249, 0x0001 Symbols */
{ 0x6C38, 0x6C38, 0x0193 },/* Segment 250, 0x0001 Symbols */
{ 0x6C60, 0x6C60, 0x0194 },/* Segment 251, 0x0001 Symbols */
{ 0x6CB9, 0x6CB9, 0x0195 },/* Segment 252, 0x0001 Symbols */
{ 0x6CD5, 0x6CD5, 0x0196 },/* Segment 253, 0x0001 Symbols */
{ 0x6CE8, 0x6CE8, 0x0197 },/* Segment 254, 0x0001 Symbols */
{ 0x6CF5, 0x6CF5, 0x0198 },/* Segment 255, 0x0001 Symbols */
{ 0x6D3B, 0x6D3B, 0x0199 },/* Segment 256, 0x0001 Symbols */
{ 0x6D41, 0x6D41, 0x019A },/* Segment 257, 0x0001 Symbols */
{ 0x6D4B, 0x6D4B, 0x019B },/* Segment 258, 0x0001 Symbols */
{ 0x6D53, 0x6D53, 0x019C },/* Segment 259, 0x0001 Symbols */
{ 0x6D77, 0x6D77, 0x019D },/* Segment 260, 0x0001 Symbols */
{ 0x6D88, 0x6D88, 0x019E },/* Segment 261, 0x0001 Symbols */
{ 0x6DB2, 0x6DB2, 0x019F },/* Segment 262, 0x0001 Symbols */
{ 0x6E05, 0x6E05, 0x01A0 },/* Segment 263, 0x0001 Symbols */
{ 0x6E29, 0x6E29, 0x01A1 },/* Segment 264, 0x0001 Symbols */
{ 0x6E7F, 0x6E7F, 0x01A2 },/* Segment 265, 0x0001 Symbols */
{ 0x6E90, 0x6E90, 0x01A3 },/* Segment 266, 0x0001 Symbols */
{ 0x6ED1, 0x6ED1, 0x01A4 },/* Segment 267, 0x0001 Symbols */
{ 0x6EDA, 0x6EDA, 0x01A5 },/* Segment 268, 0x0001 Symbols */
{ 0x6EE4, 0x6EE4, 0x01A6 },/* Segment 269, 0x0001 Symbols */
{ 0x6F0F, 0x6F0F, 0x01A7 },/* Segment 270, 0x0001 Symbols */
{ 0x6F14, 0x6F14, 0x01A8 },/* Segment 271, 0x0001 Symbols */
{ 0x706F, 0x706F, 0x01A9 },/* Segment 272, 0x0001 Symbols */
{ 0x70ED, 0x70ED, 0x01AA },/* Segment 273, 0x0001 Symbols */
{ 0x7164, 0x7164, 0x01AB },/* Segment 274, 0x0001 Symbols */
{ 0x7269, 0x7269, 0x01AC },/* Segment 275, 0x0001 Symbols */
{ 0x7275, 0x7275, 0x01AD },/* Segment 276, 0x0001 Symbols */
{ 0x7279, 0x7279, 0x01AE },/* Segment 277, 0x0001 Symbols */
{ 0x72B6, 0x72B6, 0x01AF },/* Segment 278, 0x0001 Symbols */
{ 0x72C4, 0x72C4, 0x01B0 },/* Segment 279, 0x0001 Symbols */
{ 0x7387, 0x7387, 0x01B1 },/* Segment 280, 0x0001 Symbols */
{ 0x7406, 0x7406, 0x01B2 },/* Segment 281, 0x0001 Symbols */
{ 0x74E6, 0x74E6, 0x01B3 },/* Segment 282, 0x0001 Symbols */
{ 0x7528, 0x7528, 0x01B4 },/* Segment 283, 0x0001 Symbols */
{ 0x7535, 0x7535, 0x01B5 },/* Segment 284, 0x0001 Symbols */
{ 0x754C, 0x754C, 0x01B6 },/* Segment 285, 0x0001 Symbols */
{ 0x7559, 0x7559, 0x01B7 },/* Segment 286, 0x0001 Symbols */
{ 0x767E, 0x767E, 0x01B8 },/* Segment 287, 0x0001 Symbols */
{ 0x76D1, 0x76D1, 0x01B9 },/* Segment 288, 0x0001 Symbols */
{ 0x76D8, 0x76D8, 0x01BA },/* Segment 289, 0x0001 Symbols */
{ 0x76F4, 0x76F4, 0x01BB },/* Segment 290, 0x0001 Symbols */
{ 0x76F8, 0x76F8, 0x01BC },/* Segment 291, 0x0001 Symbols */
{ 0x7701, 0x7701, 0x01BD },/* Segment 292, 0x0001 Symbols */
{ 0x771F, 0x7720, 0x01BE },/* Segment 293, 0x0002 Symbols */
{ 0x77ED, 0x77ED, 0x01C0 },/* Segment 294, 0x0001 Symbols */
{ 0x77FF, 0x77FF, 0x01C1 },/* Segment 295, 0x0001 Symbols */
{ 0x7801, 0x7801, 0x01C2 },/* Segment 296, 0x0001 Symbols */
{ 0x7834, 0x7834, 0x01C3 },/* Segment 297, 0x0001 Symbols */
{ 0x786C, 0x786C, 0x01C4 },/* Segment 298, 0x0001 Symbols */
{ 0x786E, 0x786E, 0x01C5 },/* Segment 299, 0x0001 Symbols */
{ 0x788D, 0x788E, 0x01C6 },/* Segment 300, 0x0002 Symbols */
{ 0x793A, 0x793A, 0x01C8 },/* Segment 301, 0x0001 Symbols */
{ 0x79BB, 0x79BB, 0x01C9 },/* Segment 302, 0x0001 Symbols */
{ 0x79D2, 0x79D2, 0x01CA },/* Segment 303, 0x0001 Symbols */
{ 0x79FB, 0x79FB, 0x01CB },/* Segment 304, 0x0001 Symbols */
{ 0x7A0B, 0x7A0B, 0x01CC },/* Segment 305, 0x0001 Symbols */
{ 0x7A7A, 0x7A7A, 0x01CD },/* Segment 306, 0x0001 Symbols */
{ 0x7ACB, 0x7ACB, 0x01CE },/* Segment 307, 0x0001 Symbols */
{ 0x7AD9, 0x7AD9, 0x01CF },/* Segment 308, 0x0001 Symbols */
{ 0x7AEF, 0x7AEF, 0x01D0 },/* Segment 309, 0x0001 Symbols */
{ 0x7B52, 0x7B52, 0x01D1 },/* Segment 310, 0x0001 Symbols */
{ 0x7B97, 0x7B97, 0x01D2 },/* Segment 311, 0x0001 Symbols */
{ 0x7BB1, 0x7BB1, 0x01D3 },/* Segment 312, 0x0001 Symbols */
{ 0x7C73, 0x7C73, 0x01D4 },/* Segment 313, 0x0001 Symbols */
{ 0x7CBE, 0x7CBE, 0x01D5 },/* Segment 314, 0x0001 Symbols */
{ 0x7CFB, 0x7CFB, 0x01D6 },/* Segment 315, 0x0001 Symbols */
{ 0x7EA4, 0x7EA4, 0x01D7 },/* Segment 316, 0x0001 Symbols */
{ 0x7EB5, 0x7EB5, 0x01D8 },/* Segment 317, 0x0001 Symbols */
{ 0x7EBF, 0x7EBF, 0x01D9 },/* Segment 318, 0x0001 Symbols */
{ 0x7EDC, 0x7EDC, 0x01DA },/* Segment 319, 0x0001 Symbols */
{ 0x7EDF, 0x7EDF, 0x01DB },/* Segment 320, 0x0001 Symbols */
{ 0x7EED, 0x7EED, 0x01DC },/* Segment 321, 0x0001 Symbols */
{ 0x7EF4, 0x7EF4, 0x01DD },/* Segment 322, 0x0001 Symbols */
{ 0x7F06, 0x7F06, 0x01DE },/* Segment 323, 0x0001 Symbols */
{ 0x7F29, 0x7F29, 0x01DF },/* Segment 324, 0x0001 Symbols */
{ 0x7F51, 0x7F51, 0x01E0 },/* Segment 325, 0x0001 Symbols */
{ 0x7F6E, 0x7F6E, 0x01E1 },/* Segment 326, 0x0001 Symbols */
{ 0x8003, 0x8003, 0x01E2 },/* Segment 327, 0x0001 Symbols */
{ 0x8017, 0x8017, 0x01E3 },/* Segment 328, 0x0001 Symbols */
{ 0x8054, 0x8054, 0x01E4 },/* Segment 329, 0x0001 Symbols */
{ 0x80CC, 0x80CC, 0x01E5 },/* Segment 330, 0x0001 Symbols */
{ 0x80FD, 0x80FD, 0x01E6 },/* Segment 331, 0x0001 Symbols */
{ 0x8111, 0x8111, 0x01E7 },/* Segment 332, 0x0001 Symbols */
{ 0x81C2, 0x81C2, 0x01E8 },/* Segment 333, 0x0001 Symbols */
{ 0x81EA, 0x81EA, 0x01E9 },/* Segment 334, 0x0001 Symbols */
{ 0x81F4, 0x81F4, 0x01EA },/* Segment 335, 0x0001 Symbols */
{ 0x822A, 0x822A, 0x01EB },/* Segment 336, 0x0001 Symbols */
{ 0x822C, 0x822C, 0x01EC },/* Segment 337, 0x0001 Symbols */
{ 0x83DC, 0x83DC, 0x01ED },/* Segment 338, 0x0001 Symbols */
{ 0x843D, 0x843D, 0x01EE },/* Segment 339, 0x0001 Symbols */
{ 0x853D, 0x853D, 0x01EF },/* Segment 340, 0x0001 Symbols */
{ 0x87BA, 0x87BA, 0x01F0 },/* Segment 341, 0x0001 Symbols */
{ 0x884C, 0x884C, 0x01F1 },/* Segment 342, 0x0001 Symbols */
{ 0x88C5, 0x88C5, 0x01F2 },/* Segment 343, 0x0001 Symbols */
{ 0x89C6, 0x89C6, 0x01F3 },/* Segment 344, 0x0001 Symbols */
{ 0x89D2, 0x89D2, 0x01F4 },/* Segment 345, 0x0001 Symbols */
{ 0x89E3, 0x89E3, 0x01F5 },/* Segment 346, 0x0001 Symbols */
{ 0x8B66, 0x8B66, 0x01F6 },/* Segment 347, 0x0001 Symbols */
{ 0x8BA1, 0x8BA1, 0x01F7 },/* Segment 348, 0x0001 Symbols */
{ 0x8BAF, 0x8BB0, 0x01F8 },/* Segment 349, 0x0002 Symbols */
{ 0x8BB8, 0x8BB8, 0x01FA },/* Segment 350, 0x0001 Symbols */
{ 0x8BBE, 0x8BBE, 0x01FB },/* Segment 351, 0x0001 Symbols */
{ 0x8BC1, 0x8BC1, 0x01FC },/* Segment 352, 0x0001 Symbols */
{ 0x8BCA, 0x8BCA, 0x01FD },/* Segment 353, 0x0001 Symbols */
{ 0x8BDD, 0x8BDD, 0x01FE },/* Segment 354, 0x0001 Symbols */
{ 0x8BEF, 0x8BEF, 0x01FF },/* Segment 355, 0x0001 Symbols */
{ 0x8BF4, 0x8BF4, 0x0200 },/* Segment 356, 0x0001 Symbols */
{ 0x8BF7, 0x8BF7, 0x0201 },/* Segment 357, 0x0001 Symbols */
{ 0x8C03, 0x8C03, 0x0202 },/* Segment 358, 0x0001 Symbols */
{ 0x8C6A, 0x8C6A, 0x0203 },/* Segment 359, 0x0001 Symbols */
{ 0x8D27, 0x8D27, 0x0204 },/* Segment 360, 0x0001 Symbols */
{ 0x8D70, 0x8D70, 0x0205 },/* Segment 361, 0x0001 Symbols */
{ 0x8DCC, 0x8DCC, 0x0206 },/* Segment 362, 0x0001 Symbols */
{ 0x8DEF, 0x8DEF, 0x0207 },/* Segment 363, 0x0001 Symbols */
{ 0x8EAB, 0x8EAB, 0x0208 },/* Segment 364, 0x0001 Symbols */
{ 0x8F6C, 0x8F6C, 0x0209 },/* Segment 365, 0x0001 Symbols */
{ 0x8F6E, 0x8F6E, 0x020A },/* Segment 366, 0x0001 Symbols */
{ 0x8F74, 0x8F74, 0x020B },/* Segment 367, 0x0001 Symbols */
{ 0x8F7D, 0x8F7D, 0x020C },/* Segment 368, 0x0001 Symbols */
{ 0x8F93, 0x8F93, 0x020D },/* Segment 369, 0x0001 Symbols */
{ 0x8FB9, 0x8FB9, 0x020E },/* Segment 370, 0x0001 Symbols */
{ 0x8FBE, 0x8FBE, 0x020F },/* Segment 371, 0x0001 Symbols */
{ 0x8FC7, 0x8FC7, 0x0210 },/* Segment 372, 0x0001 Symbols */
{ 0x8FCE, 0x8FCE, 0x0211 },/* Segment 373, 0x0001 Symbols */
{ 0x8FD0, 0x8FD0, 0x0212 },/* Segment 374, 0x0001 Symbols */
{ 0x8FD4, 0x8FD4, 0x0213 },/* Segment 375, 0x0001 Symbols */
{ 0x8FDB, 0x8FDB, 0x0214 },/* Segment 376, 0x0001 Symbols */
{ 0x8FDE, 0x8FDE, 0x0215 },/* Segment 377, 0x0001 Symbols */
{ 0x9000, 0x9001, 0x0216 },/* Segment 378, 0x0002 Symbols */
{ 0x9006, 0x9006, 0x0218 },/* Segment 379, 0x0001 Symbols */
{ 0x9009, 0x9009, 0x0219 },/* Segment 380, 0x0001 Symbols */
{ 0x901A, 0x901A, 0x021A },/* Segment 381, 0x0001 Symbols */
{ 0x901F, 0x901F, 0x021B },/* Segment 382, 0x0001 Symbols */
{ 0x9053, 0x9053, 0x021C },/* Segment 383, 0x0001 Symbols */
{ 0x9065, 0x9065, 0x021D },/* Segment 384, 0x0001 Symbols */
{ 0x90A6, 0x90A6, 0x021E },/* Segment 385, 0x0001 Symbols */
{ 0x90AE, 0x90AE, 0x021F },/* Segment 386, 0x0001 Symbols */
{ 0x90E8, 0x90E8, 0x0220 },/* Segment 387, 0x0001 Symbols */
{ 0x91C7, 0x91C7, 0x0221 },/* Segment 388, 0x0001 Symbols */
{ 0x91CD, 0x91CD, 0x0222 },/* Segment 389, 0x0001 Symbols */
{ 0x91CF, 0x91CF, 0x0223 },/* Segment 390, 0x0001 Symbols */
{ 0x94AE, 0x94AE, 0x0224 },/* Segment 391, 0x0001 Symbols */
{ 0x94BB, 0x94BB, 0x0225 },/* Segment 392, 0x0001 Symbols */
{ 0x94C3, 0x94C3, 0x0226 },/* Segment 393, 0x0001 Symbols */
{ 0x94F2, 0x94F2, 0x0227 },/* Segment 394, 0x0001 Symbols */
{ 0x9500, 0x9501, 0x0228 },/* Segment 395, 0x0002 Symbols */
{ 0x9519, 0x951A, 0x022A },/* Segment 396, 0x0002 Symbols */
{ 0x952E, 0x952E, 0x022C },/* Segment 397, 0x0001 Symbols */
{ 0x957F, 0x957F, 0x022D },/* Segment 398, 0x0001 Symbols */
{ 0x95ED, 0x95ED, 0x022E },/* Segment 399, 0x0001 Symbols */
{ 0x95F4, 0x95F4, 0x022F },/* Segment 400, 0x0001 Symbols */
{ 0x963B, 0x963B, 0x0230 },/* Segment 401, 0x0001 Symbols */
{ 0x9640, 0x9640, 0x0231 },/* Segment 402, 0x0001 Symbols */
{ 0x9645, 0x9645, 0x0232 },/* Segment 403, 0x0001 Symbols */
{ 0x964D, 0x964D, 0x0233 },/* Segment 404, 0x0001 Symbols */
{ 0x9650, 0x9650, 0x0234 },/* Segment 405, 0x0001 Symbols */
{ 0x9664, 0x9664, 0x0235 },/* Segment 406, 0x0001 Symbols */
{ 0x9669, 0x9669, 0x0236 },/* Segment 407, 0x0001 Symbols */
{ 0x9694, 0x9694, 0x0237 },/* Segment 408, 0x0001 Symbols */
{ 0x969C, 0x969C, 0x0238 },/* Segment 409, 0x0001 Symbols */
{ 0x96C6, 0x96C6, 0x0239 },/* Segment 410, 0x0001 Symbols */
{ 0x96F7, 0x96F7, 0x023A },/* Segment 411, 0x0001 Symbols */
{ 0x9762, 0x9762, 0x023B },/* Segment 412, 0x0001 Symbols */
{ 0x9876, 0x9876, 0x023C },/* Segment 413, 0x0001 Symbols */
{ 0x987A, 0x987A, 0x023D },/* Segment 414, 0x0001 Symbols */
{ 0x9891, 0x9891, 0x023E },/* Segment 415, 0x0001 Symbols */
{ 0x98CE, 0x98CE, 0x023F },/* Segment 416, 0x0001 Symbols */
{ 0x9988, 0x9988, 0x0240 },/* Segment 417, 0x0001 Symbols */
{ 0x9AD8, 0x9AD8, 0x0241 },/* Segment 418, 0x0001 Symbols */
{ 0x9F7F, 0x9F7F, 0x0242 },/* Segment 419, 0x0001 Symbols */
{ 0xFEFF, 0xFEFF, 0x0243 },/* Segment 420, 0x0001 Symbols */
{ 0xFF01, 0xFF01, 0x0244 },/* Segment 421, 0x0001 Symbols */
{ 0xFF03, 0xFF03, 0x0245 },/* Segment 422, 0x0001 Symbols */
{ 0xFF08, 0xFF09, 0x0246 },/* Segment 423, 0x0002 Symbols */
{ 0xFF0C, 0xFF0C, 0x0248 },/* Segment 424, 0x0001 Symbols */
{ 0xFF1A, 0xFF1A, 0x0249 },/* Segment 425, 0x0001 Symbols */
{ 0xFF1F, 0xFF20, 0x024A }/* Segment 426, 0x0002 Symbols */
}
};
