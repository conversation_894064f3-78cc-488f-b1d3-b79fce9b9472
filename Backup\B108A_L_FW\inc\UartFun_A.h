/*******CAN_bx.h for CAN fuunction in 2G TDECS system********/
#ifndef __UART_FUN_H
#define __UART_FUN_H
#include "stdint.h"
#include "SysConfig.h"
#include "ThisDevice.h"
//Define const or symbol
//----------------------------------------------------------------
#define  MB_FRAME_GAP_T  38500  //11*3.5*1000 in ms,according to  modbus define
#define  U_DB_MAX        254    //Uart receive buffer size in bytes
//---------------------------------------------------------------------------

#define  U_TX_IDLE            0x00
#define  U_TX_WAIT_FOR_IDLE   0x01
#define  U_TX_WAIT_FOR_SEND   0x02
#define  U_TX_CONTINUE_SEND   0x04
#define  U_TX_WAIT_FOR_FINISH 0x08
//---

/**********************************/
#define  U_TIM_NUM           2 /*Number of uart function related timers in the system  */

#define  UB_TIMER            0 //dispatch system timer0-1 to monitor UART B channel port communication

//-----------------------------------------------------------------------------
typedef struct TagUARTStruct
{
 volatile uint8_t                  rxd_sta ;//Receiver status register
 volatile uint8_t                      rdl ;//Internal receiving data number
 volatile uint8_t                     *rdp ;//Internal receiving buffer position pointer
 volatile uint8_t                  *RxBuf0 ;//RX receiving buffer 0 pointer
 volatile uint8_t                  *RxBuf1 ;//RX receiving buffer 1 pointer
 volatile uint8_t                    RxDL0 ;//Data number in receiving buffer 0
 volatile uint8_t                    RxDL1 ;//Data number in receiving buffer 1
 volatile uint8_t                      tcs ;//TX control and status register
 volatile uint8_t                      tdl ;//Internal TX data package length
 volatile uint8_t                   *TxBuf ;//TX buffer pointer
 volatile uint8_t                     *tdp ;//Internal TX data pointer
 volatile uint16_t               RxPkCount ;//Received data package counting number
 volatile uint16_t               TxPkCount ;//Transmitted data package counting
 volatile uint8_t                  TimerID ;//Denote which timer used to moniter communiction operation
 volatile uint8_t                   RWtime ;//Receiver wait  timer(timeout monitor control setting)
 volatile uint8_t                   TDtime ;//Transmittion delay between continue TX request (control setting)
 volatile uint8_t                   RxFlag ;//
} UARTStruct ;
//---------------------------------------------
typedef struct TagModStruct
{
   volatile uint8_t      NodeAdd ;//modbus local node address when protocol run as A Slaver
   volatile uint8_t          Opc ;//modbus operation control code
   volatile uint8_t          Sta ;//modbus protocol stack status
   volatile uint8_t     RTryHold ;//hold current channel control setting of retry
   volatile uint8_t         Rtry ;//modbus timeout retry counter
   volatile uint8_t       Length ;//modbus frame length in byte
   volatile uint8_t        *MBuf ;//protocol receiving buffer
   uint16_t          Ret ;//modbus routine return varible
   uint16_t          Err ;//modbus protocol error status
   volatile uint8_t          Add ;//Modbus slaver address in low byte(high byte must be zero)
   volatile uint8_t          Fun ;//Modbus function code(high byte must be zero)
   uint16_t       RegAdd ;//Register address in target
   uint16_t       DatNum ;//Number of register to accesss (words)
   volatile uint8_t      ByteNum ;//Number of data in bytes(high byte must be zero)
   uint8_t         tmp_c ;
   volatile uint8_t         *SDp ;//Source data pointer,in function 03 point to input data save destination address
                     //in function 16 point to output source data address ****************
   uint16_t         RAdd ;//used to support function 23
   uint16_t         RNum ;//used to support function 23
   volatile uint8_t        *RSDp ;//used to support function 23
   const AccessCtrlStruct   *AccTable ;//point to access control table for port
   uint8_t     (*TXPort) (volatile uint8_t *Tx_UA_BF,int16_t TxdLen) ;//point to corresponding TX routine UART port
   uint8_t     (*DModNotify) (uint16_t Add ,uint16_t Len) ;//point to Slaver data area have been modified notify function
} ModStruct ;

void  InitUartTimer(void) ;//For system mode uart timer init

void  UartTimerFun(void) ;

void  USART_Config(void)	;

uint8_t  DModNotify_UB(uint16_t Add ,uint16_t Len) ;
uint8_t  Tx_UB(volatile uint8_t *Tx_UB_BF,int16_t TxdLen) ;

void  UXTxP(void) ;

void  USART2_ISR(void) ;

void  USART2_DMA_ISR(void) ;

//----------------------------------------
/*********Function related to use modbus RTU communication ******/
void  ModProcA(UARTStruct *UX , ModStruct *ModX) ;

uint16_t  AccessCtrlCheck( ModStruct *ModX, uint16_t DenAdd,uint16_t Length,uint16_t AccType) ;//ModbusRTU access check funtion
//--------
extern  UARTStruct  UB,*UX  ;//

extern  ModStruct  ModB,*ModX  ;

#endif  //__UART_FUN_H
