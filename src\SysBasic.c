#include "SysBasic.h"
#include "ThisDevice.h"

const uint16_t wCRCTable[] = {
   0x0000, 0xC0C1, 0xC181, 0x0140, 0xC301, 0x03C0, 0x0280, 0xC241,
   0xC601, 0x06C0, 0x0780, 0xC741, 0x0500, 0xC5C1, 0xC481, 0x0440,
   0xCC01, 0x0CC0, 0x0D80, 0xCD41, 0x0F00, 0xCFC1, 0xCE81, 0x0E40,
   0x0A00, 0xCAC1, 0xCB81, 0x0B40, 0xC901, 0x09C0, 0x0880, 0xC841,
   0xD801, 0x18C0, 0x1980, 0xD941, 0x1B00, 0xDBC1, 0xDA81, 0x1A40,
   0x1E00, 0xDEC1, 0xDF81, 0x1F40, 0xDD01, 0x1DC0, 0x1C80, 0xDC41,
   0x1400, 0xD4C1, 0xD581, 0x1540, 0xD701, 0x17C0, 0x1680, 0xD641,
   0xD201, 0x12C0, 0x1380, 0xD341, 0x1100, 0xD1C1, 0xD081, 0x1040,
   0xF001, 0x30C0, 0x3180, 0xF141, 0x3300, 0xF3C1, 0xF281, 0x3240,
   0x3600, 0xF6C1, 0xF781, 0x3740, 0xF501, 0x35C0, 0x3480, 0xF441,
   0x3C00, 0xFCC1, 0xFD81, 0x3D40, 0xFF01, 0x3FC0, 0x3E80, 0xFE41,
   0xFA01, 0x3AC0, 0x3B80, 0xFB41, 0x3900, 0xF9C1, 0xF881, 0x3840,
   0x2800, 0xE8C1, 0xE981, 0x2940, 0xEB01, 0x2BC0, 0x2A80, 0xEA41,
   0xEE01, 0x2EC0, 0x2F80, 0xEF41, 0x2D00, 0xEDC1, 0xEC81, 0x2C40,
   0xE401, 0x24C0, 0x2580, 0xE541, 0x2700, 0xE7C1, 0xE681, 0x2640,
   0x2200, 0xE2C1, 0xE381, 0x2340, 0xE101, 0x21C0, 0x2080, 0xE041,
   0xA001, 0x60C0, 0x6180, 0xA141, 0x6300, 0xA3C1, 0xA281, 0x6240,
   0x6600, 0xA6C1, 0xA781, 0x6740, 0xA501, 0x65C0, 0x6480, 0xA441,
   0x6C00, 0xACC1, 0xAD81, 0x6D40, 0xAF01, 0x6FC0, 0x6E80, 0xAE41,
   0xAA01, 0x6AC0, 0x6B80, 0xAB41, 0x6900, 0xA9C1, 0xA881, 0x6840,
   0x7800, 0xB8C1, 0xB981, 0x7940, 0xBB01, 0x7BC0, 0x7A80, 0xBA41,
   0xBE01, 0x7EC0, 0x7F80, 0xBF41, 0x7D00, 0xBDC1, 0xBC81, 0x7C40,
   0xB401, 0x74C0, 0x7580, 0xB541, 0x7700, 0xB7C1, 0xB681, 0x7640,
   0x7200, 0xB2C1, 0xB381, 0x7340, 0xB101, 0x71C0, 0x7080, 0xB041,
   0x5000, 0x90C1, 0x9181, 0x5140, 0x9301, 0x53C0, 0x5280, 0x9241,
   0x9601, 0x56C0, 0x5780, 0x9741, 0x5500, 0x95C1, 0x9481, 0x5440,
   0x9C01, 0x5CC0, 0x5D80, 0x9D41, 0x5F00, 0x9FC1, 0x9E81, 0x5E40,
   0x5A00, 0x9AC1, 0x9B81, 0x5B40, 0x9901, 0x59C0, 0x5880, 0x9841,
   0x8801, 0x48C0, 0x4980, 0x8941, 0x4B00, 0x8BC1, 0x8A81, 0x4A40,
   0x4E00, 0x8EC1, 0x8F81, 0x4F40, 0x8D01, 0x4DC0, 0x4C80, 0x8C41,
   0x4400, 0x84C1, 0x8581, 0x4540, 0x8701, 0x47C0, 0x4680, 0x8641,
   0x8201, 0x42C0, 0x4380, 0x8341, 0x4100, 0x81C1, 0x8081, 0x4040 } ;
//-----------------------------------------------------------------------------

/* Global variable define */
volatile  uint32_t      OpCtrl =0 ;
volatile  uint32_t       GFlag =0 ;
volatile  uint32_t   GFlag_Pre =0 ;

volatile  uint16_t    SYS_STA =0 ;  //POWER_DOWN_STA
volatile  int16_t     TxToken =0 ;

volatile  uint16_t     IsrFlag=0 ;//Special flag for used in ISR	,Begin with IFG_

volatile  int16_t    daemon_lock=0 ;
volatile  uint16_t     TestCtrlW=0 ;

//-----------
volatile  uint16_t     KeyRepeatTimer=0 ;
volatile  uint16_t     HeartBeatTimer=0 ;
//-----------
volatile  SYS_SETTING_VAR  SysSetting ;
   
volatile  uint16_t  Def_CRC_Seed=LEFT_CRC_SEED ;//default CRC seed for left or right side operation

RfTickTokenCtrl_t *RfTT_p ;//define a pointer for current RF tick and token setting

//---------Define most important funtion pointer for multimode operation-----
void (*AppMain_fp)(void) __attribute__ ((noreturn));//Define a function pointer for particular application main funtion 

void (*Exi15_10_isr_fp)(void) ;//Define function pointer for interrupt handler

void (*SysTick_isr_fp)(void) ;

void (*Tim9_isr_fp)(void ) ;

void (*Tim10_isr_fp)(void ) ;

void (*Tim11_isr_fp)(void ) ;

void (*TxToken_fp)(void ) ;

void (*Proc10mS_fp)(void) ;//Define function pointer for periodical called timer function--10mS
void (*Proc100mS_fp)(void) ;
void (*Proc1S_fp)(void) ;
void (*Proc1H_fp)(void) ;
//----
void (*PanelDatTxLoad_fp)(void) ;
void (*MachDatRx_fp)(void) ;
void (*PanelLcdPrint_fp)(void) ;
void (*GetKeyID_fp)(void) ;
//-----------------------------------------------------------------------------

SysAreaStruct    S_Area __attribute__ ((aligned));//defien an object for system data

UserAreaStruct   U_Area __attribute__ ((aligned));//define an object for user data

//------------------------------------------------------------------------------

uint16_t CRC16 (const uint16_t *nData, uint16_t wLength)//Gerate 16 bit CRC value from series words
{
  uint16_t nTemp  ;
  uint16_t wCRCWord ;
  wCRCWord =0xffff;
  while (wLength--)
  {
    nTemp     = (*nData >>8) ;
    nTemp     ^= wCRCWord ;
    nTemp     &= 0x00ff ;
    wCRCWord  >>= 8 ;
    wCRCWord  &=0x00ff ; 
    wCRCWord  ^=wCRCTable[nTemp];
    nTemp      = *nData ^ wCRCWord;
    nData++ ;
    nTemp    &=0x00ff ;
    wCRCWord >>= 8;
    wCRCWord &= 0x00ff ;
    wCRCWord ^= wCRCTable[nTemp];
   }
  nTemp=wCRCWord ;
  wCRCWord>>=8 ;
  wCRCWord &=0x00ff ;
  nTemp<<=8 ;
  wCRCWord |=nTemp ;
  return wCRCWord; 
}

uint16_t CRC16WithSeed (uint16_t seed,const uint16_t *nData, uint16_t wLength)//Gerate 16 bit CRC value from series words
{
   uint16_t nTemp  ;//byte value (data saved as 16 bit word)
   uint16_t wCRCWord ;
   wCRCWord =seed;
   while (wLength--)
   {
      nTemp     = (*nData >>8) ;
      nTemp     ^= wCRCWord ;
      nTemp     &= 0x00ff ;
      wCRCWord  >>= 8 ;
      wCRCWord  &=0x00ff ; 
      wCRCWord  ^=wCRCTable[nTemp];

      nTemp      = *nData ^ wCRCWord;
      nData++ ;
      nTemp    &=0x00ff ;
      wCRCWord >>= 8;
      wCRCWord &= 0x00ff ;
      wCRCWord ^= wCRCTable[nTemp];
   }
   nTemp=wCRCWord ;
   wCRCWord>>=8 ;
   wCRCWord &=0x00ff ;
   nTemp<<=8 ;
   wCRCWord |=nTemp ;
   return wCRCWord;  
}

uint32_t CRC32(const uint32_t *nData, uint32_t wLength)
{//CRC32 use STM32L hardware accelerator
  uint32_t index ;
  RCC->AHBENR  |= 0x00001000 ;//Enable CRC for use
  __nop() ;//wait 
   /* Reset CRC generator */
  CRC->CR = ((uint32_t)0x00000001);
  //begin to caculate new CRC 32
  for(index = 0; index < wLength; index++)
  {
    CRC->DR = *nData;
    nData++ ;
   }
  index =CRC->DR ;
  RCC->AHBENR  &= 0xffffefff ;//Disable CRC for use
  return (index);
}

uint32_t CalReqCode(void)
{//Calculate authentication request code
  __IO uint32_t *ltp  ;
  uint32_t index ;
  RCC->AHBENR  |= 0x00001000 ;//Enable CRC for use
  __nop() ;//wait 
  ltp=(__IO uint32_t *) 0x1FF80050 ;//Unique device ID register address
  CRC->CR =((uint32_t)0x00000001); // Reset CRC generator 
  CRC->DR =0x9d38cae5 ;
  CRC->DR = *ltp++^0xb7fa8c42;
  CRC->DR = *ltp++^0x3d90b51e;
  CRC->DR = *ltp;
  index =CRC->DR ;
  RCC->AHBENR  &= 0xffffefff ;//Disable CRC for use
  return (index);
}

uint32_t GenAuthCode(uint32_t ReqCode)
{
  uint32_t index ;
  RCC->AHBENR  |= 0x00001000 ;//Enable CRC for use
  __nop() ;//wait 
  CRC->CR = ((uint32_t)0x00000001);// Reset CRC generator
  CRC->DR =0xb2c7a58d ;//Authentication seed
  CRC->DR =ReqCode ;
  CRC->DR =0x47fc29b6 ;
  index =CRC->DR ;
  RCC->AHBENR  &= 0xffffefff ;//Disable CRC for use
  return (index);
}

uint32_t VerifyAuthCode(uint32_t AuthCode)
{//return 0 if verify success,otherwise non zero
  uint32_t ltmp ;
  ltmp=CalReqCode() ;//Get UID
  RCC->AHBENR  |= 0x00001000 ;//Enable CRC for use
  __nop() ;//wait 
  CRC->CR = ((uint32_t)0x00000001);// Reset CRC generator
  CRC->DR =0xb2c7a58d ;//Verify seed
  CRC->DR =ltmp ;
  CRC->DR =0x47fc29b6 ;
  ltmp =CRC->DR^AuthCode ;
  RCC->AHBENR  &= 0xffffefff ;//Disable CRC for use
  return (ltmp);
}

//------------------------------------------------

void ClrObj(uint8_t *obj,uint16_t size)
{//clear memory object
 uint16_t i ;
 for(i=0 ;i<size ;i++)
 {
  *(obj++)=(uint8_t)0 ;
 }
}

void ClrObj16(uint16_t *obj,uint16_t size)
{//clear memory object
 uint16_t i ;
 for(i=0 ;i<size ;i++)
 {
  *(obj++)=(uint16_t)0 ;
 }
}

void ClrObj32(uint32_t *obj,uint16_t size)
{//clear memory object
 uint16_t i ;
 for(i=0 ;i<size ;i++)
 {
  *(obj++)=(uint32_t)0 ;
 }
}

void CopyObj(uint8_t *scr,uint8_t *den,uint16_t size)
{//copy memory object in byte mode
 uint16_t i ;
 for(i=0 ;i<size ;i++)
 {
  *(den++)=*(scr++) ;
 }
}

void CopyObj16(uint16_t *scr,uint16_t *den,uint16_t size)
{//clear memory object in 16 bit word mode
 uint16_t i ;
 for(i=0 ;i<size ;i++)
 {
  *(den++)=*(scr++) ;
 }
}

void CopyObj32(uint32_t *scr,uint32_t *den,uint16_t size)
{//clear memory object in 32 bit word mode
 uint16_t i ;
 for(i=0 ;i<size ;i++)
 {
  *(den++)=*(scr++) ;
 }
}
//-----------------------------------------------------------------------------
void InitWatchDog(void)
{
  IWDG->KR = ((uint16_t)0x5555) ;//Enable write access to IWDG_PR and IWDG_RLR 
  // Configure IWDG Prescaler register value 
  IWDG->PR =0;//IWDG use 40 kHz internal RC clock,prescaler=4
  // Configure WDG Pre-load register value 
  IWDG->RLR = 1000 ;//About 100mS reset cycle
  // Select WDG mode 
  IWDG->KR = ((uint16_t)0x0000) ;//Disable write access to IWDG_PR and IWDG_RLR 
  IWDG->KR = ((uint16_t)0xCCCC) ;//Start the watchdog
  IWDG->KR = ((uint16_t)0xAAAA) ;//Key for Reset(Reload) watchdog 
}

void KickWatchDog(void)
{
  IWDG->KR = ((uint16_t)0xAAAA) ;//Key for Reset(Reload) watchdog 
 }

/*******************************************************************************
* Function Name  : InitSTM32Lxx(
* Description    : Perform customized system init for particularly use
* Input          : None.
* Output         : None
* Return         : None
*******************************************************************************/
//------------------------
void  InitSTM32Lxx(void)
{
  /* Private variables ---------------------------------------------------------*/
  NVIC_InitTypeDef NVIC_InitStructure;
  uint8_t PreemptionPriorityValue = 0; 
  //Begin to init application data
//-------------------------------------------------------------------------------
  __disable_irq();//Disable all interrupt
  SystemCoreClockUpdate() ;
  APB1_Clock = SystemCoreClock ;
  APB2_Clock = SystemCoreClock ;
  /* Setup SysTick Timer for 1 msec interrupts.*/
  if (SysTick_Config(SystemCoreClock /10000))//Use System tick timer to generate 10 mS timebase
  { 
    /* Capture error */ 
    while (1);
   }
  //initialize the interrupt controller
  /* Configure two bits for preemption priority */
  NVIC_PriorityGroupConfig(NVIC_PriorityGroup_2);//preemption priority[0-3],NVIC_IRQChannelSubPriority[0-3]
//------------
  /* Configure the SysTick Handler Priority: Preemption priority and subpriority */
  NVIC_SetPriority(SysTick_IRQn, (!PreemptionPriorityValue << 0x03));
  /* Enable the EXTI15_10 Interrupt */
  NVIC_InitStructure.NVIC_IRQChannel = EXTI15_10_IRQn;
  NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 0;
  NVIC_InitStructure.NVIC_IRQChannelSubPriority = 1;
  NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;
  NVIC_Init(&NVIC_InitStructure);
  /* Enable the USART2 Interrupt */
  NVIC_InitStructure.NVIC_IRQChannel = USART2_IRQn;
  NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 1;
  NVIC_InitStructure.NVIC_IRQChannelSubPriority = 2;
  NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;
  NVIC_Init(&NVIC_InitStructure);
  /* Enable the DMA1  Channel1 Interrupt--ADC1 caused */
  NVIC_InitStructure.NVIC_IRQChannel = DMA1_Channel1_IRQn;
  NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 0;
  NVIC_InitStructure.NVIC_IRQChannelSubPriority = 3;
  NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;
  NVIC_Init(&NVIC_InitStructure);
  /* Enable the DMA1  Channel7 Interrupt--USART2_TX caused */
  NVIC_InitStructure.NVIC_IRQChannel = DMA1_Channel7_IRQn;
  NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 1;
  NVIC_InitStructure.NVIC_IRQChannelSubPriority = 3;
  NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;
  NVIC_Init(&NVIC_InitStructure);
  /* Enable the TIM9 update Interrupt  */
  NVIC_InitStructure.NVIC_IRQChannel =TIM9_IRQn ;
  NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 2;
  NVIC_InitStructure.NVIC_IRQChannelSubPriority = 3;
  NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;
  NVIC_Init(&NVIC_InitStructure);
  /* Enable the TIM10 update Interrupt  */
  NVIC_InitStructure.NVIC_IRQChannel =TIM10_IRQn ;
  NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 2;
  NVIC_InitStructure.NVIC_IRQChannelSubPriority = 2;
  NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;
  NVIC_Init(&NVIC_InitStructure);
  /* Enable the TIM11 update Interrupt  */
  NVIC_InitStructure.NVIC_IRQChannel =TIM11_IRQn ;
  NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 2;
  NVIC_InitStructure.NVIC_IRQChannelSubPriority = 1;
  NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;
  NVIC_Init(&NVIC_InitStructure);
  __enable_irq();
}
//----------------
int32_t get_lock(void)
{
 __disable_irq() ;
 if(daemon_lock==0)
 {
   daemon_lock++ ;
   __enable_irq() ;
   return 1 ;
   }
 else
 {
   __enable_irq();
   return 0 ;
  }
}
void release_lock(void)
{
  __disable_irq() ;
  daemon_lock=0 ;
  __enable_irq();
}

//----------------
void  DisableTIM9(void) 
{
  TIM9->CR1  =0x0000 ;//Disable TIM9
  TIM9->DIER =0x0000 ;//Disable TIM9 interrupt
}

void  DisableTIM11(void)
{
  TIM11->CR1  =0x0000 ;//Disable TIM11
  TIM11->DIER =0x0000 ;//Disable TIM11 interrupt
}
void  EnableTIM11(void)
{
  TIM11->SR   =0x0000 ;//Clear the TIM11 inperrupt flag
  TIM11->CR1  =0x0001 ;//Enable TIM11
  TIM11->DIER =0x0001 ;//Enable TIM11 interrupt
}

//----------------

void GetSysRstKeySta(void)
{//Read System Reset Key input status and store in a global variable,under some circumstance
  ;
}
//---------------------------------
void  SetupLowSpeedRunMode(void)
{//Set the device to run about 262kHz--MSI range 2 -38108+38403B consume about 700uA in this speed mode
  __IO uint32_t StartUpCounter = 0, MSIStatus = 0;
#ifndef NO_STANDBY
  RCC->ICSCR&=0xffff1fff ;//This will set MSI clock range 0--65kHz
  //  RCC->ICSCR|=0x00002000 ;//Set MSI clock range 1--131kHz
  RCC->ICSCR|=0x00004000 ;//Set MSI clock range 2--262kHz
  /* Enable MSI */
  RCC->CR |= ((uint32_t)RCC_CR_MSION);
  /* Wait till MSI is ready or if Time out is reached exit */
  do
  {
    MSIStatus = RCC->CR & RCC_CR_MSIRDY;
    StartUpCounter++;
   } while((MSIStatus == 0) && (StartUpCounter != HSE_STARTUP_TIMEOUT));
  if ((RCC->CR & RCC_CR_MSIRDY) != RESET)
  {
    /* Select MSI as system clock source */
    RCC->CFGR &= (uint32_t)((uint32_t)~(RCC_CFGR_SW));
    RCC->CFGR |= (uint32_t)RCC_CFGR_SW_MSI;
    /* disable PLL */
    RCC->CR &=~RCC_CR_PLLON;
	GFlag|=GF_LOW_SPEED_RUN ;
   }
#endif 
}
void  ExitLowSpeedRunMode(void)
{//Exit low speed runstatus and enter normal run status(use HSE ,8MHz)
  __IO uint32_t StartUpCounter = 0, HSEStatus = 0;
#ifndef NO_STANDBY
  /* Enable HSE */
  RCC->CR |= ((uint32_t)RCC_CR_HSEON);
  /* Wait till HSE is ready and if Time out is reached exit */
  do
  {
    HSEStatus = RCC->CR & RCC_CR_HSERDY;
    StartUpCounter++;
   } while((HSEStatus == 0) && (StartUpCounter != HSE_STARTUP_TIMEOUT));

  if ((RCC->CR & RCC_CR_HSERDY) != RESET)
  {
    /* Enable PLL */
    RCC->CR |= RCC_CR_PLLON;
    /* Wait till PLL is ready */
    while((RCC->CR & RCC_CR_PLLRDY) == 0) ;
    /* Select PLL as system clock source */
    RCC->CFGR &= (uint32_t)((uint32_t)~(RCC_CFGR_SW));
    RCC->CFGR |= (uint32_t)RCC_CFGR_SW_PLL;
    /* Wait till PLL is used as system clock source */
    while ((RCC->CFGR & (uint32_t)RCC_CFGR_SWS) != (uint32_t)RCC_CFGR_SWS_PLL) ;
	GFlag&=~GF_LOW_SPEED_RUN ;
  }
#endif
}
/**
  * @brief  Enters STANDBY mode.
  * @note   In Standby mode, all I/O pins are high impedance except for:
  *          - Reset pad (still available) 
  *          - RTC_AF1 pin (PC13) if configured for Wakeup pin 2 (WKUP2), tamper, 
  *            time-stamp, RTC Alarm out, or RTC clock calibration out.
  *          - WKUP pin 1 (PA0) and WKUP pin 3 (PE6), if enabled.       
  * @param  None
  * @retval None
  */
void  EnStandby_LCD_021(void)
{
#ifndef NO_STANDBY		  
  LcdEnterSleep() ;//put the LCD to Sleep mode
  PWR->CR|=0x00000100 ;//Set DBP--enable to access RTC backup register and RCC CSR 
  RCC->CSR=0x00000000 ;//LSE set to OFF,Remove reset flag
  PWR->CR&=0xfffffeff ;//Reset DBP--disable to access RTC backup register and RCC CSR

  GPIOA->PUPDR =0x6400a82a ;
  GPIOB->PUPDR =0x08a8010a ;//--
  GPIOC->PUPDR =0x00000a80 ;
  GPIOD->PUPDR =0x00010000 ;
  GPIOE->PUPDR =0x0002aa0a ;
//---
  /* Clear Wakeup flag */
  PWR->CR |= PWR_CR_CWUF;
  /* Select STANDBY mode */
  PWR->CR |= PWR_CR_PDDS;
  /* Set SLEEPDEEP bit of Cortex System Control Register */
  SCB->SCR |= SCB_SCR_SLEEPDEEP;
  /* This option is used to ensure that store operations are completed */
  #if defined ( __CC_ARM   )
     __force_stores();
  #endif
  /* Request Wait For Interrupt */
  __WFI();
#endif
}
void  EnStandby_NoLCD_01(void)
{
#ifndef NO_STANDBY
  PWR->CR|=0x00000100 ;//Set DBP--enable to access RTC backup register and RCC CSR 
  RCC->CSR=0x00000000 ;//LSE set to OFF,Remove reset flag
  PWR->CR&=0xfffffeff ;//Reset DBP--disable to access RTC backup register and RCC CSR
  ADC1->CR2=0x00000000 ;//Power down the ADC1 to reduce power consume
//--
  GPIOA->PUPDR =0x6400a82a ;
  GPIOB->PUPDR =0x0aa8010a ;//--
  GPIOC->PUPDR =0x00000a80 ;
  GPIOD->PUPDR =0x00010000 ;
  GPIOE->PUPDR =0x0102aaaa ;//
  //-------
  RCC->CR=0x00000000 ;//disable all clock
  /* Voltage scalling range 3=1.2V,Ultralow power mode,Set LPDSR bit */
  PWR->CR = 0x1a01;
  /* Set SLEEPDEEP bit of Cortex System Control Register */
  SCB->SCR |= SCB_SCR_SLEEPDEEP;
  /* Request Wait For Event */
  __WFE();
#endif
}

/*******************************************************************************
* Function Name  : ReadKeySta
* Description    : read operation key status for global use
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/

__inline void ReadKeyBoard(void)//runtime about [14uS-27uS]@8MHz CPU
{//Read all normal function Key input status and store in a global variable
  uint16_t tmp ;
  tmp=0x0000 ;
  if(GPIOA->IDR&0x00000001)
    tmp|=0x2000 ;
  if(GPIOA->IDR&0x00000020)
    tmp|=0x0010 ;
  if(GPIOA->IDR&0x00000040)
    tmp|=0x1000 ;
  if(GPIOA->IDR&0x00000080)
    tmp|=0x0800 ;
//--
  if(GPIOB->IDR&0x00000001)
    tmp|=0x0100 ;
  if(GPIOB->IDR&0x00000002)
    tmp|=0x0001 ;
  if(GPIOB->IDR&0x00000400)
    tmp|=0x0004 ;
//--
  if(GPIOC->IDR&0x00000008)
    tmp|=0x0008 ;
  if(GPIOC->IDR&0x00000010)
    tmp|=0x0400 ;
  if(GPIOC->IDR&0x00000020)
    tmp|=0x0200 ;
//--
  if(GPIOE->IDR&0x00000001)
    tmp|=0x0020 ;
  if(GPIOE->IDR&0x00000002)
    tmp|=0x0040 ;
  if(GPIOE->IDR&0x00000010)
    tmp|=0x0080 ;
  if(GPIOE->IDR&0x00000020)
    tmp|=0x8000 ;
  if(GPIOE->IDR&0x00000040)
    tmp|=0x4000 ;
  if(GPIOE->IDR&0x00000080)
    tmp|=0x0002 ;
  U_Area.KeyCodeRaw.cw=~tmp; //Now get raw key status
}

//---------------
void  InitRfTickTimer(void )
{ //Timer10 be set to generate required periodical interrupt--as RF operation ticks
  TIM10->CR1  =0x0000 ;
  TIM10->CNT  =0x0000 ;//
  TIM10->ARR  =RfTT_p->RfTickTimerPV  ;//Set RF tick interrupt interval
  TIM10->SMCR =0x4000 ;
  TIM10->SR   =0x0000 ;//Clear the TIM10 inperrupt flag
  TIM10->DIER =0x0001 ;//Enable TIM10 interrupt
  TIM10->CR1  =0x0001 ;//Enable the timer
}

//---------------
void  AdjustRfTickTimer(uint16_t NewCountValue) 
{
 TIM10->CNT  = NewCountValue; //Sync the loacal Timer10 to Rx station's clock
}
//-------------
void  DisableRfTickTimer(void)
{
  TIM10->CR1  =0x0000 ;//Disable TIM10
  TIM10->DIER =0x0000 ;//Disable TIM10 interrupt
}
//---------------

/*******************************************************************************
* Function Name  : GetKeySta
* Description    : get all operation key for global use
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void GetKeySta(void)
{//Get all normal function Key act status--run time [33uS--52uS]@ 8MHz cpu
  uint16_t tmp ;
  ReadKeyBoard() ;//Read in the key board status
  tmp=U_Area.KeyCodeRaw.cw ;
  ReadKeyBoard() ;//Read in the key board status
  if(U_Area.KeyCodeRaw.cw!=tmp)
  {
    return ;
   }
  U_Area.RisingKeyRaw.cw = U_Area.KeyCodeRaw.cw^S_Area.PreKeyRaw.cw ;
  tmp=U_Area.RisingKeyRaw.cw ;
  U_Area.FallingKeyRaw.cw = U_Area.RisingKeyRaw.cw&(~U_Area.KeyCodeRaw.cw) ;
  U_Area.RisingKeyRaw.cw &= U_Area.KeyCodeRaw.cw ;
  S_Area.PreKeyRaw.cw     = U_Area.KeyCodeRaw.cw ;
  //---------
  U_Area.KeyCode.cw = U_Area.KeyCodeRaw.cw & S_Area.KeyMask.cw ;
  if(GFlag & GF_IN_SETUP_STA)
  {
    U_Area.KeyCode.cw=0 ;
   }
  else
  {
   if(tmp&S_Area.KeyMask.cw)//The key have been changed
   {
	 TIM9->CNT  =0x0000 ;//
     TIM9->SR   =0x0000 ;//Clear the TIM9 inperrupt flag
     S_Area.TPool_p=0 ;//Reset Token pool pointer to First pool
     S_Area.RemainToken= RfTT_p->TokenPool[0].TokenNum ;
	 __disable_irq();
     S_Area.RFTickCount = RfTT_p->TokenPool[0].Interval-1;
     S_Area.ActTimeOutMonitor = S_Area.SysActWaitTime ;//Restart and refresh active monitor
     OpCtrl  |= CB_KB_CHANGED ;
	 IsrFlag |= IFG_KB_CHANGED ;
	 __enable_irq();
     Led_BL_on ;//Turn on the LCD backlight LED 1
     U_Area.PSta.InquiryPackage=0x0000 ;//InquiryPackage=0
     if(U_Area.KeyCodeRaw.cw==0)
     {
       S_Area.LCD_BL_Timer=S_Area.BLShutOffTime>>1 ;
      }
     else
     {
       S_Area.LCD_BL_Timer=S_Area.BLShutOffTime ;
      }
    }
  }
}

/*******************************************************************************
* Function Name  : TxTokenGenProc_Bi
* Description    : Generate TxToken according to keyboard operation status in bidirection mode
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void TxTokenGenProc_Bi(void)
{
 if(IsrFlag&IFG_GTOKEN_EN )
 {
   __disable_irq();
   IsrFlag&=~IFG_GTOKEN_EN ;
   __enable_irq();
   TxToken=TOKEN_UINT ;
   S_Area.RemainToken-- ;
   if(S_Area.RemainToken<=0)
   {
     if(S_Area.TPool_p<TOKENPOOLS)
     {
       if(U_Area.KeyCode.cw>0)
       {
         Led_BL_on ;//Turn on the LCD backlight LED 1
         S_Area.TPool_p=KEY_ON_TPOOL ;
         S_Area.RemainToken=RfTT_p->TokenPool[KEY_ON_TPOOL].TokenNum ;
        }
       else
       {
         U_Area.PSta.InquiryPackage=0x0001 ;//InquiryPackage=1
         S_Area.TPool_p++ ;
         S_Area.RemainToken=RfTT_p->TokenPool[S_Area.TPool_p].TokenNum ;
        }
      }
     else
     {
       S_Area.RemainToken=0 ;
       TxToken=0 ;
      }
    }
 }
}
/*******************************************************************************
* Function Name  : TxTokenGenProc_Uni
* Description    : Generate TxToken according to keyboard operation status in unidirection mode
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void TxTokenGenProc_Uni(void)
{
 if(IsrFlag&IFG_GTOKEN_EN )
 {
   __disable_irq();
   IsrFlag&=~IFG_GTOKEN_EN ;
   __enable_irq();
   TxToken=TOKEN_UINT ;
   S_Area.RemainToken-- ;
   if(S_Area.RemainToken<=0)
   {
     if(S_Area.TPool_p<TOKENPOOLS)
     {
       if(U_Area.KeyCode.cw>0)
       {
         S_Area.TPool_p=KEY_ON_TPOOL ;
         S_Area.RemainToken=RfTT_p->TokenPool[KEY_ON_TPOOL].TokenNum ;
        }
       else
       {
         U_Area.PSta.InquiryPackage=0x0001 ;//InquiryPackage=1
         S_Area.TPool_p++ ;
         S_Area.RemainToken=RfTT_p->TokenPool[S_Area.TPool_p].TokenNum ;
       }
      }
     else
     {
       S_Area.RemainToken=0 ;
       TxToken=0 ;
      }
    }
 }
}
//----------Main function for device test------
void  __TestMain(void)
{
  while(1) ;
}
//----------define a default function for all no operation call 
void  NoOperation(void)
{
  __nop() ;
}

//---------
void  Led_1x2_on(void)
{//Extended function(may changed for led1 and led4) for output on led 1
  if(S_Area.LedFunExchTimer<=0)
    Led_1_on;
  else
    Led_2_on;
 }

void  Led_1x2_off(void)
{
  if(S_Area.LedFunExchTimer<=0)
    Led_1_off;
  else
    Led_2_off;
 }

uint32_t Led_1x2_sta(void)
{
  uint32_t sta ;
  if(S_Area.LedFunExchTimer<=0)
  {
    if(Led_1_sta)
	  sta=1;
	else
	  sta=0 ;
   }
  else
  {
    if(Led_2_sta)
	  sta=1;
	else
	  sta=0 ;
   }
  return sta ;
}
//----------  
void  Led_2x1_on(void)
{
  if(S_Area.LedFunExchTimer<=0)
    Led_2_on;
  else
    Led_1_on;
}

void  Led_2x1_off(void)
{
  if(S_Area.LedFunExchTimer<=0)
    Led_2_off;
  else
    Led_1_off;
}

uint32_t Led_2x1_sta(void)
{
  uint32_t sta ;
  if(S_Area.LedFunExchTimer<=0)
  {
    if(Led_2_sta)
	  sta=1;
	else
	  sta=0 ;
   }
  else
  {
    if(Led_1_sta)
	  sta=1;
	else
	  sta=0 ;
   }
  return sta ;
}

//End of the file
