# 汉字字库生成工具

这是一个专门为嵌入式LCD显示系统生成12x12汉字点阵字库的Python工具。

## 功能特点

- 🎯 **专业定制**: 专门为你的字库格式设计
- 🖼️ **可视化预览**: 实时显示12x12点阵效果
- 📝 **即用代码**: 直接生成C语言数组格式
- 🔄 **批量处理**: 支持一次生成多个汉字
- 💾 **多种输出**: 复制到剪贴板或保存到文件

## 安装要求

### Windows系统
1. 确保已安装Python 3.6+
2. 双击运行 `install_requirements.bat` 安装依赖包
3. 或者手动执行：
```bash
pip install pillow numpy
```

### 其他系统
```bash
pip install pillow numpy tkinter
```

## 使用方法

### 1. 启动工具
```bash
python font_generator.py
```

### 2. 选择字体
- 点击"选择字体"按钮选择TTF/TTC字体文件
- 推荐使用宋体(simsun.ttc)或黑体(simhei.ttf)
- 工具会自动尝试加载系统默认中文字体

### 3. 生成单个字符
1. 在"输入汉字"框中输入一个汉字，如"底"
2. 点击"生成字库"或按回车键
3. 左侧会显示12x12点阵预览
4. 右侧会显示生成的C代码

### 4. 批量生成
1. 点击"批量生成"按钮
2. 在弹出窗口中输入多个汉字，如"底座"
3. 点击"生成"按钮
4. 会生成所有字符的完整代码

## 输出格式

工具会生成两部分代码：

### CodePage12.c 条目
```c
{ 0x5E95, 0x5E95, 0x0XXX }/* Segment XXX, 0x0001 Symbols */
```

### Unicode12.c 条目
```c
{{12, 12},{
0x00,    /*  ............  */
0x04,    /*  .....@......  */
0xE2,    /*  @@@...@.....  */
// ... 更多数据
}}
```

## 使用生成的代码

### 1. 更新CodePage12.c
将生成的CodePage条目插入到 `src/Display/CodePage12.c` 的适当位置，按Unicode值排序。

### 2. 更新Unicode12.c
将生成的Unicode条目插入到 `src/Display/Unicode12.c` 的对应索引位置。

### 3. 更新头文件
在 `inc/Display.h` 中更新字库大小：
```c
#define UNICODE12_NUM  XXX   // 增加字符数量
```

### 4. 在代码中使用
```c
// 在GSH_KeyInfoStr数组中添加
{ 0x955E, 0xA75E, 0x2000, 0x2000, 0x01FF }  // 底座
```

## 注意事项

1. **字体选择**: 不同字体生成的点阵效果不同，建议使用清晰的中文字体
2. **索引管理**: 添加新字符时要正确管理CodePage和Unicode数组的索引
3. **字节序**: 生成的Unicode值已经是正确的字节序，可直接使用
4. **预览确认**: 生成前请确认预览效果符合要求

## 故障排除

### 字体加载失败
- 确保选择的是有效的TTF/TTC字体文件
- 检查字体文件是否包含所需的汉字

### 生成的点阵不清晰
- 尝试不同的字体文件
- 12x12像素限制了细节，选择笔画清晰的字体

### 代码格式问题
- 生成的代码已经按照你的项目格式设计
- 可以直接复制粘贴到对应文件中

## 技术支持

如有问题，请检查：
1. Python版本是否3.6+
2. 依赖包是否正确安装
3. 字体文件是否有效
4. 输入的字符是否为有效汉字

---
*工具版本: 1.0*  
*适用于: 12x12点阵LCD字库系统*
