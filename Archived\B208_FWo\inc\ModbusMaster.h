#ifndef __MD_MASTER_FUN_H
#define __MD_MASTER_FUN_H

#include  "stm32f10x.h"
#include  "ThisDevice.h"
#include  "Timer.h"
#include  "UartFun_A.h"
#include  "ModbusRTU_A.h"

#define  HOST_ID        5 //MODBUS monitor computer address
#define  U1_DEV_NUM     1 //total device access number in each access period of UART channel A

extern timer_t U_Timer[U_TIM_NUM]  ;

extern UARTStruct  UA  ;//

extern ModStruct  ModA ;

extern  volatile   uint32_t   OpCtrl ;

extern  volatile   uint32_t   GFlag  ;

extern  SysAreaStruct    S_Area ;//Extern defined an object for system data --in file ".h"

extern  UserAreaStruct   U_Area ;//Extern defined an object for user data

void ModDevComMon(ModStruct *ModX,ComModOpStruct *UXModOp,uint16_t DevNum) ;

void ActiveAntenna(void) ;

#endif   //__MD_MASTER_FUN_H
//--------------------------------


