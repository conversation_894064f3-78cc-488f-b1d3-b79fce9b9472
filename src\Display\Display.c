#include "Display.h"
#include "ThisDevice.h"
#include "Timer.h"
#include "RF_BasicFun.h"

//--------------------------------------------------------------------------------------------------
// Include font files varible define
//--------------------------------------------------------------------------------------------------
extern const sFont6x8 ANSI6x8[] ;
extern const sFont8x8 ANSI8x8[] ;
extern const sFont8x16 ANSI8x16[] ;
extern const G<PERSON><PERSON><PERSON><PERSON>12 CodePage12 ;
extern const sFont12x12 Unicode12[] ;
extern const GCODEPAGE16 CodePage16 ;
extern const sFont16x16 Unicode16[];
extern const sBack back1 ;
extern const sBack back2 ;
//---------------Following variables defined in "SysBasic.c"
extern  UserAreaStruct   U_Area ;//define an object for user data
extern   SysAreaStruct   S_Area ;//define an object for system internal use data

extern  volatile  uint16_t   SYS_STA ;
extern  volatile   int16_t   TxToken ;
extern  volatile  uint32_t    OpCtrl ;
extern  volatile  uint32_t     GFlag ;
extern  void (*PanelLcdPrint_fp)(void) ;
//---------------Following variables defined in "RF_BasicFun.c"
extern  RFC_WorkDS_t     RfDs ;//Main data struct used for RF related function

//---------------Following variables defined in "TimerCounter.c"
extern  tim_count   Timer10[TIM10ms_NUM]  ;//user defined timer(1 tick=10ms)

//---------------
static struct
 {
  signed char xl ;//print window's left limit(X axis,column address)
  signed char xr ;//print window's right limit(X axis,column address)
  signed char yt ;//print window's top limit(Y axis,conver to page address,and remainder)
  signed char yb ;//print window's bottom limit(Y axis,conver to page address,and remainder)
 } window = {0,127,0,63} ;

//-----------
const uint16_t CorpName[6] =
//{ 0x0526,0xfa56,0x7763,0x3575,0x146c,0x0526 } ;//��̽ݵ�����
{0x0526,0x0a4e,0x776d,0xa690,0xc472,0x0526} ;//���Ϻ���ҡ�

const uint16_t WelcomStr[5] = {0x226b,0xce8f,0x7f4f,0x2875,0x01ff} ;//��ӭʹ�ã�

const uint16_t SaftyHint[6] = { 0x0526, 0xE86C, 0x0F61, 0x895B, 0x6851, 0x0526} ;//��ע�ⰲȫ��

const uint16_t CommandHint[3] = {0x7d54 ,0xe44e ,0x1aff} ;//���

const uint16_t RowClrStr12[11] = {0x0808, 0x0808, 0x0808, 0x0808, 0x0808, 0x0808, 0x0808, 0x0808, 0x0808, 0x0808, 0x0808 } ;

const uint16_t GSH_KeyInfoStr[49][5]={
	{ 0xE65D, 0x4764, 0xC281, 0x4D96, 0x01FF },//��ҡ�۽��� 
	{ 0xE65D, 0x4764, 0xC281, 0x4753, 0x01FF },//��ҡ������ 
	{ 0x1154, 0xE65D, 0x7572, 0x155F, 0x01FF },//����ǣ���� 
	{ 0x3B4E, 0x3A67, 0xAD65, 0x3575, 0x01FF },//�����ϵ磡 
	{ 0x5C50, 0x626B, 0x7572, 0x155F, 0x01FF },//ֹͣǣ���� 
	{ 0x1154, 0xF353, 0x7572, 0x155F, 0x01FF },//����ǣ���� 
	{ 0xF353, 0x4764, 0xC281, 0x4753, 0x01FF },//��ҡ������ 
	{ 0xF353, 0x4764, 0xC281, 0x4D96, 0x01FF },//��ҡ�۽���--keycode.7 
	{ 0x2000, 0xFB7F, 0x7598, 0x2000, 0x01FF },//��ҳ  �� 
	{ 0x2000, 0x1154, 0xE65D, 0xDB8F, 0x01FF },//����� �� 
	{ 0x6962, 0x555C, 0x9F52, 0xFD80, 0x3100 },//��չ����1 
	{ 0x2000, 0x6E78, 0x9A5B, 0x2000, 0x01FF },//ȷ��  �� 
	{ 0x2000, 0x2560, 0x5C50, 0x2000, 0x01FF },//��ͣ  �� 
	{ 0x6962, 0x555C, 0x9F52, 0xFD80, 0x3200 },//��չ����2 
	{ 0x2000, 0x1154, 0xF353, 0xDB8F, 0x01FF },//���ҽ� ��
	{ 0x2000, 0x0D59, 0x4D4F, 0x2000, 0x01FF },//��λ  �� --keycode.15
	
	{ 0x08FF, 0x7A7A, 0x09FF, 0x2000, 0x01FF },//���գ� �� 
	{ 0x08FF, 0x7A7A, 0x09FF, 0x2000, 0x01FF },//���գ� �� 
	{ 0x08FF, 0x7A7A, 0x09FF, 0x2000, 0x01FF },//���գ� �� 
	{ 0x0808, 0x0808, 0x0808, 0x0808, 0x0808 },//--Key ID=19
	{ 0x2000, 0xDC8F, 0xA763, 0x2000, 0x01FF },//Զ��  �� 
	{ 0x08FF, 0x7A7A, 0x09FF, 0x2000, 0x01FF },//���գ� �� 
	{ 0x08FF, 0x7A7A, 0x09FF, 0x2000, 0x01FF },//���գ� �� 
	{ 0x08FF, 0x7A7A, 0x09FF, 0x2000, 0x01FF },//���գ� ��--F1+keycode.7
	{ 0x2000, 0xEA81, 0xA852, 0x2000, 0x01FF },//�Զ�  �� 
	{ 0x2000, 0x0768, 0xE65D, 0x2000, 0x01FF },//����  �� 
	{ 0x08FF, 0x7A7A, 0x09FF, 0x2000, 0x01FF },//���գ� �� 
	{ 0x2000, 0xD48F, 0xDE56, 0x2000, 0x01FF },//����  �� 
	{ 0x08FF, 0x7A7A, 0x09FF, 0x2000, 0x01FF },//���գ� �� 
	{ 0x08FF, 0x7A7A, 0x09FF, 0x2000, 0x01FF },//���գ� �� 
	{ 0x2000, 0x0768, 0xF353, 0x2000, 0x01FF },//����  �� 
	{ 0x2000, 0x665B, 0x604E, 0x2000, 0x01FF },//ѧϰ  �� --F1+keycode.15
	
	{ 0x08FF, 0x7A7A, 0x09FF, 0x2000, 0x01FF },//���գ� �� 
	{ 0x08FF, 0x7A7A, 0x09FF, 0x2000, 0x01FF },//���գ� �� 
	{ 0x08FF, 0x7A7A, 0x09FF, 0x2000, 0x01FF },//���գ� �� 
	{ 0x08FF, 0x7A7A, 0x09FF, 0x2000, 0x01FF },//���գ� �� 
	{ 0x08FF, 0x7A7A, 0x09FF, 0x2000, 0x01FF },//���գ� �� 
	{ 0x08FF, 0x7A7A, 0x09FF, 0x2000, 0x01FF },//���գ� �� 
	{ 0x08FF, 0x7A7A, 0x09FF, 0x2000, 0x01FF },//���գ� �� 
	{ 0x08FF, 0x7A7A, 0x09FF, 0x2000, 0x01FF },//���գ� ��--F2+keycode.7 
	{ 0x08FF, 0x7A7A, 0x09FF, 0x2000, 0x01FF },//���գ� �� 
	{ 0x08FF, 0x7A7A, 0x09FF, 0x2000, 0x01FF },//���գ� �� 
	{ 0x08FF, 0x7A7A, 0x09FF, 0x2000, 0x01FF },//���գ� �� 
	{ 0x08FF, 0x7A7A, 0x09FF, 0x2000, 0x01FF },//���գ� �� 
	{ 0x08FF, 0x7A7A, 0x09FF, 0x2000, 0x01FF },//���գ� �� 
	{ 0x08FF, 0x7A7A, 0x09FF, 0x2000, 0x01FF },//���գ� �� 
	{ 0x08FF, 0x7A7A, 0x09FF, 0x2000, 0x01FF },//���գ� �� 
	{ 0x08FF, 0x7A7A, 0x09FF, 0x2000, 0x01FF }, //���գ� ��--F2+keycode.15
	{ 0x945E, 0xA65E, 0x2000, 0x2000, 0x01FF }  //底座    --新增按键显示(使用现有字符)
};

const uint16_t GSH_KeyInfoStr3[48][5] = {
	{ 0xE65D, 0x4764, 0xC281, 0x4D96, 0x01FF },//��ҡ�۽���
	{ 0xE65D, 0x4764, 0xC281, 0x4753, 0x01FF },//��ҡ������
	{ 0x1154, 0xE65D, 0x7572, 0x155F, 0x01FF },//����ǣ����
	{ 0x3B4E, 0x3A67, 0xAD65, 0x3575, 0x01FF },//�����ϵ磡
	{ 0x5C50, 0x626B, 0x7572, 0x155F, 0x01FF },//ֹͣǣ����
	{ 0x1154, 0xF353, 0x7572, 0x155F, 0x01FF },//����ǣ����
	{ 0xF353, 0x4764, 0xC281, 0x4753, 0x01FF },//��ҡ������
	{ 0xF353, 0x4764, 0xC281, 0x4D96, 0x01FF },//��ҡ�۽���--keycode.7
	{ 0x3478, 0x8E78, 0xC281, 0x4753, 0x01FF },//���������
	{ 0x3478, 0x8E78, 0xC281, 0x4D96, 0x01FF },//����۽���
	{ 0x6962, 0x555C, 0x9F52, 0xFD80, 0x3100 },//��չ����1
	{ 0x3575, 0x3A67, 0x3B60, 0x5C50, 0x01FF },//�����ͣ��
	{ 0x7572, 0x155F, 0xCF51, 0x1F90, 0x01FF },//ǣ�����٣�
	{ 0x6962, 0x555C, 0x9F52, 0xFD80, 0x3200 },//��չ����2
	{ 0xEA81, 0xA852, 0x038C, 0xD89A, 0x01FF },//�Զ����ߣ�
	{ 0x7572, 0x155F, 0xA052, 0x1F90, 0x01FF },//ǣ�����٣�--keycode.15
	{ 0xE65D, 0x2163, 0x6471, 0x0690, 0x6C8F },//��ú��ת
	{ 0xE65D, 0x2163, 0x6471, 0x7A98, 0x6C8F },//��ú˳ת
	{ 0x1154, 0xE65D, 0xDB8F, 0x2000, 0x01FF },//�����  ��
											   //  {0x2000, 0x2000, 0x2000, 0x2000, 0x2000},//--Key ID=19
	{ 0x0808, 0x0808, 0x0808, 0x0808, 0x0808 },//--Key ID=19
	{ 0x2F54, 0xA852, 0x7572, 0x155F, 0x01FF },//����ǣ����
	{ 0x1154, 0xF353, 0xDB8F, 0x2000, 0x01FF },//���ҽ�  ��
	{ 0xF353, 0x2163, 0x6471, 0x0690, 0x6C8F },//�ҵ�ú��ת
	{ 0xF353, 0x2163, 0x6471, 0x7A98, 0x6C8F },//�ҵ�ú˳ת--F1+keycode.7
	{ 0x4753, 0x7698, 0xA462, 0x7F67, 0x01FF },//�������壡
	{ 0x4D96, 0x7698, 0xA462, 0x7F67, 0x01FF },//�������壡
	{ 0x08FF, 0x7A7A, 0x09FF, 0x01FF, 0x2000 },//���գ�  ��
	{ 0xD653, 0x886D, 0x01FF, 0x2000, 0x2000 },//ȡ����
	{ 0x0E54, 0x0090, 0x01FF, 0x2000, 0x2000 },//���ˣ�
	{ 0x08FF, 0x7A7A, 0x09FF, 0x01FF, 0x2000 },//���գ�  ��
	{ 0x6E78, 0x9A5B, 0x01FF, 0x2000, 0x2000 },//ȷ����
	{ 0x4D52, 0xDB8F, 0x01FF, 0x2000, 0x2000 },//ǰ����--F1+keycode.15
	{ 0x5C50, 0xE65D, 0x2A62, 0x7252, 0x01FF },//ͣ��ظ
	{ 0x2F54, 0xE65D, 0x2A62, 0x7252, 0x01FF },//����ظ
	{ 0x08FF, 0x7A7A, 0x09FF, 0x01FF, 0x2000 },//���գ�  ��
	{ 0x08FF, 0x7A7A, 0x09FF, 0x01FF, 0x2000 },//���գ�  ��
	{ 0x08FF, 0x7A7A, 0x09FF, 0x01FF, 0x2000 },//���գ�  ��
	{ 0x08FF, 0x7A7A, 0x09FF, 0x01FF, 0x2000 },//���գ�  ��
	{ 0x2F54, 0xF353, 0x2A62, 0x7252, 0x01FF },//���ҽظ
	{ 0x5C50, 0xF353, 0x2A62, 0x7252, 0x01FF },//ͣ�ҽظ--F2+keycode.7
	{ 0x2F54, 0x3478, 0x8E78, 0x3A67, 0x01FF },//���������
	{ 0x5C50, 0x3478, 0x8E78, 0x3A67, 0x01FF },//ͣ�������
	{ 0x08FF, 0x7A7A, 0x09FF, 0x01FF, 0x2000 },//���գ�  ��
	{ 0x08FF, 0x7A7A, 0x09FF, 0x01FF, 0x2000 },//���գ�  ��
	{ 0x5C50, 0xF56C, 0x3575, 0x3A67, 0x01FF },//ͣ�õ����
	{ 0x08FF, 0x7A7A, 0x09FF, 0x01FF, 0x2000 },//���գ�  ��
	{ 0x08FF, 0x7A7A, 0x09FF, 0x01FF, 0x2000 },//���գ�  ��
	{ 0x2F54, 0xF56C, 0x3575, 0x3A67, 0x01FF } //���õ����--F2+keycode.15
};

const uint16_t GSH_KeyInfoStr1[48][5]={//Button name define for ZCZG-1
   {0x1154, 0xE65D, 0x7572, 0x155F, 0x01FF},// ����ǣ����
   {0xE65D, 0x4764, 0xC281, 0x4D96, 0x01FF},// ��ҡ�۽�!
   {0xE65D, 0x4764, 0xC281, 0x4753, 0x01FF},// ��ҡ����!
   {0x2000, 0x3B60, 0x5C50, 0x626B, 0x01FF},// ��ֹͣ!
   {0x7572, 0x155F, 0x5C50, 0x626B, 0x01FF},// ǣ��ֹͣ!
   {0xF353, 0x4764, 0xC281, 0x4753, 0x01FF},// ��ҡ����!
   {0xF353, 0x4764, 0xC281, 0x4D96, 0x01FF},// ��ҡ�۽�!
   {0x1154, 0xF353, 0x7572, 0x155F, 0x01FF},// ����ǣ��!--keycode.7
   {0x4100, 0x7500, 0x7400, 0x6F00, 0x01FF},//  Auto!
   {0x2000, 0x1154, 0xE65D, 0x2000, 0x01FF},// ����!
   {0x6962, 0x555C, 0x9F52, 0xFD80, 0x3100},// ��չ����1
   {0x2000, 0x1154, 0x0B4E, 0x2000, 0x01FF},// ����!
   {0x2000, 0x1154, 0x0A4E, 0x2000, 0x01FF},// ����!
   {0x6962, 0x555C, 0x9F52, 0xFD80, 0x3200},// ��չ����2
   {0x2000, 0x1154, 0xF353, 0x2000, 0x01FF},// ����!
   {0x2000, 0x6E78, 0x9A5B, 0x2000, 0x01FF},// ȷ��!--keycode.15
   {0x08FF, 0x7A7A, 0x09FF, 0x2000, 0x01FF},// ���գ�!
   {0x08FF, 0x7A7A, 0x09FF, 0x2000, 0x01FF},// ���գ�!
   {0x08FF, 0x7A7A, 0x09FF, 0x2000, 0x01FF},// ���գ�!
   {0x0808, 0x0808, 0x0808, 0x0808, 0x0808},// --Key ID=19//  {0x2000, 0x2000, 0x2000, 0x2000, 0x2000},//--Key ID=19
   {0x08FF, 0x7A7A, 0x09FF, 0x2000, 0x01FF},// ���գ�!
   {0x08FF, 0x7A7A, 0x09FF, 0x2000, 0x01FF},// ���գ�!
   {0x08FF, 0x7A7A, 0x09FF, 0x2000, 0x01FF},// ���գ�!
   {0x08FF, 0x7A7A, 0x09FF, 0x2000, 0x01FF},// ���գ�!--F1+keycode.7
   {0x2000, 0x5400, 0x6100, 0x6200, 0x01FF},//   Tab!
   {0x08FF, 0x7A7A, 0x09FF, 0x2000, 0x01FF},// ���գ�!
   {0x08FF, 0x7A7A, 0x09FF, 0x2000, 0x01FF},// ���գ�!
   {0x08FF, 0x7A7A, 0x09FF, 0x2000, 0x01FF},// ���գ�!
   {0x08FF, 0x7A7A, 0x09FF, 0x2000, 0x01FF},// ���գ�!
   {0x08FF, 0x7A7A, 0x09FF, 0x2000, 0x01FF},// ���գ�!
   {0x08FF, 0x7A7A, 0x09FF, 0x2000, 0x01FF},// ���գ�!
   {0x08FF, 0x7A7A, 0x09FF, 0x2000, 0x01FF},// ���գ�!--F1+keycode.15
   {0x08FF, 0x7A7A, 0x09FF, 0x2000, 0x01FF},// ���գ�!
   {0x08FF, 0x7A7A, 0x09FF, 0x2000, 0x01FF},// ���գ�!
   {0x08FF, 0x7A7A, 0x09FF, 0x2000, 0x01FF},// ���գ�!
   {0x08FF, 0x7A7A, 0x09FF, 0x2000, 0x01FF},// ���գ�!
   {0x08FF, 0x7A7A, 0x09FF, 0x2000, 0x01FF},// ���գ�!
   {0x08FF, 0x7A7A, 0x09FF, 0x2000, 0x01FF},// ���գ�!
   {0x08FF, 0x7A7A, 0x09FF, 0x2000, 0x01FF},// ���գ�!
   {0x08FF, 0x7A7A, 0x09FF, 0x2000, 0x01FF},// ���գ�!--F2+keycode.7
   {0x08FF, 0x7A7A, 0x09FF, 0x2000, 0x01FF},// ���գ�!
   {0x08FF, 0x7A7A, 0x09FF, 0x2000, 0x01FF},// ���գ�!
   {0x08FF, 0x7A7A, 0x09FF, 0x2000, 0x01FF},// ���գ�!
   {0x08FF, 0x7A7A, 0x09FF, 0x2000, 0x01FF},// ���գ�!
   {0x08FF, 0x7A7A, 0x09FF, 0x2000, 0x01FF},// ���գ�!
   {0x08FF, 0x7A7A, 0x09FF, 0x2000, 0x01FF},// ���գ�!
   {0x08FF, 0x7A7A, 0x09FF, 0x2000, 0x01FF},// ���գ�!
   {0x08FF, 0x7A7A, 0x09FF, 0x2000, 0x01FF} // ���գ�!--F2+keycode.15
};

const uint16_t GSH_KeyInfoStr2[48][5]={//Button name define for ZCZG Tunneler
   {0x2000, 0xF353, 0x735E, 0xFB79, 0x01FF},//��ƽ�ƣ�
   {0x2000, 0xE65D, 0x735E, 0xFB79, 0x01FF},//��ƽ�ƣ�
   {0x2000, 0x4D52, 0xDB8F, 0x2000, 0x01FF},// ǰ�� ��
   {0x2000, 0xE65D, 0xCB65, 0x6C8F, 0x01FF},//����ת ��
   {0x2000, 0xF353, 0xCB65, 0x6C8F, 0x01FF},//����ת��  
   {0x2000, 0x0E54, 0x0090, 0x2000, 0x01FF},// ���� ��
   {0x2000, 0x2F65, 0x7698, 0x4753, 0x01FF},//֧������  
   {0x2000, 0x2F65, 0x7698, 0x4D96, 0x01FF},//֧������
   {0x2000, 0xD16E, 0xB667, 0x4753, 0x01FF},//��������  
   {0x2000, 0xD16E, 0xB667, 0x4D96, 0x01FF},//���ܽ���  
   {0x2000, 0x8257, 0xF476, 0x2000, 0x01FF},// ��ֱ��  
   {0x2000, 0xBB94, 0x3A67, 0xCD53, 0x01FF},//�������  
   {0x2000, 0xBB94, 0x3A67, 0x636B, 0x01FF},//�������  
   {0x2000, 0x346C, 0x735E, 0x2000, 0x01FF},// ˮƽ��  
   {0x2000, 0xBB94, 0x0090, 0x2000, 0x01FF},// ����� 
   {0x2000, 0xBB94, 0xDB8F, 0x2000, 0x01FF},// ���ˣ� 
   {0x08FF, 0x7A7A, 0x09FF, 0x2000, 0x01FF},// ���գ�!  
   {0x08FF, 0x7A7A, 0x09FF, 0x2000, 0x01FF},// ���գ�!  
   {0x08FF, 0x7A7A, 0x09FF, 0x2000, 0x01FF},// ���գ�!  
   {0x0808, 0x0808, 0x0808, 0x0808, 0x0808},// --Key ID=19//  {0x2000, 0x2000, 0x2000, 0x2000, 0x2000},//--Key ID=19  
   {0x08FF, 0x7A7A, 0x09FF, 0x2000, 0x01FF},// ���գ�!  
   {0x08FF, 0x7A7A, 0x09FF, 0x2000, 0x01FF},// ���գ�!  
   {0x08FF, 0x7A7A, 0x09FF, 0x2000, 0x01FF},// ���գ�!  
   {0x08FF, 0x7A7A, 0x09FF, 0x2000, 0x01FF},// ���գ�!--F1+keycode.7  
   {0x08FF, 0x7A7A, 0x09FF, 0x2000, 0x01FF},// ���գ�!  
   {0x08FF, 0x7A7A, 0x09FF, 0x2000, 0x01FF},// ���գ�!  
   {0x08FF, 0x7A7A, 0x09FF, 0x2000, 0x01FF},// ���գ�!  
   {0x08FF, 0x7A7A, 0x09FF, 0x2000, 0x01FF},// ���գ�!  
   {0x08FF, 0x7A7A, 0x09FF, 0x2000, 0x01FF},// ���գ�!  
   {0x08FF, 0x7A7A, 0x09FF, 0x2000, 0x01FF},// ���գ�!  
   {0x08FF, 0x7A7A, 0x09FF, 0x2000, 0x01FF},// ���գ�!  
   {0x08FF, 0x7A7A, 0x09FF, 0x2000, 0x01FF},// ���գ�!--F1+keycode.15  
   {0x08FF, 0x7A7A, 0x09FF, 0x2000, 0x01FF},// ���գ�!
   {0x08FF, 0x7A7A, 0x09FF, 0x2000, 0x01FF},// ���գ�!
   {0x08FF, 0x7A7A, 0x09FF, 0x2000, 0x01FF},// ���գ�!
   {0x08FF, 0x7A7A, 0x09FF, 0x2000, 0x01FF},// ���գ�!
   {0x08FF, 0x7A7A, 0x09FF, 0x2000, 0x01FF},// ���գ�!
   {0x08FF, 0x7A7A, 0x09FF, 0x2000, 0x01FF},// ���գ�!
   {0x08FF, 0x7A7A, 0x09FF, 0x2000, 0x01FF},// ���գ�!
   {0x08FF, 0x7A7A, 0x09FF, 0x2000, 0x01FF},// ���գ�!--F2+keycode.7
   {0x08FF, 0x7A7A, 0x09FF, 0x2000, 0x01FF},// ���գ�!
   {0x08FF, 0x7A7A, 0x09FF, 0x2000, 0x01FF},// ���գ�!
   {0x08FF, 0x7A7A, 0x09FF, 0x2000, 0x01FF},// ���գ�!
   {0x08FF, 0x7A7A, 0x09FF, 0x2000, 0x01FF},// ���գ�!
   {0x08FF, 0x7A7A, 0x09FF, 0x2000, 0x01FF},// ���գ�!
   {0x08FF, 0x7A7A, 0x09FF, 0x2000, 0x01FF},// ���գ�!
   {0x08FF, 0x7A7A, 0x09FF, 0x2000, 0x01FF},// ���գ�!
   {0x08FF, 0x7A7A, 0x09FF, 0x2000, 0x01FF} // ���գ�!--F2+keycode.15
};

//------------
const uint16_t StausAlarmStr[3][10]={
  {0x3575, 0xCF91, 0x0D4E, 0xB38D, 0x0CFF, 0xF78B, 0x6263, 0x4551, 0x3575, 0x606C}, //�������㣬�뻻����
  {0xD153, 0x045C, 0x3A67, 0xA763, 0x3652, 0x1662, 0x858D, 0xFA51, 0x0383, 0xF456}, //��������ƻ򳬳���Χ
  {0xD153, 0x045C, 0x3A67, 0xDB8F, 0x6551, 0x0177, 0x3575, 0xB672, 0x0160, 0x01ff}  //���������ʡ��״̬��
} ;
const uint16_t MachSpeedStr[5]={0xBE8B, 0x9A5B,0x1F90, 0xA65E, 0x1AFF};//�趨�ٶ�:

const uint16_t S260_DevStaStr[12][4]={
   { 0x4C88, 0x708D, 0x216A, 0x0F5F},//����ģʽ 0
   { 0x9863, 0xDB8F, 0x216A, 0x0F5F},//���ģʽ
   { 0xB08B, 0xC65F, 0x2D4E, 0x2620},//�����С� 2
   { 0xEA81, 0xA852, 0x216A, 0x0F5F},//�Զ�ģʽ
   { 0x4B62, 0xA852, 0x216A, 0x0F5F},//�ֶ�ģʽ 4
   { 0xEA81, 0xA852, 0x2F54, 0x2875},//�Զ�����
   { 0xEA81, 0xA852, 0x5C50, 0x626B},//�Զ�ֹͣ 6
   { 0x4151, 0xB88B, 0xA562, 0x668B},//��������
   { 0x4151, 0xB88B, 0x2A62, 0x2F54},//�������� 8
   { 0x4151, 0xB88B, 0x2A62, 0x384F},//��������
   { 0x4151, 0xB88B, 0x2A62, 0x297F},//�������� 10
   { 0x2000, 0x2000, 0x2000, 0x2000} //Blank line     
} ;
//---------------------------------------
const uint16_t S160T1_KeyInfoStr[32][5]=
{
   { 0x2A62, 0x7252, 0xE65D, 0x4664, 0x01FF},//�ظ���ڣ� 
   { 0x668B, 0xC394, 0x005F, 0x2F54, 0x01FF},//���忪���� 
   { 0x004E, 0xD08F, 0xCD53, 0x6C8F, 0x01FF},//һ�˷�ת�� 
   { 0x2A62, 0x7252, 0x4753, 0x01FF, 0x2000},//�ظ�����  
   { 0x2A62, 0x7252, 0x4D96, 0x01FF, 0x2000},//�ظ��  
   { 0x004E, 0xD08F, 0x636B, 0x6C8F, 0x01FF},//һ����ת�� 
   { 0xEA81, 0xA852, 0xCD64, 0x5C4F, 0x01FF},//�Զ������� 
   { 0x2A62, 0x7252, 0xF353, 0x4664, 0x01FF},//�ظ��Ұڣ�--keycode.7 
   { 0xF294, 0x7F67, 0x4753, 0x01FF, 0x2000},//��������  
   { 0xF294, 0x7F67, 0x4D96, 0x01FF, 0x2000},//���彵��  
   { 0x6E78, 0xA48B, 0x4600, 0x3100, 0x01FF},//ȷ��F1�� 
   { 0x2F65, 0x9164, 0x4D96, 0x01FF, 0x2000},//֧�Ž���  
   { 0x2F65, 0x9164, 0x4753, 0x01FF, 0x2000},//֧������  
   { 0x0090, 0xFA51, 0x4600, 0x3200, 0x01FF},//�˳�F2�� 
   { 0x1F66, 0x6E8F, 0xCD53, 0x6C8F, 0x01FF},//���ַ�ת�� 
   { 0x1F66, 0x6E8F, 0x636B, 0x6C8F, 0x01FF},//������ת��--keycode.15 
   { 0x08FF, 0x7A7A, 0x09FF, 0x2000, 0x01FF},//���գ� �� 
   { 0x668B, 0xC394, 0x7351, 0xED95, 0x01FF},//����رգ� 
   { 0x08FF, 0x7A7A, 0x09FF, 0x2000, 0x01FF},//���գ� �� 
   { 0x08FF, 0x7A7A, 0x09FF, 0x2000, 0x01FF},//���գ� �� 
   { 0x08FF, 0x7A7A, 0x09FF, 0x2000, 0x01FF},//���գ� �� 
   { 0x08FF, 0x7A7A, 0x09FF, 0x2000, 0x01FF},//���գ� �� 
   { 0x6590, 0xA763, 0xCD64, 0x5C4F, 0x01FF},//ң�ز����� 
   { 0x08FF, 0x7A7A, 0x09FF, 0x2000, 0x01FF},//���գ� ��--keycode.23 
   { 0x08FF, 0x7A7A, 0x09FF, 0x2000, 0x01FF},//���գ� �� 
   { 0x08FF, 0x7A7A, 0x09FF, 0x2000, 0x01FF},//���գ� �� 
   { 0x08FF, 0x7A7A, 0x09FF, 0x2000, 0x01FF},//���գ� �� 
   { 0x08FF, 0x7A7A, 0x09FF, 0x2000, 0x01FF},//���գ� �� 
   { 0x08FF, 0x7A7A, 0x09FF, 0x2000, 0x01FF},//���գ� �� 
   { 0x0808, 0x0808, 0x0808, 0x0808, 0x0808},//Blank line --Key ID=29
   { 0x2A62, 0x7252, 0xDE56, 0x297F, 0x01FF},//�ظ������ 
   { 0x2A62, 0x7252, 0x384F, 0xFA51, 0x01FF} //�ظ������--keycode.31 
};

const uint16_t S160T2_KeyInfoStr[32][5]=
{
   { 0x0759, 0x2875, 0x01FF, 0x2000, 0x2000},//���ã�   
   { 0x8C4E, 0xD08F, 0x5C50, 0x01FF, 0x2000},//����ͣ��  
   { 0x1F90, 0xA65E, 0x2B00, 0x01FF, 0x2000},//�ٶ�+��  
   { 0x2A62, 0x7252, 0xD89A, 0x2F54, 0x01FF},//�ظ������ 
   { 0x2A62, 0x7252, 0x4E4F, 0x2F54, 0x01FF},//�ظ������ 
   { 0x1F90, 0xA65E, 0x2D00, 0x01FF, 0x2000},//�ٶ�-��  
   { 0xB96C, 0xF56C, 0x5C50, 0x01FF, 0x2000},//�ͱ�ͣ��  
   { 0xCE98, 0x3A67, 0x5C50, 0x01FF, 0x2000},//���ͣ��--keycode.7  
   { 0xE65D, 0x4C88, 0x708D, 0xDB8F, 0x01FF},//�����߽��� 
   { 0xE65D, 0x4C88, 0x708D, 0x0090, 0x01FF},//�������ˣ� 
   { 0x2C7B, 0x8C4E, 0x9F52, 0xFD80, 0x01FF},//�ڶ����ܣ� 
   { 0x277D, 0x2560, 0x5C50, 0x626B, 0x01FF},//����ֹͣ�� 
   { 0x2A62, 0x7252, 0x5C50, 0x626B, 0x01FF},//�ظ�ֹͣ�� 
   { 0x0D59, 0x4D4F, 0x01FF, 0x2000, 0x2000},//��λ��   
   { 0xF353, 0x4C88, 0x708D, 0x0090, 0x01FF},//�������ˣ� 
   { 0xF353, 0x4C88, 0x708D, 0xDB8F, 0x01FF},//�����߽���--keycode.15 
   { 0x08FF, 0x7A7A, 0x09FF, 0x2000, 0x01FF},//���գ� �� 
   { 0x8C4E, 0xD08F, 0x2F54, 0xA852, 0x01FF},//���������� 
   { 0x08FF, 0x7A7A, 0x09FF, 0x2000, 0x01FF},//���գ� �� 
   { 0x08FF, 0x7A7A, 0x09FF, 0x2000, 0x01FF},//���գ� �� 
   { 0x08FF, 0x7A7A, 0x09FF, 0x2000, 0x01FF},//���գ� �� 
   { 0x08FF, 0x7A7A, 0x09FF, 0x2000, 0x01FF},//���գ� �� 
   { 0xB96C, 0xF56C, 0x2F54, 0xA852, 0x01FF},//�ͱ������� 
   { 0xCE98, 0x3A67, 0x2F54, 0xA852, 0x01FF},//���������--keycode.23 
   { 0x08FF, 0x7A7A, 0x09FF, 0x2000, 0x01FF},//���գ� �� 
   { 0x08FF, 0x7A7A, 0x09FF, 0x2000, 0x01FF},//���գ� �� 
   { 0x08FF, 0x7A7A, 0x09FF, 0x2000, 0x01FF},//���գ� �� 
   { 0x08FF, 0x7A7A, 0x09FF, 0x2000, 0x01FF},//���գ� �� 
   { 0x08FF, 0x7A7A, 0x09FF, 0x2000, 0x01FF},//���գ� �� 
   { 0x0808, 0x0808, 0x0808, 0x0808, 0x0808},//Blank line   --Key ID=29
   { 0x08FF, 0x7A7A, 0x09FF, 0x2000, 0x01FF},//���գ� �� 
   { 0x08FF, 0x7A7A, 0x09FF, 0x2000, 0x01FF} //���գ� ��--keycode.31 
};
//---------------------------------------
const uint16_t S260T1_KeyInfoStr[19][5]={
 {0xF294, 0x7F67, 0x4D96, 0x0B4E, 0x2000},//���彵�� 0
 {0xF294, 0x7F67, 0x4753, 0x778D, 0x2000},//�������� 
 {0x0D59, 0x4D4F, 0x2000, 0x2000, 0x2000},//��λ   2
 {0x2F65, 0x9164, 0x4753, 0x778D, 0x2000},//֧������ 
 {0x2F65, 0x9164, 0x4D96, 0x0B4E, 0x2000},//֧�Ž��� 4
 {0xD153, 0xE14F, 0xF753, 0x2000, 0x2000},//���ź�  
 {0x1F66, 0x6E8F, 0x636B, 0x6C8F, 0x2000},//������ת 6
 {0x1F66, 0x6E8F, 0xCD53, 0x6C8F, 0x2000},//���ַ�ת 
 {0x4B62, 0xA852, 0x216A, 0x0F5F, 0x2000},//�ֶ�ģʽ 8
 {0x2A62, 0x7252, 0xE65D, 0x6C8F, 0x2000},//�ظ���ת 
 {0x9E58, 0xA052, 0x1F90, 0xA65E, 0x2000},//�����ٶ� 10
 {0x2A62, 0x7252, 0x4D96, 0x2000, 0x2000},//�ظ  
 {0x2A62, 0x7252, 0x4753, 0x2000, 0x2000},//�ظ���  12
 {0xCF51, 0x6261, 0x1F90, 0xA65E, 0x2000},//�����ٶ� 
 {0x2A62, 0x7252, 0xF353, 0x6C8F, 0x2000},//�ظ���ת 14
 {0xEA81, 0xA852, 0x216A, 0x0F5F, 0x2F54},//�Զ�ģʽ��
 {0xEA81, 0xA852, 0x216A, 0x0F5F, 0x2000},//�Զ�ģʽ 16
 {0xEA81, 0xA852, 0x216A, 0x0F5F, 0x5C50},//�Զ�ģʽͣ
 {0x2000, 0x2000, 0x2000, 0x2000, 0x2000} //Blank line   --Key ID=18
};

const uint16_t S260T2_KeyInfoStr[17][5]={
  { 0x2A62, 0x7252, 0x5C50, 0x626B, 0x2000}, //�ظ�ֹͣ 0
  { 0x2A62, 0x7252, 0x2F54, 0xA852, 0x2000}, //�ظ����� 
  { 0xB96C, 0xF56C, 0x2F54, 0xA852, 0x2000}, //�ͱ����� 2
  { 0x8C4E, 0xD08F, 0x2F54, 0xA852, 0x2000}, //��������
  { 0x8C4E, 0xD08F, 0x5C50, 0x626B, 0x2000}, //����ֹͣ 4
  { 0xB96C, 0xF56C, 0x5C50, 0x626B, 0x2000}, //�ͱ�ֹͣ
  { 0xCE98, 0x3A67, 0x2F54, 0xA852, 0x2000}, //������� 6
  { 0xCE98, 0x3A67, 0x5C50, 0x626B, 0x2000}, //���ֹͣ
  { 0x004E, 0xD08F, 0x636B, 0x204F, 0x2000}, //һ������ 8
  { 0x4C88, 0x708D, 0xE65D, 0x6C8F, 0x2000}, //������ת
  { 0xFB7F, 0x7598, 0x2000, 0x2000, 0x2000}, //��ҳ     10
  { 0x4C88, 0x708D, 0x0E54, 0x0090, 0x2000}, //���ߺ���
  { 0x4C88, 0x708D, 0x4D52, 0xDB8F, 0x2000}, //����ǰ�� 12
  { 0x277D, 0x2560, 0x5C50, 0x626B, 0x2000}, //����ֹͣ
  { 0x4C88, 0x708D, 0xF353, 0x6C8F, 0x2000}, //������ת 14
  { 0x004E, 0xD08F, 0xCD53, 0x6C8F, 0x2000}, //һ�˷�ת
  { 0x2000, 0x2000, 0x2000, 0x2000, 0x2000}  //Blank line  --Key ID=16
};

const STR_INFO GSH_LCD_LAYOUT[18]={
//  x  , y , W , H , Str len,Front
  { 0  ,11 ,24 ,12 ,  2 , UINCODE_12}, //A0, Battery status area
  { 25 ,11 ,24 ,12 ,  2 , UINCODE_12}, //A1, Sync status area
  { 50 ,11 ,24 ,12 ,  2 , UINCODE_12}, //A2, Operation confirm status area
  { 74 ,11 ,12 ,12 ,  1 , UINCODE_12}, //A3, RF TX/RX status area

  { 86  ,11 ,24 ,12 ,  2 , UINCODE_12}, //A4, RSSI indicate area
  { 114 ,11 ,12  ,12 ,  1 , UINCODE_12}, //A5, Control Channel indicate,"L" or "R"
  
  { 0   ,63 ,36 ,12 ,  3 , UINCODE_12}, //A6, Fixed Key Command indicator
  { 36  ,63 ,75 ,12 ,  5 , UINCODE_12+REV_DIS}, //A7,Current Key ID display W 60

  { 0 ,31 ,60  ,16 ,  5 , UINCODE_12}, //A8, 
  { 0 ,47 ,60  ,16 ,  5 , UINCODE_12}, //A9,

  { 61  ,31 ,64  ,16 ,  8 , A_16_0D_R}, //A10,
  { 61  ,47 ,64  ,16 ,  8 , A_16_0D_R}, //A11,
  
  { 120 ,47 ,8   ,16 ,  1 , A_16_0D_R}, //A12,
  { 127 ,47 ,32  ,16 , 10 , A_16_0D_R+REV_DIS},  // A13 
  { 120  ,31 ,100 ,16 ,  12 , A_16_0D_R},// A14 ,Rx status measured RSSI in dBm--For debug
  { 120  ,47 ,100 ,16 ,  12 , A_16_3D_R}, // A15 ,Tx status measured battery voltage in V--For debug
  //--------------
  { 43 ,27 ,80  ,12 ,  5 , UINCODE_12},// Welcom
  { 31 ,43 ,96  ,12 ,  6 , UINCODE_12} // Be safty hint
} ;

const STR_INFO  S260T1_LCD_LAYOUT[20]={
//  x  , y , W , H , Str len,Front
  { 0  ,11 ,24 ,12 ,  2 , UINCODE_12}, //Battery status area, use-u12_str[0],A0
  { 49 ,11 ,24 ,12 ,  2 , UINCODE_12}, //Operation confirm area, use-u12_str[1]
  { 25 ,11 ,24 ,12 ,  1 , UINCODE_12}, //Key operation confirm status area, use-u12_str[2]
  { 73 ,11 ,24 ,12 ,  2 , UINCODE_12}, //Flag 2 status area
  
  { 103,11 ,24 ,12 ,  2 , UINCODE_12}, //RSSI status,use-u12_str[4] ,A4
  { 3  ,39 ,63 ,12 ,  5 , UINCODE_12}, //A5 ,Machine speed setting fixed flag
  { 3  ,51 ,60 ,12 ,  5 , UINCODE_12}, //A6 ,First operating Key name
  { 123  ,51 ,60 ,12 ,  5 , UINCODE_12_R}, //A7,Second operating Key name

  { 0  ,63 ,120 ,12 ,  10 , UINCODE_12+REV_DIS}, //A8,Staus Alarm output
  { 64 ,39 ,60  ,16 ,  5 , A_8_0D}, //A9,Machine speed current value 
  { 116 ,47 ,120  ,16 ,  12 , A_8_0D_R}, // A10(No use)
  { 0 ,27 ,60  ,16 ,  4 , UINCODE_12}, //A11,for "Automatic/Manual"Mode indicate 

  { 64 ,27 ,60 ,12 ,  4 , UINCODE_12}, // A12,for "In memory cut"mode indicate 
  { 4 ,27 ,60 ,12 ,  4 , UINCODE_12}, // A13,mannual/auto mode flag
  { 127 ,27 ,60 ,12 ,  4 , UINCODE_12_R}, // A14,"Cutter shrink/protrude"for T1, "Automatic Run/Stop" mode indicate D2
  { 91 ,11 ,12 ,12 ,  1 , UINCODE_12}, // A15 print in Flag 2 status area for Tx/Rx indicate,use -u12_str[5]
  //--------------
  { 15 ,31 ,96  ,16 ,  6 , UINCODE_16},// Corp Name
  { 3 ,27 ,60  ,12 ,  5 , UINCODE_12}, // Welcom
  { 120  ,31 ,100 ,16 ,  12 , A_16_0D_R},// For A14 ,Rx Test status measured RSSI in dBm
  { 120  ,47 ,100 ,16 ,  12 , A_16_3D_R}  // For A15 ,Tx Test status measured battery voltage in V
} ;

const STR_INFO  S260T2_LCD_LAYOUT[20]={
//  x  , y , W , H , Str len,Front
  { 0  ,11 ,24 ,12 ,  2 , UINCODE_12}, //Battery status area, use-u12_str[0],A0
  { 49 ,11 ,24 ,12 ,  2 , UINCODE_12}, //Operation confirm area, use-u12_str[1]
  { 25 ,11 ,24 ,12 ,  1 , UINCODE_12}, //Key operation confirm status area, use-u12_str[2]
  { 73 ,11 ,24 ,12 ,  2 , UINCODE_12}, //Flag 2 status area
  
  { 103,11 ,24 ,12 ,  2 , UINCODE_12}, //RSSI status,use-u12_str[4] ,A4
  { 0  ,27 ,60 ,12 ,  5 , UINCODE_12}, //A5 ,Machine speed setting fixed flag for D1
  { 3  ,51 ,60 ,12 ,  5 , UINCODE_12}, //A6 ,First operating Key name
  { 123  ,51 ,60 ,12 ,  5 , UINCODE_12_R}, //A7,Second operating Key name

  { 0  ,63 ,120 ,12 ,  10 , UINCODE_12+REV_DIS}, //A8,Staus Alarm output
  { 60 ,23 ,60  ,16 ,  5 , A_8_0D}, //A9,Machine speed current value for D1 
  { 116 ,47 ,120  ,16 ,  12 , A_8_0D_R}, // A10(No use)
  { 0 ,27 ,60  ,16 ,  4 , UINCODE_12}, //A11,for "Automatic/Manual"Mode indicate 

  { 127 ,27 ,60 ,12 ,  4 , UINCODE_12_R}, // A12,for "In memory cut"mode indicate 
  { 8 ,39 ,60 ,12 ,  4 , UINCODE_12}, // A13,"Sound alarm/cutter start"for D1, "Walk/Cut" mode indicate D2
  { 119 ,39 ,60 ,12 ,  4 , UINCODE_12_R}, // A14,"Cutter shrink/protrude"for D1, "Automatic Run/Stop" mode indicate D2
  { 91 ,11 ,12 ,12 ,  1 , UINCODE_12}, // A15 print in Flag 2 status area for Tx/Rx indicate,use -u12_str[5]
  //--------------
  { 15 ,31 ,96  ,16 ,  6 , UINCODE_12},// Be safty hint
  { 31 ,27 ,80  ,16 ,  5 , UINCODE_16},// Welcom
  { 120  ,31 ,100 ,16 ,  12 , A_16_0D_R},// For A14 ,Rx Test status measured RSSI in dBm
  { 120  ,47 ,100 ,16 ,  12 , A_16_3D_R}  // For A15 ,Tx Test status measured battery voltage in V
} ;

//------------------------------------------------------------------------------
void LcdWelcomPrint(void)
{
  SetLayoutOnLCD((STR_INFO *)&GSH_LCD_LAYOUT[16]);
  U_Area.u_pcb.str.uc_p=WelcomStr ;//Welcom string unicode code
  LcdPrintStr(&U_Area.u_pcb) ;//call string print function
}

void LcdSaftyHintPrint(void)
{
  SetLayoutOnLCD((STR_INFO *)&GSH_LCD_LAYOUT[17]);
  U_Area.u_pcb.str.uc_p=SaftyHint ;//Be safty hint unicode code
  LcdPrintStr(&U_Area.u_pcb) ;//call string print function
}
//-----------------------------------------------------------------------------
void LcdPrintDaemon(void)
{
 uint16_t tmp,bit_p_l,bit_p_r ;

 //if(U_Area.LcdCtrl.Set.Req==0) return ;//no LCD print(display) request exist
 if((U_Area.LcdCtrl.Set.Req==0)&&((GFlag&(GF_MAREA1_V_U_OK+GF_MAREA2_V_U_OK))==(GF_MAREA1_V_U_OK+GF_MAREA2_V_U_OK))) return ;//no LCD print(display) request exist
 if(U_Area.LcdCtrl.Check.ModuleInitOk==0)return ;//wait the LCD module  complete initialization before perform print function
 if(U_Area.LcdCtrl.Check.DisplayOn==0)
 {
  if(U_Area.LcdCtrl.Check.SleepMode==0)
  {
    LcdSendCommand (0xaf) ;//Turn on the display
    U_Area.LcdCtrl.Check.DisplayOn=1 ;
   }
  else
    return ;//The lcd in sleep mode,print any thing is useless
  }
 bit_p_l=0x0001 ;
 bit_p_r=0x8000 ;
 for(tmp=0;tmp<8 ;tmp++)
 {
   if(U_Area.LcdCtrl.Set.Req&bit_p_l)
   {
     U_Area.LcdCtrl.PRINT_STAGE=(tmp<<1) ;
     break ;
   }
   else if(U_Area.LcdCtrl.Set.Req&bit_p_r)
   {
     U_Area.LcdCtrl.PRINT_STAGE=((15-tmp)<<1) ;
     break ;
   }
  bit_p_l<<=1 ;
  bit_p_r>>=1 ;
 }
 (*PanelLcdPrint_fp)() ;//Call LCD screen print function
}
//----
void ClrAlarmHintArea(void)
{
  U_Area.u_pcb.info.x      = 0 ;
  U_Area.u_pcb.info.y      = 63 ;
  U_Area.u_pcb.info.width  = 128 ;
  U_Area.u_pcb.info.height = 12 ;
  U_Area.u_pcb.info.len    = 11 ;
  U_Area.u_pcb.info.ctrl.b = UINCODE_12 ;
  U_Area.u_pcb.str.uc_p    = RowClrStr12 ;//Blank unicode code string
  ClrPrintedStr(&U_Area.u_pcb) ;//Clear the area
}

void AHAreaPrintDeamon(void)
{
	switch(AHAreaStaB){
		case 0://in blank idle status
		
		break ;
		case 1://have Key operation hint output
		if(S_Area.NRT_KeyCodeRaw.cw==0)
		{
			if(U_Area.ShrMachDat.MachSta != 0)
			{
				S_Area.PreAlarmSta = 0 ;//Force previous AlarmSta to 0
			 }
			else
			{
				AHAreaStaB = 0;
			 }
		}
		break ;
		case 2://have Alarm/status information output
		
		break ;
		default:{ClrAlarmHintArea();}
	}
}

//---------------------------------------------------------------------------------------
void  Shr_LCD_print(void) //LCD screen print function for shearer 
{
 switch(U_Area.LcdCtrl.PRINT_STAGE)
 {
   case 0 ://Print_A0
     if(U_Area.LcdCtrl.Set.Print_A0)
     {
      U_Area.LcdCtrl.Set.Print_A0=0 ;//reset the request falg
      SetLayoutOnLCD((STR_INFO *)&GSH_LCD_LAYOUT[0]);
      U_Area.u_pcb.str.uc_p=U_Area.uc_str[0] ;//battery status unicode code
      if(U_Area.LcdCtrl.C_Set.Print_A0)
      {
        U_Area.LcdCtrl.C_Set.Print_A0=0 ;
        ClrPrintedStr(&U_Area.u_pcb) ;//Clear the area
       }
      else
        LcdPrintStr(&U_Area.u_pcb) ;//call string print function
      U_Area.LcdCtrl.Test.LCD_A0_OK=1 ;//set print Ok flag
     }
     break ;
   case 2 ://Print_A1
     if(U_Area.LcdCtrl.Set.Print_A1)
     {
      U_Area.LcdCtrl.Set.Print_A1=0 ;//reset the request falg
      SetLayoutOnLCD((STR_INFO *)&GSH_LCD_LAYOUT[1]);
      U_Area.u_pcb.str.uc_p=U_Area.uc_str[1] ;//Sync status unicode code
      if(U_Area.LcdCtrl.C_Set.Print_A1)
      {
        U_Area.LcdCtrl.C_Set.Print_A1=0 ;
        ClrPrintedStr(&U_Area.u_pcb) ;//Clear the area
       }
      else
        LcdPrintStr(&U_Area.u_pcb) ;//call string print function
      U_Area.LcdCtrl.Test.LCD_A1_OK=1 ;//set print Ok flag
     }
     break ;
   case 4 ://Print_A2
     if(U_Area.LcdCtrl.Set.Print_A2)
     {
      U_Area.LcdCtrl.Set.Print_A2=0 ;//reset the request falg
      SetLayoutOnLCD((STR_INFO *)&GSH_LCD_LAYOUT[2]);
      U_Area.u_pcb.str.uc_p=U_Area.uc_str[2] ;//Operation confirm status unicode code
      if(U_Area.LcdCtrl.C_Set.Print_A2)
      {
        U_Area.LcdCtrl.C_Set.Print_A2=0 ;
        ClrPrintedStr(&U_Area.u_pcb) ;//Clear the area
       }
      else
        LcdPrintStr(&U_Area.u_pcb) ;//call string print function
      U_Area.LcdCtrl.Test.LCD_A2_OK=1 ;//set print Ok flag
     }
     break ;
   case 6 ://Print_A3
     if(U_Area.LcdCtrl.Set.Print_A3)
     {
      U_Area.LcdCtrl.Set.Print_A3=0 ;//reset the request falg
      SetLayoutOnLCD((STR_INFO *)&GSH_LCD_LAYOUT[3]);
      U_Area.u_pcb.str.uc_p=U_Area.uc_str[3] ;//RF Tx/RX status unicode code
      if(U_Area.LcdCtrl.C_Set.Print_A3)
      {
        U_Area.LcdCtrl.C_Set.Print_A3=0 ;
        ClrPrintedStr(&U_Area.u_pcb) ;//Clear the area
       }
      else
        LcdPrintStr(&U_Area.u_pcb) ;//call string print function
      U_Area.LcdCtrl.Test.LCD_A3_OK=1 ;//set print Ok flag
     }
     break ;
   case 8 ://Print_A4
     if(U_Area.LcdCtrl.Set.Print_A4)
     {
      U_Area.LcdCtrl.Set.Print_A4=0 ;//reset the request falg
      SetLayoutOnLCD((STR_INFO *)&GSH_LCD_LAYOUT[4]);
      U_Area.u_pcb.str.uc_p=U_Area.uc_str[4] ;//RSSI indicator unicode
      if(U_Area.LcdCtrl.C_Set.Print_A4)
      {
        U_Area.LcdCtrl.C_Set.Print_A4=0 ;
        ClrPrintedStr(&U_Area.u_pcb) ;//Clear the area
       }
      else
        LcdPrintStr(&U_Area.u_pcb) ;//call string print function
      U_Area.LcdCtrl.Test.LCD_A4_OK=1 ;//set print Ok flag
     }
     break ;
   case 10 ://Print_A5
     if(U_Area.LcdCtrl.Set.Print_A5)
     {
      U_Area.LcdCtrl.Set.Print_A5=0 ;//reset the request falg
      SetLayoutOnLCD((STR_INFO *)&GSH_LCD_LAYOUT[5]);
      U_Area.u_pcb.str.uc_p=U_Area.uc_str[5] ;//Control channel unicode code
      if(U_Area.LcdCtrl.C_Set.Print_A5)
      {
        U_Area.LcdCtrl.C_Set.Print_A5=0 ;
        ClrPrintedStr(&U_Area.u_pcb) ;//Clear the area
       }
      else
        LcdPrintStr(&U_Area.u_pcb) ;//call string print function
      U_Area.LcdCtrl.Test.LCD_A5_OK=1 ;//set print Ok flag
     }
     break ;
   case 12 ://Print_A6
     if(U_Area.LcdCtrl.Set.Print_A6)
     {
      U_Area.LcdCtrl.Set.Print_A6=0 ;//reset the request falg
      SetLayoutOnLCD((STR_INFO *)&GSH_LCD_LAYOUT[6]);
      U_Area.u_pcb.str.uc_p=CommandHint ;//U_Area.uc_str[6] ;Key comment fixed unicode string
      if(GFlag&GF_IN_SETUP_STA)
      {
        U_Area.LcdCtrl.C_Set.Print_A6=0 ;
        break ;//No Print
       }
      if(U_Area.LcdCtrl.C_Set.Print_A6)
      {
        U_Area.LcdCtrl.C_Set.Print_A6=0 ;
        ClrPrintedStr(&U_Area.u_pcb) ;//Clear the area
       }
      else
	  {	  
        LcdPrintStr(&U_Area.u_pcb) ;//call string print function
	   }
      U_Area.LcdCtrl.Test.LCD_A6_OK=1 ;//set print Ok flag
     }
     break ;
   case 14 ://Print_A7
     if(U_Area.LcdCtrl.Set.Print_A7)
     {
      U_Area.LcdCtrl.Set.Print_A7=0 ;//reset the request falg
      SetLayoutOnLCD((STR_INFO *)&GSH_LCD_LAYOUT[7]);
		 
#ifdef  SHR_S160_MODE
	  if(Def_CRC_Seed==RIGHT_CRC_SEED)	 
	    U_Area.u_pcb.str.uc_p=S160T2_KeyInfoStr[U_Area.KeyDisID_1st];//U_Area.uc_str[7]; Current Key ID display unicode string for right side
	  else
	    U_Area.u_pcb.str.uc_p=S160T1_KeyInfoStr[U_Area.KeyDisID_1st];//U_Area.uc_str[7]; Current Key ID display unicode string for left side
      if(U_Area.KeyDisID_1st==29)
        U_Area.u_pcb.info.ctrl.b=UINCODE_12 ;//Not to do reverse display
#else
	  U_Area.u_pcb.str.uc_p=GSH_KeyInfoStr[U_Area.KeyDisID_1st];//U_Area.uc_str[7]; Current Key ID display unicode string
      if(U_Area.KeyDisID_1st==19)
        U_Area.u_pcb.info.ctrl.b=UINCODE_12 ;//Not to do reverse display
#endif
      if(GFlag&GF_IN_SETUP_STA)
      {
        U_Area.LcdCtrl.C_Set.Print_A7=0 ;
        break ;//No Print
       }
      if(U_Area.LcdCtrl.C_Set.Print_A7)
      {
        U_Area.LcdCtrl.C_Set.Print_A7=0 ;
        ClrPrintedStr(&U_Area.u_pcb) ;//Clear the area
       }
      else
        LcdPrintStr(&U_Area.u_pcb) ;//call string print function
      U_Area.LcdCtrl.Test.LCD_A7_OK=1 ;//set print Ok flag
     }
     break ;
   case 16 ://Print_A8
     if(U_Area.LcdCtrl.Set.Print_A8)
     {
      U_Area.LcdCtrl.Set.Print_A8=0 ;//reset the request falg
      SetLayoutOnLCD((STR_INFO *)&GSH_LCD_LAYOUT[8]);
      U_Area.u_pcb.str.uc_p=U_Area.uc_str[8] ;//-- unicode string
      if(U_Area.LcdCtrl.C_Set.Print_A8)
      {
        U_Area.LcdCtrl.C_Set.Print_A8=0 ;
        ClrPrintedStr(&U_Area.u_pcb) ;//Clear the area
       }
      else
        LcdPrintStr(&U_Area.u_pcb) ;//call string print function
      U_Area.LcdCtrl.Test.LCD_A8_OK=1 ;//set print Ok flag
     }
     break ;
   case 18 ://Print_A9
     if(U_Area.LcdCtrl.Set.Print_A9)
     {
      U_Area.LcdCtrl.Set.Print_A9=0 ;//reset the request falg
      SetLayoutOnLCD((STR_INFO *)&GSH_LCD_LAYOUT[9]);
      U_Area.u_pcb.info.len=0 ;
      U_Area.u_pcb.str.uc_p=U_Area.uc_str[9] ;//--unicode string
      if(U_Area.LcdCtrl.C_Set.Print_A9)
      {
        U_Area.LcdCtrl.C_Set.Print_A9=0 ;
        ClrPrintedStr(&U_Area.u_pcb) ;//Clear the area
       }
      else
        LcdPrintStr(&U_Area.u_pcb) ;//call string print function
      U_Area.LcdCtrl.Test.LCD_A9_OK=1 ;//set print Ok flag
     }
     break ;
   case 20 ://Print_A10
     if(U_Area.LcdCtrl.Set.Print_A10)
     {
      U_Area.LcdCtrl.Set.Print_A10=0 ;//reset the request falg
      SetLayoutOnLCD((STR_INFO *)&GSH_LCD_LAYOUT[10]);
      U_Area.u_pcb.str.ch_p=U_Area.a_str[0] ;//-- ascii string
      if(U_Area.LcdCtrl.C_Set.Print_A10)
      {
        U_Area.LcdCtrl.C_Set.Print_A10=0 ;
        ClrPrintedStr(&U_Area.u_pcb) ;//Clear the area
       }
      else
        LcdPrintStr(&U_Area.u_pcb) ;//call string print function
      U_Area.LcdCtrl.Test.LCD_A10_OK=1 ;//set print Ok flag
     }
     break ;
   case 22 ://Print_A11
     if(U_Area.LcdCtrl.Set.Print_A11)
     {
      U_Area.LcdCtrl.Set.Print_A11=0 ;//reset the request falg
      SetLayoutOnLCD((STR_INFO *)&GSH_LCD_LAYOUT[11]);
      U_Area.u_pcb.str.ch_p=U_Area.a_str[1] ;//-- ascii string
      if(U_Area.LcdCtrl.C_Set.Print_A11)
      {
        U_Area.LcdCtrl.C_Set.Print_A11=0 ;
        ClrPrintedStr(&U_Area.u_pcb) ;//Clear the area
       }
      else
        LcdPrintStr(&U_Area.u_pcb) ;//call string print function
      U_Area.LcdCtrl.Test.LCD_A11_OK=1 ;//set print Ok flag
     }

     break ;
   case 24 ://Print_A12
     if(U_Area.LcdCtrl.Set.Print_A12)
     {
      U_Area.LcdCtrl.Set.Print_A12=0 ;//reset the request falg
      SetLayoutOnLCD((STR_INFO *)&GSH_LCD_LAYOUT[12]);
      U_Area.u_pcb.str.ch_p=U_Area.a_str[2] ;// --ascii string
      if(U_Area.LcdCtrl.C_Set.Print_A12)
      {
        U_Area.LcdCtrl.C_Set.Print_A12=0 ;
        ClrPrintedStr(&U_Area.u_pcb) ;//Clear the area
       }
      else
        LcdPrintStr(&U_Area.u_pcb) ;//call string print function
      U_Area.LcdCtrl.Test.LCD_A12_OK=1 ;//set print Ok flag
     }
     break ;
   case 26 ://Print_A13
     if(U_Area.LcdCtrl.Set.Print_A13)
     {
      U_Area.LcdCtrl.Set.Print_A13=0 ;//reset the request falg
      SetLayoutOnLCD((STR_INFO *)&GSH_LCD_LAYOUT[13]);
      U_Area.u_pcb.str.ch_p=U_Area.a_str[3] ;//-- ascii string
      if(U_Area.LcdCtrl.C_Set.Print_A13)
      {
        U_Area.LcdCtrl.C_Set.Print_A13=0 ;
        ClrPrintedStr(&U_Area.u_pcb) ;//Clear the area
       }
      else
        LcdPrintStr(&U_Area.u_pcb) ;//call string print function
      U_Area.LcdCtrl.Test.LCD_A13_OK=1 ;//set print Ok flag
     }

     break ;
   case 28 ://Print_A14
     if(U_Area.LcdCtrl.Set.Print_A14)
     {
      U_Area.LcdCtrl.Set.Print_A14=0 ;//reset the request falg
      SetLayoutOnLCD((STR_INFO *)&GSH_LCD_LAYOUT[14]);
      U_Area.u_pcb.info.len=sprintf((char *)(char *)U_Area.a_str[4],"RSSI:%ddBm",RfDs.RSSI) ;
      U_Area.u_pcb.str.ch_p=U_Area.a_str[4] ;//Rx status measured RSSI in dBm--For debug ascii string
      if(U_Area.LcdCtrl.C_Set.Print_A14)
      {
        U_Area.LcdCtrl.C_Set.Print_A14=0 ;
        ClrPrintedStr(&U_Area.u_pcb) ;//Clear the area
       }
      else
        LcdPrintStr(&U_Area.u_pcb) ;//call string print function
      U_Area.LcdCtrl.Test.LCD_A14_OK=1 ;//set print Ok flag
     }

     break ;
   case 30 ://Print_A15
     if(U_Area.LcdCtrl.Set.Print_A15)
     {
      U_Area.LcdCtrl.Set.Print_A15=0 ;//reset the request falg
      SetLayoutOnLCD((STR_INFO *)&GSH_LCD_LAYOUT[15]);
      U_Area.u_pcb.info.len=sprintf((char *)(char *)U_Area.a_str[5],"BAT:%dV",U_Area.BatVoltage) ;
      U_Area.u_pcb.str.ch_p=U_Area.a_str[5] ;//Tx status measured battery voltage in V--For debug ascii string
      if(U_Area.LcdCtrl.C_Set.Print_A15)
      {
        U_Area.LcdCtrl.C_Set.Print_A15=0 ;
        ClrPrintedStr(&U_Area.u_pcb) ;//Clear the area
       }
      else
        LcdPrintStr(&U_Area.u_pcb) ;//call string print function
      U_Area.LcdCtrl.Test.LCD_A15_OK=1 ;//set print Ok flag
     }

     break ;
   default :
     U_Area.LcdCtrl.PRINT_STAGE=30 ;//will be set to 0

   }//End of switch
  U_Area.LcdCtrl.PRINT_STAGE +=2 ;
  U_Area.LcdCtrl.PRINT_STAGE &=0x001e ;//limited to 30
//-----
  MonitorDataDraw() ;//Output monitor data
#ifdef SHR_S160_MODE   
  ZkAlarmDeamon() ;
#else
  MachineStatusAlarmDraw() ;//
  AHAreaPrintDeamon() ;//2023.08.17 
#endif   
}

//---------------------------------------------------------------------------------------------
void S260T1_Print_A9(void)
{
    if(U_Area.LcdCtrl.Set.Print_A9)
     {
      U_Area.LcdCtrl.Set.Print_A9=0 ;//reset the request falg
      SetLayoutOnLCD((STR_INFO *)&S260T1_LCD_LAYOUT[9]);
      U_Area.u_pcb.info.len=sprintf((char *)U_Area.a_str[0],"%u%%Max",U_Area.TunMachDat.MachSpeed) ;
      U_Area.u_pcb.str.ch_p=U_Area.a_str[0] ;//ascii string 1
      if(U_Area.LcdCtrl.C_Set.Print_A9)
      {
        U_Area.LcdCtrl.C_Set.Print_A9=0 ;
        ClrPrintedStr(&U_Area.u_pcb) ;//Clear the area
        U_Area.LcdCtrl.Test.LCD_A9_OK=0 ;//clear print Ok flag
       }
      else
      {
        LcdPrintStr(&U_Area.u_pcb) ;//call string print function
        U_Area.LcdCtrl.Test.LCD_A9_OK=1 ;//set print Ok flag
       }
     }
}
void S260T1_Print_A10(void)
{
   if(U_Area.LcdCtrl.Set.Print_A10)
     {
      U_Area.LcdCtrl.Set.Print_A10=0 ;//reset the request falg
      SetLayoutOnLCD((STR_INFO *)&S260T1_LCD_LAYOUT[10]);
      U_Area.u_pcb.info.len=sprintf((char *)U_Area.a_str[1],"RxDP:%u",RfDs.RF_RxPackage) ;
      U_Area.u_pcb.str.ch_p=U_Area.a_str[1] ;//ascii string 1
      if(U_Area.LcdCtrl.C_Set.Print_A10)
      {
        U_Area.LcdCtrl.C_Set.Print_A10=0 ;
        ClrPrintedStr(&U_Area.u_pcb) ;//Clear the area
        U_Area.LcdCtrl.Test.LCD_A10_OK=0 ;//clear print Ok flag
      }
      else
      {
        LcdPrintStr(&U_Area.u_pcb) ;//call string print function
        U_Area.LcdCtrl.Test.LCD_A10_OK=1 ;//set print Ok flag
       }
     }  
}
void S260T1_Print_A11(void)
{
    if(U_Area.LcdCtrl.Set.Print_A11)
     {
      U_Area.LcdCtrl.Set.Print_A11=0 ;//reset the request falg
      SetLayoutOnLCD((STR_INFO *)&S260T1_LCD_LAYOUT[11]);
      if(U_Area.LcdCtrl.C_Set.Print_A11)
      {
        U_Area.LcdCtrl.C_Set.Print_A11=0 ;
        ClrPrintedStr(&U_Area.u_pcb) ;//Clear the area
        U_Area.LcdCtrl.Test.LCD_A11_OK=0 ;//clear print Ok flag
       }
      else
      {
        LcdPrintStr(&U_Area.u_pcb) ;//call string print function
        U_Area.LcdCtrl.Test.LCD_A11_OK=1 ;//set print Ok flag
       }
     }
  
}
void S260T1_Print_A12(void)
{
   if(U_Area.LcdCtrl.Set.Print_A12)
     {
      U_Area.LcdCtrl.Set.Print_A12=0 ;//reset the request falg
      SetLayoutOnLCD((STR_INFO *)&S260T1_LCD_LAYOUT[17]);
      U_Area.u_pcb.str.uc_p=WelcomStr  ;//
      if(U_Area.LcdCtrl.C_Set.Print_A12)
      {
        U_Area.LcdCtrl.C_Set.Print_A12=0 ;
        ClrPrintedStr(&U_Area.u_pcb) ;//Clear the area
        U_Area.LcdCtrl.Test.LCD_A12_OK=0 ;//clear print Ok flag
       }
      else
      {
        LcdPrintStr(&U_Area.u_pcb) ;//call string print function
        U_Area.LcdCtrl.Test.LCD_A12_OK=1 ;//set print Ok flag
       }
     }
}
void S260T1_Print_A13(void)
{
    if(U_Area.LcdCtrl.Set.Print_A13)
     {
      U_Area.LcdCtrl.Set.Print_A13=0 ;//reset the request falg
      SetLayoutOnLCD((STR_INFO *)&S260T1_LCD_LAYOUT[13]);
      if(U_Area.TunMachDat.MachSta&0x0008)
      { 
        U_Area.u_pcb.info.ctrl.b=UINCODE_12+REV_DIS ;//Set to reverse display mode
        U_Area.u_pcb.str.uc_p=S260_DevStaStr[3] ;//Print auto mdoe falg
       }
      else
        U_Area.u_pcb.str.uc_p=S260_DevStaStr[4] ;//Print manaul mode flag
      if(U_Area.LcdCtrl.C_Set.Print_A13)
      {
        U_Area.LcdCtrl.C_Set.Print_A13=0 ;
        ClrPrintedStr(&U_Area.u_pcb) ;//Clear the area
        U_Area.LcdCtrl.Test.LCD_A13_OK=0 ;//clear print Ok flag
       }
      else
      {
        LcdPrintStr(&U_Area.u_pcb) ;//call string print function
        U_Area.LcdCtrl.Test.LCD_A13_OK=1 ;//set print Ok flag
       }
     }
}
void S260T1_Print_A14(void)
{
  if(U_Area.LcdCtrl.Set.Print_A14)
  {
    U_Area.LcdCtrl.Set.Print_A14=0 ;//reset the request falg
    if(TestCtrlW==0)
    {
      SetLayoutOnLCD((STR_INFO *)&S260T1_LCD_LAYOUT[14]);
      U_Area.u_pcb.str.uc_p=S260_DevStaStr[5] ;//Print in auto operation start 
      if((U_Area.TunMachDat.MachSta&0x0010)==0)
        U_Area.LcdCtrl.C_Set.Print_A14=1 ;//Print black line
     }
    else
    {
      SetLayoutOnLCD((STR_INFO *)&S260T1_LCD_LAYOUT[18]);
      U_Area.u_pcb.info.len=sprintf((char *)U_Area.a_str[4],"RSSI:%ddBm",RfDs.RSSI) ;
      U_Area.u_pcb.str.ch_p=U_Area.a_str[4] ;//Rx status measured RSSI in dBm--For debug ascii string
     }
    if(U_Area.LcdCtrl.C_Set.Print_A14)
    {
      U_Area.LcdCtrl.C_Set.Print_A14=0 ;
      ClrPrintedStr(&U_Area.u_pcb) ;//Clear the area
      U_Area.LcdCtrl.Test.LCD_A14_OK=0 ;//clear print Ok flag
     }
    else
    {
      LcdPrintStr(&U_Area.u_pcb) ;//call string print function
      U_Area.LcdCtrl.Test.LCD_A14_OK=1 ;//set print Ok flag
     }
   }
}
void S260T1_Print_A15(void)
{
  if(U_Area.LcdCtrl.Set.Print_A15)
  {
    U_Area.LcdCtrl.Set.Print_A15=0 ;//reset the request falg
    if(TestCtrlW==0)
    {
      SetLayoutOnLCD((STR_INFO *)&S260T1_LCD_LAYOUT[15]);
      U_Area.u_pcb.str.uc_p=U_Area.uc_str[5] ;//TX/RX status indicate string
     }
    else
    {
      SetLayoutOnLCD((STR_INFO *)&S260T1_LCD_LAYOUT[19]);
      U_Area.u_pcb.info.len=sprintf((char *)U_Area.a_str[5],"BAT:%dV",U_Area.BatVoltage) ;
      U_Area.u_pcb.str.ch_p=U_Area.a_str[5] ;//Tx status measured battery voltage in V--For debug ascii string
     }
    if(U_Area.LcdCtrl.C_Set.Print_A15)
    {
      U_Area.LcdCtrl.C_Set.Print_A15=0 ;
      ClrPrintedStr(&U_Area.u_pcb) ;//Clear the area
      U_Area.LcdCtrl.Test.LCD_A15_OK=0 ;//Clear print Ok flag
     }
    else
    {
      LcdPrintStr(&U_Area.u_pcb) ;//call string print function
      U_Area.LcdCtrl.Test.LCD_A15_OK=1 ;//set print Ok flag
     }
   }
}
//-----------------------------------------------------------------------------------------------
void  S260T1_LCD_print(void) //LCD screen print function for S260 tunneller (T1) 
{
 switch(U_Area.LcdCtrl.PRINT_STAGE)
 {
   case 0 ://Print_A0
     if(U_Area.LcdCtrl.Set.Print_A0)
     {
      U_Area.LcdCtrl.Set.Print_A0=0 ;//reset the request falg
      SetLayoutOnLCD((STR_INFO *)&S260T1_LCD_LAYOUT[0]);
      U_Area.u_pcb.str.uc_p=U_Area.uc_str[0] ;//battery status unicode code
      if(U_Area.LcdCtrl.C_Set.Print_A0)
      {
        U_Area.LcdCtrl.C_Set.Print_A0=0 ;
        ClrPrintedStr(&U_Area.u_pcb) ;//Clear the area
        U_Area.LcdCtrl.Test.LCD_A0_OK=0 ;//clear print Ok flag
       }
      else
      {
        LcdPrintStr(&U_Area.u_pcb) ;//call string print function
        U_Area.LcdCtrl.Test.LCD_A0_OK=1 ;//set print Ok flag
       }
     }
     break ;
   case 2 ://Print_A1
     if(U_Area.LcdCtrl.Set.Print_A1)
     {
      U_Area.LcdCtrl.Set.Print_A1=0 ;//reset the request falg
      SetLayoutOnLCD((STR_INFO *)&S260T1_LCD_LAYOUT[1]);
      U_Area.u_pcb.str.uc_p=U_Area.uc_str[1] ;//Operation confirm status unicode code
      if(U_Area.LcdCtrl.C_Set.Print_A1)
      {
        U_Area.LcdCtrl.C_Set.Print_A1=0 ;
        ClrPrintedStr(&U_Area.u_pcb) ;//Clear the area
        U_Area.LcdCtrl.Test.LCD_A1_OK=0 ;//clear print Ok flag
       }
      else
      {
        LcdPrintStr(&U_Area.u_pcb) ;//call string print function
        U_Area.LcdCtrl.Test.LCD_A1_OK=1 ;//set print Ok flag
       }
     }
     break ;
   case 4 ://Print_A2
     if(U_Area.LcdCtrl.Set.Print_A2)
     {
      U_Area.LcdCtrl.Set.Print_A2=0 ;//reset the request falg
      SetLayoutOnLCD((STR_INFO *)&S260T1_LCD_LAYOUT[2]);
      U_Area.u_pcb.str.uc_p=U_Area.uc_str[2] ;//Flag 1 status unicode code
      if(U_Area.LcdCtrl.C_Set.Print_A2)
      {
        U_Area.LcdCtrl.C_Set.Print_A2=0 ;
        ClrPrintedStr(&U_Area.u_pcb) ;//Clear the area
        U_Area.LcdCtrl.Test.LCD_A2_OK=0 ;//clear print Ok flag
       }
      else
      {
        LcdPrintStr(&U_Area.u_pcb) ;//call string print function
        U_Area.LcdCtrl.Test.LCD_A2_OK=1 ;//set print Ok flag
       }
     }
     break ;
   case 6 ://Print_A3
     if(U_Area.LcdCtrl.Set.Print_A3)
     {
      U_Area.LcdCtrl.Set.Print_A3=0 ;//reset the request falg
      SetLayoutOnLCD((STR_INFO *)&S260T1_LCD_LAYOUT[3]);
      U_Area.u_pcb.str.uc_p=U_Area.uc_str[3] ;//Flag 2 status unicode code
      if(U_Area.LcdCtrl.C_Set.Print_A3)
      {
        U_Area.LcdCtrl.C_Set.Print_A3=0 ;
        ClrPrintedStr(&U_Area.u_pcb) ;//Clear the area
        U_Area.LcdCtrl.Test.LCD_A3_OK=0 ;//clear print Ok flag
       }
      else
      {
        LcdPrintStr(&U_Area.u_pcb) ;//call string print function
        U_Area.LcdCtrl.Test.LCD_A3_OK=1 ;//set print Ok flag
       }
     }
     break ;
   case 8 ://Print_A4
     if(U_Area.LcdCtrl.Set.Print_A4)
     {
      U_Area.LcdCtrl.Set.Print_A4=0 ;//reset the request falg RSSI
      SetLayoutOnLCD((STR_INFO *)&S260T1_LCD_LAYOUT[4]);
      U_Area.u_pcb.str.uc_p=U_Area.uc_str[4] ;//
      if(U_Area.LcdCtrl.C_Set.Print_A4)
      {
        U_Area.LcdCtrl.C_Set.Print_A4=0 ;
        ClrPrintedStr(&U_Area.u_pcb) ;//Clear the area
        U_Area.LcdCtrl.Test.LCD_A4_OK=0 ;//clear print Ok flag
       }
      else
      {
        LcdPrintStr(&U_Area.u_pcb) ;//call string print function
        U_Area.LcdCtrl.Test.LCD_A4_OK=1 ;//set print Ok flag
       }
     }
     break ;
   case 10 ://Print_A5
     if(U_Area.LcdCtrl.Set.Print_A5)
     {
      U_Area.LcdCtrl.Set.Print_A5=0 ;//reset the request falg
      if(GFlag&GF_IN_SETUP_STA)
        break ;//No Print
      U_Area.LcdCtrl.Set.Print_A5=0 ;//reset the request falg
      SetLayoutOnLCD((STR_INFO *)&S260T1_LCD_LAYOUT[5]);
      U_Area.u_pcb.str.uc_p=MachSpeedStr;//Print machine speed fixed flag (unicode code)
      if(U_Area.LcdCtrl.C_Set.Print_A5)
      {
        U_Area.LcdCtrl.C_Set.Print_A5=0 ;
        ClrPrintedStr(&U_Area.u_pcb) ;//Clear the area
        U_Area.LcdCtrl.Test.LCD_A5_OK=0 ;//clear print Ok flag
       }
      else
      {
        LcdPrintStr(&U_Area.u_pcb) ;//call string print function
        U_Area.LcdCtrl.Test.LCD_A5_OK=1 ;//set print Ok flag
       }
     }
     break ;
   case 12 ://Print_A6
     if(U_Area.LcdCtrl.Set.Print_A6)
     {
      U_Area.LcdCtrl.Set.Print_A6=0 ;//reset the request falg
      if(GFlag&GF_IN_SETUP_STA)
        break ;//No Print
      SetLayoutOnLCD((STR_INFO *)&S260T1_LCD_LAYOUT[6]);
      U_Area.u_pcb.str.uc_p=S260T1_KeyInfoStr[U_Area.KeyDisID_1st] ;//Print first operating key ID (unicode string)
      if(U_Area.LcdCtrl.C_Set.Print_A6)
      {
        U_Area.LcdCtrl.C_Set.Print_A6=0 ;
        ClrPrintedStr(&U_Area.u_pcb) ;//Clear the area
        U_Area.LcdCtrl.Test.LCD_A6_OK=0 ;//clear print Ok flag
       }
      else
      {
        LcdPrintStr(&U_Area.u_pcb) ;//call string print function
        U_Area.LcdCtrl.Test.LCD_A6_OK=1 ;//set print Ok flag
       }
     }
     break ;
   case 14 ://Print_A7
     if(U_Area.LcdCtrl.Set.Print_A7)
     {
      U_Area.LcdCtrl.Set.Print_A7=0 ;//reset the request falg
      if(GFlag&GF_IN_SETUP_STA)
        break ;//No Print
      SetLayoutOnLCD((STR_INFO *)&S260T1_LCD_LAYOUT[7]);
      U_Area.u_pcb.str.uc_p=S260T1_KeyInfoStr[U_Area.KeyDisID_2nd] ;//Print second operating key ID (unicode string)
      if(U_Area.LcdCtrl.C_Set.Print_A7)
      {
        U_Area.LcdCtrl.C_Set.Print_A7=0 ;
        ClrPrintedStr(&U_Area.u_pcb) ;//Clear the area
        U_Area.LcdCtrl.Test.LCD_A7_OK=0 ;//clear print Ok flag
       }
      else
      {
        LcdPrintStr(&U_Area.u_pcb) ;//call string print function
        U_Area.LcdCtrl.Test.LCD_A7_OK=1 ;//set print Ok flag
		//--
		AHAreaStaB = 1;  
		//--
       }
     }
     break ;
   case 16 ://Print_A8
     if(U_Area.LcdCtrl.Set.Print_A8)
     {
      U_Area.LcdCtrl.Set.Print_A8=0 ;//reset the request falg
      if(GFlag&GF_IN_SETUP_STA)
        break ;//No Print
      SetLayoutOnLCD((STR_INFO *)&S260T1_LCD_LAYOUT[8]);
      if(U_Area.AlarmStatus==0xffff)
      {
        U_Area.u_pcb.str.uc_p=StausAlarmStr[0] ;
        U_Area.LcdCtrl.C_Set.Print_A8=1 ;
       }
      else
        U_Area.u_pcb.str.uc_p=StausAlarmStr[U_Area.AlarmStatus] ;//Transmmiter alarm status(unicode string)
      if(U_Area.LcdCtrl.C_Set.Print_A8)
      {
        U_Area.LcdCtrl.C_Set.Print_A8=0 ;
        ClrPrintedStr(&U_Area.u_pcb) ;//Clear the area
        U_Area.LcdCtrl.Test.LCD_A8_OK=0 ;//clear print Ok flag
       }
      else
      {
        LcdPrintStr(&U_Area.u_pcb) ;//call string print function
        U_Area.LcdCtrl.Test.LCD_A8_OK=1 ;//set print Ok flag
       }
     }
     break ;
   case 18 ://Print_A9
     S260T1_Print_A9() ;
     break ;
   case 20 ://Print_A10
     S260T1_Print_A10() ;
     break ;
   case 22 ://Print_A11
     S260T1_Print_A11() ;
     break ;
   case 24 ://Print_A12
     S260T1_Print_A12() ;
     break ;
   case 26 ://Print_A13
     S260T1_Print_A13() ;
     break ;
   case 28 ://Print_A14
     S260T1_Print_A14() ;
     break ;
   case 30 ://Print_A15
     S260T1_Print_A15() ;
     break ;
   default :
     U_Area.LcdCtrl.PRINT_STAGE=30 ;//will be set to 0

   }//End of switch
  U_Area.LcdCtrl.PRINT_STAGE +=2 ;
  U_Area.LcdCtrl.PRINT_STAGE &=0x001e ;//limited to 30
}

//---------------------------------------------------------------------------------------------
//---------------------------------------------------------------------------------------------
void S260T2_Print_A9(void)
{
    if(U_Area.LcdCtrl.Set.Print_A9)
     {
      U_Area.LcdCtrl.Set.Print_A9=0 ;//reset the request falg
      SetLayoutOnLCD((STR_INFO *)&S260T2_LCD_LAYOUT[9]);
      U_Area.u_pcb.info.len=sprintf((char *)U_Area.a_str[0],"%u%%",U_Area.TunMachDat.MachSpeed) ;
      U_Area.u_pcb.str.ch_p=U_Area.a_str[0] ;//ascii string 1
      if(U_Area.LcdCtrl.C_Set.Print_A9)
      {
        U_Area.LcdCtrl.C_Set.Print_A9=0 ;
        ClrPrintedStr(&U_Area.u_pcb) ;//Clear the area
       }
      else
        LcdPrintStr(&U_Area.u_pcb) ;//call string print function
      U_Area.LcdCtrl.Test.LCD_A9_OK=1 ;//set print Ok flag
     }
}
void S260T2_Print_A10(void)
{
   if(U_Area.LcdCtrl.Set.Print_A10)
     {
      U_Area.LcdCtrl.Set.Print_A10=0 ;//reset the request falg
      SetLayoutOnLCD((STR_INFO *)&S260T2_LCD_LAYOUT[10]);
      U_Area.u_pcb.info.len=sprintf((char *)U_Area.a_str[1],"RxDP:%u",RfDs.RF_RxPackage) ;
      U_Area.u_pcb.str.ch_p=U_Area.a_str[1] ;//ascii string 1
      if(U_Area.LcdCtrl.C_Set.Print_A10)
      {
        U_Area.LcdCtrl.C_Set.Print_A10=0 ;
        ClrPrintedStr(&U_Area.u_pcb) ;//Clear the area
       }
      else
        LcdPrintStr(&U_Area.u_pcb) ;//call string print function
      U_Area.LcdCtrl.Test.LCD_A10_OK=1 ;//set print Ok flag
     }  
}
void S260T2_Print_A11(void)
{
    if(U_Area.LcdCtrl.Set.Print_A11)
     {
      U_Area.LcdCtrl.Set.Print_A11=0 ;//reset the request falg
      SetLayoutOnLCD((STR_INFO *)&S260T2_LCD_LAYOUT[11]);
      U_Area.u_pcb.str.uc_p=S260_DevStaStr[11] ;//Print in blank line
      if(U_Area.LcdCtrl.C_Set.Print_A11)
      {
        U_Area.LcdCtrl.C_Set.Print_A11=0 ;
        ClrPrintedStr(&U_Area.u_pcb) ;//Clear the area
       }
      else
        LcdPrintStr(&U_Area.u_pcb) ;//call string print function
      U_Area.LcdCtrl.Test.LCD_A11_OK=1 ;//set print Ok flag
     }
}
void S260T2_Print_A12(void)
{
   if(U_Area.LcdCtrl.Set.Print_A12)
     {
      U_Area.LcdCtrl.Set.Print_A12=0 ;//reset the request falg
      SetLayoutOnLCD((STR_INFO *)&S260T2_LCD_LAYOUT[17]);
      U_Area.u_pcb.str.uc_p=WelcomStr ;//Print in blank line
      if(U_Area.LcdCtrl.C_Set.Print_A12)
      {
        U_Area.LcdCtrl.C_Set.Print_A12=0 ;
        ClrPrintedStr(&U_Area.u_pcb) ;//Clear the area
       }
      else
        LcdPrintStr(&U_Area.u_pcb) ;//call string print function
      U_Area.LcdCtrl.Test.LCD_A12_OK=1 ;//set print Ok flag
     }
}
void S260T2_Print_A13(void)
{
    if(U_Area.LcdCtrl.Set.Print_A13)
     {
      U_Area.LcdCtrl.Set.Print_A13=0 ;//reset the request falg
      SetLayoutOnLCD((STR_INFO *)&S260T2_LCD_LAYOUT[13]);
      U_Area.u_pcb.str.uc_p=S260_DevStaStr[11] ;//Print in blank line
      if(U_Area.LcdCtrl.C_Set.Print_A13)
      {
        U_Area.LcdCtrl.C_Set.Print_A13=0 ;
        ClrPrintedStr(&U_Area.u_pcb) ;//Clear the area
       }
      else
        LcdPrintStr(&U_Area.u_pcb) ;//call string print function
      U_Area.LcdCtrl.Test.LCD_A13_OK=1 ;//set print Ok flag
     }
}
void S260T2_Print_A14(void)
{
  if(U_Area.LcdCtrl.Set.Print_A14)
  {
    U_Area.LcdCtrl.Set.Print_A14=0 ;//reset the request falg
    if(TestCtrlW==0)
    {
      SetLayoutOnLCD((STR_INFO *)&S260T2_LCD_LAYOUT[14]);
      U_Area.u_pcb.str.uc_p=S260_DevStaStr[11] ;//Print blank line
     }
    else
    {
      SetLayoutOnLCD((STR_INFO *)&S260T2_LCD_LAYOUT[18]);
      U_Area.u_pcb.info.len=sprintf((char *)U_Area.a_str[4],"RSSI:%ddBm",RfDs.RSSI) ;
      U_Area.u_pcb.str.ch_p=U_Area.a_str[4] ;//Rx status measured RSSI in dBm--For debug ascii string
     }
    if(U_Area.LcdCtrl.C_Set.Print_A14)
    {
      U_Area.LcdCtrl.C_Set.Print_A14=0 ;
      ClrPrintedStr(&U_Area.u_pcb) ;//Clear the area
     }
    else
     LcdPrintStr(&U_Area.u_pcb) ;//call string print function
    U_Area.LcdCtrl.Test.LCD_A14_OK=1 ;//set print Ok flag
   }
}
void S260T2_Print_A15(void)
{
  if(U_Area.LcdCtrl.Set.Print_A15)
  {
    U_Area.LcdCtrl.Set.Print_A15=0 ;//reset the request falg
    if(TestCtrlW==0)
    {
      SetLayoutOnLCD((STR_INFO *)&S260T2_LCD_LAYOUT[15]);
      U_Area.u_pcb.str.uc_p=U_Area.uc_str[5] ;//TX/RX status indicate string
     }
    else
    {
      SetLayoutOnLCD((STR_INFO *)&S260T2_LCD_LAYOUT[19]);
      U_Area.u_pcb.info.len=sprintf((char *)U_Area.a_str[5],"BAT:%dV",U_Area.BatVoltage) ;
      U_Area.u_pcb.str.ch_p=U_Area.a_str[5] ;//Tx status measured battery voltage in V--For debug ascii string
     }
    if(U_Area.LcdCtrl.C_Set.Print_A15)
    {
      U_Area.LcdCtrl.C_Set.Print_A15=0 ;
      ClrPrintedStr(&U_Area.u_pcb) ;//Clear the area
     }
    else
    {
      LcdPrintStr(&U_Area.u_pcb) ;//call string print function
     }
    U_Area.LcdCtrl.Test.LCD_A15_OK=1 ;//set print Ok flag
   }
}
//--------------------------------------------------------------------------------------
void  S260T2_LCD_print(void) //LCD screen print function for S260 tunneller (T2) 
{
 switch(U_Area.LcdCtrl.PRINT_STAGE)
 {
   case 0 ://Print_A0
     if(U_Area.LcdCtrl.Set.Print_A0)
     {
      U_Area.LcdCtrl.Set.Print_A0=0 ;//reset the request falg
      SetLayoutOnLCD((STR_INFO *)&S260T2_LCD_LAYOUT[0]);
      U_Area.u_pcb.str.uc_p=U_Area.uc_str[0] ;//battery status unicode code
      if(U_Area.LcdCtrl.C_Set.Print_A0)
      {
        U_Area.LcdCtrl.C_Set.Print_A0=0 ;
        ClrPrintedStr(&U_Area.u_pcb) ;//Clear the area
       }
      else
        LcdPrintStr(&U_Area.u_pcb) ;//call string print function
      U_Area.LcdCtrl.Test.LCD_A0_OK=1 ;//set print Ok flag
     }
     break ;
   case 2 ://Print_A1
     if(U_Area.LcdCtrl.Set.Print_A1)
     {
      U_Area.LcdCtrl.Set.Print_A1=0 ;//reset the request falg
      SetLayoutOnLCD((STR_INFO *)&S260T2_LCD_LAYOUT[1]);
      U_Area.u_pcb.str.uc_p=U_Area.uc_str[1] ;//Operation confirm status unicode code
      if(U_Area.LcdCtrl.C_Set.Print_A1)
      {
        U_Area.LcdCtrl.C_Set.Print_A1=0 ;
        ClrPrintedStr(&U_Area.u_pcb) ;//Clear the area
       }
      else
        LcdPrintStr(&U_Area.u_pcb) ;//call string print function
      U_Area.LcdCtrl.Test.LCD_A1_OK=1 ;//set print Ok flag
     }
     break ;
   case 4 ://Print_A2
     if(U_Area.LcdCtrl.Set.Print_A2)
     {
      U_Area.LcdCtrl.Set.Print_A2=0 ;//reset the request falg
      SetLayoutOnLCD((STR_INFO *)&S260T2_LCD_LAYOUT[2]);
      U_Area.u_pcb.str.uc_p=U_Area.uc_str[2] ;//Flag 1 status unicode code
      if(U_Area.LcdCtrl.C_Set.Print_A2)
      {
        U_Area.LcdCtrl.C_Set.Print_A2=0 ;
        ClrPrintedStr(&U_Area.u_pcb) ;//Clear the area
       }
      else
        LcdPrintStr(&U_Area.u_pcb) ;//call string print function
      U_Area.LcdCtrl.Test.LCD_A2_OK=1 ;//set print Ok flag
     }
     break ;
   case 6 ://Print_A3
     if(U_Area.LcdCtrl.Set.Print_A3)
     {
      U_Area.LcdCtrl.Set.Print_A3=0 ;//reset the request falg
      SetLayoutOnLCD((STR_INFO *)&S260T2_LCD_LAYOUT[3]);
      U_Area.u_pcb.str.uc_p=U_Area.uc_str[3] ;//Flag 2 status unicode code
      if(U_Area.LcdCtrl.C_Set.Print_A3)
      {
        U_Area.LcdCtrl.C_Set.Print_A3=0 ;
        ClrPrintedStr(&U_Area.u_pcb) ;//Clear the area
       }
      else
        LcdPrintStr(&U_Area.u_pcb) ;//call string print function
      U_Area.LcdCtrl.Test.LCD_A3_OK=1 ;//set print Ok flag
     }
     break ;
   case 8 ://Print_A4
     if(U_Area.LcdCtrl.Set.Print_A4)
     {
      U_Area.LcdCtrl.Set.Print_A4=0 ;//reset the request falg RSSI
      SetLayoutOnLCD((STR_INFO *)&S260T2_LCD_LAYOUT[4]);
      U_Area.u_pcb.str.uc_p=U_Area.uc_str[4] ;//
      if(U_Area.LcdCtrl.C_Set.Print_A4)
      {
        U_Area.LcdCtrl.C_Set.Print_A4=0 ;
        ClrPrintedStr(&U_Area.u_pcb) ;//Clear the area
       }
      else
        LcdPrintStr(&U_Area.u_pcb) ;//call string print function
      U_Area.LcdCtrl.Test.LCD_A4_OK=1 ;//set print Ok flag
     }
     break ;
   case 10 ://Print_A5
     if(U_Area.LcdCtrl.Set.Print_A5)
     {
      U_Area.LcdCtrl.Set.Print_A5=0 ;//reset the request falg
      if(GFlag&GF_IN_SETUP_STA)
        break ;//No Print
      U_Area.LcdCtrl.Set.Print_A5=0 ;//reset the request falg
      SetLayoutOnLCD((STR_INFO *)&S260T2_LCD_LAYOUT[5]);
      U_Area.u_pcb.str.uc_p=MachSpeedStr;//Print machine speed fixed flag (unicode code)
      if(U_Area.LcdCtrl.C_Set.Print_A5)
      {
        U_Area.LcdCtrl.C_Set.Print_A5=0 ;
        ClrPrintedStr(&U_Area.u_pcb) ;//Clear the area
       }
      else
        LcdPrintStr(&U_Area.u_pcb) ;//call string print function
      U_Area.LcdCtrl.Test.LCD_A5_OK=1 ;//set print Ok flag
     }
     break ;
   case 12 ://Print_A6
     if(U_Area.LcdCtrl.Set.Print_A6)
     {
      U_Area.LcdCtrl.Set.Print_A6=0 ;//reset the request falg
      if(GFlag&GF_IN_SETUP_STA)
        break ;//No Print
      SetLayoutOnLCD((STR_INFO *)&S260T2_LCD_LAYOUT[6]);
      U_Area.u_pcb.str.uc_p=S260T2_KeyInfoStr[U_Area.KeyDisID_1st] ;//Print first operating key ID (unicode string)
      if(U_Area.LcdCtrl.C_Set.Print_A6)
      {
        U_Area.LcdCtrl.C_Set.Print_A6=0 ;
        ClrPrintedStr(&U_Area.u_pcb) ;//Clear the area
       }
      else
        LcdPrintStr(&U_Area.u_pcb) ;//call string print function
      U_Area.LcdCtrl.Test.LCD_A6_OK=1 ;//set print Ok flag
     }
     break ;
   case 14 ://Print_A7
     if(U_Area.LcdCtrl.Set.Print_A7)
     {
      U_Area.LcdCtrl.Set.Print_A7=0 ;//reset the request falg
      if(GFlag&GF_IN_SETUP_STA)
        break ;//No Print
      SetLayoutOnLCD((STR_INFO *)&S260T2_LCD_LAYOUT[7]);
      U_Area.u_pcb.str.uc_p=S260T2_KeyInfoStr[U_Area.KeyDisID_2nd] ;//Print second operating key ID (unicode string)
      if(U_Area.LcdCtrl.C_Set.Print_A7)
      {
        U_Area.LcdCtrl.C_Set.Print_A7=0 ;
        ClrPrintedStr(&U_Area.u_pcb) ;//Clear the area
       }
      else
        LcdPrintStr(&U_Area.u_pcb) ;//call string print function
      U_Area.LcdCtrl.Test.LCD_A7_OK=1 ;//set print Ok flag
     }
     break ;
   case 16 ://Print_A8
     if(U_Area.LcdCtrl.Set.Print_A8)
     {
      U_Area.LcdCtrl.Set.Print_A8=0 ;//reset the request falg
      if(GFlag&GF_IN_SETUP_STA)
        break ;//No Print
      SetLayoutOnLCD((STR_INFO *)&S260T2_LCD_LAYOUT[8]);
      if(U_Area.AlarmStatus==0xffff)
      {
        U_Area.u_pcb.str.uc_p=StausAlarmStr[0] ;
        U_Area.LcdCtrl.C_Set.Print_A8=1 ;
       }
      else
        U_Area.u_pcb.str.uc_p=StausAlarmStr[U_Area.AlarmStatus] ;//Transmmiter alarm status(unicode string)
      if(U_Area.LcdCtrl.C_Set.Print_A8)
      {
        U_Area.LcdCtrl.C_Set.Print_A8=0 ;
        ClrPrintedStr(&U_Area.u_pcb) ;//Clear the area
       }
      else
        LcdPrintStr(&U_Area.u_pcb) ;//call string print function
      U_Area.LcdCtrl.Test.LCD_A8_OK=1 ;//set print Ok flag
     }
     break ;
   case 18 ://Print_A9
     S260T2_Print_A9() ;
     break ;
   case 20 ://Print_A10
     S260T2_Print_A10() ;
     break ;
   case 22 ://Print_A11
     S260T2_Print_A11() ;
     break ;
   case 24 ://Print_A12
     S260T2_Print_A12() ;
     break ;
   case 26 ://Print_A13
     S260T2_Print_A13() ;
     break ;
   case 28 ://Print_A14
     S260T2_Print_A14() ;
     break ;
   case 30 ://Print_A15
     S260T2_Print_A15() ;
     break ;
   default :
     U_Area.LcdCtrl.PRINT_STAGE=30 ;//will be set to 0

   }//End of switch
  U_Area.LcdCtrl.PRINT_STAGE +=2 ;
  U_Area.LcdCtrl.PRINT_STAGE &=0x001e ;//limited to 30
}
//---------------------------------------------------------------------------------------------

void SetLayoutOnLCD(STR_INFO *area_p)
{
  U_Area.u_pcb.info.x=area_p->x ;
  U_Area.u_pcb.info.y=area_p->y ;
  U_Area.u_pcb.info.width=area_p->width ;
  U_Area.u_pcb.info.height=area_p->height ;
  U_Area.u_pcb.info.len=area_p->len ;
  U_Area.u_pcb.info.ctrl.b=area_p->ctrl.b ;
}

/*
  This routine will convert unicode to a index in specified unicodepage
*/
void UnicodeToIndex12(const uint16_t *unicode,uint16_t *index,int16_t length)
{
  int16_t  MinLimit,MaxLimit,nonius ;
  int16_t  tmp ;
  uint16_t uc ;
  for(tmp=0 ;tmp<length ;tmp++)
  {
    MinLimit=0 ;
    MaxLimit=CodePage12.cph.num_cp_ranges-1 ;
//--swap unicode data file byte order
	uc=(*unicode)<<8 ;
	uc|=(((*unicode)>>8)&0x00ff) ;
//	uc=(uint16_t)__rev((uint16_t)*unicode) ;
//--
    do{
       nonius=(MinLimit+MaxLimit)>>1 ;//divide by 2
       if(uc<CodePage12.cpr[nonius].min)
       {
         if(nonius==MinLimit)// out of lowest range
          {
            *index=CodePage12.cph.deault_symbol ;
            break ;
           }
         else
           MaxLimit=nonius-1 ;
        }
       else if(uc>CodePage12.cpr[nonius].max)
       {
         if(nonius==MaxLimit)//out of upper limit
          {
           *index=CodePage12.cph.deault_symbol ;
           break ;
          }
         else
           MinLimit=nonius+1 ;
        }
       else
        {
          *index=(uc-CodePage12.cpr[nonius].min)+CodePage12.cpr[nonius].index;
          break ;
         }
    } while(MinLimit<=MaxLimit) ;
    unicode++ ;//move the pointer to next unicode
    index++ ;//move the result pointer
  }
}
void UnicodeToIndex16(const uint16_t *unicode,uint16_t *index,int16_t length)
{
  int16_t   MinLimit, MaxLimit, nonius ;
  int16_t   tmp ;
  uint16_t  uc ;
  for(tmp=0 ;tmp<length ;tmp++)
  {
    MinLimit=0 ;
    MaxLimit=CodePage16.cph.num_cp_ranges-1 ;
//--swap unicode data file byte order
	uc=(*unicode)<<8 ;
	uc|=(((*unicode)>>8)&0x00ff) ;
//	uc=(uint16_t)__revsh((uint16_t)*unicode) ;
//--
    do{
       nonius=(MinLimit+MaxLimit)>>1 ;//divide by 2
       if(uc<CodePage16.cpr[nonius].min)
       {
         if(nonius==MinLimit)// out of lowest range
          {
            *index=CodePage16.cph.deault_symbol ;
            break ;
           }
         else
           MaxLimit=nonius-1 ;
        }
       else if(uc>CodePage16.cpr[nonius].max)
       {
         if(nonius==MaxLimit)//out of upper limit
          {
           *index=CodePage16.cph.deault_symbol ;
           break ;
           }
         else
            MinLimit=nonius+1 ;
        }
       else
        {
          *index=(uc-CodePage16.cpr[nonius].min)+CodePage16.cpr[nonius].index;
          break;
         }
       } while(MinLimit<=MaxLimit) ;
    unicode++ ;//move the pointer to next unicode
    index++ ;//move the result pointer
  }
}
//----------------------------------------------------------------------------------------------------------------
void LcdPrintStr(UNI_PRINT * pcb_lp)
{
 int8_t   xBegin,yBegin ;//store column and page address
 int8_t   chBegin,chEnd,StrLen,chIndex,Margin;//use to store begin/end character position in the string
 int16_t  tmp=0,tmp2=0 ;//temp variable
 uint8_t  font,digits,ch,ch2,*ch_p ;

 uint16_t  UniCode,*UCode_p ;

 if((*pcb_lp).info.len)
   StrLen=(*pcb_lp).info.len ;
 else
 {
   if((*pcb_lp).info.ctrl.unicode)
     StrLen=1;//for unicode string must specify the length parameter
              //otherwise I will set it to 1, to reduce complex of this program
   else
     StrLen=strlen((const char *)(*pcb_lp).str.ch_p);//tmp ;
   if(StrLen==0) return ;//no character need to output
  }
 font=(*pcb_lp).info.ctrl.font ;//use that user specified font
 //---------------------------------------------------------------------------
 UCode_p=S_Area.CODEPAGE_BUF ;//UCode_p now point to unicode index buffer for specified font
 if((*pcb_lp).info.ctrl.unicode)
 {//it is unicode string,first need to do codepage convert
   if(font==FONT_UNICODE16)//using 16x16 font
     UnicodeToIndex16((*pcb_lp).str.uc_p,S_Area.CODEPAGE_BUF,StrLen) ;
   else //default using 12x12 font
     UnicodeToIndex12((*pcb_lp).str.uc_p,S_Area.CODEPAGE_BUF,StrLen) ;
   //now unicode index for specified font availabe in the code page buffer
  }
 else
 {//it is ANSI string
   digits=(*pcb_lp).info.ctrl.digits ;//required decimal digits when print integer data
   if(digits)//currently this function only support pure  integer output with digits
    {
      ch_p=(*pcb_lp).str.ch_p;//get first character's address
      chIndex=StrLen-1;
      tmp=0 ;
      while(chIndex>=0)
      {
        if((*(ch_p+chIndex)>=0x30)&&(*(ch_p+chIndex)<=0x39))
        {
          tmp++;
          if(tmp>digits)
          {//Now I get enough number to set digits
             for(tmp2=(StrLen-1);tmp2>chIndex ;tmp2--)
             {
               *(ch_p+tmp2+1)=*(ch_p+tmp2) ;//move last part of the string backwards
              }
             *(ch_p+tmp2+1)=DIGIT_CHAR ;//set decimal
             StrLen++ ;
             *(ch_p+StrLen)=NULL_CHAR ;
             break ;
           }
          else if(chIndex==0)
          {
            for(tmp2=StrLen-1 ;tmp2>=0 ;tmp2--)
            {
              *(ch_p+tmp2+(digits-tmp+2))=*(ch_p+tmp2) ;//move last part of the string backwards
             }
            *(ch_p+chIndex++)=ZERO_CHAR ;//leading zero
            *(ch_p+chIndex++)=DIGIT_CHAR ;//set decimal
            tmp2=digits-tmp ;
            while(tmp2)
            {
              *(ch_p+chIndex++)=ZERO_CHAR ;//fill zero after decimal
              tmp2--;
             }
            StrLen+=(digits-tmp+2) ;
            *(ch_p+StrLen)=NULL_CHAR ;
            break ;
           }
         }
        else if(tmp)//encounter first non digit number after some digit number encountered before
         {//I cannot get enough number,but get some,I will fill the string to set digit properly
           for(tmp2=StrLen-1 ;tmp2>chIndex ;tmp2--)
            {
              *(ch_p+tmp2+(digits-tmp+2))=*(ch_p+tmp2) ;//move last part of the string backwards
             }
           chIndex++ ;
           *(ch_p+chIndex++)=ZERO_CHAR ;//leading zero
           *(ch_p+chIndex++)=DIGIT_CHAR ;//set decimal
           tmp2=digits-tmp ;
           while(tmp2)
           {
             *(ch_p+chIndex++)=ZERO_CHAR ;//fill zero after decimal
             tmp2--;
             }
           StrLen+=(digits-tmp+2) ;
           *(ch_p+StrLen)=NULL_CHAR ;
           break ;
          }
        chIndex-- ;//move the pointer
       }
    }
  }
//-------------------------------------------------
 if((*pcb_lp).info.x>window.xr) (*pcb_lp).info.x=window.xr ;//can not print character that out of window range,set an limit
 else if((*pcb_lp).info.x<window.xl) (*pcb_lp).info.x=window.xl ;
 if((*pcb_lp).info.y>window.yb) (*pcb_lp).info.y=window.yb ;//can not print character that out of window range,set an limit
 else if((*pcb_lp).info.y<window.yt) (*pcb_lp).info.y=window.yt ;
//-------------------------------------------------
 if((*pcb_lp).info.ctrl.right)//be reqired to print string at right align(X axis)
  {
   if(((*pcb_lp).info.x+1-(*pcb_lp).info.width)<window.xl) (*pcb_lp).info.width=(*pcb_lp).info.x-window.xl+1 ;//limit to boundary
   if((*pcb_lp).info.ctrl.unicode)//for unicode string
   {
     chIndex=StrLen ;
     tmp=0 ;
     if(font==FONT_UNICODE16)//use 16x16 unicode font
     {
       while((tmp<=(*pcb_lp).info.width)&&(chIndex!=0))
        {
         chIndex-- ;
         UniCode=*(UCode_p+chIndex);
         tmp2=Unicode16[UniCode].sH.cxPix ;
         tmp+=tmp2 ;
         }
      }
     else//default use 12x12 unicode font
     {
       while((tmp<=(*pcb_lp).info.width)&&(chIndex!=0))
        {
         chIndex-- ;
         UniCode=*(UCode_p+chIndex);
         tmp2=Unicode12[UniCode].sH.cxPix ;
         tmp+=tmp2 ;
         }
      }
     if(tmp>(*pcb_lp).info.width)
     {
      tmp-=tmp2 ;
      xBegin=(*pcb_lp).info.x-tmp+1 ;
      chBegin=chIndex+1 ;
      }
     else
     {
      xBegin=(*pcb_lp).info.x-tmp+1 ;
      chBegin=0 ;
      }
     chEnd=StrLen ;
    }
   else//For ANSI character string
   {
      Margin=0 ;
      tmp=(*pcb_lp).info.width ;
      if((font==FONT_ANSI8x8)||(font==FONT_ANSI8x16))//use 8x8 or 8x16 ANSI font
       {
         tmp2=tmp>>3 ;//=tmp divide by 8 get char number max number  can be print
         tmp&=0xf8 ;//actual width for the string
        }
      else//default use 6x8 ANSI font
      {
        tmp2=tmp/6 ;//get max number of char number can be print
        tmp=tmp2*6 ;
       }
//------for ANSI string I will automatic full fill text window with SPACE,
//------if the printting string cannot fullfill the window you specified
      xBegin=(*pcb_lp).info.x+1-tmp ;
      if(StrLen<tmp2)
      {
        Margin=tmp2-StrLen;
        ch_p=(*pcb_lp).str.ch_p;//get first character's address
        for(chIndex=StrLen-1 ;chIndex>=0;chIndex--)
         {
           *(ch_p+chIndex+Margin)=*(ch_p+chIndex);//shift the string to right
          }
        for(chIndex=0;chIndex<Margin ;chIndex++)
         {
          *(ch_p+chIndex)=SPACE_CHAR ;
          }
        chBegin=0 ;
        chEnd=tmp2 ;
       }
      else
      {
        chBegin=StrLen-tmp2 ;
        chEnd=StrLen ;
       }
    }
  }
 else//left align (defualt)
 {
   xBegin=(*pcb_lp).info.x;//get X axis Begin position( column number)
   chBegin=0 ;
   if((*pcb_lp).info.ctrl.unicode)//for unicode character
   {
     chIndex=0 ;
     tmp=0 ;
     if(font==FONT_UNICODE16)
      {
        while((tmp<=(*pcb_lp).info.width)&&(chIndex<StrLen))
        {
          UniCode=*(UCode_p+chIndex++);
          tmp2=Unicode16[UniCode].sH.cxPix ;//use 16x16 font
          tmp+=tmp2 ;
         }
       }
     else
      {
        while((tmp<=(*pcb_lp).info.width)&&(chIndex<StrLen))
        {
          UniCode=*(UCode_p+chIndex++);
          tmp2=Unicode12[UniCode].sH.cxPix ;//use 12x12 font
          tmp+=tmp2 ;
         }
       }
     if(tmp>(*pcb_lp).info.width)
      {
       chEnd=chIndex-1 ;
       tmp-=tmp2 ;
       }
     chEnd=chIndex ;
    }
   else//for ANSI character
   {
    tmp=(*pcb_lp).info.width ;
    if((font==FONT_ANSI8x8)||(font==FONT_ANSI8x16))
    {
      tmp2=tmp>>3;//get max number of char number can be print
      tmp&=0xf8 ;//calculate actual x width can be use
     }
    else
    {
      tmp2=tmp/6 ;//get max number of char number can be print
      tmp=tmp2*6 ;//calculate actual x width can be use
     }
//------for ANSI string I will automatic full fill text window with SPACE,
//------if the printting string cannot fullfill the window you specified
    if(StrLen<tmp2)
    {//need automatic fill with SPACE
      Margin=tmp2-StrLen;
      ch_p=(*pcb_lp).str.ch_p;//get first character's address
      for(chIndex=StrLen;chIndex<tmp2 ;chIndex++)
      {
        *(ch_p+chIndex)=SPACE_CHAR ;
       }
     }
    chEnd=tmp2 ;
   }
  }
 if(chBegin>=chEnd) return ;//specified window can not contain a character
//-----------------------------------------------------------------------------------------
//now I can print string on LCD screen, using the xBegin,chBegin and chEnd information
//String be printed on screen, always aligned to the bottom of specified location
 if((*pcb_lp).info.ctrl.unicode)
 {
   if(font==FONT_UNICODE16)
   {
     yBegin=(*pcb_lp).info.y>>3;// divide by 8
     if(yBegin<1)return ;//not allow to output out of range
     LcdSetPageAdd(yBegin-1) ;
     LcdSetColumnAdd(xBegin) ;
     chIndex=chBegin ;
     while(chIndex<chEnd)
     {
       UniCode=*(UCode_p+chIndex++);
       for(tmp=0;tmp<Unicode16[UniCode].sH.cxPix;tmp++)
       {
         if((*pcb_lp).info.ctrl.reverse)//be required to print reversely
          LcdSendData(~(Unicode16[UniCode].b[tmp])) ;
         else
          LcdSendData(Unicode16[UniCode].b[tmp]) ;
        }
      }
     LcdSetPageAdd(yBegin) ;
     LcdSetColumnAdd(xBegin) ;
     chIndex=chBegin ;
     while(chIndex<chEnd)
     {
       UniCode=*(UCode_p+chIndex++);
       for(tmp=0;tmp<Unicode16[UniCode].sH.cxPix;tmp++)
       {
         if((*pcb_lp).info.ctrl.reverse)//be required to print reversely
           LcdSendData(~(Unicode16[UniCode].b[tmp+16])) ;
         else
           LcdSendData(Unicode16[UniCode].b[tmp+16]) ;
        }
      }
    }
   else//default 12x12 unicode font
   {
     yBegin=(*pcb_lp).info.y>>2;
     if(yBegin<2) return ;//
     if((yBegin+1)&0x01)//top aligned to boundary,first byte can used directly
     {
       LcdSetPageAdd((yBegin>>1)-1) ;
       LcdSetColumnAdd(xBegin) ;
       chIndex=chBegin ;
       while(chIndex<chEnd)
        {
         UniCode=*(UCode_p+chIndex++);
         for(tmp=0;tmp<Unicode12[UniCode].sH.cxPix;tmp++)
          {
           if((*pcb_lp).info.ctrl.reverse)//be required to print reversely
            LcdSendData(~(Unicode12[UniCode].b[tmp])) ;
           else
            LcdSendData(Unicode12[UniCode].b[tmp]) ;
           }
        }
       LcdSetPageAdd(yBegin>>1) ;
       LcdSetColumnAdd(xBegin) ;
       chIndex=chBegin ;
       LcdSendCommand(0xe0);//enter read-modify-write mode
       while(chIndex<chEnd)
        {
         UniCode=*(UCode_p+chIndex++);
         for(tmp=0;tmp<Unicode12[UniCode].sH.cxPix;tmp++)
          {
            //ch2=LcdReadData();//dummy read
//            GPIOD->ODR=DEF_LCD_P_OUT ;
            ch=LcdReadData();//read current display data
            ch&=0xf0 ;
            if((*pcb_lp).info.ctrl.reverse)//be required to print reversely
              ch2=~Unicode12[UniCode].b[tmp+12];
            else
              ch2=Unicode12[UniCode].b[tmp+12];
			ch|=(ch2&0x0f) ;
            LcdSendData(ch) ;
           }
         }
       LcdSendCommand(0xee) ;//end the read-modify-write mode
      }
     else//12x12 symbol's second row align to bundary,but no data can be used directly
     {
       LcdSetPageAdd((yBegin>>1)-1) ;
       LcdSetColumnAdd(xBegin) ;
       chIndex=chBegin ;
       LcdSendCommand(0xe0);//enter read-modify-write mode
       while(chIndex<chEnd)
        {
         UniCode=*(UCode_p+chIndex++);
         for(tmp=0;tmp<Unicode12[UniCode].sH.cxPix;tmp++)
          {
            //ch2=LcdReadData();//dummy read
//            GPIOD->ODR=DEF_LCD_P_OUT ;
            ch=LcdReadData() ;//read current display data
            ch&=0x0f ;
            if((*pcb_lp).info.ctrl.reverse)//be required to print reversely
              ch2=~Unicode12[UniCode].b[tmp] ;
            else
              ch2=Unicode12[UniCode].b[tmp] ;
            ch2<<=4 ;
            LcdSendData(ch2|ch) ;
           }
         }
       LcdSendCommand(0xee) ;//end the read-modify-write mode
       LcdSetPageAdd(yBegin>>1) ;
       LcdSetColumnAdd(xBegin) ;
       chIndex=chBegin ;
       while(chIndex<chEnd)
        {
         UniCode=*(UCode_p+chIndex++);
         for(tmp=0;tmp<Unicode12[UniCode].sH.cxPix;tmp++)
          {
            if((*pcb_lp).info.ctrl.reverse)//be required to print reversely
             {
              ch2=~Unicode12[UniCode].b[tmp];
              ch=~Unicode12[UniCode].b[tmp+12] ;
             }
            else
             {
              ch2=Unicode12[UniCode].b[tmp];
              ch=Unicode12[UniCode].b[tmp+12] ;
              }
            ch2>>=4 ;
            ch2&=0x0f ;
            ch<<=4 ;
            ch&=0xf0 ;
            LcdSendData(ch|ch2) ;
           }
        }
      }
    }
  }
 else//It is ANSI characher
 {
   if(font==FONT_ANSI8x8)
   {//
     yBegin=(*pcb_lp).info.y>>3;
     LcdSetPageAdd(yBegin) ;
     LcdSetColumnAdd(xBegin) ;
     ch_p=(*pcb_lp).str.ch_p ;
     chIndex=chBegin ;
     while(chIndex<chEnd)
      {
       ch=*(ch_p+chIndex++) ;
       for(tmp=0;tmp<ANSI8x8[ch].sH.cxPix;tmp++)
       {
         if((*pcb_lp).info.ctrl.reverse)//be required to print reversely
           LcdSendData(~ANSI8x8[ch].b[tmp]) ;
         else
           LcdSendData(ANSI8x8[ch].b[tmp]) ;
        }
       }
    }
   else if(font==FONT_ANSI8x16)
   {
     yBegin=(*pcb_lp).info.y>>3;
     if(yBegin<1) return ;//out of range
     LcdSetPageAdd(yBegin-1) ;
     LcdSetColumnAdd(xBegin) ;
     ch_p=(*pcb_lp).str.ch_p ;
     chIndex=chBegin ;
     while(chIndex<chEnd)
      {
       ch=*(ch_p+chIndex++) ;
       for(tmp=0;tmp<ANSI8x16[ch].sH.cxPix;tmp++)
       {
         if((*pcb_lp).info.ctrl.reverse)//be required to print reversely
           LcdSendData(~ANSI8x16[ch].b[tmp]) ;
         else
           LcdSendData(ANSI8x16[ch].b[tmp]) ;
        }
       }
     LcdSetPageAdd(yBegin) ;
     LcdSetColumnAdd(xBegin) ;
     ch_p=(*pcb_lp).str.ch_p ;
     chIndex=chBegin ;
     while(chIndex<chEnd)
      {
       ch=*(ch_p+chIndex++) ;
       for(tmp=0;tmp<ANSI8x16[ch].sH.cxPix;tmp++)
       {
         if((*pcb_lp).info.ctrl.reverse)//be required to print reversely
           LcdSendData(~ANSI8x16[ch].b[tmp+8]) ;
         else
           LcdSendData(ANSI8x16[ch].b[tmp+8]) ;
        }
       }
    }
   else//default 6x8
   {
     yBegin=(*pcb_lp).info.y>>3;
     LcdSetPageAdd(yBegin) ;
     LcdSetColumnAdd(xBegin) ;
     ch_p=(*pcb_lp).str.ch_p ;
     chIndex=chBegin ;
     while(chIndex<chEnd)
      {
       ch=*(ch_p+chIndex++) ;
       for(tmp=0;tmp<ANSI6x8[ch].sH.cxPix;tmp++)
       {
         if((*pcb_lp).info.ctrl.reverse)//be required to print reversely
           LcdSendData(~ANSI6x8[ch].b[tmp]) ;
         else
           LcdSendData(ANSI6x8[ch].b[tmp]) ;
        }
       }
    }
 }
 GPIOD->ODR=DEF_LCD_P_OUT ;
}
//------------------------------------------------------------------------------------------
void ClrPrintedStr(UNI_PRINT * pcb_lp)
{
 int8_t   xBegin,xEnd,yBegin ;//store column and page address
 int16_t  tmp ;//temp variable
 uint8_t   ch2,font ;

 if((*pcb_lp).info.width<1) return ;//no need to do clear operation
 font=(*pcb_lp).info.ctrl.font ;//use that user specified font
//-------------------------------------------------
 if((*pcb_lp).info.x>window.xr) (*pcb_lp).info.x=window.xr ;//can not print character that out of window range,set an limit
 else if((*pcb_lp).info.x<window.xl) (*pcb_lp).info.x=window.xl ;
 if((*pcb_lp).info.y>window.yb) (*pcb_lp).info.y=window.yb ;//can not print character that out of window range,set an limit
 else if((*pcb_lp).info.y<window.yt) (*pcb_lp).info.y=window.yt ;
//-------------------------------------------------
 if((*pcb_lp).info.ctrl.right)//be reqired to print string at right align(X axis)
  {
    if(((*pcb_lp).info.x+1-(*pcb_lp).info.width)<window.xl) xBegin=window.xl ;//limit to boundary
    else xBegin=(*pcb_lp).info.x+1-(*pcb_lp).info.width;
	xEnd=(*pcb_lp).info.x ;
   }
 else
  {
	if(((*pcb_lp).info.x-1+(*pcb_lp).info.width)>window.xr) xEnd=window.xr ;
	else xEnd=(*pcb_lp).info.x-1+(*pcb_lp).info.width ;
	xBegin=(*pcb_lp).info.x ;
   }
//-----------------------------------------------------------------------------------------
//now I can clear the printed string on LCD screen, using the xBegin,yBegin and xEnd information
//String be printed on screen, always aligned to the bottom of specified location
 if((*pcb_lp).info.ctrl.unicode)
 {
   if(font==FONT_UNICODE16)
   {
     yBegin=(*pcb_lp).info.y>>3;// divide by 8
     if(yBegin<1)return ;//not allow to output out of range
     LcdSetPageAdd(yBegin-1) ;
     LcdSetColumnAdd(xBegin) ;
     tmp=xBegin ;
     while(tmp<=xEnd)
     {
          LcdSendData(0x00) ;
		  tmp++ ;
      }
     LcdSetPageAdd(yBegin) ;
     LcdSetColumnAdd(xBegin) ;
     tmp=xBegin ;
     while(tmp<=xEnd)
     {
          LcdSendData(0x00) ;
		  tmp++ ;
      }
    }
   else//default 12x12 unicode font
   {
     yBegin=(*pcb_lp).info.y>>2;//Divide by 4
     if(yBegin<2) return ;//
     if((yBegin+1)&0x01)//top aligned to boundary,first byte can used directly
     {
       LcdSetPageAdd((yBegin>>1)-1) ;
       LcdSetColumnAdd(xBegin) ;
       tmp=xBegin ;
       while(tmp<=xEnd)
       {
          LcdSendData(0x00) ;
		  tmp++ ;
        }
       LcdSetPageAdd(yBegin>>1) ;
       LcdSetColumnAdd(xBegin) ;
       tmp=xBegin ;
       LcdSendCommand(0xe0);//enter read-modify-write mode
       while(tmp<=xEnd)
       {
          //ch2=LcdReadData();//dummy read
//         GPIOD->ODR=DEF_LCD_P_OUT ;
         ch2=LcdReadData();//read current display data
         ch2&=0xf0 ;
         LcdSendData(ch2) ;
		 tmp++ ;
        }
       LcdSendCommand(0xee) ;//end the read-modify-write mode
      }
     else//12x12 symbol's second row align to bundary,but no data can be used directly
     {
       LcdSetPageAdd((yBegin>>1)-1) ;
       LcdSetColumnAdd(xBegin) ;
       tmp=xBegin ;
       LcdSendCommand(0xe0);//enter read-modify-write mode
       while(tmp<=xEnd)
       {
         //ch2=LcdReadData();//dummy read
//         GPIOD->ODR=DEF_LCD_P_OUT ;
         ch2=LcdReadData() ;//read current display data
         ch2&=0x0f ;
         LcdSendData(ch2) ;
         tmp++ ;
        }
       LcdSendCommand(0xee) ;//end the read-modify-write mode
       LcdSetPageAdd(yBegin>>1) ;
       LcdSetColumnAdd(xBegin) ;
       tmp=xBegin ;
       while(tmp<=xEnd)
       {
         LcdSendData(0x00) ;
         tmp++ ;
        }
      }
    }
  }
 else//It is ANSI characher
 {
  if(font==FONT_ANSI8x16)
   {
     yBegin=(*pcb_lp).info.y>>3;
     if(yBegin<1) return ;//out of range
     LcdSetPageAdd(yBegin-1) ;
     LcdSetColumnAdd(xBegin) ;
     tmp=xBegin ;
     while(tmp<=xEnd)
     {
       LcdSendData(0x00) ;
       tmp++ ;
       }
     LcdSetPageAdd(yBegin) ;
     LcdSetColumnAdd(xBegin) ;
     tmp=xBegin ;
     while(tmp<=xEnd)
     {
       LcdSendData(0x00) ;
       tmp++ ;
       }
    }
   else
   {//
     yBegin=(*pcb_lp).info.y>>3;
     LcdSetPageAdd(yBegin) ;
     LcdSetColumnAdd(xBegin) ;
     tmp=xBegin ;
     while(tmp<=xEnd)
     {
         LcdSendData(0x00) ;
         tmp++ ;
      }
    }
 }
 GPIOD->ODR=DEF_LCD_P_OUT ;
}
//------------------------------------------------------------------------------------------
__inline void LcdSetPageAdd(uint8_t page)
{
  LcdSendCommand ((page&0x0f)|0xb0) ;//set page address(0-8) of the display RAM
}
__inline void LcdSetColumnAdd(uint8_t col)
{
  LcdSendCommand (((col>>4)&0x0f)|0x10) ;//first set 4 higher bits
  LcdSendCommand ((col&0x0f)) ;//then set 4 lower bits
}
//--------------------------------------------------------------------------------------------------
// Name : LcdInit
// Description : Performs SPI & LCD controller initialization.
// Argument(s) : None if you don't use DRAW_OVER_BACKGROUND
//               background -> Pointer to background in another case
// Return value : None.
//--------------------------------------------------------------------------------------------------
#ifdef DRAW_OVER_BACKGROUND
void LcdInit (const uint8_t * background)
#else  //DRAW_OVER_BACKGROUND
void LcdInit ( void )
#endif //DRAW_OVER_BACKGROUND
{
  int16_t i;
  uint8_t j,k;

  LcdSendCommand (0xb0) ;//set page address of the display RAM is 0
  LcdSendCommand (0x10) ;//set 4 higher bits of column address of display RAM in register to 0
  LcdSendCommand (0x00) ;//set 4 lower bits of column address of display RAM in register to 0
  j=0 ;
  k=0 ;
#ifdef DRAW_OVER_BACKGROUND
  Background = background;
  for(i=0; i<1024; i++)
  {
    LcdSendData(Background[i]);
    j++ ;
    if(j==128)
    {
     j=0 ;
     k++ ;
     LcdSetPageAdd(k) ;//increase page number
     LcdSetColumnAdd(0);
     }
   }
#else  //DRAW_OVER_BACKGROUND
  for(i=0; i<1024; i++)
  {
    LcdSendData(0x00);
    j++ ;
    if(j==128)
    {
     j=0 ;
     k++ ;
     LcdSetPageAdd(k) ;//increase page number
     LcdSetColumnAdd(0);
     }
   }
#endif //DRAW_OVER_BACKGROUND
  GPIOD->ODR=DEF_LCD_P_OUT ;
}

//--------------------------------------------------------------------------------------------------
// Name : SetBackground
// Description : SetPointer to dispaly background.
// Argument(s) : dataPtr -> Pointer on aray that contains background.
// Return value : None.
// Note : Awailale only with DRAW_OVER_BACKGROUND directive
//--------------------------------------------------------------------------------------------------
#ifdef DRAW_OVER_BACKGROUND
void LcdSetBackground ( const uint8_t *dataPtr )
{
  Background = dataPtr;
}
#endif //DRAW_OVER_BACKGROUND

//--------------------------------------------------------------------------------------------------
// Name : LcdContrast
// Description : Set display brightness(contrast)of the LCD.
// Argument(s) : contrast -> Contrast value from 0x00 to 0x3F.
// Return value : None.
//--------------------------------------------------------------------------------------------------
void LcdContrast ( uint8_t contrast )
{
  //----2024.04.21
  LcdSendCommand (0x24) ;//
  LcdSendCommand (0x81) ;//
  LcdSendCommand (0x21) ;//
  //----
  //--2024.04.21  LcdSendCommand (0x81)     ;//Use this command force LCD controller enter electronic volume mode
  LcdSendCommand (contrast) ;//set electronic volume to specfied value(see DS Page22 for detail)
  GPIOD->ODR=DEF_LCD_P_OUT ;
}

//--------------------------------------------------------------------------------------------------
// Name : LcdClear
// Description : Clears the display.
// Argument(s) : None.
// Return value : None.
//--------------------------------------------------------------------------------------------------
void LcdClear ( void )
{
  uint16_t i;
  uint8_t j,k;
  LcdSendCommand (0xb0) ;//set page address of the display RAM is 0
  LcdSendCommand (0x10) ;//set 4 higher bits of column address of display RAM in register to 0
  LcdSendCommand (0x00) ;//set 4 lower bits of column address of display RAM in register to 0
  j=0 ;
  k=0 ;
#ifdef DRAW_OVER_BACKGROUND
  for(i=0; i<1024; i++)
  {
    LcdSendData(Background[i]);
    j++ ;
    if(j==128)
    {
     j=0 ;
     k++ ;
     LcdSetPageAdd(k) ;//increase page number
     LcdSetColumnAdd(0);
     }
   }
#else  //DRAW_OVER_BACKGROUND
  for(i=0; i<1024; i++)
  {
    LcdSendData(0x00);
    j++ ;
    if(j==128)
    {
     j=0 ;
     k++ ;
     LcdSetPageAdd(k) ;//increase page number
     LcdSetColumnAdd(0);
     }
   }
#endif //DRAW_OVER_BACKGROUND
  GPIOD->ODR=DEF_LCD_P_OUT ;
}
//----------------------------------------------------------------------------------------------------
void LcdEnterSleep(void)
{
  LcdSendCommand (0xac) ;//set static indicator to OFF status(always to do this when use YM-MSB068 LCM)
  LcdSendCommand (0xae) ;//Turn off the display
  LcdSendCommand (0xa5) ;//issue entire display ON,after this command the LCD module enter sleep mode
  U_Area.LcdCtrl.Check.DisplayOn=0 ;
  U_Area.LcdCtrl.Check.SleepMode=1 ;//set a flag to indicate the status of the LCD module
  GPIOD->ODR=DEF_LCD_P_OUT ;
}
//----------------------------------------------------------------------------------------------------
void LcdWakeup(void)
{
  LcdSendCommand (0xa4) ;//issue entire display OFF(normal display)
  U_Area.LcdCtrl.Check.SleepMode=0 ;//set a flag to indicate the status of the LCD module
  GPIOD->ODR=DEF_LCD_P_OUT ;
}
//----------------------------------------------------------------------------------------------------
void LcdReset(void)
{
  U_Area.LcdCtrl.Check.Flag=0 ;//this will guide the LCD init daemon to perform reset operation
  U_Area.LcdCtrl.InitSta=0 ;//
//  LcdModuleInitDaemon() ;//call the initialization function
}
//--------------------------------------------------------------------------------------------------
// Name : LcdSendData
// Description : Sends data to display controller.
// Argument(s) : data -> Data to be sent
// Return value : None.
//--------------------------------------------------------------------------------------------------
void LcdSendData ( uint8_t data )
{
__disable_irq() ;
  GPIOA->BSRRH=(LCD_WR_PIN+LCD_CS1_PIN) ;//lcd nWR  to low
  GPIOD->ODR=(uint16_t)data ;//output data to bus
  GPIOA->BSRRL=(LCD_WR_PIN+LCD_CS1_PIN) ;// lcd nWR  to high (latch data)
__enable_irq() ;
}

//--------------------------------------------------------------------------------------------------
// Name : LcdSendCommand
// Description : Sends command to display controller.
// Argument(s) : command -> Command to be sent
// Return value : None.
//--------------------------------------------------------------------------------------------------
void LcdSendCommand ( uint8_t command )
{
__disable_irq() ;
  GPIOA->BSRRH=(LCD_WR_PIN+LCD_A0_PIN+LCD_CS1_PIN) ;// lcd nWR and A0 pin to low,enter command access status
  GPIOD->ODR=(uint16_t)command ;//output command to bus
  GPIOA->BSRRL=(LCD_WR_PIN+LCD_A0_PIN+LCD_CS1_PIN)  ;//lcd nWR  to high (latch data)
__enable_irq() ;
}
//--------------------------------------------------------------------------------------------------
// Name : LcdReadData
// Description : Read display data from current page and column address.No dummy read inserd that required by
// NT7532 when column address setup
// Argument(s) : None.
// Return value : read back display data.
//--------------------------------------------------------------------------------------------------
uint8_t LcdReadData(void)
{
  volatile uint8_t data ;
__disable_irq() ;
  GPIOD->MODER =0x00540000 ;//set PD.0-7 to input status,other pin to normal default function setting
  GPIOA->BSRRH=(LCD_RD_PIN+LCD_CS1_PIN)  ;// lcd nRD pin to low,enter display data access status
  //data =(uint8_t)GPIOD->IDR ;//input data from LCD controller
  __nop() ;
  __nop() ;
  GPIOA->BSRRL=(LCD_RD_PIN+LCD_CS1_PIN) ;//lcd nCS1 to high release LCD controller
  __nop() ;
  __nop() ;
  __nop() ;
  GPIOA->BSRRH=(LCD_RD_PIN+LCD_CS1_PIN)  ;// lcd nRD pin to low,enter display data access status
  __nop() ;
  __nop() ;
  __nop() ;
  data =(uint8_t)GPIOD->IDR ;//input data from LCD controller
  GPIOA->BSRRL=(LCD_RD_PIN+LCD_CS1_PIN) ;//lcd nCS1 to high release LCD controller
  GPIOD->MODER =0x00545555 ;//set PD.0-7 to output status,other pin to normal default function setting
__enable_irq() ;
  return data ;
}
//--------------------------------------------------------------------------------------------------
// Name : LcdReadStatus
// Description : Read internal status from NT7532.
// Argument(s) : None.
// Return value : Status of the NT7532.
//--------------------------------------------------------------------------------------------------
uint8_t LcdReadStatus(void)
{
  uint8_t status ;
__disable_irq() ;
  GPIOD->MODER =0x00540000 ;//set PD.0-7 to input status,other pin to normal default function setting
  GPIOA->BSRRH=(LCD_RD_PIN+LCD_A0_PIN+LCD_CS1_PIN)  ;// lcd nWR and A0 pin to low,enter status read status
  __nop() ;//wait data valid
  status =(uint8_t)GPIOD->IDR ;//Get LCD controller status
  GPIOA->BSRRL=(LCD_RD_PIN+LCD_A0_PIN+LCD_CS1_PIN) ;// return to display data access status,lcd nRD  to high
  GPIOD->MODER =0x00545555 ;//set PD.0-7 to output status,other pin to normal default function setting
__enable_irq() ;
  return status ;
}
//--------------------------------------------------------------------------------------------------
// Name : LcdModuleInitDaemon
// Description : Daemon routine used to performs the LCD module controller initialization.
// Argument(s) : None
// Return value : None.
//--------------------------------------------------------------------------------------------------
void LcdModuleInitDaemon(void)
{
 if(U_Area.LcdCtrl.Check.ModuleInitOk) return ;//No need to perform module init operation
 switch(U_Area.LcdCtrl.InitSta)
 {
  case 0 :
          //GPIOB->BSRRL =0x1000 ;////LED2 ON  --debug
          GPIOA->BSRRL=LCD_RST_PIN ;//release LCD reset signal to high
          U_Area.LcdCtrl.Check.Flag=0 ;
          U_Area.LcdCtrl.InitSta=2 ;
          break ;
  case 2 :
          GPIOA->BSRRH=LCD_RST_PIN ;//put LCD reset signal to low
          U_Area.LcdCtrl.LcdPage=0 ;
          U_Area.LcdCtrl.LcdColumn=0 ;
          U_Area.LcdCtrl.InitSta=4 ;
          Timer10[LCD_TIMER].csr.csword=0x4000 ;//enable the Timer10[LCD_TIMER] to generate wait time
          Timer10[LCD_TIMER].pv=LCD_VC_TIME;//wait time out value x10mS
          Timer10[LCD_TIMER].cv=0 ;
          break ;
  case 4 :
          if(Timer10[LCD_TIMER].csr.csbit.q)
          {
            GPIOA->BSRRL=LCD_RST_PIN ;//release LCD reset signal to high
            U_Area.LcdCtrl.InitSta=6 ;
		  }
          break ;
  case 6 :
          LcdSendCommand (0xa0) ;//Set the display RAM address SEG output correspondence to Normal Direction
                                 //(or set to 0xa1 for Reverse direction)
          LcdSendCommand (0xa6) ;//Set normal indication(or 0xa7 for Reverse display)
          LcdSendCommand (0xc8) ;//Set scan direction of COM output pad to Reverse(COM63/53/47/31->COM0)
                                 //(0xc0 for Normal(COM0->COM63/53/47/31) see datasheet Page17 for detail)
          LcdSendCommand (0xa2) ;//Set LCD drive voltage bias ratio (See datasheet Page30 for detail)
          LcdSendCommand (0x2c) ;//VC ,voltage booster turn on command
          Timer10[LCD_TIMER].csr.csword=0x4000 ;//enable the Timer10[LCD_TIMER] to generate wait time
          Timer10[LCD_TIMER].pv=LCD_VC_TIME;//wait time out value x10mS
          Timer10[LCD_TIMER].cv=0 ;
          U_Area.LcdCtrl.InitSta=8 ;//switch to wait VC command take effect state
          break ;
  case 8 :
          if(Timer10[LCD_TIMER].csr.csbit.q)
          {
           LcdSendCommand (0x2e) ;//VR ,issue voltage regulator turn on command
           Timer10[LCD_TIMER].pv=LCD_VR_TIME;//wait time out value x10mS
           Timer10[LCD_TIMER].cv=0 ;
           U_Area.LcdCtrl.InitSta=10 ;//switch to wait VR command take effect state
           }
          break ;
  case 10 :
          if(Timer10[LCD_TIMER].csr.csbit.q)
          {
           LcdSendCommand (0x2f) ;//VF ,issue voltage follower turn on command
           Timer10[LCD_TIMER].pv=LCD_POW_ON_TIME;//wait time out value x10mS
           Timer10[LCD_TIMER].cv=0 ;
           U_Area.LcdCtrl.InitSta=12 ;//switch to wait LCD system voltage set up
                                      //time need to wait determined by smoothing capacitor charge time
           }
          break ;
  case 12 :
          if(Timer10[LCD_TIMER].csr.csbit.q)
          {
           LcdSendCommand (0x24) ;//set internal resistor ratio(Ra/Rb)(See DS Page23)--(0x27)
           LcdSendCommand (0x81) ;//Use this command force LCD controller enter electronic volume mode
           Timer10[LCD_TIMER].pv=LCD_REF_ON_TIME;//wait time out value x10mS
           Timer10[LCD_TIMER].cv=0 ;
           U_Area.LcdCtrl.InitSta=14 ;//switch to wait reference voltage set up state
           }
          break ;
  case 14 :
          if(Timer10[LCD_TIMER].csr.csbit.q)
          {
           LcdSendCommand (30) ;//set electronic volume to 32(see DS Page22 for detail,orignal 50)--(50)
                                //after this command issued LCD controller exit electronic volume set mode
           Timer10[LCD_TIMER].pv=LCD_VOL_ON_Time;//wait time out value x10mS
           Timer10[LCD_TIMER].cv=0 ;
           LcdSendCommand (0xb0) ;//set page address of the display RAM is 0
           LcdSendCommand (0x10) ;//set 4 higher bits of column address of display RAM in register to 0
           LcdSendCommand (0x00) ;//set 4 lower bits of column address of display RAM in register to 0
           U_Area.LcdCtrl.InitSta=16 ;//switch to wait electronic volume set command take effect state
           }
          break ;
  case 16 :
#ifdef DRAW_OVER_BACKGROUND
           LcdSetBackground (back1.b) ;//set default background
#endif
           LcdClear () ;//clear the LCD screen
           U_Area.LcdCtrl.InitSta=18 ;//switch to wait finish the initialization state
          break ;
  case 18 :
          if(Timer10[LCD_TIMER].csr.csbit.q)
          {
           LcdSendCommand (0x40) ;//set display start line(RAM display line for COM0)(see DS Page27 for detail)
           LcdSendCommand (0xb0) ;//set page address of the display RAM is 0
           LcdSendCommand (0x10) ;//set 4 higher bits of column address of display RAM in register to 0
           LcdSendCommand (0x00) ;//set 4 lower bits of column address of display RAM in register to 0
           GPIOD->ODR=DEF_LCD_P_OUT ;
           U_Area.LcdCtrl.Check.ModuleInitOk=1 ;//Set init OK flag
           U_Area.LcdCtrl.InitSta=0 ;           //now LCD module init complete
           Timer10[LCD_TIMER].csr.csword=0x0000 ;//disable the timer
           //GPIOB->BSRRH =0x1000 ;////LED2 OFF  --debug
           }
          break ;
  default :
           U_Area.LcdCtrl.Check.ModuleInitOk=0 ;//clear OK flag guide to start LCD module initialization
           U_Area.LcdCtrl.InitSta=0 ;//reset to LCD module init start state
 }
}
//-EOF-----------------------------------------------------------------------------------------------
