/**
  ******************************************************************************
  * @file    /src/Main.c 
  * <AUTHOR>
  * @version V1.0.0
  * @date    01-April-2011
  * @brief   Main program body.
  ******************************************************************************
  */

/* Includes ------------------------------------------------------------------*/
#include "Main.h"

/* Extern variables ----------------------------------------------------------*/

/** @addtogroup binary_template
  * @{
  */

/** @addtogroup SysTick
  * @{
  */ 

/* Private typedef -----------------------------------------------------------*/
/* Private define ------------------------------------------------------------*/
/* Private macro -------------------------------------------------------------*/
/* Private variables ---------------------------------------------------------*/

/* Private function prototypes -----------------------------------------------*/

/* Private functions ---------------------------------------------------------*/

/**
  * @brief  Main program.
  * @param  None
  * @retval None
  */
int main(void)
{
 /* Set the Vector Table base location at 0x3000 */ 
 //NVIC_SetVectorTable(NVIC_VectTab_FLASH, 0x3000);
 AppMain_fp=__TestMain ;
 Exi15_10_isr_fp=NoOperation ;
 SysTick_isr_fp=NoOperation ;
 Tim9_isr_fp=NoOperation ;
 Tim10_isr_fp=NoOperation ;
 Tim11_isr_fp=NoOperation ;
 Proc10mS_fp=NoOperation ;
 Proc100mS_fp=NoOperation ;
 Proc1S_fp=NoOperation ;
 Proc1H_fp=NoOperation ;
 TxToken_fp=NoOperation ;
//----	
 PanelDatTxLoad_fp=NoOperation  ;
 MachDatRx_fp=NoOperation  ;
 PanelLcdPrint_fp=NoOperation  ;
 GetKeyID_fp=NoOperation  ;

#ifndef PROG_DEBUG	
 if(FLASH_OB_GetRDP()==RESET)
 {
	FLASH_OB_Unlock();
    FLASH_OB_RDPConfig(0x11);//Set to RDP level 1--(must not set to 0xcc --level 2 will permanently disable JTAG like fuse)
    FLASH_OB_Launch();
    FLASH_OB_Lock() ;	 
  }
#endif
  
 RfTT_p=(RfTickTokenCtrl_t *) &APP_PB.BiTickToken ;//Use bidirection mode tick and token config
 SysConfigInit() ;//Call to get configuartion parameter for this device--APP_PA ,APP_PC_p ,APP_PD_p..etc
 if((APP_PA.work_mode&DEVICE_MODE_MASK)==FYF25_MODE)
 {//This device be configured to work in FYF25 mode
   AppMain_fp=__F25Main ;
   Exi15_10_isr_fp=EXTI15_10_ISR_01 ;
   SysTick_isr_fp=NoOperation ;
   Tim9_isr_fp=TIM9_ISR_F25 ;
   Tim10_isr_fp=RfTickTimer_ISR_F25 ;
   Tim11_isr_fp=TIM11_ISR_F25 ;
   Proc10mS_fp=Proc10mS_F25 ;
   Proc100mS_fp=Proc100mS_F25 ;
   Proc1S_fp=Proc1S_F25 ;
   Proc1H_fp=Proc1H_F25 ;
   TxToken_fp=TxTokenGenProc_Uni ;
   RfTT_p=(RfTickTokenCtrl_t *) &APP_PB.UniTickToken ;
  }
 else if(((APP_PA.work_mode&DEVICE_MODE_MASK)==FYF35_SHR_MODE)||((APP_PA.work_mode&DEVICE_MODE_MASK)==FYF35_TUN_MODE))
 {//This device be configured to work in FYF35 mode
   AppMain_fp=__F35Main ;
   Exi15_10_isr_fp=EXTI15_10_ISR_021 ;
   SysTick_isr_fp=SysTickIsr_F35 ;
   Tim9_isr_fp=TIM9_ISR_F35 ;
   Tim10_isr_fp=RfTickTimer_ISR_F35 ;
   Tim11_isr_fp=TIM11_ISR_F35 ;
   Proc10mS_fp=Proc10mS_F35 ;
   Proc100mS_fp=Proc100mS_F35 ;
   Proc1S_fp=Proc1S_F35 ;
   Proc1H_fp=Proc1H_F35 ;
   TxToken_fp=TxTokenGenProc_Bi ;
   if((APP_PA.work_mode&DEVICE_MODE_MASK)==FYF35_SHR_MODE)
   {
     PanelDatTxLoad_fp=ShrPanelDatTxLoad_021  ;
     MachDatRx_fp=ShrMachDatRxDaemon_021  ;
     PanelLcdPrint_fp=Shr_LCD_print  ;
#ifdef  SHR_S160_MODE
	 GetKeyID_fp=S160Tx_GetKeyID  ;  
#else
     GetKeyID_fp=Shr_GetKeyID  ;
#endif
    }
   else
   {//Run tunneller mode
     PanelDatTxLoad_fp=TunPanelDatTxLoad_021  ;
     MachDatRx_fp=TunMachDatRxDaemon_021  ;
	 if((APP_PA.work_mode&MACHINE_TYPE_MASK)==MACHINE_TYPE_S260T1) 
     {		 
       PanelLcdPrint_fp=S260T1_LCD_print  ;
       GetKeyID_fp=S260T1_GetKeyID  ;
	  }
	 else
     {//I assume it is S260T2
       PanelLcdPrint_fp=S260T2_LCD_print  ;
       GetKeyID_fp=S260T2_GetKeyID  ;
	  }		 
    }	   
  }
 else if((APP_PA.work_mode&DEVICE_MODE_MASK)==FYS30_021_MODE)
 {//This device be configured to work in FYS30_021 mode
   AppMain_fp=__S30Main_021 ;
   Exi15_10_isr_fp=EXTI15_10_ISR_021 ;
   SysTick_isr_fp=NoOperation ;
   Tim9_isr_fp=TIM9_ISR_S30 ;
   Tim10_isr_fp=RfTickTimer_ISR_S30 ;
   Tim11_isr_fp=TIM11_ISR_S30 ;
   Proc10mS_fp=Proc10mS_S30_021 ;
   Proc100mS_fp=Proc100mS_S30_021 ;
   Proc1S_fp=Proc1S_S30_021 ;
   Proc1H_fp=Proc1H_S30_021 ;
   TxToken_fp=NoOperation ;
  }
 else if((APP_PA.work_mode&DEVICE_MODE_MASK)==FYS30_01_MODE)
 {//This device be configured to work in FYS30_01 mode 
   AppMain_fp=__S30Main_01 ;
   Exi15_10_isr_fp=EXTI15_10_ISR_01 ;
   SysTick_isr_fp=NoOperation ;
   Tim9_isr_fp=TIM9_ISR_S30 ;
   Tim10_isr_fp=RfTickTimer_ISR_S30 ;
   Tim11_isr_fp=TIM11_ISR_S30 ;
   Proc10mS_fp=Proc10mS_S30_01 ;
   Proc100mS_fp=Proc100mS_S30_01 ;
   Proc1S_fp=Proc1S_S30_01 ;
   Proc1H_fp=Proc1H_S30_01 ;
   TxToken_fp=NoOperation ;
  }
 
 //------Now begin to enter particular operation mode
 (*AppMain_fp)() ;//Normally this function call never return.

}

//-------END of main----------
#ifdef  USE_FULL_ASSERT
/**
  * @brief  Reports the name of the source file and the source line number
  *         where the assert_param error has occurred.
  * @param  file: pointer to the source file name
  * @param  line: assert_param error line source number
  * @retval None
  */
void assert_failed(uint8_t* file, uint32_t line)
{ 
  /* User can add his own implementation to report the file name and line number,
     ex: printf("Wrong parameters value: file %s on line %d\r\n", file, line) */

  /* Infinite loop */
  while (1)
  {
  }
}
#endif

/**
  * @}
  */

/**
  * @}
  */

/******************* (C) COPYRIGHT 2011 STMicroelectronics *****END OF FILE****/
