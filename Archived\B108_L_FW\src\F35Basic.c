#include "F35Basic.h"

void  Init108_F35Mode(void)
{//Init the 38108 board(main environment,like:STM32L15x )for Fyf35 mode
  //Begin to init application data
  ClrObj16((uint16_t *)&S_Area,sizeof(SysAreaStruct)/2) ;
  ClrObj16((uint16_t *)&U_Area,sizeof(UserAreaStruct)/2) ;

  U_Area.BootCounter=BootDats->rst_count ;

  U_Area.AreaFlag100=0xf100 ;//for debug
  U_Area.AreaFlag400=0xf400 ;
  U_Area.AreaFlag441=0xf441 ;
	
  DoBufIndex=0 ;
  EmptyBufIndex=0 ;
	
//-------------------------------------------------------------------------------
  __disable_irq();//Disable all interrupt
  RCC->AHBENR  = 0x0100803f ;//Enable DMA1,FLITF,GPIOA-H
  RCC->APB2ENR = 0x0000121d ;//Enable SPI1,ADC1,TIM11[bit4],,TIM10[bit3],TIM9,SYSCFGR--Use TIM9[bit2] to trigger ADC convertion,TIM11[bit4]
  RCC->APB1ENR = 0x10000000 ;//Enable PWR,0x10020000->USART2--in FYS30 mode

  //------------------------
  //  SYSCFG->EXTICR[0]=0x0000 ;//EXTI line0-3 config
  //  SYSCFG->EXTICR[1]=0x0000 ;//EXTI line7-4 config
  SYSCFG->EXTICR[2]=0x4400 ;//EXTI line11-8 config,EXTI Line 10,11 use PE.10(DIO,GDO2),11(DCLK,GDO0) 
  //  SYSCFG->EXTICR[3]=0x0000 ;//EXTI line15-12 config

  //--Init the STM32L15x GPIO for Fyf35 mode---
  //GP output--MODER[1:0]=01,
  //AF output--MODER[1:0]=10,
  //GP or AF input--MODER[1:0]=00,
  //Analog input--MODER[1:0]=11 and PUPDR [1:0]=00
  //OSPEEDR[1:0]=00-400kHz,=01-2MHz,10-10MHz,11-40MHz
  //PUPDR[1:0]=00-No pull-up,pull-down,=01-Pull up,=10-Pull-down
  //OTYPER[0]=0-Output push-pull(reset state),=1-Output open-drain
  GPIOE->ODR     =0x00001000 ;
  GPIOE->MODER   =0xa9000000 ;
  GPIOE->OTYPER  =0x00000000 ;
  GPIOE->OSPEEDR =0xa9000000 ;
  GPIOE->PUPDR   =0x00055505 ;//->0x00a25505
  GPIOE->LCKR    =0x000000f3 ;
//   GPIOE->AFR[0]  =0x00000000 ;
  GPIOE->AFR[1]  =0x55500000 ;
  //---
#ifdef DEV_DEBUG
  GPIOA->ODR     =0x0000bb04 ;
  GPIOA->MODER   =0xa95500a4 ;//--use RS485 for debug
  GPIOA->OTYPER  =0x00000000 ;
  GPIOA->OSPEEDR =0x01550000 ;
  GPIOA->PUPDR   =0x64005409 ;//
#else
  GPIOA->ODR     =0x0000bb04 ;
  GPIOA->MODER   =0xa9550000 ;//--RS485 interface not used 
  GPIOA->OTYPER  =0x00000000 ;
  GPIOA->OSPEEDR =0x01550000 ;
  GPIOA->PUPDR   =0x64005429 ;//
#endif
  GPIOA->LCKR    =0x0000ffef ;
  GPIOA->AFR[0]  =0x00007700 ;//0x00000000 ,--debug
//  GPIOA->AFR[1]  =0x00000000 ;
  //---
  GPIOB->ODR     =0x00000090 ;
  GPIOB->MODER   =0x55105690 ;
  GPIOB->OTYPER  =0x00000000 ;
  GPIOB->OSPEEDR =0x000054c0 ;
  GPIOB->PUPDR   =0x00480105 ;
  GPIOB->LCKR    =0x0000ffff ;
//  GPIOB->AFR[0]  =0x00000000 ;
  GPIOB->AFR[1]  =0x33000000 ;//PB.14 maps to AFIO3-TIM9_CH2,PB15 maps to AFIO3-TIM11_CH1
  //---
  GPIOC->ODR     =0x00000000 ;
  GPIOC->MODER   =0xa5500015 ;
  GPIOC->OTYPER  =0x00000000 ;
  GPIOC->OSPEEDR =0x00000000 ;
  GPIOC->PUPDR   =0x00000540 ;
  GPIOC->LCKR    =0x0000ffff ;
//  GPIOC->AFR[0]  =0x00000000 ;
//  GPIOC->AFR[1]  =0x00000000 ;
  //---
  GPIOD->ODR     =0x00000000 ;
  GPIOD->MODER   =0x00545555 ;
  GPIOD->OTYPER  =0x00000000 ;
  GPIOD->OSPEEDR =0x00005555 ;
  GPIOD->PUPDR   =0x0001aaaa ;
  GPIOD->LCKR    =0x0000fe00 ;
//  GPIOD->AFR[0]  =0x00000000 ;
//  GPIOD->AFR[1]  =0x00000000 ;
  //---
  GPIOH->ODR     =0x00000000 ;
  GPIOH->MODER   =0x0000001a ;
  GPIOH->OTYPER  =0x00000000 ;
  GPIOH->OSPEEDR =0x00000000 ;
  GPIOH->PUPDR   =0x00000000 ;
  GPIOH->LCKR    =0x00000007 ;
//  GPIOH->AFR[0]  =0x00000000 ;
//  GPIOH->AFR[1]  =0x00000000 ;
//--
  if(GPIOE->IDR&0x0100)//PE.8 = '1' ,the device be set to worked in left mode
  {
    Def_CRC_Seed=LEFT_CRC_SEED ;//and keep the PE.8 IPU configuration status
   }
  else //PE.8 = '0' ,the device be set to worked in right mode
  {
    Def_CRC_Seed=RIGHT_CRC_SEED ;
	GPIOE->PUPDR&=0xfffcffff ;
	GPIOE->PUPDR|=0x00020000 ;//Set PE.8 to IPD status--reduce power consumption
   }
//--Lock most of the pin configuration

}
//-----------------
void  SysTickIsr_F35(void)
{
  ;//No operation in this 1mS periodic interrupt
 }
//-----------------
void  InitTIM9_F35(void )
{//Timer9 be set to generate 1 Second time periodical interrupt
//------------------------
  TIM9->CR1  =0x0000 ;
  TIM9->CNT  =0x0000 ;//
  TIM9->ARR  =32768  ;//Set to 1Hz interrupt
  TIM9->SMCR =0x4000 ;
  TIM9->CR2  =0x0020 ;//Update event as TRGO --to trigger the ADC 
  TIM9->SR   =0x0000 ;//Clear the TIM9 inperrupt flag
  TIM9->DIER =0x0001 ;//Enable TIM9 interrupt
  TIM9->CR1  =0x0001 ;//Enable the timer
}

//---------------
void  InitTIM11_F35(void )
{ //Timer11 be set to generate 0.9765mS time periodical interrupt
  //------------------------
  TIM11->CR1  =0x0000 ;
  TIM11->CNT  =0x0000 ;//
  TIM11->ARR  =32  ;//Set to 1024 Hz interrupt
  TIM11->SMCR =0x4000 ;
  TIM11->SR   =0x0000 ;//Clear the TIM11 inperrupt flag
  TIM11->DIER =0x0001 ;//Enable TIM11 interrupt
  TIM11->CR1  =0x0001 ;//Enable the timer

}

//---------------
void  TIM9_ISR_F35(void)
{//1S periodic interrupt
  //-----------
  if(S_Area.ActTimeOutMonitor>0)
  {
    S_Area.ActTimeOutMonitor-- ;
    if(S_Area.ActTimeOutMonitor<=0)
    {
	  if((APP_PA.work_mode&NO_AUTO_SHUTOFF)==0)//Auto shutoff function not be disabled
        IsrFlag|=IFG_TO_BE_SHUTOFF ;//guide the system shut down automaticlly
     }
   }
  if(S_Area.LCD_BL_Timer>0) S_Area.LCD_BL_Timer-- ;
  //-----------
  //SetStartAdcFlag ;//Set flag --Request to start AD Convertion
}
//---------------------------------
void  RfTickTimer_ISR_F35(void ) 
{//99.94mS periodic interrupt
  if(S_Area.RFTickCount>0)
  {
    S_Area.RFTickCount-- ;
   }
  else
  {
    S_Area.RFTickCount=RfTT_p->TokenPool[S_Area.TPool_p].Interval-1;
    IsrFlag |=IFG_GTOKEN_EN  ;
   }
  if(IsrFlag&IFG_KB_CHANGED)
  {
    IsrFlag &=~IFG_KB_CHANGED ;
    IsrFlag |=IFG_GTOKEN_EN ;
   }
  if(S_Area.SyncTimeOut>0)
  {
    S_Area.SyncTimeOut-- ;
    if(S_Area.SyncTimeOut<=0)
      ClearSyncOpOkFlag ;
   }
}

//---------------
void  TIM11_ISR_F35(void )
{//1024Hz-0.9765ms periodic interrupt
  //-----------
  TBM_ISR();
  if(GFlag&GF_SYS_INIT_OK)
  {
   if(get_lock())
   {
     RealTimeFunction() ;//Main program function ,RT task
     release_lock() ;
    }
  }
}
//------------


