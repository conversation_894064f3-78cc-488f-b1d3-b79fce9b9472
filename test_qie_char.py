import tkinter as tk
from tkinter import ttk, messagebox
from PIL import Image, ImageDraw, ImageFont
import numpy as np

class FontGenerator:
    def __init__(self):
        self.window = tk.Tk()
        self.window.title("12x12 宋体汉字取模工具")
        self.window.geometry("800x600")
        self.setup_ui()
        
        # 尝试加载字体
        try:
            self.font = ImageFont.truetype("simsun.ttc", 12)
        except:
            try:
                self.font = ImageFont.truetype("simsun.ttf", 12)
            except:
                messagebox.showwarning("字体警告", "未找到宋体字体，将使用默认字体，结果可能不准确")
                self.font = ImageFont.load_default()

    def setup_ui(self):
        # 输入区域
        input_frame = ttk.Frame(self.window, padding="10")
        input_frame.pack(fill=tk.X)
        
        ttk.Label(input_frame, text="输入汉字:").grid(row=0, column=0, sticky=tk.W)
        self.text_entry = ttk.Entry(input_frame, width=20)
        self.text_entry.grid(row=0, column=1, sticky=tk.W+tk.E, padx=5)
        self.text_entry.insert(0, "切")
        
        ttk.Button(input_frame, text="生成字模", command=self.generate_font).grid(row=0, column=2, padx=5)
        
        # 预览区域
        preview_frame = ttk.LabelFrame(self.window, text="点阵预览", padding="10")
        preview_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        self.canvas = tk.Canvas(preview_frame, width=144, height=144, bg="white")
        self.canvas.pack(pady=10)
        
        # 输出区域
        output_frame = ttk.LabelFrame(self.window, text="字模输出", padding="10")
        output_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 创建标签页
        notebook = ttk.Notebook(output_frame)
        notebook.pack(fill=tk.BOTH, expand=True)
        
        # C代码标签页
        c_frame = ttk.Frame(notebook, padding="5")
        notebook.add(c_frame, text="C代码")
        
        self.c_text = tk.Text(c_frame, height=15, width=80)
        scrollbar_c = ttk.Scrollbar(c_frame, orient=tk.VERTICAL, command=self.c_text.yview)
        self.c_text.configure(yscrollcommand=scrollbar_c.set)
        self.c_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar_c.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 二进制标签页
        bin_frame = ttk.Frame(notebook, padding="5")
        notebook.add(bin_frame, text="二进制")
        
        self.bin_text = tk.Text(bin_frame, height=15, width=80)
        scrollbar_bin = ttk.Scrollbar(bin_frame, orient=tk.VERTICAL, command=self.bin_text.yview)
        self.bin_text.configure(yscrollcommand=scrollbar_bin.set)
        self.bin_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar_bin.pack(side=tk.RIGHT, fill=tk.Y)

    def generate_font(self):
        text = self.text_entry.get().strip()
        if not text:
            messagebox.showwarning("输入错误", "请输入汉字")
            return
        
        char = text[0]  # 只取第一个字符
        unicode_val = ord(char)
        
        # 生成点阵
        bitmap = self.char_to_bitmap(char)
        
        # 显示点阵预览
        self.display_bitmap(bitmap)
        
        # 生成字模数据
        font_data = self.bitmap_to_font_data(bitmap)
        
        # 生成输出
        self.generate_output(char, unicode_val, font_data)

    def char_to_bitmap(self, char):
        # 创建12x12图像
        img = Image.new('1', (12, 12), 0)
        draw = ImageDraw.Draw(img)
        
        # 绘制文字
        try:
            draw.text((0, -2), char, font=self.font, fill=1)
        except:
            # 如果字体绘制失败，使用简单替代
            draw.rectangle([2, 2, 10, 10], outline=1, fill=0)
        
        # 转换为numpy数组
        bitmap = np.array(img, dtype=np.uint8)
        return bitmap

    def display_bitmap(self, bitmap):
        self.canvas.delete("all")
        cell_size = 12
        
        for y in range(12):
            for x in range(12):
                color = "black" if bitmap[y, x] else "white"
                self.canvas.create_rectangle(
                    x * cell_size, y * cell_size,
                    (x + 1) * cell_size, (y + 1) * cell_size,
                    fill=color, outline="gray"
                )

    def bitmap_to_font_data(self, bitmap):
        # 列行式逆向取模
        font_data = []
        
        # 处理左半部分 (前6列)
        for col in range(6):
            byte_val = 0
            for row in range(11, -1, -1):  # 逆向，从底部开始
                byte_val = (byte_val << 1) | bitmap[row, col]
            font_data.append(byte_val)
        
        # 处理右半部分 (后6列)
        for col in range(6, 12):
            byte_val = 0
            for row in range(11, -1, -1):  # 逆向，从底部开始
                byte_val = (byte_val << 1) | bitmap[row, col]
            font_data.append(byte_val)
        
        return font_data

    def generate_output(self, char, unicode_val, font_data):
        # 生成C代码输出
        c_output = self.generate_c_code(char, unicode_val, font_data)
        self.c_text.delete(1.0, tk.END)
        self.c_text.insert(1.0, c_output)
        
        # 生成二进制输出
        bin_output = self.generate_binary_output(font_data)
        self.bin_text.delete(1.0, tk.END)
        self.bin_text.insert(1.0, bin_output)

    def generate_c_code(self, char, unicode_val, font_data):
        # 生成CodePage条目
        codepage_entry = f"{{ 0x{unicode_val:04X}, 0x{unicode_val:04X}, 0x0000 }}/* {char}, 0x{unicode_val:04X} */\n\n"
        
        # 生成Unicode12数组条目
        unicode_entry = "{{12, 12},{\n"
        
        # 添加左半部分数据
        for i in range(12):
            unicode_entry += f"0x{font_data[i]:02X},    /*  {self.byte_to_binary_string(font_data[i])}  */\n"
        
        unicode_entry += "\n"
        
        # 添加右半部分数据
        for i in range(12, 24):
            unicode_entry += f"0x{font_data[i]:02X},    /*  {self.byte_to_binary_string(font_data[i])}  */\n"
        
        unicode_entry += "}}"
        
        return f"// CodePage条目 (需要插入到CodePage12.c中):\n{codepage_entry}// Unicode12数组条目 (需要插入到Unicode12.c中):\n{unicode_entry}"

    def generate_binary_output(self, font_data):
        output = "二进制数据:\n"
        for i, byte_val in enumerate(font_data):
            output += f"0x{byte_val:02X} "
            if (i + 1) % 6 == 0:
                output += "\n"
        return output

    def byte_to_binary_string(self, byte_val):
        # 将字节转换为二进制字符串表示
        binary = bin(byte_val)[2:].zfill(8)
        return binary.replace('0', '.').replace('1', '@')

    def run(self):
        # 初始生成
        self.generate_font()
        self.window.mainloop()

if __name__ == "__main__":
    app = FontGenerator()
    app.run()