import tkinter as tk
from tkinter import filedialog, messagebox
from PIL import Image, ImageDraw, ImageFont, ImageTk

def get_bitmap_data(char, font_path, size=12):
    font = ImageFont.truetype(font_path, size)
    # 创建黑白图像
    img = Image.new("1", (size, size), 1)
    draw = ImageDraw.Draw(img)
    draw.text((0, 0), char, font=font, fill=0)
    pixels = img.load()

    upper_bytes = []
    lower_bytes = []

    for x in range(size):
        upper = 0
        lower = 0
        for y in range(8):  # 上8行
            if y < size and pixels[x, y] == 0:
                upper |= (1 << (7 - y))
        for y in range(8, size):  # 下4行
            if y < size and pixels[x, y] == 0:
                lower |= (1 << (11 - y))  # 下半部分4位
        upper_bytes.append(upper)
        lower_bytes.append(lower >> 4)

    return upper_bytes, lower_bytes, img

def bytes_to_carray(upper, lower, size=12):
    lines = []
    lines.append(f"{{{{{size}, {size}}},{{")
    for i, b in enumerate(upper):
        lines.append(f"0x{b:02X},    /*  {byte_to_ascii(b)}  */")
    lines.append("")
    for i, b in enumerate(lower):
        lines.append(f"0x{b:02X},    /*  {byte_to_ascii(b)}  */")
    lines.append("}}}")
    return "\n".join(lines)

def byte_to_ascii(byte):
    s = ""
    for i in range(8):
        s += "@" if (byte & (1 << (7 - i))) else "."
    return s

def generate_font():
    char = entry_char.get()
    if not char:
        messagebox.showwarning("提示", "请输入要生成的汉字！")
        return
    font_path = entry_font.get()
    if not font_path:
        messagebox.showwarning("提示", "请选择字体文件！")
        return

    try:
        size = int(entry_size.get())
    except ValueError:
        messagebox.showwarning("提示", "字号必须是整数！")
        return

    result_text.delete(1.0, tk.END)
    preview_canvas.delete("all")

    for c in char:
        upper, lower, img = get_bitmap_data(c, font_path, size)
        c_code = bytes_to_carray(upper, lower, size)
        result_text.insert(tk.END, f"// 字符: {c}\n{c_code}\n\n")

        # 图像预览
        preview_img = img.resize((size * 10, size * 10), Image.NEAREST)
        tk_img = ImageTk.PhotoImage(preview_img)
        preview_canvas.create_image(0, 0, anchor="nw", image=tk_img)
        preview_canvas.image = tk_img

def select_font():
    path = filedialog.askopenfilename(title="选择字体文件", filetypes=[("字体文件", "*.ttf *.ttc")])
    if path:
        entry_font.delete(0, tk.END)
        entry_font.insert(0, path)

def save_file():
    data = result_text.get(1.0, tk.END).strip()
    if not data:
        messagebox.showwarning("提示", "没有生成内容可保存！")
        return
    path = filedialog.asksaveasfilename(title="保存字库文件", defaultextension=".h", filetypes=[("C头文件", "*.h")])
    if path:
        with open(path, "w", encoding="utf-8") as f:
            f.write(data)
        messagebox.showinfo("成功", "文件已保存！")

# ===================== GUI =====================
root = tk.Tk()
root.title("12x12 字库生成工具")
root.geometry("820x600")

frame_top = tk.Frame(root)
frame_top.pack(pady=5)

tk.Label(frame_top, text="输入文字：").grid(row=0, column=0)
entry_char = tk.Entry(frame_top, width=20)
entry_char.grid(row=0, column=1, padx=5)

tk.Label(frame_top, text="字体路径：").grid(row=1, column=0)
entry_font = tk.Entry(frame_top, width=50)
entry_font.grid(row=1, column=1, padx=5)
tk.Button(frame_top, text="选择字体", command=select_font).grid(row=1, column=2, padx=5)

tk.Label(frame_top, text="字号：").grid(row=2, column=0)
entry_size = tk.Entry(frame_top, width=5)
entry_size.insert(0, "12")
entry_size.grid(row=2, column=1, sticky="w")

tk.Button(frame_top, text="生成字模", command=generate_font, bg="#4CAF50", fg="white").grid(row=2, column=2, padx=5)
tk.Button(frame_top, text="保存为.h文件", command=save_file).grid(row=2, column=3, padx=5)

frame_bottom = tk.Frame(root)
frame_bottom.pack(fill="both", expand=True, padx=10, pady=10)

preview_canvas = tk.Canvas(frame_bottom, width=120, height=120, bg="white")
preview_canvas.pack(side="left", padx=10)

result_text = tk.Text(frame_bottom, wrap="none")
result_text.pack(side="left", fill="both", expand=True)

root.mainloop()
