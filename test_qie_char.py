import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
from PIL import Image, ImageFont, ImageDraw, ImageTk

# ==========================================================
# 点阵生成核心（上8行 + 下4行，低4位有效）
# ==========================================================
def generate_split_font(ch, font_path, size=12):
    font = ImageFont.truetype(font_path, size)
    image = Image.new('1', (size, size), 0)
    draw = ImageDraw.Draw(image)
    draw.text((0, 0), ch, 1, font=font)

    pixels = image.load()
    upper_data = []
    lower_data = []

    # 上半部分 8 行
    for y in range(8):
        byte = 0
        for x in range(8):
            if x < size and pixels[x, y]:
                byte |= (1 << (7 - x))
        upper_data.append(byte)

    # 下半部分 4 行（只用低 4 位）
    for y in range(8, 12):
        byte = 0
        for x in range(8):
            if x < size and pixels[x, y]:
                byte |= (1 << (7 - x))
        lower_data.append(byte & 0x0F)  # 只保留低4位

    return upper_data, lower_data, image


# ==========================================================
# 输出 C 数组格式
# ==========================================================
def print_c_format(upper, lower, size=12):
    lines = []
    lines.append(f"{{{{{size}, {size}}},{{")

    # 上半部分
    for val in upper:
        bits = ''.join('@' if (val >> (7 - b)) & 1 else '.' for b in range(8))
        lines.append(f"0x{val:02X},    /*  {bits}  */")

    # 下半部分（仅低四位有效）
    for val in lower:
        bits = ''.join('@' if ((val >> (7 - b)) & 1 and b >= 4) else '.' for b in range(8))
        lines.append(f"0x{val:02X},    /*  {bits}  */")

    lines.append("}}}")
    return "\n".join(lines)


# ==========================================================
# 生成逻辑
# ==========================================================
def generate():
    ch = entry_text.get().strip()
    if not ch:
        messagebox.showwarning("提示", "请输入字符！")
        return
    font_path = font_path_var.get()
    if not font_path:
        messagebox.showwarning("提示", "请选择字体！")
        return
    try:
        size = int(entry_size.get())
    except ValueError:
        messagebox.showerror("错误", "点阵大小必须是整数！")
        return

    try:
        upper, lower, image = generate_split_font(ch, font_path, size)
        c_code = print_c_format(upper, lower, size)
        output_box.delete(1.0, tk.END)
        output_box.insert(tk.END, c_code)

        # 显示点阵预览
        img = image.resize((size * 8, size * 8), Image.NEAREST)
        img_tk = ImageTk.PhotoImage(img)
        label_preview.config(image=img_tk)
        label_preview.image = img_tk
    except Exception as e:
        messagebox.showerror("错误", str(e))


def choose_font():
    path = filedialog.askopenfilename(filetypes=[("字体文件", "*.ttf *.ttc")])
    if path:
        font_path_var.set(path)


def set_font(path):
    font_path_var.set(path)
    messagebox.showinfo("字体已选择", f"当前字体：\n{path}")


# ==========================================================
# GUI 布局
# ==========================================================
root = tk.Tk()
root.title("12x12 切字字库生成器")
root.geometry("900x520")

font_path_var = tk.StringVar()

# 上部输入区
frame_top = tk.Frame(root)
frame_top.pack(pady=10)

tk.Label(frame_top, text="输入字符：").grid(row=0, column=0, padx=5)
entry_text = tk.Entry(frame_top, width=10, font=('Consolas', 14))
entry_text.grid(row=0, column=1)

tk.Label(frame_top, text="点阵大小：").grid(row=0, column=2, padx=5)
entry_size = tk.Entry(frame_top, width=5)
entry_size.insert(0, "12")
entry_size.grid(row=0, column=3)

tk.Button(frame_top, text="生成字模", command=generate, bg="#4CAF50", fg="white").grid(row=0, column=4, padx=15)

# 字体选择 Tab
notebook = ttk.Notebook(root)
notebook.pack(fill=tk.X, padx=20)

tab_common = tk.Frame(notebook)
tab_custom = tk.Frame(notebook)

notebook.add(tab_common, text="常用字体")
notebook.add(tab_custom, text="自定义字体")

# 常用字体 Tab
tk.Label(tab_common, text="快速选择常用字体：", font=("微软雅黑", 10)).pack(pady=8)
btn_simsun = tk.Button(tab_common, text="宋体 (simsun.ttc)", width=25,
                       command=lambda: set_font("C:/Windows/Fonts/simsun.ttc"))
btn_simsun.pack(pady=5)

btn_simhei = tk.Button(tab_common, text="黑体 (simhei.ttf)", width=25,
                       command=lambda: set_font("C:/Windows/Fonts/simhei.ttf"))
btn_simhei.pack(pady=5)

tk.Label(tab_common, textvariable=font_path_var, fg="gray").pack(pady=5)

# 自定义字体 Tab
tk.Label(tab_custom, text="选择本地字体文件：", font=("微软雅黑", 10)).pack(pady=8)
tk.Button(tab_custom, text="浏览字体文件", command=choose_font).pack(pady=5)

# 输出 + 预览区
frame_bottom = tk.Frame(root)
frame_bottom.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

output_box = scrolledtext.ScrolledText(frame_bottom, width=60, height=25, font=('Consolas', 10))
output_box.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

label_preview = tk.Label(frame_bottom, bg="white", width=200, relief="sunken")
label_preview.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)

root.mainloop()
