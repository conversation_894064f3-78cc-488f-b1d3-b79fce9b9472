/**
  @page STM32L1xx_IAP AN3310 STM32L1xx in-application programming using the USART Readme file
  
  @verbatim
  ******************** (C) COPYRIGHT 2011 STMicroelectronics *******************
  * @file    STM32L1xx_IAP/readme.txt 
  * <AUTHOR> Application Team
  * @version V1.0.0
  * @date    01-April-2011
  * @brief   Description of the AN3310 "STM32L1xx in-application programming
  *          using the USART (IAP)".
  ******************************************************************************
  * THE PRESENT FIRMWARE WHICH IS FOR GUIDANCE ONLY AIMS AT PROVIDING CUSTOMERS
  * WITH CODING INFORMATION REGARDING THEIR PRODUCTS IN ORDER FOR THEM TO SAVE
  * TIME. AS A RESULT, STMICROELECTRONICS SHALL NOT BE HELD LIABLE FOR ANY
  * DIRECT, INDIRECT OR CONSEQUENTIAL DAMAGES WITH RESPECT TO ANY CLAIMS ARISING
  * FROM THE CONTENT OF SUCH FIRMWARE AND/OR THE USE MADE BY CUSTOMERS OF THE
  * CODING INFORMATION CONTAINED HEREIN IN CONNECTION WITH THEIR PRODUCTS.
  ******************************************************************************
   @endverbatim

@par Description

This directory contains a set of sources files and pre-configured projects that 
describes how to build an application to be loaded into Flash memory using
In-Application Programming (IAP, through USART).


@par Directory contents 

 - "STM32L1xx_IAP\inc": contains the IAP firmware header files 
    - STM32L1xx_IAP/inc/common.h            This file provides all the headers of the common functions.
    - STM32L1xx_IAP/inc/flash_if.h          This file provides all the firmware 
                                            function headers of the flash_if.c file.
    - STM32L1xx_IAP/inc/menu.h              This file provides all the firmware
                                            function headers of the menu.c file.
    - STM32L1xx_IAP/inc/ymodem.h            This file provides all the firmware
                                            function headers of the ymodem.c file.
    - STM32L1xx_IAP/inc/stm32l1xx_conf.h    Library Configuration file
    - STM32L1xx_IAP/inc/stm32l1xx_it.h      Header for stm32l1xx_it.c

 - "STM32L1xx_IAP\MDK-ARM": contains pre-configured project for MDK-ARM toolchain

 - "STM32L1xx_IAP\RIDE": contains pre-configured project for RIDE toolchain

 - "STM32L1xx_IAP\HiTOP": contains pre-configured project for HiTOP toolchain

 - "STM32L1xx_IAP\EWARM": contains pre-configured project for EWARM toolchain

 - "STM32L1xx_IAP\TrueSTUDIO": contains pre-configured project for TrueSTUDIO toolchain

 - "STM32L1xx_IAP\src": contains the IAP firmware source files
    - STM32L1xx_IAP/src/main.c              Main program
    - STM32L1xx_IAP/src/stm32l1xx_it.c      Interrupt handlers
    - STM32L1xx_IAP/src/flash_if.c          The file contains write, erase and disable
                                            write protection of the internal Flash
                                            memory.
    - STM32L1xx_IAP/src/menu.c              This file contains the menu to select
                                            downloading a new binary file, uploading
                                            internal Flash memory, executing the binary
                                            and disabling the write protection of
                                            write-protected pages
    - STM32L1xx_IAP/src/common.c            This file provides functions related to
                                            read/write from/to USART peripheral
    - STM32L1xx_IAP/src/ymodem.c            This file provides all the firmware functions
                                            related to the ymodem protocol.
    - STM32L1xx_IAP/src/system_stm32l1xx.c  STM32L1xx system source file

@note The "system_stm32l1xx.c" is generated by an automatic clock configuration 
      system and can be easily customized to your own configuration. 
      To select different clock setup, use the "STM32L1xx_Clock_Configuration_V1.0.0.xls" 
      provided with the AN3309 package available on <a href="http://www.st.com/internet/mcu/family/141.jsp">  ST Microcontrollers </a>
      
 - "STM32L1xx_IAP\binary_template": contains the binary template firmware source files

@par Hardware and Software environment

  - This example runs on STM32L1xx Ultra Low Power Medium-Density Devices.

  - This example has been tested with STMicroelectronics STM32L152-EVAL (STM32L1xx 
    Ultra Low Power Medium-Density) evaluation board and can be easily tailored 
    to any other supported device and development board.

  - STM32L152-EVAL Set-up
    - Connect a null-modem female/female RS232 cable between the DB9 connector 
      CN2 (USART2) and PC serial port.
    - Use the Key push-button.

  - Hyperterminal configuration: 
    - Word Length = 8 Bits
    - One Stop Bit
    - No parity
    - BaudRate = 115200 baud
    - flow control: None 


@par How to use it ? 

In order to make the program work, you must do the following:

  1. Generate a binary image for the program provided in the 
     "Project\STM32L1xx_IAP\binary_template" directory. 
  2. Program the internal Flash with the IAP (see below) 
  3. Open HyperTerminal window using the settings already defined in section
     "Hardware and Software environment" 
  4. To run the IAP driver, keep the Key push-button pressed at Reset. 
     The IAP main menu is then displayed on the HyperTerminal window.
  5. To download an application, press 1 and use the Ymodem protocol


  In order to load the IAP code, you have do the following:
   - MDK-ARM:
      - Open the IAP.uvproj project
      - In the build toolbar select the project config:
          - STM32L152-EVAL: to configure the project for STM32L Ultra Low Power Medium-density devices
      - Rebuild all files: Project->Rebuild all target files
      - Load project image: Debug->Start/Stop Debug Session
      - Run program: Debug->Run (F5)

@note
  - Ultra Low Power Medium-density devices are STM32L151xx and STM32L152xx 
    microcontrollers where the Flash memory density ranges between 64 and 128 Kbytes.

 * <h2><center>&copy; COPYRIGHT 2011 STMicroelectronics</center></h2>
 */
