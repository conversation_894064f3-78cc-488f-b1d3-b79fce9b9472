// File: STM32L100_151_152_162.dbgconf
// Version: 1.0.1
// Note: refer to STM32L100xx STM32L151xx STM32L152xx STM32L162xx Reference manual (RM0038)
//       refer to STM32L100xx datasheet
//       refer to STM32L151xx STM32L152xx datasheets
//       refer to STM32L162xx datasheet

// <<< Use Configuration Wizard in Context Menu >>>

// <h> Debug MCU configuration register (DBGMCU_CR)
//                                   <i> Reserved bits must be kept at reset value
//   <o.2>  DBG_STANDBY              <i> Debug Standby mode
//   <o.1>  DBG_STOP                 <i> Debug Stop mode
//   <o.0>  DBG_SLEEP                <i> Debug Sleep mode
// </h>
DbgMCU_CR = 0x00000007;

// <h> Debug MCU APB1 freeze register (DBGMCU_APB1_FZ)
//                                   <i> Reserved bits must be kept at reset value
//   <o.22> DBG_I2C2_SMBUS_TIMEOUT   <i> SMBUS timeout mode stopped when core is halted
//   <o.21> DBG_I2C1_SMBUS_TIMEOUT   <i> SMBUS timeout mode stopped when core is halted
//   <o.12> DBG_IWDG_STOP            <i> Debug independent watchdog stopped when core is halted
//   <o.11> DBG_WWDG_STOP            <i> Debug window watchdog stopped when core is halted
//   <o.10> DBG_RTC_STOP             <i> Debug RTC stopped when core is halted
//   <o.5>  DBG_TIM7_STOP            <i> TIM7 counter stopped when core is halted
//   <o.4>  DBG_TIM6_STOP            <i> TIM6 counter stopped when core is halted
//   <o.2>  DBG_TIM4_STOP            <i> TIM4 counter stopped when core is halted
//   <o.1>  DBG_TIM3_STOP            <i> TIM3 counter stopped when core is halted
//   <o.0>  DBG_TIM2_STOP            <i> TIM2 counter stopped when core is halted
// </h>
DbgMCU_APB1_Fz = 0x00601837;

// <h> Debug MCU APB2 freeze register (DBGMCU_APB2_FZ)
//                                   <i> Reserved bits must be kept at reset value
//   <o.4>  DBG_TIM11_STOP           <i> TIM11 counter stopped when core is halted
//   <o.3>  DBG_TIM10_STOP           <i> TIM10 counter stopped when core is halted
//   <o.2>  DBG_TIM9_STOP            <i> TIM9 counter stopped when core is halted
// </h>
DbgMCU_APB2_Fz = 0x0000001C;

// <h> Flash Download Options
//   <o.0> Option Byte Loading       <i> Launch the Option Byte Loading after a Flash Download by setting the OBL_LAUNCH bit (causes a reset)
// </h>
DoOptionByteLoading = 0x00000000;

// <<< end of configuration section >>>
