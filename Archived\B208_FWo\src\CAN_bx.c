/*********Function relate to CAN RTM operation***********/
#include "CAN_bx.h"
#include "Timer.h"
#include "ThisDevice.h"
#include "SysConfig.h"

//---------------
timer_t CanTimer[3]  ;//Reserved

timer_t CanTimer10[CAN_TIMER10_NUM]  ;//user defined timer(1 tick=10ms),CAN_TIMER10_NUM==2

CANObjStruct         CANObj  ;//define a CAN communication object

extern   uint32_t   APB1_Clock ;//defined in "system_stm32f10xc"

extern  SysConfigB_t    APP_PB ;//Application parameter Group B

extern  SysAreaStruct   S_Area ;//Extern defined an object for system data --in file ".h"

extern  UserAreaStruct  U_Area ;//Extern defined an object for user data

extern __inline void SetupCanDevObj(void) ;
/* 
  Define a pointer arry of device's RTM Rx buffer Base Address,0-15 for RTM_H ,16-31 for RTM_L
*/
extern uint16_t CanDevComSta ;//used to record the CAN bus device's communication status

extern uint16_t  SetupKeyW,SetupCmdW ;
//---------------------------------------------------------------------

void CANErrMonitor_ISR(void)
{//CAN_SCE status change error interrupt
 U_Area.CAN_Sta.CAN_ESR_L=(uint16_t) CAN1->ESR ;
 U_Area.CAN_Sta.CAN_ESR_H=(uint16_t) (CAN1->ESR>>16) ;
 CAN1->ESR=0x00000070 ;
 if((U_Area.CAN_Sta.CAN_ESR_L&(uint16_t)0x0070)==(uint16_t)0x0010)
   ++U_Area.CAN_Sta.StuffError ;
 else if((U_Area.CAN_Sta.CAN_ESR_L&(uint16_t)0x0070)==(uint16_t)0x0020)
   ++U_Area.CAN_Sta.FormError ;
 else if((U_Area.CAN_Sta.CAN_ESR_L&(uint16_t)0x0070)==(uint16_t)0x0030)
   ++U_Area.CAN_Sta.AckError ;
 else if((U_Area.CAN_Sta.CAN_ESR_L&(uint16_t)0x0070)==(uint16_t)0x0040)
   ++U_Area.CAN_Sta.Bit1Error ;
 else if((U_Area.CAN_Sta.CAN_ESR_L&(uint16_t)0x0070)==(uint16_t)0x0050)
   ++U_Area.CAN_Sta.Bit0Error ;
 else if((U_Area.CAN_Sta.CAN_ESR_L&(uint16_t)0x0070)==(uint16_t)0x0060)
   ++U_Area.CAN_Sta.CrcError ;
 else if((U_Area.CAN_Sta.CAN_ESR_L&(uint16_t)0x0004)==(uint16_t)0x0004)
   ++U_Area.CAN_Sta.BusOff ;
 CAN1->MSR=0x0000001c ;//Clear the interrupt request
}

void CANErrMonitor(void) 
{
 if(CAN1->ESR & 0x00000006)//In bus off status
 {
   InitCanOpen() ;
  }
}
//--------------------------------------------------------------------------------
void InitCanOpen(void)
{//In can open mode,bus speed only allow 125kbps,250kbps,500kbps
  uint8_t  tmp;
  uint32_t tmp32 ;	
//Enable CAN reset state 
  RCC->APB1RSTR |=0x02000000 ;
   __nop() ;
  U_Area.CANOpenID=(uint16_t) APP_PB.CANOpenID ;
  U_Area.SYNC_Interval=APP_PB.SYNC_Interval ;
  U_Area.PDO_Interval=APP_PB.PDO_Interval ;   
  if((APP_PB.CAN_Speed!=125)&&(APP_PB.CAN_Speed!=250)&&(APP_PB.CAN_Speed!=500))
     APP_PB.CAN_Speed=250 ;//Set to default value 250kbps
// Release CAN from reset state 
  RCC->APB1RSTR &=0xfdffffff ;

  CAN1->MCR = 0x00000043;//Automatic bus-off management,request to do initialization
//  CAN1->MSR = 0x0000001c;//Clear Sleep,Wakeup & error interrupt
  
  while((CAN1->MSR&0x00000001)==0) ;//Wait for CAN module enter initialization mode
  if(APP_PB.CAN_Speed==500)
  {
    if(APB1_Clock==32000000)//MCLK=64.000 MHz ,APB1 Clock=32MHz   
      // value   bitrate         NTQ  TSEG1     TSEG2  SJW  BRP
      //        500.0 kbit/s    16   12-0xc      1      1    3
      CAN1->BTR = 0x001c0003 ;
    else if(APB1_Clock==36000000)//MCLK=72.000 MHz ,APB1 Clock=36MHz 
      // value   bitrate         NTQ  TSEG1     TSEG2  SJW  BRP
      //        500.0 kbit/s    18   13-0xd      2      1    3
      CAN1->BTR = 0x002d0003 ;
    else   //I will assum APB1_Clock==********
      // value   bitrate         NTQ  TSEG1     TSEG2  SJW  BRP
      // 0x1a07  500.0 kbit/s    16   12-0xc      1      1    2
      CAN1->BTR = 0x001c0002 ;
   }
   else if(APP_PB.CAN_Speed==250)
   {
    if(APB1_Clock==32000000)//MCLK=64.000 MHz ,APB1 Clock=32MHz   
      // value   bitrate         NTQ  TSEG1     TSEG2  SJW  BRP
      //        250.0 kbit/s     16   12-0xc      1      1    7
      CAN1->BTR = 0x001c0007 ;
    else if(APB1_Clock==36000000)//MCLK=72.000 MHz ,APB1 Clock=36MHz 
      // value   bitrate         NTQ  TSEG1     TSEG2  SJW  BRP
      //        250.0 kbit/s     16   12-0xc      1      1    8
      CAN1->BTR = 0x001c0008 ;
    else   //I will assum APB1_Clock==********
      // value   bitrate         NTQ  TSEG1     TSEG2  SJW  BRP
      // 0x1a07  250.0 kbit/s    16   12-0xc      1      1    5
      CAN1->BTR = 0x001c0005 ;
    }
   else
   {//Must be 125 kbps
    if(APB1_Clock==32000000)//MCLK=64.000 MHz ,APB1 Clock=32MHz   
      // value   bitrate         NTQ  TSEG1     TSEG2  SJW  BRP
      //        125.0 kbit/s     16   12-0xc      1      1   15
      CAN1->BTR = 0x001c000f ;
    else if(APB1_Clock==36000000)//MCLK=72.000 MHz ,APB1 Clock=36MHz 
      // value   bitrate         NTQ  TSEG1     TSEG2  SJW  BRP
      //        125.0 kbit/s     16   12-0xc      1      1   17
      CAN1->BTR = 0x001c0011 ;
    else   //I will assum APB1_Clock==********
      // value   bitrate         NTQ  TSEG1     TSEG2  SJW  BRP
      // 0x1a07  125.0 kbit/s    16   12-0xc      1      1   11
      CAN1->BTR = 0x001c000b ;
	}
  CAN1->MCR = 0x00000044;//Automatic bus-off management,release the module from initialization
  
  CAN1->FMR |= 0x00000001;// Initialisation mode for the filter 
  CAN1->FA1R = 0x00000000 ;//deactive all filter bank
  CAN1->FM1R = 0x00000000 ;
  CAN1->FS1R = 0x00003fff ;
  CAN1->FFA1R =0x00003f80 ;//Fliter bank[0:6] for FIFO 0,bank[7:13] for FIFO 1
  tmp32=(uint32_t)APP_PB.CANOpenR_ID_1<<21;//First Node ID for RPDO  
  tmp=0 ;
  while(tmp<7)
  {//Setup filter bank0-7
    CAN1->sFilterRegister[tmp].FR1=tmp32 ;//--ID,Received message use specified STD ID(11 bits ID) ,STID[6:0] must equal
    CAN1->sFilterRegister[tmp].FR2=(COB_NODE_ID_MASK+COB_STD_ID_MASK)  ;//--MASK,All received message must be STD ID(11 bits ID) & no Remote frame
    tmp++ ;//+COB_RXDO_ID_MASK
   }
  tmp32=(uint32_t)APP_PB.CANOpenR_ID_2<<21;//Second Node ID for RPDO  
  while(tmp<14)
  {//Setup filter bank7-13
    CAN1->sFilterRegister[tmp].FR1=tmp32 ;//--ID,Received message use specified STD ID(11 bits ID) ,STID[6:0] must equal
    CAN1->sFilterRegister[tmp].FR2=(COB_NODE_ID_MASK+COB_STD_ID_MASK)  ;//--MASK,All received message must be STD ID(11 bits ID) & no Remote frame
    tmp++ ;//+COB_RXDO_ID_MASK
   }
  CAN1->FA1R =0x00003fff ;//active all filter bank
  CAN1->IER=0x00008c12 ;//enable Bus off error,RX FIFO 0&1 meassage pending inerrupt
  CAN1->FMR  = 0x00000000;// release fliter bank from Initialisation mode
//------------
  CanTimer[PDO_TX_TIMER].pv=U_Area.PDO_Interval ;//
  CanTimer[PDO_TX_TIMER].cv=0 ;
  CanTimer[PDO_TX_TIMER].csr.csword=0x4000 ;//enable Timer[PDO_TX_TIMER] for PDO TX operation

  CanTimer[SYNC_O_TIMER].pv=U_Area.SYNC_Interval ;//CANopen SYNC object Interval ;
  CanTimer[SYNC_O_TIMER].cv=0 ;
  CanTimer[SYNC_O_TIMER].csr.csword=0x4000 ;//enable Timer[SYNC_O_TIMER] for SYNC operation

  CanTimer[RPDO_TX_TIMER].pv=5 ;//for system test or debug use
  CanTimer[RPDO_TX_TIMER].cv=0 ;
  CanTimer[RPDO_TX_TIMER].csr.csword=0x4000 ;//enable Timer[PDO_TX_TIMER] for PDO TX operation

  CanTimer10[TPDO_M_TIMER].pv=60 ;//Setup CAN TPDO recieving communication monitor
  CanTimer10[TPDO_M_TIMER].cv=0 ;
  CanTimer10[TPDO_M_TIMER].csr.csword=0x4000 ; //start Timer10[TPDO_M_TIMER]
  U_Area.WiredComSta&=0xffef ;//Reset CANOpen PDO received OK flag
//------------
}

//--------------------------------------------------------------------------------
void InitCanTimer10(void)
{
  unsigned i ;
  for(i=0;i<CAN_TIMER10_NUM ;i++)
  {
    CanTimer10[i].pv=CanTimer10[i].cv=CanTimer10[i].csr.csword=0 ;
    }
}
void InitCanTimer(void)
{
  CanTimer[0].pv=CanTimer[0].cv=CanTimer[0].csr.csword=0 ;
  CanTimer[1].pv=CanTimer[1].cv=CanTimer[1].csr.csword=0 ;
  CanTimer[2].pv=CanTimer[2].cv=CanTimer[2].csr.csword=0 ;
}
void  CanTimer10Fun(void)
{
  unsigned i ;
  for(i=0;i<CAN_TIMER10_NUM;i++)
   {
     if(CanTimer10[i].csr.csbit.en==1)
     {
       if(CanTimer10[i].cv<65535) ++(CanTimer10[i].cv) ;
       if(CanTimer10[i].cv>=CanTimer10[i].pv)
       {
         CanTimer10[i].csr.csbit.q=1 ;
        }
      }
   }
}

void CanTimerFun(void) //This function must be called in system tick process ISR
{
  if(CanTimer[0].csr.csbit.en==1)
  {
    if(CanTimer[0].cv<65535) ++(CanTimer[0].cv) ;
    if(CanTimer[0].cv>=CanTimer[0].pv)
    {
      CanTimer[0].csr.csbit.q=1 ;
     }
   }
  if(CanTimer[1].csr.csbit.en==1)
  {
    if(CanTimer[1].cv<65535) ++(CanTimer[1].cv) ;
    if(CanTimer[1].cv>=CanTimer[1].pv)
    {
      CanTimer[1].csr.csbit.q=1 ;
     }
   }
  if(CanTimer[2].csr.csbit.en==1)
  {
    if(CanTimer[2].cv<65535) ++(CanTimer[2].cv) ;
    if(CanTimer[2].cv>=CanTimer[2].pv)
    {
      CanTimer[2].csr.csbit.q=1 ;
     }
   }
}
//--------------------------------------------------------------
void CAN_OPEN_RX_0_ISR(void) 
{//CANOpen mode ISR for RTM RX using FIFO 0 in STM32
  RPDOStruct *pdo_p ;  
  uint32_t tmp32 ;
  uint16_t tmp ;
  do{
      //------------
	  U_Area.WiredComSta|=0x0010 ;//Preset CANOpen PDO received OK flag
	  tmp32=(CAN1->sFIFOMailBox[0].RIR)>>21 ;//Get STD ID 
	  tmp32&=0x000007ff ;
	  tmp=(uint16_t)(tmp32&0x0000007f) ;//Get Node ID
      if(tmp==APP_PB.CANOpenR_ID_1)
        pdo_p=&U_Area.RPDO_ID1[0] ;
      else if(tmp==APP_PB.CANOpenR_ID_2)
        pdo_p=&U_Area.RPDO_ID2[0] ;
      else
	  {
        pdo_p=(RPDOStruct *)&S_Area.SwallowArea[0];
		U_Area.WiredComSta&=0xffef ;//Reset CANOpen PDO received OK flag
	   }
      tmp=(uint8_t)((tmp32>>8)&0x00000007) ;//Get RPDO FUNC ID
      tmp-=2 ;
      if(tmp>3)
      {//Not allowed FUNC ID received
        pdo_p=(RPDOStruct *)&S_Area.SwallowArea[0];
		U_Area.WiredComSta&=0xffef ;//Reset CANOpen PDO received OK flag
        tmp=0 ;          
       }
      pdo_p+=tmp ;//Now the pointer valid to loacal that store received data
	  (*pdo_p).rdata_1 =(uint16_t)(0x0000ffff & (CAN1->sFIFOMailBox[0].RDLR)) ;//Begin to copy recieved data to designed area
	  (*pdo_p).rdata_2 =(uint16_t)(0x0000ffff & (CAN1->sFIFOMailBox[0].RDLR>>16)) ;//
	  (*pdo_p).rdata_3 =(uint16_t)(0x0000ffff & (CAN1->sFIFOMailBox[0].RDHR)) ;//
	  (*pdo_p).rdata_4 =(uint16_t)(0x0000ffff & (CAN1->sFIFOMailBox[0].RDHR>>16)) ;//
	  CAN1->RF0R =0x00000020;// Release the FIFO 
      CanTimer10[TPDO_M_TIMER].cv=0 ;
      CanTimer10[TPDO_M_TIMER].csr.csword=0x4000 ; //restart Timer10[TPDO_M_TIMER]
     } while(CAN1->RF0R & 0x00000003) ;
 }

void CAN_OPEN_RX_1_ISR(void)
{//CANOpen mode ISR for RTM RX using FIFO 1 in STM32
  RPDOStruct *pdo_p ;  
  uint32_t tmp32 ;
  uint16_t tmp ;
  do{
      //------------
	  U_Area.WiredComSta|=0x0010 ;//Preset CANOpen PDO received OK flag
	  tmp32=(CAN1->sFIFOMailBox[1].RIR)>>21 ;//Get STD ID 
	  tmp32&=0x000007ff ;
	  tmp=(uint16_t)(tmp32&0x0000007f) ;//Get Node ID
      if(tmp==APP_PB.CANOpenR_ID_1)
        pdo_p=&U_Area.RPDO_ID1[0] ;
      else if(tmp==APP_PB.CANOpenR_ID_2)
        pdo_p=&U_Area.RPDO_ID2[0] ;
      else
	  {
        pdo_p=(RPDOStruct *)&S_Area.SwallowArea[0];
		U_Area.WiredComSta&=0xffef ;//Reset CANOpen PDO received OK flag
	   }
      tmp=(uint8_t)((tmp32>>8)&0x00000007) ;//Get RPDO FUNC ID
      tmp-=2 ;
      if(tmp>3)
      {//Not allowed FUNC ID received
        pdo_p=(RPDOStruct *)&S_Area.SwallowArea[0];
		U_Area.WiredComSta&=0xffef ;//Reset CANOpen PDO received OK flag
        tmp=0 ;          
       }
      pdo_p+=tmp ;//Now the pointer valid to loacal that store received data
	  (*pdo_p).rdata_1 =(uint16_t)(0x0000ffff & (CAN1->sFIFOMailBox[1].RDLR)) ;//Begin to copy recieved data to designed area
	  (*pdo_p).rdata_2 =(uint16_t)(0x0000ffff & (CAN1->sFIFOMailBox[1].RDLR>>16)) ;//
	  (*pdo_p).rdata_3 =(uint16_t)(0x0000ffff & (CAN1->sFIFOMailBox[1].RDHR)) ;//
	  (*pdo_p).rdata_4 =(uint16_t)(0x0000ffff & (CAN1->sFIFOMailBox[1].RDHR>>16)) ;//
	  CAN1->RF1R =0x00000020;// Release the FIFO 
      CanTimer10[TPDO_M_TIMER].cv=0 ;
      CanTimer10[TPDO_M_TIMER].csr.csword=0x4000 ; //restart Timer10[TPDO_M_TIMER]
     } while(CAN1->RF1R & 0x00000003) ;
 }
//------------------------------------------------
void CANOpen_Proc(void)  
{
  uint16_t tmp,*tp ;
  uint32_t tmp32,TransmitMailbox ;

  if(CanTimer[PDO_TX_TIMER].csr.csbit.q==1)
  {
   // Select one empty transmit mailbox 
    if (CAN1->TSR&0x04000000)
    {
       TransmitMailbox = 0;
     }
    else if (CAN1->TSR&0x08000000)
    {
       TransmitMailbox = 1;
     }
    else if (CAN1->TSR&0x10000000)
    {
       TransmitMailbox = 2;
     }
    else
    {
       TransmitMailbox = 0xff;
       CanTimer[PDO_TX_TIMER].pv=U_Area.PDO_Interval ;//
	   CanTimer[PDO_TX_TIMER].cv=0 ;
	   CanTimer[PDO_TX_TIMER].csr.csword=0x4000 ;//renable monitor timer
       return ;
	 }
    tmp32=U_Area.CANOpenID ;
    tmp32<<=21 ;
    tmp32|=COB_TPDO1_FUN_ID ;
    CAN1->sTxMailBox[TransmitMailbox].TIR=tmp32 ;//Set CAN object ID
    CAN1->sTxMailBox[TransmitMailbox].TDTR = (uint32_t)0x00000008;// Set up the DLC, fixed to 8 
	tp=(uint16_t *) &U_Area.RF_PDODat[0] ;//set the data buffer position pointer
//-----------------
    tmp=*(tp++) ;
    tmp32=(uint32_t)*(tp++) ;
    tmp32<<=16 ;
    CAN1->sTxMailBox[TransmitMailbox].TDLR = tmp32|(uint32_t) tmp ;//set first & second data
    tmp=*(tp++) ;
    tmp32=(uint32_t)*(tp++) ;
    tmp32<<=16 ;
    CAN1->sTxMailBox[TransmitMailbox].TDHR = tmp32|(uint32_t) tmp ;//set third & 4th data 
//------------
    CAN1->sTxMailBox[TransmitMailbox].TIR |= 0x00000001;// Request transmission ,use 11 bits STD ID
    CanTimer[PDO_TX_TIMER].pv=U_Area.PDO_Interval ;//
    CanTimer[PDO_TX_TIMER].cv=0 ;
    CanTimer[PDO_TX_TIMER].csr.csword=0x4000 ;//renable Timer[PDO_TX_TIMER]
   }
  if(CanTimer10[TPDO_M_TIMER].csr.csbit.q==1)
  {
	U_Area.WiredComSta&=0xffef ;//Reset CANOpen PDO received OK flag
   }
}
//---------------------------
