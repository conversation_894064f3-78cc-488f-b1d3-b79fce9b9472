#include "SysBasic.h"
#include "Timer.h"
#include "RF_BasicFun.h"
//------------------------------------------------------------------------------

extern  timer_t  RfcTimer[RFC_TIMER_NUM]  ;//system ISR process

extern  timer_t  RfcRfcTimer10[RFC_TIMER10_NUM]  ;//user level timer(1 tick=10ms),RFC_TIMER10_NUM==2

extern  uint8_t   ShiftReg_L ;
extern  uint8_t   BitCounter_L;
extern  uint8_t   PreambleCount_L;
extern  uint8_t   PreambleError_L;
extern  uint8_t   ByteCounter_L;
extern  LongWord  IC021Reg ;

extern  uint8_t  gain_correction[] ;


uint32_t      TestReg_L=0x0000010f ;

//----------------------------------------------------------------------------------------
#ifdef  USE_OLD_FRAME
void RfRxDaemon_021_L(void)
{
 uint16_t tmp,*tp1 ;
 uint16_t *tp2 ;

 if(RfcTimer10[RF_OP_TIMER_L].csr.csbit.q==1)
 {
   U_Area.PaDat_L.PanSettingW&=0x3f1f ;//reset the main pannel setting status
   U_Area.PaDat_L.PanStaW =0x0000 ;//reset the pannel status
   U_Area.PaDat_L.KeyCode =0 ;//reset the key operation status,except for F2nd,F3rd key
   OpCtrl &=~CB_RX_OK_L ;//Turn off the left side RF operation received OK LED
  }
 if(RfcTimer10[AIR_TIMER_L].csr.csbit.q==1)
 {
   OpCtrl &=~CB_T_ONLINE_L ;//Turn off the left side linkage OK flag
   U_Area.RSSI_L =-128 ;//RfDs.RSSI_L ;
  }
 if(RfDs.RBuf_StaL&0x02)//begine to check and process RF receiver buffer 0
  {
    if(CRC16WithSeed(LEFT_CRC_SEED,(const uint16_t *) &U_Area.PanelDat_RB0_L,RfDs.RxPackageLen/2)==0)
    {
      tp1=(uint16_t *) &U_Area.PanelDat_RB0_L ;
      tp2=(uint16_t *) &U_Area.PaDat_L ;
      for(tmp=0 ;tmp<RfDs.RxPackageLen/2;tmp++)
      {
       *tp2++=*tp1++ ;//copy valid data to output area
       }
      if((U_Area.PaDat_L.InquiryPackage!=0)||(U_Area.PaDat_L.FixedFlag!=0x0003))
        U_Area.PaDat_L.KeyCode=0x0000 ;//reset the operation status
      // set air linkage valid flag
      RfDs.RF_ACK_Count_L=RF_ACK_NUM ;
      U_Area.DatReq_L=U_Area.PaDat_L.PanSettingW ;//Display setting
      OpCtrl |=CB_RX_OK_L+CB_T_ONLINE_L ;//Turn on the left side RF linkage OK flag
      U_Area.RSSI_L=RfDs.RSSI_L ;
      if(RfDs.RSSI_L>=RSSI_LIMIT)
        GFlag |=GF_RSSI_GOOD_L ;
      else
        GFlag &=~GF_RSSI_GOOD_L ;
      //
	  if(U_Area.PaDat_L.InquiryPackage==0)
	  {
        RfcTimer10[RF_OP_TIMER_L].cv=0 ;
        RfcTimer10[RF_OP_TIMER_L].csr.csword=0x4000 ; //start RfcTimer10[RF_OP_TIMER_L]
	   }
      RfcTimer10[AIR_TIMER_L].csr.csword=0x4000 ;//reset the monitor timer
      RfcTimer10[AIR_TIMER_L].cv=0 ;
     }
    else
       RfDs.RF_RxErr_L++ ;//record the CRC error
    __disable_irq();//Disable IRQ
    RfDs.RBuf_StaL &=0xfd ;//reset the buffer to empty status
    __enable_irq();//Enable IRQ
  }
 if(RfDs.RBuf_StaL&0x08)//begine to check and process RF receiver buffer 1
  {
    if(CRC16WithSeed(LEFT_CRC_SEED,(const uint16_t *) &U_Area.PanelDat_RB1_L,RfDs.RxPackageLen/2)==0)
    {
      tp1=(uint16_t *) &U_Area.PanelDat_RB1_L ;
      tp2=(uint16_t *) &U_Area.PaDat_L ;
      for(tmp=0 ;tmp<RfDs.RxPackageLen/2;tmp++)
      {
       *tp2++=*tp1++ ;//copy valid data to output area
       }
      if((U_Area.PaDat_L.InquiryPackage!=0)||(U_Area.PaDat_L.FixedFlag!=0x0003))
        U_Area.PaDat_L.KeyCode=0x0000 ;//reset the operation status
      // set air linkage valid flag
      RfDs.RF_ACK_Count_L=RF_ACK_NUM ;
      U_Area.DatReq_L=U_Area.PaDat_L.PanSettingW ;//Display setting
      OpCtrl |=CB_RX_OK_L+CB_T_ONLINE_L ;//Turn on the left side RF linkage OK flag
      U_Area.RSSI_L=RfDs.RSSI_L ;
      if(RfDs.RSSI_L>=RSSI_LIMIT)
        GFlag |=GF_RSSI_GOOD_L ;
      else
        GFlag &=~GF_RSSI_GOOD_L ;
      //
	  if(U_Area.PaDat_L.InquiryPackage==0)
	  {
        RfcTimer10[RF_OP_TIMER_L].cv=0 ;
        RfcTimer10[RF_OP_TIMER_L].csr.csword=0x4000 ; //start RfcTimer10[RF_OP_TIMER_L]
	   }
      RfcTimer10[AIR_TIMER_L].csr.csword=0x4000 ;//reset the monitor timer
      RfcTimer10[AIR_TIMER_L].cv=0 ;
     }
    else
      RfDs.RF_RxErr_L++ ;//record the CRC error
    __disable_irq();//Disable IRQ
    RfDs.RBuf_StaL &=0xf7 ;//reset the buffer to empty status
    __enable_irq();//Enable IRQ
  }
}
#else
void RfRxDaemon_021_L(void)
{
 uint16_t tmp,*tp1 ;
 uint16_t *tp2 ;

 if(RfcTimer10[RF_OP_TIMER_L].csr.csbit.q==1)
 {
   U_Area.PaDat_L.PSet.SettingW&=0xdf3f ;//reset the main pannel setting status
   U_Area.PaDat_L.PSta.StaW &=0xf1ff ;//reset the pannel status
   U_Area.PaDat_L.KeyCode =0 ;//reset the key operation status,except for F2nd,F3rd key
   OpCtrl &=~CB_RX_OK_L ;//Turn off the left side RF operation received OK LED
  }
 if(RfcTimer10[AIR_TIMER_L].csr.csbit.q==1)
 {
   OpCtrl &=~CB_T_ONLINE_L ;//Turn off the left side linkage OK flag
   U_Area.RSSI_L =-128 ;//RfDs.RSSI_L ;
  }
 if(RfDs.RBuf_StaL&0x02)//begine to check and process RF receiver buffer 0
  {
    if(CRC16WithSeed(LEFT_CRC_SEED,(const uint16_t *) &U_Area.PanelDat_RB0_L,RfDs.RxPackageLen/2)==0)
    {
      tp1=(uint16_t *) &U_Area.PanelDat_RB0_L ;
      tp2=(uint16_t *) &U_Area.PaDat_L ;
      for(tmp=0 ;tmp<RfDs.RxPackageLen/2;tmp++)
      {
       *tp2++=*tp1++ ;//copy valid data to output area
       }
      if((U_Area.PaDat_L.PSta.InquiryPackage!=0)||(U_Area.PaDat_L.PSet.FixedBit!=0x0001))
        U_Area.PaDat_L.KeyCode=0x0000 ;//reset the operation status
      // set air linkage valid flag
      RfDs.RF_ACK_Count_L=RF_ACK_NUM ;
      U_Area.DatReq_L=U_Area.PaDat_L.PSet.SettingW ;//Display setting
      OpCtrl |=CB_RX_OK_L+CB_T_ONLINE_L ;//Turn on the left side RF linkage OK flag
      U_Area.RSSI_L=RfDs.RSSI_L ;
      if(RfDs.RSSI_L>=RSSI_LIMIT)
        GFlag |=GF_RSSI_GOOD_L ;
      else
        GFlag &=~GF_RSSI_GOOD_L ;
      //
	  if(U_Area.PaDat_L.PSta.InquiryPackage==0)
	  {
        RfcTimer10[RF_OP_TIMER_L].cv=0 ;
        RfcTimer10[RF_OP_TIMER_L].csr.csword=0x4000 ; //start RfcTimer10[RF_OP_TIMER_L]
	   }
      RfcTimer10[AIR_TIMER_L].csr.csword=0x4000 ;//reset the monitor timer
      RfcTimer10[AIR_TIMER_L].cv=0 ;
     }
    else
       RfDs.RF_RxErr_L++ ;//record the CRC error
    __disable_irq();//Disable IRQ
    RfDs.RBuf_StaL &=0xfd ;//reset the buffer to empty status
    __enable_irq();//Enable IRQ
  }
 if(RfDs.RBuf_StaL&0x08)//begine to check and process RF receiver buffer 1
  {
    if(CRC16WithSeed(LEFT_CRC_SEED,(const uint16_t *) &U_Area.PanelDat_RB1_L,RfDs.RxPackageLen/2)==0)
    {
      tp1=(uint16_t *) &U_Area.PanelDat_RB1_L ;
      tp2=(uint16_t *) &U_Area.PaDat_L ;
      for(tmp=0 ;tmp<RfDs.RxPackageLen/2;tmp++)
      {
       *tp2++=*tp1++ ;//copy valid data to output area
       }
      if((U_Area.PaDat_L.PSta.InquiryPackage!=0)||(U_Area.PaDat_L.PSet.FixedBit!=0x0001))
        U_Area.PaDat_L.KeyCode=0x0000 ;//reset the operation status
      // set air linkage valid flag
      RfDs.RF_ACK_Count_L=RF_ACK_NUM ;
      U_Area.DatReq_L=U_Area.PaDat_L.PSet.SettingW ;//Display setting
      OpCtrl |=CB_RX_OK_L+CB_T_ONLINE_L ;//Turn on the left side RF linkage OK flag
      U_Area.RSSI_L=RfDs.RSSI_L ;
      if(RfDs.RSSI_L>=RSSI_LIMIT)
        GFlag |=GF_RSSI_GOOD_L ;
      else
        GFlag &=~GF_RSSI_GOOD_L ;
      //
	  if(U_Area.PaDat_L.PSta.InquiryPackage==0)
	  {
        RfcTimer10[RF_OP_TIMER_L].cv=0 ;
        RfcTimer10[RF_OP_TIMER_L].csr.csword=0x4000 ; //start RfcTimer10[RF_OP_TIMER_L]
	   }
      RfcTimer10[AIR_TIMER_L].csr.csword=0x4000 ;//reset the monitor timer
      RfcTimer10[AIR_TIMER_L].cv=0 ;
     }
    else
      RfDs.RF_RxErr_L++ ;//record the CRC error
    __disable_irq();//Disable IRQ
    RfDs.RBuf_StaL &=0xf7 ;//reset the buffer to empty status
    __enable_irq();//Enable IRQ
  }
}
#endif

void RfRxTestDaemon_021_L(void)
{
  uint16_t tmp,*tp1 ;
  uint16_t tmp3 ;
 //check the received data ==0x55 ==0xaa or not
  if((RfDs.char_count_L>64)&(RfDs.flag_L==0))
    RfDs.flag_L=1 ;
  if((RfDs.char_count_L<64)&(RfDs.flag_L==2))
    RfDs.flag_L=0 ;
  if(RfDs.flag_L==1)
  {
    tp1=U_Area.Ultra_BUF_L ;
    RfDs.flag_L=2 ;
    tmp3=0 ;
    for(tmp=0 ;tmp<32 ;tmp++)
    {
      tmp3 |=*tp1++ ;
     }
    if((tmp3==0x5555)||(tmp3==0xaaaa))
      OpCtrl |=CB_RX_OK_L ;//Turn on the left side linkage OK LED
    else
      OpCtrl &=~CB_RX_OK_L ;//Turn off the right side linkage OK LED
   }
}
//---------------------------
void RfDaemon_021_L(void)
{
//Process IC021 Initalization operation
 switch (RfDs.IC021Init_L.OpSta)
 {
  case NOT_INIT_STA :
    if((RfDs.IC021Init_L.OpReq&0xff00)==SET_CODE )
	{
	  __disable_irq();
      RfcTimer[RF_TIMER_L].pv=OSC_TIMEOUT ;
      RfcTimer[RF_TIMER_L].cv=0 ;
      RfcTimer[RF_TIMER_L].csr.csword=0x4000 ;//enable RfcTimer[RF_TIMER_L]
      __enable_irq();
      RfDs.IC021Init_L.OpSta =RfDs.IC021Init_L.OpReq&0x00ff ;
	  RfDs.IC021Init_L.OpReq=0 ;
	  }
    break ;
  case IN_RX_STA :
    break;
  case IN_TX_STA :
    break;
  case SET_UP_RX :
    if(RfcTimer[RF_TIMER_L].csr.csbit.q==1)
    {
	  __disable_irq();
      RfcTimer[RF_TIMER_L].pv=MIN_TIMEOUT ;
      RfcTimer[RF_TIMER_L].cv=0 ;
      RfcTimer[RF_TIMER_L].csr.csword=0x4000 ;//enable RfcTimer[RF_TIMER_L]
      __enable_irq();
	  WriteReg_021_L(&(Rx021_Ch_p_L->R1_VCO_OSC_Rx));//write R1, turn on VCO
      RfDs.IC021Init_L.OpSta=SET_UP_RX_WAIT1 ;
	}
  case SET_UP_RX_WAIT1 :
    if(RfcTimer[RF_TIMER_L].csr.csbit.q==1)
    {
	  //write R3, turn on TX/RX clocks
	  WriteReg_021_L(&APP_PC_p->IC021Def.R3_TxRx_Clk);
      if(GFlag&GF_RFIC_CAL_OK_L)
      {
        WriteReg_021_L(&RfDs.IC021Init_L.FilterCalReadback);//write R5 to start
        RfcTimer[RF_TIMER_L].csr.csword=0x5000 ;//enable Timer[RFIC_TIMER] and .q=1
       }
      else if(((GFlag&GF_RFIC_CAL_OK_L)==0)&&(APP_PC_p->IC021Def.R6_IF_Cal&0x00000010))
      { //Configured require IF Filter Fine calibration
	    __disable_irq();
        RfcTimer[RF_TIMER_L].pv=IF_CAL_FINE_TIME ;
        RfcTimer[RF_TIMER_L].cv=0 ;
        RfcTimer[RF_TIMER_L].csr.csword=0x4000 ;//enable RfcTimer[RF_TIMER_L]
        __enable_irq();
        WriteReg_021_L(&APP_PC_p->IC021Def.R6_IF_Cal);//write R6 here, if fine IF filter cal is wanted
	    WriteReg_021_L(&APP_PC_p->IC021Def.R5_IF_Filter);//write R5 to start IF filter cal
       }
      else
      {//Do coarse calibration
	    __disable_irq();
        RfcTimer[RF_TIMER_L].pv=MIN_TIMEOUT ;
        RfcTimer[RF_TIMER_L].cv=0 ;
        RfcTimer[RF_TIMER_L].csr.csword=0x4000 ;//enable RfcTimer[RF_TIMER_L]
        __enable_irq();
        WriteReg_021_L(&APP_PC_p->IC021Def.R5_IF_Filter);//write R5 to start IF filter cal
       }
      RfDs.IC021Init_L.OpSta=SET_UP_RX_WAIT2 ;
    }
    break ;
  case SET_UP_RX_WAIT2 :
    if(RfcTimer[RF_TIMER_L].csr.csbit.q==1)
    {
      if(((GFlag&GF_RFIC_CAL_OK_L)==0)&&(APP_PC_p->IC021Def.R6_IF_Cal&0x00000010))
      { //Configured require IF Filter Fine calibration
        GFlag|=GF_RFIC_CAL_OK_L ;
        RfDs.IC021Init_L.FilterCalRaw=ReadFilterCal_021_L() ;
        RfDs.IC021Init_L.CalTemp=ReadTemperature_021_L() ;
       }
	  __disable_irq();
      RfcTimer[RF_TIMER_L].pv=MIN_TIMEOUT ;
      RfcTimer[RF_TIMER_L].cv=0 ;
      RfcTimer[RF_TIMER_L].csr.csword=0x4000 ;//enable RfcTimer[RF_TIMER_L]
      __enable_irq();
	  //write R8, configure power down test register
	  WriteReg_021_L(&APP_PC_p->IC021Def.R8_PDT);//Set internal Rx/Tx switch off;
	  //write R11, configure sync word detect
	  WriteReg_021_L(&APP_PC_p->IC021Def.R11_SYNC);//sync word = ;
 	  //write R12, start sync word detect
	  WriteReg_021_L(&APP_PC_p->IC021Def.R12_SWD);//for sync word detect;
   	  //write R0, turn on PLL
	  WriteReg_021_L(&(Rx021_Ch_p_L->R0_N_Rx));
      RfDs.IC021Init_L.OpSta=SET_UP_RX_WAIT3 ;
    }
    break ;
  case SET_UP_RX_WAIT3 :
    if(RfcTimer[RF_TIMER_L].csr.csbit.q==1)
    {
	  __disable_irq();
	  RfcTimer[RF_TIMER_L].csr.csword=0x0000 ;//disable timer
	  RfcTimer[RF_TIMER_L].cv=0 ;
      __enable_irq();
      //write R4, turn on demodulation
	  WriteReg_021_L(&APP_PC_p->IC021Def.R4_Demod);
  	  if (APP_PC_p->IC021Def.R10_AFC&0x00000010)//Required to use AFC
	  {
		//write R10, turn AFC on
		WriteReg_021_L(&APP_PC_p->IC021Def.R10_AFC);
	   }
      if((TestCtrlW&0xff00)==0x8000)
        RfDs.IC021Init_L.OpSta=RX_TEST_STA ;//Enter RF RX Test status
      else
        RfDs.IC021Init_L.OpSta=IN_RX_STA ;//Set to rx setup ok status
      RfDs.IC021Init_L.OpReq=0 ;
      //----
      EnterRxMode_021_L() ;
    }
    break ;
  case TX_TEST_STA :
  case RX_TEST_STA :
    RfRxTestDaemon_021_L() ;
    break;
  default ://There must have enter wrong status
    {
	  RfDs.IC021Init_L.OpReq=0 ;
      RfDs.IC021Init_L.OpSta=NOT_INIT_STA ;
      RfDs.RF_State_L=RF_IDLE_STATUS ;
	 }
 }
}
//------------------------------------------------------
__inline void EnterRxMode_021_L(void)
{
  __disable_irq();//Disable IRQ
  ShiftReg_L=0 ;
  BitCounter_L=0;
  RfDs.RF_State_L=RX_STATUS ;//Set to RX state
  if((TestCtrlW&0xff00)==0x8000)
    RfDs.RF_RxState_L=RX_RF_TEST_INIT ;//Prepar to enter RX TEST status
  else
    RfDs.RF_RxState_L=RX_SEARCH_PREAMBLE ;//search for a vaild preamble status
  GPIOB->CRL &=0x0fffffff ;
  GPIOB->CRL |=0x40000000 ;//Set PB.7(R_DIO) to input float status
  EXTI->RTSR |=0x00000100 ;//Using Rising trigger(for RX mode,read received data on low-to-high transition )
  EXTI->FTSR &=0x0007feff ;//Disable Falling trigger
  EXTI->PR    =0x00000100 ;//clear pending ext interrupt line8# PB.8(R_DCLK)
  EXTI->IMR  |=0x00000100 ;//Allow PB.8 to generate interrupt
  RfDs.EntRxCnt_L++ ;
  __enable_irq();//Enable IRQ
}

//Function related to SRD IC 021
uint16_t  ReadReg_021_L(u8 readback_config )
{
  uint32_t regv32;
  uint16_t i,regv;
/* Write readback and ADC control value */
  regv32 = ((uint32_t)readback_config & 0x0000001F) << 4;
  regv32 |=0x00000007; // Address the readback setup register
  WriteReg_021_L(&regv32) ;//After this call R_SLE=1
  __disable_irq();//Disable all interrupt __enable_irq()
  GPIOA->BRR =0x000000e0 ;//SCLK,MOSI,MISO ODR to 0
  GPIOA->CRL&=0x000fffff ;
  GPIOA->CRL|=SPI1Pins_GPIO_CONFIG ;//Set PA.5,6,7(SCLK,MISO,MOSI) to GPIO status
  //Clock in first bit and discard
  GPIOA->BSRR=0x00000020 ;//SLE=1,SDATA=0,SCLK=1,SREAD input dir
  regv = 0; // Slight pulse extend
  GPIOA->BRR =0x00000020 ;//SLE=1,SDATA=SCLK=0,SREAD input dir
  /* Clock in data MSbit first */
  for (i=16; i>0;i-- )
  {
    GPIOA->BSRR=0x00000020 ;//SLE=1,SDATA=0,SCLK=1,SREAD input dir
    regv <<= 1; // left shift 1
    GPIOA->BRR =0x00000020 ;//SLE=1,SDATA=SCLK=0,SREAD input dir
    if (GPIOA->IDR &0x00000040)
       regv |= 1;//SREAD=1
   }
   //Generate additional clock pulse
  GPIOA->BSRR=0x00000020 ;//SLE=1,SDATA=0,SCLK=1,SREAD input dir
//  GPIOB->BRR =0x00000200 ;//Reset PB.9  force SLE to '0' 	--2011.3.20
  GPIOA->BRR =0x00000020 ;//SLE=1,SDATA=SCLK=0,SREAD input dir
  // All port lines left low ?
  GPIOA->CRL&=0x000fffff ;
  GPIOA->CRL|=SPI1Pins_SPI_CONFIG ;//Set PA.5,6,7(SCLK,MISO,MOSI) to SPI status
  __enable_irq();
  return regv;
}
void WriteReg_021_L(uint32_t *regv_p)
{//The read back data all be ignored
  uint16_t dummy ;
  IC021Reg.w32=*regv_p ;
  while((SPI1->SR&0x0080)!=0) ;//Wait the SPI1  to idle
  dummy=SPI1->DR ;
  __disable_irq();
  GPIOB->BRR =0x00000200 ;//Reset PB.9 force SLE to '0' 
  SPI1->DR=IC021Reg.w16[1]; // Write high half word to SPI1
  while((SPI1->SR&0x0002)==0)  ;//Wait the SPI1 TX buffer to empty
  SPI1->DR=IC021Reg.w16[0]; // Write low half word to SPI1
  dummy=SPI1->DR ;
  // Wait until data is written
  while((SPI1->SR&0x0080)!=0)  ;//Wait the SPI1 to idle
  dummy=SPI1->DR ;
  GPIOB->BSRR =0x00000200 ;// Set R_PSEL(021 SLE) to High latch the data
  __enable_irq();//Enable IRQ
  dummy=dummy ;
}

/****************************************************************************
 *                   		   readback functions                           *
 ***************************************************************************/
__inline uint16_t Read_AFC_021_L(void)
{
  if( RfDs.IC021Init_L.OpSta==IN_RX_STA)
    return ReadReg_021_L(0x10);
  else
    return 0 ;
}

int16_t Read_RSSI_021_L(void)
{
  int16_t rssi = 0;
  uint16_t RSSI_value;
  if( RfDs.IC021Init_L.OpSta==IN_RX_STA)
  {
	RSSI_value = ReadReg_021_L(0x14)&0x07ff;
    rssi = RSSI_value>>7 ;
    RSSI_value&=0x007f ;//only use last 7 bits
	RSSI_value += gain_correction[rssi&0x0F] ;
    rssi = RSSI_value>>1 ;//*0.5
    return (rssi-130) ;//RSSI(dBm) = rssi + 130
   }
  else
    return -128 ;//Should set to invalid value
}
int16_t  ReadTemperature_021_L(void)
{
  int32_t temp = -45;
  uint32_t tmp ;
  uint16_t TMP_value ;
  if(RfDs.IC021Init_L.OpSta==IN_TX_STA)
  {//I assume the ADC enabled in Tx mode
    TMP_value=ReadReg_021_L(0x16)&0x007f;
    TMP_value&=0x007f ;
    TMP_value *=10 ;
    temp=(684-TMP_value)*932 ;
    temp/=1000 ;
    temp-=40 ;
    return (int16_t)temp ;
  }
  else
  {
    tmp=APP_PC_p->IC021Def.R9_AGC|0x00080000 ;
	WriteReg_021_L(&tmp);//Disable(Freeze) the AGC function
    TMP_value=ReadReg_021_L(0x16)&0x007f;
	WriteReg_021_L(&APP_PC_p->IC021Def.R9_AGC);//re_enable the AGC function
    TMP_value&=0x007f ;
    TMP_value *=10 ;
    temp=(684-TMP_value)*932 ;
    temp/=1000 ;
    temp-=40 ;
    return (int16_t)temp ;
  }
}
uint16_t ReadFilterCal_021_L(void)
{
  uint16_t vreturn ;
  vreturn=ReadReg_021_L(0x18)&0x00ff ;
  RfDs.IC021Init_L.FilterCalReadback=vreturn-128 ;
  RfDs.IC021Init_L.FilterCalReadback<<=14 ;
  RfDs.IC021Init_L.FilterCalReadback&=0x000fc000 ;
  RfDs.IC021Init_L.FilterCalReadback|=APP_PC_p->IC021Def.R5_IF_Filter ;
  RfDs.IC021Init_L.FilterCalReadback&=0xffffffef ;//Cear calibration req bit
  return(vreturn);

}

uint16_t ReadVersion_021_L(void)
{
  return(ReadReg_021_L(0x1c));//So readback value >=0x2104
}
//-----------------------------------------------------------------------------
void EXTI9_5_ISR_021(void)
{
if((EXTI->PR)&0x00000100)
{//EXTI Line 8 (PB.8) interrupt
  EXTI->PR=0x00000100 ;//Clear the interrupt
  RfDs.DClkCnt_L++  ;//MD20066
  switch (RfDs.RF_State_L) {
    case TX_STATUS:  //TX_STATE
                  RfDs.RF_TxState_L=TX_IDLE ;//No other operation suite for the receiver on left channel
			      break;
			
    case RX_STATUS : //RX_STATE
		          //----------------------------------------------------------------------------
		          if(RfDs.RF_TxState_R>TX_IDLE)
			        break ; //When Right Channel in TX mode ,Not allowed to do RX
   		          //----------------------------------------------------------------------------
                  /* Read data from IC021 */
			      ShiftReg_L=ShiftReg_L<<1;
			      if(GPIOB->IDR &0x00000080) ShiftReg_L|=(uint8_t)0x01 ;//DIO pin = high
			      BitCounter_L++;
                  switch (RfDs.RF_RxState_L) {
                    case RX_NO_INIT_IDLE :  break ;
                    case RX_SEARCH_PREAMBLE : //search for a vaild preamble status
						       /* If valid preamble, increase counter */
						       if ((ShiftReg_L==(uint8_t)0x55)||(ShiftReg_L==(uint8_t)0xAA))
								PreambleCount_L++;
						       /* If not, reset counter */
						       else
							    PreambleCount_L=0;
						       /* If preamble requirement has been reached, declare that preamble */
						       /* has been found */
						       if (PreambleCount_L>=PREAMBLE_REQ)
                               {
								 RfDs.RF_RxState_L=RX_SEARCH_SOF1;//switch to preamble found status
								 PreambleError_L=0;
                                 IsrFlag |=IFG_RSSI_D_OK_L ;//Request to do RSSI check operation
								}
                               break ;
                    case RX_SEARCH_SOF1 ://vailid preable be found already
                               //here must start receiving monitor timer#####################
                               //after receiving last data byte,must disable receiving monitor timer
                               //overflow of the timer,will reset the receiver deamon

				               /* Look for SOF/unique identifier */
							   if (ShiftReg_L==(uint8_t)SOF_ID1)
                               {
								 // If SOF found, go into RX mode
								 BitCounter_L=0 ;
								 RfDs.RF_RxState_L=RX_SEARCH_SOF2;//first SOF already received status
								 }
								/* Are we still receiving the preamble? */
							   else if ((ShiftReg_L==(uint8_t)0x55)||(ShiftReg_L==(uint8_t)0xAA))
                               {
								 ; /* If so, do nothing */
								}
								/* If we are not receiving a correct preamble, declare an error */
							   else if (PreambleError_L==0)
                               {
								PreambleError_L++;
								}
							   if (PreambleError_L>0)
								/* Increase the error counter regardless of bits read if */
								/* we have found en error */
								   PreambleError_L++;
								/* Once an error condition has occurred, a correct SOF must be */
								/* found within 9 bits (we increment by 2 at the first bit), */
								/* otherwise we abort and start looking for the preamble again */
							   if (PreambleError_L>10)
                               {
								RfDs.RF_RxState_L=RX_SEARCH_PREAMBLE;//switch to search for preamble status
								}
                               break ;
                    case RX_SEARCH_SOF2 : //
			                    /* Byte received? */
			                    if (BitCounter_L>=8)
			                    {
					              BitCounter_L=0;
								  if (ShiftReg_L==(uint8_t)SOF_ID2)
								  {//clear receive buffer ,reset pointer,ready to receiving datapackage
								    ByteCounter_L=0 ;
								    if((RfDs.RBuf_StaL&0x02)==0)
							        {
								      RfDat_rp_L=(uint8_t*)U_Area.RfRxChBuf0_L ;
								      RfDs.RBuf_StaL &=(uint8_t)0xfc ;//
								      RfDs.RBuf_StaL |=(uint8_t)0x01 ;//
								     }
								    else
								    {
								      RfDat_rp_L=(uint8_t*)U_Area.RfRxChBuf1_L ;
								      RfDs.RBuf_StaL &=(uint8_t)0xf3 ;//
                                      RfDs.RBuf_StaL |=(uint8_t)0x04 ;//
								     }
								    RfDs.RF_RxState_L=RX_RECIEVE_DP;//switch to receiving data package status
								   }
								  else
								  {
								    RfDs.RF_RxState_L=RX_SEARCH_PREAMBLE;//switch to search for preamble status
								   }
								 }
                                break ;
                    case RX_RECIEVE_DP : //
			                    /* Byte received? */
			                    if (BitCounter_L>=8)
			                    {
					              BitCounter_L=0;
					              *(RfDat_rp_L+ByteCounter_L++)=ShiftReg_L^(uint8_t)0xaa;
					              if(ByteCounter_L>=RfDs.RxPackageLen)
					              {
								    if(RfDat_rp_L==(uint8_t*)U_Area.RfRxChBuf0_L)
                                    {
							          RfDs.RBuf_StaL &=(uint8_t)0xfe ;
                                      RfDs.RBuf_StaL |=(uint8_t)0x02 ;//set data buffer0 valid flag
                                     }
								    else
                                    {
								      RfDs.RBuf_StaL &=(uint8_t)0xfb ;
                                      RfDs.RBuf_StaL |=(uint8_t)0x08 ;//set data buffer1 valid flag
                                     }
								    RfDs.RF_RxState_L=RX_SEARCH_PREAMBLE;//enter to search new data frame status
								                               //the received data
								    RfDs.RF_RxPackage_L++ ;
					               }
                                 }
                                break ;
                    case RX_RF_TEST_INIT:
						       if ((ShiftReg_L==(uint8_t)0x55)||(ShiftReg_L==(uint8_t)0xAA))
                               {
                                 RfDs.char_count_L=0 ;
                                 RfDat_rp_L=(uint8_t*) U_Area.Ultra_BUF_L ;
	                             RfDs.RF_RxState_L=RX_RF_TEST;//switch to RF test status
	                            }
                                break ;
                    case RX_RF_TEST:
                               if (BitCounter_L==8)
                                {
                                  BitCounter_L=0 ;
                                  *RfDat_rp_L=ShiftReg_L ;
                                  RfDat_rp_L++ ;
                                  RfDs.char_count_L++ ;
                                  if((RfDs.char_count_L&(u16)0x001f)==0)
                                    IsrFlag |=IFG_RSSI_D_OK_L ;//Request to do RSSI check operation
                                  if(RfDs.char_count_L>=96)
                                  {
                                    RfDs.char_count_L=0 ;
                                    RfDat_rp_L=(uint8_t*) U_Area.Ultra_BUF_L ;
                                   }
                                 }
                                break ;
			        default :
								RfDs.RF_RxState_L=RX_SEARCH_PREAMBLE;//enter to search new data frame status
					}
					break ;
	default :{
	           RfDs.RF_State_L=RF_IDLE_STATUS ;//=0x0000
               EXTI->IMR  &=0xfffffeff ; // disable DCLK signal to generate interrupt in current state
	          }
	}
 }
}

/* EOF */

