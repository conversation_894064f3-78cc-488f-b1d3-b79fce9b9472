# 列行式逆向取模汉字取模工具

专门用于生成12×12宋体汉字的列行式逆向取模字库数据的Python工具。

## 🎯 **逆向取模格式说明**

### 取模方式对比
- **顺向取模**：第0行→bit7, 第1行→bit6, ..., 第7行→bit0
- **逆向取模**：第0行→bit0, 第1行→bit1, ..., 第7行→bit7

### 数据结构
- **前12字节**：12列，每列前8行（逆向取模）
- **后12字节**：12列，每列后4行（逆向取模）
- **总尺寸**：12×12点阵

## 🚀 **工具特点**

### 四栏布局设计
```
┌─────────────┬─────────────┬─────────────┬─────────────┐
│ 字符输入    │ 列数据分析   │ 顺向vs逆向  │ 生成的C代码  │
│ 输入汉字:底 │ 列行式逆向   │ 对比分析    │ {{12, 12},{ │
│ [生成取模]  │ 取模数据分析 │ 列号 顺向   │ 0x02, /*..  │
│ [批量取模]  │ 列0: 0x02   │ 逆向 差异   │ 0x00, /*..  │
│ ┌─────────┐ │ 列1: 0x00   │ 0  0x02    │ ...         │
│ │12×12预览│ │ ...         │ 0x80 不同   │             │
│ │████░░░░│ │             │ ...         │             │
│ │░░██████│ │             │             │             │
│ └─────────┘ │             │             │             │
└─────────────┴─────────────┴─────────────┴─────────────┘
```

### 核心功能
1. **实时取模**：输入汉字自动生成12×12点阵
2. **逆向取模**：专门的逆向取模算法
3. **对比分析**：顺向vs逆向取模数据对比
4. **列数据分析**：详细的列数据分解
5. **批量处理**：支持多个汉字批量取模
6. **宋体字体**：专门优化的宋体字体支持

## 📊 **生成的代码格式**

```c
// 字符: 底 (Unicode: 0x5E95) - 列行式逆向取模
// 字体: 宋体 12pt, 12×12点阵

// CodePage条目 (需要插入到CodePage12.c中):
{ 0x5E95, 0x5E95, 0xXXXX }/* Segment XXX, 0x0001 Symbols */

// Unicode12数组条目 (需要插入到Unicode12.c中):
{{12, 12},{
// 前12字节：12列×前8行（逆向取模）
0x02,    /*  .......@  */  // 列0
0x00,    /*  ........  */  // 列1
0x80,    /*  @.......  */  // 列2
...

// 后12字节：12列×后4行（逆向取模）
0x00,    /*  ....    */    // 列0
0x00,    /*  ....    */    // 列1
0x07,    /*  @@@.    */    // 列2
...
}}

// 列行式逆向取模格式说明:
// 前12字节: 12列，每列前8行（逆向取模）
// 后12字节: 12列，每列后4行（逆向取模）
// 逆向取模: 第0行→bit0, 第1行→bit1, ..., 第7行→bit7
```

## 💡 **使用方法**

### 1. 启动工具
```bash
python font_generator_column_reverse.py
```
或双击 `run_column_reverse.bat`

### 2. 单个字符取模
1. 在"输入汉字"框中输入一个汉字
2. 点击"生成取模"或按回车键
3. 查看四个区域的详细信息：
   - 左上：12×12可视化预览
   - 左中：详细的列数据分析
   - 右中：顺向vs逆向对比
   - 右侧：完整的C代码

### 3. 批量取模
1. 点击"批量取模"按钮
2. 在弹出窗口中输入多个汉字
3. 点击"开始批量生成"
4. 复制或保存批量结果

### 4. 示例字符
点击"示例字符"按钮，选择预设的汉字进行快速测试。

## 🔧 **字体设置**

### 字体选择
- 点击"选择宋体字体"选择TTF/TTC字体文件
- 工具会自动尝试加载系统宋体
- 支持Windows、macOS、Linux系统

### 字体大小
- 默认12pt，可调整10-16pt
- 实时预览字体大小变化
- 自动重新生成取模数据

## 📋 **列数据分析示例**

```
列行式逆向取模数据分析:
==================================================

前12字节（12列×前8行，逆向取模）:
列 0: 0x02 = ·█······ (bit0→行0)
列 1: 0x00 = ········ (bit0→行0)
列 2: 0x80 = ·······█ (bit0→行0)
...

后12字节（12列×后4行，逆向取模）:
列 0: 0x00 = ····     (bit0→行8)
列 1: 0x00 = ····     (bit0→行8)
列 2: 0x07 = ███·     (bit0→行8)
...

逆向取模方式说明:
- 逆向取模：第0行对应字节的最低位(bit0)
- 第1行对应bit1，第2行对应bit2，以此类推
- 第7行对应bit7
- 每列独立存储，共12列
- 前8行存储在前12字节，后4行存储在后12字节
```

## 📊 **顺向vs逆向对比**

```
顺向取模 vs 逆向取模对比:
==================================================

前12字节对比（前8行）:
列号  顺向取模  逆向取模  差异
----------------------------------------
 0    <USER>     <GROUP>     不同
 1    0x00     0x00     相同
 2    0x01     0x80     不同
...

取模方式对比:
顺向取模: 行0→bit7, 行1→bit6, ..., 行7→bit0
逆向取模: 行0→bit0, 行1→bit1, ..., 行7→bit7

应用场景:
- 顺向取模：传统LCD显示，从高位开始
- 逆向取模：某些特殊硬件，从低位开始
```

## 🎨 **界面特色**

### 可视化预览
- 30×30像素的大格子显示
- 清晰的行列标号（红色行号，蓝色列号）
- 实时更新的12×12点阵

### 智能功能
- 输入字符自动延迟生成（500ms）
- 字体大小实时调整
- 自动检测系统宋体字体

### 数据导出
- 复制C代码到剪贴板
- 复制列分析数据
- 保存到.c或.txt文件
- 批量生成结果导出

## 🔍 **应用场景**

### 1. 嵌入式显示开发
- LCD/OLED显示屏字库开发
- 特殊硬件的逆向取模需求
- 12×12点阵字库系统

### 2. 字库格式转换
- 从顺向取模转换为逆向取模
- 不同硬件平台的字库适配
- 字库格式标准化

### 3. 教学和研究
- 理解不同取模方式的差异
- 字库编码原理学习
- 点阵字体设计

## 💻 **系统要求**

- Python 3.6+
- tkinter（通常随Python安装）
- PIL/Pillow：`pip install pillow`
- numpy：`pip install numpy`

## 🐛 **故障排除**

### 问题1：字体显示不清晰
- 选择高质量的宋体字体文件
- 调整字体大小（建议12pt）
- 确保字体文件路径正确

### 问题2：取模结果异常
- 检查输入字符是否为有效汉字
- 验证字体文件是否支持该字符
- 对比顺向和逆向取模结果

### 问题3：批量处理失败
- 确保输入的都是有效汉字
- 检查系统内存是否充足
- 分批处理大量字符

---
*工具版本: 1.0*  
*适用于: 12×12点阵字库系统（列行式逆向取模）*
