/*****File  EcsBasic2G.c function and vriable common for all in sencond generation TDECS  Firmware  *****/
#include "stdint.h"
#include "stm32f10x.h"
#include "SysBasic.h"
#include "ThisDevice.h"
/* Private variables ---------------------------------------------------------*/
 ErrorStatus HSEStartUpStatus;
/* Extern variables ----------------------------------------------------------*/

extern uint32_t SystemCoreClock	 ;
extern volatile  uint32_t   CK_HSI_Flag ; //='1' Flag to indicate current use HSI clock,maybe H<PERSON> failed to run
//-----------------------------------------------------------------------------------------------

const uint16_t wCRCTable[] = {
   0x0000, 0xC0C1, 0xC181, 0x0140, 0xC301, 0x03C0, 0x0280, 0xC241,
   0xC601, 0x06C0, 0x0780, 0xC741, 0x0500, 0xC5C1, 0xC481, 0x0440,
   0xCC01, 0x0CC0, 0x0D80, 0xCD41, 0x0F00, 0xCFC1, 0xCE81, 0x0E40,
   0x0A00, 0xCAC1, 0xCB81, 0x0B40, 0xC901, 0x09C0, 0x0880, 0xC841,
   0xD801, 0x18C0, 0x1980, 0xD941, 0x1B00, 0xDBC1, 0xDA81, 0x1A40,
   0x1E00, 0xDEC1, 0xDF81, 0x1F40, 0xDD01, 0x1DC0, 0x1C80, 0xDC41,
   0x1400, 0xD4C1, 0xD581, 0x1540, 0xD701, 0x17C0, 0x1680, 0xD641,
   0xD201, 0x12C0, 0x1380, 0xD341, 0x1100, 0xD1C1, 0xD081, 0x1040,
   0xF001, 0x30C0, 0x3180, 0xF141, 0x3300, 0xF3C1, 0xF281, 0x3240,
   0x3600, 0xF6C1, 0xF781, 0x3740, 0xF501, 0x35C0, 0x3480, 0xF441,
   0x3C00, 0xFCC1, 0xFD81, 0x3D40, 0xFF01, 0x3FC0, 0x3E80, 0xFE41,
   0xFA01, 0x3AC0, 0x3B80, 0xFB41, 0x3900, 0xF9C1, 0xF881, 0x3840,
   0x2800, 0xE8C1, 0xE981, 0x2940, 0xEB01, 0x2BC0, 0x2A80, 0xEA41,
   0xEE01, 0x2EC0, 0x2F80, 0xEF41, 0x2D00, 0xEDC1, 0xEC81, 0x2C40,
   0xE401, 0x24C0, 0x2580, 0xE541, 0x2700, 0xE7C1, 0xE681, 0x2640,
   0x2200, 0xE2C1, 0xE381, 0x2340, 0xE101, 0x21C0, 0x2080, 0xE041,
   0xA001, 0x60C0, 0x6180, 0xA141, 0x6300, 0xA3C1, 0xA281, 0x6240,
   0x6600, 0xA6C1, 0xA781, 0x6740, 0xA501, 0x65C0, 0x6480, 0xA441,
   0x6C00, 0xACC1, 0xAD81, 0x6D40, 0xAF01, 0x6FC0, 0x6E80, 0xAE41,
   0xAA01, 0x6AC0, 0x6B80, 0xAB41, 0x6900, 0xA9C1, 0xA881, 0x6840,
   0x7800, 0xB8C1, 0xB981, 0x7940, 0xBB01, 0x7BC0, 0x7A80, 0xBA41,
   0xBE01, 0x7EC0, 0x7F80, 0xBF41, 0x7D00, 0xBDC1, 0xBC81, 0x7C40,
   0xB401, 0x74C0, 0x7580, 0xB541, 0x7700, 0xB7C1, 0xB681, 0x7640,
   0x7200, 0xB2C1, 0xB381, 0x7340, 0xB101, 0x71C0, 0x7080, 0xB041,
   0x5000, 0x90C1, 0x9181, 0x5140, 0x9301, 0x53C0, 0x5280, 0x9241,
   0x9601, 0x56C0, 0x5780, 0x9741, 0x5500, 0x95C1, 0x9481, 0x5440,
   0x9C01, 0x5CC0, 0x5D80, 0x9D41, 0x5F00, 0x9FC1, 0x9E81, 0x5E40,
   0x5A00, 0x9AC1, 0x9B81, 0x5B40, 0x9901, 0x59C0, 0x5880, 0x9841,
   0x8801, 0x48C0, 0x4980, 0x8941, 0x4B00, 0x8BC1, 0x8A81, 0x4A40,
   0x4E00, 0x8EC1, 0x8F81, 0x4F40, 0x8D01, 0x4DC0, 0x4C80, 0x8C41,
   0x4400, 0x84C1, 0x8581, 0x4540, 0x8701, 0x47C0, 0x4680, 0x8641,
   0x8201, 0x42C0, 0x4380, 0x8341, 0x4100, 0x81C1, 0x8081, 0x4040 } ;
//-----------------------------------------------------------------------------
/* Global variable define */
volatile  uint32_t      OpCtrl =0 ;
volatile  uint32_t       GFlag =0 ;
volatile  uint32_t   GFlag_Pre =0 ;

volatile  uint16_t     SYS_STA =0 ;  //POWER_DOWN_STA
volatile  uint16_t     IsrFlag =0 ;//Special flag for used in ISR	,Begin with IFG_

volatile  uint16_t   TestKeyA, TestKeyB, TestCtrlW ;

//---------Define most important funtion pointer for multimode operation-----
void (*AppMain_fp)(void) __attribute__ ((noreturn));//Define a function pointer for particular application main funtion 

void (*Exi2_isr_fp)(void) ;//Define function pointer for interrupt handler
void (*Exi3_isr_fp)(void) ;//Define function pointer for interrupt handler
void (*Exi9_5_isr_fp)(void) ;//Define function pointer for interrupt handler

void (*Proc10mS_fp)(void) ;//Define function pointer for periodical called timer function--10mS
void (*Proc100mS_fp)(void) ;
void (*Proc1S_fp)(void) ;
void (*Proc1H_fp)(void) ;
//-----------------------------------------------------------------------------

SysAreaStruct    S_Area __attribute__ ((aligned));//defien an object for system data

UserAreaStruct   U_Area __attribute__ ((aligned));//define an object for user data

//------------------------------------------------------------------------------

void InitWatchDog(void)
{
  IWDG->KR = ((uint16_t)0x5555) ;//Enable write access to IWDG_PR and IWDG_RLR 
  // Configure IWDG Prescaler register value 
  IWDG->PR =0;//IWDG use 40 kHz internal RC clock,prescaler=4
  // Configure WDG Pre-load register value 
  IWDG->RLR = 1000 ;//About 10mS reset cycle
  // Select WDG mode 
  IWDG->KR = ((uint16_t)0x0000) ;//Disable write access to IWDG_PR and IWDG_RLR 
  IWDG->KR = ((uint16_t)0xCCCC) ;//Start the watchdog
  IWDG->KR = ((uint16_t)0xAAAA) ;//Key for Reset(Reload) watchdog 
}

void KickWatchDog(void)
{
  IWDG->KR = ((uint16_t)0xAAAA) ;//Key for Reset(Reload) watchdog 
}

uint16_t CRC16 (const uint16_t *nData, uint16_t wLength)//Gerate 16 bit CRC value from series words
{
  uint16_t nTemp  ;//byte value (data saved as 16 bit word)
  uint16_t wCRCWord ;
  wCRCWord =0xffff;
  while (wLength--)
  {
    nTemp     = (*nData >>8) ;
    nTemp     ^= wCRCWord ;
    nTemp     &= 0x00ff ;
    wCRCWord  >>= 8 ;
    wCRCWord  &=0x00ff ; //A right shift CCS always do arithmetic shift
    wCRCWord  ^=wCRCTable[nTemp];// PFUNC_wordRead(TBp) ;
    nTemp      = *nData ^ wCRCWord;
    nData++ ;
    nTemp    &=0x00ff ;
    wCRCWord >>= 8;
    wCRCWord &= 0x00ff ; //A right shift CCS always do arithmetic shift
    wCRCWord ^= wCRCTable[nTemp];//PFUNC_wordRead(TBp) ;
   }
  nTemp=wCRCWord ;
  wCRCWord>>=8 ;
  wCRCWord &=0x00ff ;
  nTemp<<=8 ;
  wCRCWord |=nTemp ;
  return wCRCWord;  //Pattention to CRC word's Lo and Hi byte order*********
}

uint32_t CRC32(const uint32_t *nData, uint32_t wLength)
{//CRC32 use STM32 hardware accelerator
  uint32_t index = 0;
  /* Reset CRC generator */
  CRC->CR = ((uint32_t)0x00000001);
  //begin to caculate new CRC 32
  for(index = 0; index < wLength; index++)
  {
    CRC->DR = *nData;
    nData++ ;
   }
  return (CRC->DR);
}

uint32_t CRC32_Calc(uint8_t *frame_buf, uint32_t frame_len)
{//CRC32 used for Ethernet
  uint32_t  i; // iterator
  uint32_t  j; // another iterator
  uint8_t   byte; // current byte
  uint32_t  crc; // CRC result
  uint32_t  q0, q1, q2, q3; // temporary variables

  crc = 0xFFFFFFFF;//Initial seed value

  for (i = 0; i < frame_len; i++)
  {
    byte = *frame_buf++;
    for (j = 0; j < 2; j++)
	{
      if (((crc >> 28) ^ (byte >> 3)) & 0x00000001)
	  {
        q3 = 0x04C11DB7;
       } 
	  else
	  {
        q3 = 0x00000000;
       }
      if (((crc >> 29) ^ (byte >> 2)) & 0x00000001)
	  {
        q2 = 0x09823B6E;
       } 
	  else 
	  {
        q2 = 0x00000000;
       }
      if (((crc >> 30) ^ (byte >> 1)) & 0x00000001) 
	  {
        q1 = 0x130476DC;
       } 
	  else 
	  {
        q1 = 0x00000000;
       }
      if (((crc >> 31) ^ (byte >> 0)) & 0x00000001) 
	  {
        q0 = 0x2608EDB8;
       } 
	  else 
	  {
        q0 = 0x00000000;
       }
      crc = (crc << 4) ^ q3 ^ q2 ^ q1 ^ q0;
      byte >>= 4;
      }
   }
   return crc;
}

uint32_t CalReqCode(void)
{//Calculate authentication request code
  __IO uint32_t *ltp  ;
  ltp=(__IO uint32_t *) 0x1FFFF7E8 ;//Unique device ID register address
  CRC->CR =((uint32_t)0x00000001); // Reset CRC generator 
  CRC->DR =0x9d38cae5 ;
  CRC->DR = *ltp++^0xb7fa8c42;
  CRC->DR = *ltp++^0x3d90b51e;
  CRC->DR = *ltp;
  return (CRC->DR);
}

uint32_t GenAuthCode(uint32_t ReqCode)
{
  CRC->CR = ((uint32_t)0x00000001);
  CRC->DR =0xb2c7a58d ;//Authentication seed
  CRC->DR =ReqCode ;
  CRC->DR =0x47fc29b6 ;
  return(CRC->DR) ;
}

uint32_t VerifyAuthCode(uint32_t AuthCode)
{//return 0 if verify success,otherwise non zero
  uint32_t ltmp ;
  ltmp=CalReqCode() ;//Get UID
  CRC->CR = ((uint32_t)0x00000001);
  CRC->DR =0xb2c7a58d ;//Verify seed
  CRC->DR =ltmp ;
  CRC->DR =0x47fc29b6 ;
  return(CRC->DR^AuthCode)  ;
}

//------------------------------------------------

void ClrObj(uint8_t *obj,uint16_t size)
{//clear memory object
 u16 i ;
 for(i=0 ;i<size ;i++)
 {
  *(obj++)=(uint8_t)0 ;
 }
}

void ClrObj16(uint16_t *obj,uint16_t size)
{//clear memory object
 u16 i ;
 for(i=0 ;i<size ;i++)
 {
  *(obj++)=(uint16_t)0 ;
 }
}

void ClrObj32(uint32_t *obj,uint16_t size)
{//clear memory object
 u16 i ;
 for(i=0 ;i<size ;i++)
 {
  *(obj++)=(uint32_t)0 ;
 }
}

void CopyObj(uint8_t *scr,uint8_t *den,uint16_t size)
{//copy memory object in byte mode
 uint16_t i ;
 for(i=0 ;i<size ;i++)
 {
  *(den++)=*(scr++) ;
 }
}

void CopyObj16(uint16_t *scr,uint16_t *den,uint16_t size)
{//Copy memory object in 16 bit word mode
 uint16_t i ;
 for(i=0 ;i<size ;i++)
 {
  *(den++)=*(scr++) ;
 }
}

void CopyObj32(uint32_t *scr,uint32_t *den,uint16_t size)
{//Copy memory object in 32 bit word mode
 uint16_t i ;
 for(i=0 ;i<size ;i++)
 {
  *(den++)=*(scr++) ;
 }
}

//-----------------------------------------------------------------------------
void  Init_IO_INT_021(void)
{
//Private variable
  NVIC_InitTypeDef NVIC_InitStructure;
  uint8_t PreemptionPriorityValue = 0; 

//Default IO configuration according to Hardware
  GPIOA->CRL=0xb4b18822 ;
  GPIOA->CRH=0x888988a2 ;//For debug 0x888b8891
  GPIOA->ODR=0x0000be12 ;//--PA.1--R_RSSI='1' enable left and right IC021 CE pin to high in old 38405 board
  
  GPIOB->CRL=0x82289422 ;
  GPIOB->CRH=0x22882228 ;
  GPIOB->ODR=0x00003e7b ;
  
  GPIOC->CRL=0x22822222 ;//PC.5 be set to IPD
  GPIOC->CRH=0x94283222 ;//PC.12 be set to IPD
  GPIOC->ODR=0x00002fdf ;
  
  GPIOD->CRL=0x444442b2 ;
  GPIOD->CRH=0x44444444 ;
  GPIOD->ODR=0x00000000 ;
  AFIO->MAPR=0x00000000 ; //No Remap
//-----------------------
  AFIO->EXTICR[0]=0x0000 ;//EXTI.0-3 connect to PA.0-3(PA.2:L_DIO-->GDO2,PA.3:L_DCLK-->GDO0(38405:R_DCLK) )
  AFIO->EXTICR[1]=0x1000 ;//EXTI.4-6 connect to PA.4-6,EXTI-7 connect to PB.7(R_DIO-->GDO2)
  AFIO->EXTICR[2]=0x0001 ;//EXTI.8 connect to PB.8(R_DCLK-->GDO0(38405:L_DCLK)),
//----------------------------------------------------------------------------------
  /* Configure two bits for preemption priority */
  NVIC_PriorityGroupConfig(NVIC_PriorityGroup_2);//preemption priority[0-3],NVIC_IRQChannelSubPriority[0-3]
//------------
  /* Configure the SysTick Handler Priority: Preemption priority and subpriority */
  NVIC_SetPriority(SysTick_IRQn, (!PreemptionPriorityValue << 0x03));
  
  /* Enable the EXTI3 Interrupt */
  NVIC_InitStructure.NVIC_IRQChannel = EXTI3_IRQn;
  NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 0;
  NVIC_InitStructure.NVIC_IRQChannelSubPriority = 2;
  NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;
  NVIC_Init(&NVIC_InitStructure);

  /* Enable the EXTI9_5 Interrupt */
  NVIC_InitStructure.NVIC_IRQChannel = EXTI9_5_IRQn;
  NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 0;
  NVIC_InitStructure.NVIC_IRQChannelSubPriority = 1;
  NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;
  NVIC_Init(&NVIC_InitStructure);
  
  /* Enable the RTC Interrupt 
  NVIC_InitStructure.NVIC_IRQChannel = RTC_IRQn;
  NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 0;
  NVIC_InitStructure.NVIC_IRQChannelSubPriority = 1;
  NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;
  NVIC_Init(&NVIC_InitStructure); */

 
  /* Enable the USB_LP_CAN_RX0 Interrupt */
  NVIC_InitStructure.NVIC_IRQChannel = USB_LP_CAN1_RX0_IRQn;
  NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 1;
  NVIC_InitStructure.NVIC_IRQChannelSubPriority = 0;
  NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;
  NVIC_Init(&NVIC_InitStructure); 
  
  /* Enable the CAN1_RX1_IRQn Interrupt  */
  NVIC_InitStructure.NVIC_IRQChannel = CAN1_RX1_IRQn;
  NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 1;
  NVIC_InitStructure.NVIC_IRQChannelSubPriority = 1;
  NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;
  NVIC_Init(&NVIC_InitStructure);
  
  /* Enable the USB_HP_CAN_TX0 Interrupt
  NVIC_InitStructure.NVIC_IRQChannel = USB_HP_CAN1_TX_IRQn;
  NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 0;
  NVIC_InitStructure.NVIC_IRQChannelSubPriority = 1;
  NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;
  NVIC_Init(&NVIC_InitStructure);*/
  
 /* Enable the CAN1_SCE Interrupt */
  NVIC_InitStructure.NVIC_IRQChannel = CAN1_SCE_IRQn ;
  NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 2;
  NVIC_InitStructure.NVIC_IRQChannelSubPriority = 2;
  NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;
  NVIC_Init(&NVIC_InitStructure);
  
  /* Enable the USART1 Interrupt */
  NVIC_InitStructure.NVIC_IRQChannel = USART1_IRQn;
  NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 2;
  NVIC_InitStructure.NVIC_IRQChannelSubPriority = 1;
  NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;
  NVIC_Init(&NVIC_InitStructure);

  /* Enable the DMA1  Channel4 Interrupt--USART1 TX channel */
  NVIC_InitStructure.NVIC_IRQChannel = DMA1_Channel4_IRQn;
  NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 2;
  NVIC_InitStructure.NVIC_IRQChannelSubPriority = 3;
  NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;
  NVIC_Init(&NVIC_InitStructure);
  
  /* Enable the TIM2 update Interrupt  */
  NVIC_InitStructure.NVIC_IRQChannel =TIM2_IRQn ;
  NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 2;
  NVIC_InitStructure.NVIC_IRQChannelSubPriority = 1;
  NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;
  NVIC_Init(&NVIC_InitStructure);
//initialize the interrupt controller
  //------------------------
  TIM2->CR1  =0x0000 ;
  TIM2->SR   =0xffe0 ;//Clear the TIM2 inperrupt flag
  TIM2->DIER =0x0000 ;//Disable TIM2 interrupt
  //------------------------
}

//-----------------------------------------------------------------------------
void  Init_IO_INT_01(void)
{
//Private variable
  NVIC_InitTypeDef NVIC_InitStructure;
  uint8_t PreemptionPriorityValue = 0; 

//Default IO configuration according to Hardware
  GPIOA->CRL=0xb4b18822 ;
  GPIOA->CRH=0x888988a2 ;//For debug 0x888b8891
  GPIOA->ODR=0x0000be12 ;//--PA.1--R_RSSI='1' 
  
  GPIOB->CRL=0x82289422 ;
  GPIOB->CRH=0x22882228 ;
  GPIOB->ODR=0x00003e7b ;
  
  GPIOC->CRL=0x22222222 ;
  GPIOC->CRH=0x94283222 ;//PC.12 be set to IPD
  GPIOC->ODR=0x00002fff ;
  
  GPIOD->CRL=0x444442b2 ;
  GPIOD->CRH=0x44444444 ;
  GPIOD->ODR=0x00000000 ;
  AFIO->MAPR=0x00000000 ; //No Remap
//-----------------------
  AFIO->EXTICR[0]=0x0000 ;//EXTI.0-3 connect to PA.0-3(PA.2:L_DIO-->GDO2,PA.3:L_DCLK-->GDO0(38405:R_DCLK) )
  AFIO->EXTICR[1]=0x1000 ;//EXTI.4-6 connect to PA.4-6,EXTI-7 connect to PB.7(R_DIO-->GDO2)
  AFIO->EXTICR[2]=0x0001 ;//EXTI.8 connect to PB.8(R_DCLK-->GDO0(38405:L_DCLK)),
//----------------------------------------------------------------------------------
  /* Configure two bits for preemption priority */
  NVIC_PriorityGroupConfig(NVIC_PriorityGroup_2);//preemption priority[0-3],NVIC_IRQChannelSubPriority[0-3]
  
//------------
  /* Configure the SysTick Handler Priority: Preemption priority and subpriority */
  NVIC_SetPriority(SysTick_IRQn, (!PreemptionPriorityValue << 0x03));
  
  /* Enable the EXTI2 Interrupt */
  NVIC_InitStructure.NVIC_IRQChannel = EXTI2_IRQn;
  NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 0;
  NVIC_InitStructure.NVIC_IRQChannelSubPriority = 2;
  NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;
  NVIC_Init(&NVIC_InitStructure); 

  /* Enable the EXTI3 Interrupt */
  NVIC_InitStructure.NVIC_IRQChannel = EXTI3_IRQn;
  NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 0;
  NVIC_InitStructure.NVIC_IRQChannelSubPriority = 2;
  NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;
  NVIC_Init(&NVIC_InitStructure);

  /* Enable the EXTI9_5 Interrupt */
  NVIC_InitStructure.NVIC_IRQChannel = EXTI9_5_IRQn;
  NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 0;
  NVIC_InitStructure.NVIC_IRQChannelSubPriority = 1;
  NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;
  NVIC_Init(&NVIC_InitStructure);
  
  
  /* Enable the RTC Interrupt 
  NVIC_InitStructure.NVIC_IRQChannel = RTC_IRQn;
  NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 0;
  NVIC_InitStructure.NVIC_IRQChannelSubPriority = 1;
  NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;
  NVIC_Init(&NVIC_InitStructure);*/

 
  /* Enable the USB_LP_CAN_RX0 Interrupt  */
  NVIC_InitStructure.NVIC_IRQChannel = USB_LP_CAN1_RX0_IRQn;
  NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 1;
  NVIC_InitStructure.NVIC_IRQChannelSubPriority = 0;
  NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;
  NVIC_Init(&NVIC_InitStructure);

  /* Enable the CAN1_RX1_IRQn Interrupt  */
  NVIC_InitStructure.NVIC_IRQChannel = CAN1_RX1_IRQn;
  NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 1;
  NVIC_InitStructure.NVIC_IRQChannelSubPriority = 1;
  NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;
  NVIC_Init(&NVIC_InitStructure);

  /* Enable the USB_HP_CAN_TX0 Interrupt
  NVIC_InitStructure.NVIC_IRQChannel = USB_HP_CAN1_TX_IRQn;
  NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 0;
  NVIC_InitStructure.NVIC_IRQChannelSubPriority = 1;
  NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;
  NVIC_Init(&NVIC_InitStructure);  */
  
 /* Enable the CAN1_SCE Interrupt */
  NVIC_InitStructure.NVIC_IRQChannel = CAN1_SCE_IRQn ;
  NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 2;
  NVIC_InitStructure.NVIC_IRQChannelSubPriority = 2;
  NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;
  NVIC_Init(&NVIC_InitStructure);
  
  /* Enable the USART1 Interrupt */
  NVIC_InitStructure.NVIC_IRQChannel = USART1_IRQn;
  NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 2;
  NVIC_InitStructure.NVIC_IRQChannelSubPriority = 1;
  NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;
  NVIC_Init(&NVIC_InitStructure);

  /* Enable the DMA1  Channel4 Interrupt--USART1 TX channel */
  NVIC_InitStructure.NVIC_IRQChannel = DMA1_Channel4_IRQn;
  NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 2;
  NVIC_InitStructure.NVIC_IRQChannelSubPriority = 3;
  NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;
  NVIC_Init(&NVIC_InitStructure);
  
  /* Enable the TIM2 update Interrupt  */
  NVIC_InitStructure.NVIC_IRQChannel =TIM2_IRQn ;
  NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 2;
  NVIC_InitStructure.NVIC_IRQChannelSubPriority = 1;
  NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;
  NVIC_Init(&NVIC_InitStructure);
//initialize the interrupt controller
  //------------------------
  TIM2->CR1  =0x0000 ;
  TIM2->SR   =0xffe0 ;//Clear the TIM2 inperrupt flag
  TIM2->DIER =0x0000 ;//Disable TIM2 interrupt
  //------------------------
}

/*******************************************************************************
* Function Name  : STM32_Init
* Description    : Initializes the STM32 for current application.
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void STM32F_Init(void)
{
  if((BootDats->f_key==F_KEY_FLAG)&&(BootDats->s_key==S_KEY_FLAG))
  { //system is hot boot
     BootDats->rst_count++ ;
	 BootDats->bt_flag=HOTBOOT_FLAG ;
   }
  else //system is cool boot
  {
	BootDats->f_key=F_KEY_FLAG ;//set cool boot flag
	BootDats->s_key=S_KEY_FLAG ;
	BootDats->rst_count=0 ;//reset boot counter
	BootDats->bt_flag=0x0000 ;
   }
//-------------------------------------------------------------------------------
  __disable_irq();//Disable all interrupt

  RCC_APB1PeriphClockCmd(RCC_APB1Periph_CAN1 |
                         RCC_APB1Periph_WWDG |
                         RCC_APB1Periph_TIM2 |
                         RCC_APB1Periph_TIM3 |
                         RCC_APB1Periph_PWR,
                         ENABLE );
  RCC_APB1PeriphResetCmd(RCC_APB1Periph_CAN1 |
                         RCC_APB1Periph_WWDG |
                         RCC_APB1Periph_TIM2 |
                         RCC_APB1Periph_TIM3 |
                         RCC_APB1Periph_PWR,
                         DISABLE );
// GPIO Init
  // Enable GPIO clock and release reset
  RCC_APB2PeriphClockCmd(RCC_APB2Periph_SPI1  |
                         RCC_APB2Periph_AFIO  |
                         RCC_APB2Periph_GPIOA |
                         RCC_APB2Periph_GPIOB |
                         RCC_APB2Periph_GPIOC |
                         RCC_APB2Periph_GPIOD |
                         RCC_APB2Periph_USART1,
                         ENABLE);
  RCC_APB2PeriphResetCmd(RCC_APB2Periph_SPI1  |
                         RCC_APB2Periph_AFIO  |
                         RCC_APB2Periph_GPIOA |
                         RCC_APB2Periph_GPIOB |
                         RCC_APB2Periph_GPIOC |
                         RCC_APB2Periph_GPIOD |
                         RCC_APB2Periph_USART1,
                         DISABLE);
/* Setup the microcontroller system. Initialize the Embedded Flash Interface,
   initialize the PLL and update the SystemFrequency variable. */
  SystemInit();//In file system_stm32f10x.c,FOR clock setup etc
  //RTC_Configuration() ;
  RCC_AHBPeriphClockCmd(RCC_AHBPeriph_DMA1|
                        RCC_AHBPeriph_SRAM|
                        RCC_AHBPeriph_CRC|
                        RCC_AHBPeriph_FLITF ,
                        ENABLE) ;
  __enable_irq();

}
//Init TIM3 for cpu run time measurement
void InitTimer3_1uS(void)
{
  TIM3->CR1=0x0284 ;
  TIM3->CNT=0x0000 ;//
  TIM3->PSC=(uint16_t)(SystemCoreClock/(uint32_t)1000000-(uint32_t)1) ;//TIM3 prescaler register--1uS (14.7456MHz HCLK)
  TIM3->ARR=20000  ;//TIM3 auto-reload register
  TIM3->DIER=0x0000 ;//TIM3 DMA/interrupt disable register ,only allow update interrupt
  TIM3->CR1=0x0285 ;//digital filter sample clock=4x tck_int,Up count mode ,only counter overflow gen interrupt,enable the counter
  TIM3->EGR=0x0001 ;//Manual set a update event force update PSC register
 }
//-------------------------------------------------------------
__inline void CpuSweepTime(uint16_t *MaxTp,uint16_t *CurTp)
{
  TIM3->CR1=0x0284 ;//Stop TIM5
  *CurTp=TIM3->CNT ;
  TIM3->CNT=0x0000 ;
  TIM3->CR1=0x0285 ;//Re_enable the TIM5
  if(*MaxTp<*CurTp)
    *MaxTp=*CurTp ;
}
//----------Main function for device test------
void  __TestMain(void)
{
  while(1) ;
}
//----------define a default function for all no operation call 
void  NoOperation(void)
{
  __nop() ;
}

/******************* END OF FILE*******************************/
