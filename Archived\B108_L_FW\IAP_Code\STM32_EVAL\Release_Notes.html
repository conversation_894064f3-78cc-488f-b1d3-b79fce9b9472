<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:w="urn:schemas-microsoft-com:office:word" xmlns="http://www.w3.org/TR/REC-html40"><head>






  
  <meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">

  
  <link rel="File-List" href="Library_files/filelist.xml">

  
  <link rel="Edit-Time-Data" href="Library_files/editdata.mso"><!--[if !mso]> <style> v\:* {behavior:url(#default#VML);} o\:* {behavior:url(#default#VML);} w\:* {behavior:url(#default#VML);} .shape {behavior:url(#default#VML);} </style> <![endif]--><title>Release Notes for STM32 Standard Peripherals Library Utilities</title><!--[if gte mso 9]><xml> <o:DocumentProperties> <o:Author>STMicroelectronics</o:Author> <o:LastAuthor>STMicroelectronics</o:LastAuthor> <o:Revision>37</o:Revision> <o:TotalTime>136</o:TotalTime> <o:Created>2009-02-27T19:26:00Z</o:Created> <o:LastSaved>2009-03-01T17:56:00Z</o:LastSaved> <o:Pages>1</o:Pages> <o:Words>522</o:Words> <o:Characters>2977</o:Characters> <o:Company>STMicroelectronics</o:Company> <o:Lines>24</o:Lines> <o:Paragraphs>6</o:Paragraphs> <o:CharactersWithSpaces>3493</o:CharactersWithSpaces> <o:Version>11.6568</o:Version> </o:DocumentProperties> </xml><![endif]--><!--[if gte mso 9]><xml> <w:WordDocument> <w:Zoom>110</w:Zoom> <w:ValidateAgainstSchemas/> <w:SaveIfXMLInvalid>false</w:SaveIfXMLInvalid> <w:IgnoreMixedContent>false</w:IgnoreMixedContent> <w:AlwaysShowPlaceholderText>false</w:AlwaysShowPlaceholderText> <w:BrowserLevel>MicrosoftInternetExplorer4</w:BrowserLevel> </w:WordDocument> </xml><![endif]--><!--[if gte mso 9]><xml> <w:LatentStyles DefLockedState="false" LatentStyleCount="156"> </w:LatentStyles> </xml><![endif]-->


  

  

  
  <style>
<!--
/* Style Definitions */
p.MsoNormal, li.MsoNormal, div.MsoNormal
{mso-style-parent:"";
margin:0in;
margin-bottom:.0001pt;
mso-pagination:widow-orphan;
font-size:12.0pt;
font-family:"Times New Roman";
mso-fareast-font-family:"Times New Roman";}
h2
{mso-style-next:Normal;
margin-top:12.0pt;
margin-right:0in;
margin-bottom:3.0pt;
margin-left:0in;
mso-pagination:widow-orphan;
page-break-after:avoid;
mso-outline-level:2;
font-size:14.0pt;
font-family:Arial;
font-weight:bold;
font-style:italic;}
a:link, span.MsoHyperlink
{color:blue;
text-decoration:underline;
text-underline:single;}
a:visited, span.MsoHyperlinkFollowed
{color:blue;
text-decoration:underline;
text-underline:single;}
p
{mso-margin-top-alt:auto;
margin-right:0in;
mso-margin-bottom-alt:auto;
margin-left:0in;
mso-pagination:widow-orphan;
font-size:12.0pt;
font-family:"Times New Roman";
mso-fareast-font-family:"Times New Roman";}
@page Section1
{size:8.5in 11.0in;
margin:1.0in 1.25in 1.0in 1.25in;
mso-header-margin:.5in;
mso-footer-margin:.5in;
mso-paper-source:0;}
div.Section1
{page:Section1;}
-->
  </style><!--[if gte mso 10]> <style> /* Style Definitions */ table.MsoNormalTable {mso-style-name:"Table Normal"; mso-tstyle-rowband-size:0; mso-tstyle-colband-size:0; mso-style-noshow:yes; mso-style-parent:""; mso-padding-alt:0in 5.4pt 0in 5.4pt; mso-para-margin:0in; mso-para-margin-bottom:.0001pt; mso-pagination:widow-orphan; font-size:10.0pt; font-family:"Times New Roman"; mso-ansi-language:#0400; mso-fareast-language:#0400; mso-bidi-language:#0400;} </style> <![endif]--><!--[if gte mso 9]><xml> <o:shapedefaults v:ext="edit" spidmax="5122"/> </xml><![endif]--><!--[if gte mso 9]><xml> <o:shapelayout v:ext="edit"> <o:idmap v:ext="edit" data="1"/> </o:shapelayout></xml><![endif]-->
  <meta content="MCD Application Team" name="author"></head>
<body link="blue" vlink="blue">
<div class="Section1">
<p class="MsoNormal"><span style="font-family: Arial;"><o:p><br>
</o:p></span></p>
<div align="center">
<table class="MsoNormalTable" style="width: 675pt;" border="0" cellpadding="0" cellspacing="0" width="900">
  <tbody>
    <tr>
      <td style="padding: 0cm;" valign="top">
      <table class="MsoNormalTable" style="width: 675pt;" border="0" cellpadding="0" cellspacing="0" width="900">
        <tbody>
          <tr>
            <td style="vertical-align: top;">
            <p class="MsoNormal"><span style="font-size: 8pt; font-family: Arial; color: blue;"><a href="../Release_Notes.html">Back to Release page</a><o:p></o:p></span></p>
            </td>
          </tr>
          <tr style="">
            <td style="padding: 1.5pt;">
            <h1 style="margin-bottom: 18pt; text-align: center;" align="center"><span style="font-size: 20pt; font-family: Verdana; color: rgb(51, 102, 255);">Release
Notes for STM32 Standard Peripherals Library Utilities (Utilities)</span><span style="font-size: 20pt; font-family: Verdana;"><o:p></o:p></span></h1>
            <p class="MsoNormal" style="text-align: center;" align="center"><span style="font-size: 10pt; font-family: Arial; color: black;">Copyright
2011 STMicroelectronics</span><span style="color: black;"><u1:p></u1:p><o:p></o:p></span></p>
            <p class="MsoNormal" style="text-align: center;" align="center"><span style="font-size: 10pt; font-family: Arial; color: black;"><img alt="" id="_x0000_i1025" src="../../_htmresc/logo.bmp" style="border: 0px solid ; width: 86px; height: 65px;"></span><span style="font-size: 10pt;"><o:p></o:p></span></p>
            </td>
          </tr>
        </tbody>
      </table>
      <p class="MsoNormal"><span style="font-family: Arial; display: none;"><o:p>&nbsp;</o:p></span></p>
      <table class="MsoNormalTable" style="width: 675pt;" border="0" cellpadding="0" width="900">
        <tbody>
          <tr style="">
            <td style="padding: 0cm;" valign="top">
            <h2 style="background: rgb(51, 102, 255) none repeat scroll 0% 50%; -moz-background-clip: initial; -moz-background-origin: initial; -moz-background-inline-policy: initial;"><span style="font-size: 12pt; color: white;">Contents<o:p></o:p></span></h2>
            <ol style="margin-top: 0cm;" start="1" type="1">
              <li class="MsoNormal" style="color: black; margin-top: 4.5pt; margin-bottom: 4.5pt;"><span style="font-size: 10pt; font-family: Verdana;"><a href="#History">STM32 Standard Peripherals Library Utilities
update History</a><o:p></o:p></span></li>
              <li class="MsoNormal" style="color: black; margin-top: 4.5pt; margin-bottom: 4.5pt;"><span style="font-size: 10pt; font-family: Verdana;"><a href="#License">License</a><o:p></o:p></span></li>
            </ol>
            <span style="font-family: &quot;Times New Roman&quot;;">
            </span>
            <h2 style="background: rgb(51, 102, 255) none repeat scroll 0% 50%; -moz-background-clip: initial; -moz-background-origin: initial; -moz-background-inline-policy: initial;"><a name="History"></a><span style="font-size: 12pt; color: white;">STM32
Standard
Peripherals Library Utilities update History</span></h2><br><h3 style="background: rgb(51, 102, 255) none repeat scroll 0% 50%; -moz-background-clip: initial; -moz-background-origin: initial; -moz-background-inline-policy: initial; margin-right: 500pt; width: 167px;"><span style="font-size: 10pt; font-family: Arial; color: white;">V4.5.0 / 07-March-2011<o:p></o:p></span></h3>
            <p class="MsoNormal" style="margin: 4.5pt 0cm 4.5pt 18pt;"><b style=""><u><span style="font-size: 10pt; font-family: Verdana; color: black;">Main
Changes<o:p></o:p></span></u></b></p>

            <ul style="margin-top: 0cm;" type="square"><li class="MsoNormal"><span style="font-size: 10pt; font-family: Verdana;">stm32_eval_sdio_sd.c\.h: driver improvement</span></li><ul><li class="MsoNormal"><span style="font-size: 10pt; font-family: Verdana;">SD Clock increased to 24MHz to improve the data transfer performance.</span></li><li class="MsoNormal"><span style="font-size: 10pt; font-family: Verdana;">Add
new functions to check the SDIO peripheral and SD Card status at any
time:&nbsp;SD_WaitReadOperation(), SD_WaitWriteOperation(). The
software sequence is little bit changed&nbsp;but without any impact on
driver API. For more details, refer to the stm32_eval_sdio_sd.c
driver&nbsp;description.</span></li><li class="MsoNormal"><span style="font-size: 10pt; font-family: Verdana;">Add
new structure containing the SD Status register parameters. This
structure is called by the
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
&nbsp;SD_SendSDStatus() function.</span></li><li class="MsoNormal"><span style="font-size: 10pt; font-family: Verdana;">Transfers mode updated</span></li><ul><li class="MsoNormal"><span style="font-size: 10pt; font-family: Verdana;">Read/Write Block using Polling and DMA modes</span></li><li class="MsoNormal"><span style="font-size: 10pt; font-family: Verdana;">Read/Write Multi Blocks using DMA mode only</span></li><li class="MsoNormal"><span style="font-size: 10pt; font-family: Verdana;">Interrupt mode removed</span></li></ul><li class="MsoNormal"><span style="font-size: 10pt; font-family: Verdana;">Data transfer functions are managing only fixed Block size (512-byte)&nbsp;</span></li></ul><li class="MsoNormal"><span style="font-size: 10pt; font-family: Verdana;">STM32100B-EVAL</span></li><ul><li class="MsoNormal"><span style="font-size: 10pt; font-family: Verdana;">stm32100b_eval_cec.c: fix some strict ANSI-C errors</span><span style="font-size: 10pt; font-family: Verdana;"></span></li></ul><li class="MsoNormal"><span style="font-size: 10pt; font-family: Verdana;">STM32100E-EVAL</span></li><ul><li class="MsoNormal"><span style="font-size: 10pt; font-family: Verdana;">stm32100e_eval_cec.c: fix some strict ANSI-C errors<br></span></li></ul></ul>
            <h3 style="background: rgb(51, 102, 255) none repeat scroll 0% 50%; -moz-background-clip: initial; -moz-background-origin: initial; -moz-background-inline-policy: initial; margin-right: 500pt; width: 167px;"><span style="font-size: 10pt; font-family: Arial; color: white;">V4.4.0 / 31-December-2010<o:p></o:p></span></h3>
            <p class="MsoNormal" style="margin: 4.5pt 0cm 4.5pt 18pt;"><b style=""><u><span style="font-size: 10pt; font-family: Verdana; color: black;">Main
Changes<o:p></o:p></span></u></b></p>

            <ul style="margin-top: 0cm;" type="square">
              <li class="MsoNormal"><span style="font-size: 10pt; font-family: Verdana;">Add new directory for STM32L152-EVAL board containing the following files:</span></li>
              <ul>
<li class="MsoNormal"><span style="font-size: 10pt; font-family: Verdana;">stm32l152_eval.h/.c, </span><span style="font-size: 10pt; font-family: Verdana;">stm32l152</span><span style="font-size: 10pt; font-family: Verdana;">_eval_lcd.h/.c, stm32l152_eval_glass_lcd.h</span><span style="font-size: 10pt; font-family: Verdana;">/.c, </span><span style="font-size: 10pt; font-family: Verdana;">stm32l152_eval_i2c_ee</span><span style="font-size: 10pt; font-family: Verdana;">.h/.c</span></li>
              </ul>
              <li class="MsoNormal"><span style="font-size: 10pt; font-family: Verdana;">Add support for the STM32100E-EVAL Rev B: SPI FLASH CS pin "sFLASH_CS_PIN" changed from PB.02 to PE.06.</span></li>
              <li class="MsoNormal"><span style="font-size: 10pt; font-family: Verdana;">stm32100e_eval_lcd.h/.c: Add support for "LCD_ILI9325" LCD controller.</span></li>
              <li class="MsoNormal"><span style="font-size: 10pt; font-family: Verdana;">stm32100e_eval_fsmc_onenand.h/.c driver updated to correct asynchronous and synchronous read operations procedures.<br>
                </span></li>
            </ul>

            <h3 style="background: rgb(51, 102, 255) none repeat scroll 0% 50%; -moz-background-clip: initial; -moz-background-origin: initial; -moz-background-inline-policy: initial; margin-right: 558.05pt;"><span style="font-size: 10pt; font-family: Arial; color: white;">4.3.0
- 10/15/2010</span></h3>
            <ol style="margin-top: 0in;" start="1" type="1">
              <li class="MsoNormal" style=""><b><i><span style="font-size: 10pt; font-family: Verdana;">General</span></i></b><i><span style="font-size: 10pt; font-family: Verdana;"> </span></i><i><span style="font-size: 10pt;"><o:p></o:p></span></i></li>
            </ol>
            
            <ul style="margin-top: 0in;" type="disc">

              
              
              
              <li class="MsoNormal" style=""><span style="font-size: 10pt; font-family: Verdana;">I2C EEPROM,&nbsp;</span><span style="font-size: 10pt; font-family: Verdana;">Temperature Sensor and&nbsp;</span><span style="font-size: 10pt; font-family: Verdana;">IOE Expander</span><span style="font-size: 10pt; font-family: Verdana;"> drivers&nbsp;updated to&nbsp;</span><span style="font-size: 10pt; font-family: Verdana;">use the DMA for read/write transfer and add more robustness</span></li>
              <li class="MsoNormal" style=""><span style="font-size: 10pt; font-family: Verdana;">SD Card (SDIO) driver updated to&nbsp;</span><span style="font-size: 10pt; font-family: Verdana;">add more robustness</span></li><li class="MsoNormal" style=""><span style="font-size: 10pt; font-family: Verdana;">SPI Flash and </span><span style="font-size: 10pt; font-family: Verdana;">SD Card (SPI) drivers: SPI MISO pin configuration changed to Input Floating&nbsp;</span></li>
            </ul>


            
            <ol style="margin-top: 0in;" start="2" type="1">
<li class="MsoNormal" style=""><b><i><span style="font-size: 10pt; font-family: Verdana;">Utilities</span></i></b><b><i><span style="font-size: 10pt;"><o:p></o:p></span></i></b></li>
            </ol>

            
            

            
            <ul style="margin-top: 0in;" type="circle">
              <li class="MsoNormal"><span style="font-size: 10pt; font-family: Verdana;">Add new directory for STM32100E-EVAL board containing the following files:</span></li>
              <ul>
                <li class="MsoNormal"><span style="font-size: 10pt; font-family: Verdana;">stm32100e_eval.h/.c,
stm32100e_eval_lcd.h/.c, stm32100e_eval_cec.h/.c,
stm32100e_eval_fsmc_onenand.h/.c, stm32100e_eval_fsmc_sram.h/.c,
stm32100e_eval_ioe.h/.c</span><br>
<span style="font-size: 10pt; font-family: Verdana;"><span style="font-style: italic;"></span></span></li>
              </ul>
              <li class="MsoNormal"><span style="font-size: 10pt; font-family: Verdana;">Common</span></li><ul><li class="MsoNormal"><span style="font-size: 10pt; font-family: Verdana;">stm32_eval_sdio_sd.c:
Update the DMA End of Transfer Check loop inside the SD_ReadBlock(),
SD_WriteBlock(), SD_ReadMultiBlocks() and SD_Write MultiBlocks().</span></li></ul>
              <ul><li class="MsoNormal"><span style="font-size: 10pt; font-family: Verdana;"><span style="font-style: italic;">stm32_eval_i2c_ee.c/.h</span> <br>
                </span></li></ul>
              <ul>
                <ul><li class="MsoNormal"><span style="font-size: 10pt; font-family: Verdana;">Enhanced sEE_WaitEepromStandbyState() function for more robustness.</span></li><li class="MsoNormal"><span style="font-size: 10pt; font-family: Verdana;">Enhanced Read and Write operations to manage I2C limitations.</span></li><li class="MsoNormal"><span style="font-size: 10pt; font-family: Verdana;">Add Timeout management with user callback implementation which allows recovering from I2C bus errors.</span></li><li class="MsoNormal"><span style="font-size: 10pt; font-family: Verdana;">Add
critical sections user callbacks allowing to disable then enable
interrupts when I2C operation require to be not interrupted.</span></li></ul></ul>
              <ul><li class="MsoNormal"><span style="font-size: 10pt; font-family: Verdana;"><span style="font-style: italic;">stm32_eval_i2c_tsensor.c/.h</span> <br>
                </span></li></ul>
              <ul>
                <ul><li class="MsoNormal"><span style="font-size: 10pt; font-family: Verdana;">Enhanced I2C communication functions by using DMA for registers Read and Write operations.</span></li><li class="MsoNormal"><span style="font-size: 10pt; font-family: Verdana;">Add Timeout management with user callback implementation which allows recovering from I2C bus errors.</span></li></ul></ul>
              <li class="MsoNormal"><span style="font-size: 10pt; font-family: Verdana;">STM32100B_EVAL</span></li><ul><li class="MsoNormal"><span style="font-size: 10pt; font-family: Verdana;">stm32100b_eval.h: Add LM75 DMA defines.</span></li></ul>
              <ul><li class="MsoNormal"><span style="font-size: 10pt; font-family: Verdana;">stm32100b_eval_lcd.c: </span><span style="font-size: 10pt; font-family: Verdana;">Change "SPI_FLASH" by "sFLASH" in LCD_DrawBMP() function.</span></li></ul>
              <li class="MsoNormal"><span style="font-size: 10pt; font-family: Verdana;">STM3210B_EVAL</span></li><ul><li class="MsoNormal"><span style="font-size: 10pt; font-family: Verdana;">stm3210b_eval.h: Add LM75 DMA defines.</span></li></ul>
              <ul><li class="MsoNormal"><span style="font-size: 10pt; font-family: Verdana;">stm3210b_eval_lcd.c: </span><span style="font-size: 10pt; font-family: Verdana;">Change "SPI_FLASH" by "sFLASH" in LCD_DrawBMP() function.</span></li></ul>
              <li class="MsoNormal"><span style="font-size: 10pt; font-family: Verdana;">STM3210C_EVAL</span></li><ul><li class="MsoNormal"><span style="font-size: 10pt; font-family: Verdana;">stm3210c_eval.h: Add EEPROM driver Timeout define.</span></li></ul>
              <ul><li class="MsoNormal"><span style="font-size: 10pt; font-family: Verdana;">stm3210c_eval_lcd.c: </span><span style="font-size: 10pt; font-family: Verdana;">Change "SPI_FLASH" by "sFLASH" in LCD_DrawBMP() function.</span></li></ul><ul><li class="MsoNormal"><span style="font-size: 10pt; font-family: Verdana;">stm3210c_eval_i2c_ioe.c</span></li><ul><li class="MsoNormal"><span style="font-size: 10pt; font-family: Verdana;">Enhanced I2C communication functions by using DMA for registers Read and Write operations.</span></li></ul><ul><li class="MsoNormal"><span style="font-size: 10pt; font-family: Verdana;">Add Timeout management with user callback implementation which allows recovering from I2C bus errors.</span></li><li class="MsoNormal"><span style="font-size: 10pt; font-family: Verdana;">change IOE_I2C_SPEED from "400000" to "300000".</span></li></ul></ul>
              <li class="MsoNormal"><span style="font-size: 10pt; font-family: Verdana;"></span><span style="font-size: 10pt; font-family: Verdana;">STM3210E_EVAL</span></li><ul><li class="MsoNormal"><span style="font-size: 10pt; font-family: Verdana;">stm3210e_eval.c: change "void SD_WaitForDMAEndOfTransfer(void)" to "uint32_t SD_DMAEndOfTransferStatus(void)".</span></li></ul>
              <ul><li class="MsoNormal"><span style="font-size: 10pt; font-family: Verdana;">stm3210e_eval.h: Add LM75 DMA defines.</span></li></ul>
              <ul><li class="MsoNormal"><span style="font-size: 10pt; font-family: Verdana;">stm3210e_eval_fsmc_nand.h: remove "NAND_CMD_AREA_TRUE1" define.</span></li></ul>
              <ul><li class="MsoNormal"><span style="font-size: 10pt; font-family: Verdana;">stm3210e_eval_fsmc_nand.c: Update FSMC timing in NAND_Init() function to be aligned with AN2784 application note.</span></li></ul>
              <ul><li class="MsoNormal"><span style="font-size: 10pt; font-family: Verdana;">stm3210e_eval_fsmc_nor.c</span></li><ul><li class="MsoNormal"><span style="font-size: 10pt; font-family: Verdana;">NOR</span><span style="font-size: 10pt; font-family: Verdana;">_Init()&nbsp;function: add FSMC_AsynchronousWait&nbsp;field&nbsp;to FSMC_NORSRAMInitStructure&nbsp;</span></li></ul></ul>
              <ul><li class="MsoNormal"><span style="font-size: 10pt; font-family: Verdana;">stm3210e_eval_fsmc_sram.c<br>
                </span></li></ul>
              <ul>
                <ul><li class="MsoNormal"><span style="font-size: 10pt; font-family: Verdana;">Update FSMC timing in SRAM_Init() function to be aligned with AN2784 application note.</span><br>
                  <span style="font-size: 10pt; font-family: Verdana;"></span></li></ul>
                <ul><li class="MsoNormal"><span style="font-size: 10pt; font-family: Verdana;">SRAM_Init()&nbsp;function: add FSMC_AsynchronousWait&nbsp;field&nbsp;to FSMC_NORSRAMInitStructure&nbsp;</span></li></ul>
              </ul>
              <ul><li class="MsoNormal"><span style="font-size: 10pt; font-family: Verdana;">stm3210e_eval_lcd.c</span></li><ul><li class="MsoNormal"><span style="font-size: 10pt; font-family: Verdana;">LCD_FSMCConfig() function: add FSMC_AsynchronousWait&nbsp;field&nbsp;to FSMC_NORSRAMInitStructure&nbsp;</span></li></ul></ul></ul>
            <ul style="margin-top: 0in;" type="disc">

            
            </ul>

            <h3 style="background: rgb(51, 102, 255) none repeat scroll 0% 50%; -moz-background-clip: initial; -moz-background-origin: initial; -moz-background-inline-policy: initial; margin-right: 558.05pt;"><span style="font-size: 10pt; font-family: Arial; color: white;">4.2.0
- 04/16/2010</span></h3>
            <ol style="margin-top: 0in;" start="1" type="1">
              <li class="MsoNormal" style=""><b><i><span style="font-size: 10pt; font-family: Verdana;">General</span></i></b><i><span style="font-size: 10pt; font-family: Verdana;"> </span></i><i><span style="font-size: 10pt;"><o:p></o:p></span></i></li>
            </ol>
            
            <ul style="margin-top: 0in;" type="disc">

              
              <li class="MsoNormal" style=""><span style="font-size: 10pt; font-family: Verdana;">I2C EEPROM driver
update to&nbsp;</span><span style="font-size: 10pt; font-family: Verdana;">use the DMA to
perform&nbsp;data transfer&nbsp;to/from EEPROM memory.</span><span style="font-size: 10pt; font-family: Verdana;"> </span><span style="font-size: 10pt;"><o:p></o:p></span></li>

            
            </ul>

            <ol style="margin-top: 0in;" start="2" type="1">
              <li class="MsoNormal" style=""><b><i><span style="font-size: 10pt; font-family: Verdana;">Utilities</span></i></b><b><i><span style="font-size: 10pt;"><o:p></o:p></span></i></b></li>
            </ol>
            <ul style="margin-top: 0in;" type="disc">
              <li class="MsoNormal" style=""><i><u><span style="font-size: 10pt; font-family: Verdana;">STM32_EVAL</span></u></i><u><span style="font-size: 10pt;"><o:p></o:p></span></u></li>
            </ul>
            <ul style="margin-top: 0in;" type="disc">
              <ul style="margin-top: 0in;" type="circle">
                <li class="MsoNormal"><span style="font-size: 10pt; font-family: Verdana;"><span style="font-style: italic;">stm32_eval_i2c_ee.c</span>:
updated to use the DMA to perform&nbsp;data transfer&nbsp;to/from
EEPROM memory. For more details, refer to the description provided
within this file.</span></li>
                <li class="MsoNormal"><span style="font-size: 10pt; font-family: Verdana;"><span style="font-style: italic;">stm3210c_eval.c</span>: add low level
functions to configure the DMA (needed for I2C EEPROM driver)<br>
                  </span></li>
                <li class="MsoNormal" style=""><span style="font-size: 10pt; font-family: Verdana;"><span style="font-style: italic;">stm3210c_eval_ioe.c</span>: add a delay
in&nbsp;IOE_TS_GetState() function to wait till the end of ADC
conversion</span></li>
                <li class="MsoNormal" style=""><span style="font-size: 10pt; font-family: Verdana;"><span style="font-style: italic;">stm3210e_eval_fsmc_nor.c</span>: add </span><span style="font-size: 10pt; font-family: Verdana;">PD6 pin </span><span style="font-size: 10pt; font-family: Verdana;">configuration&nbsp;in
NOR_Init() function</span></li>
                <li class="MsoNormal" style=""><span style="font-size: 10pt; font-family: Verdana;"><span style="font-style: italic;">stm3210b_eval_lcd.c</span>: remove the
second ";" from "static void PutPixel(int16_t x, int16_t y);;"&nbsp;</span><span style="font-size: 10pt; font-family: Verdana;"></span></li>
              </ul>
            </ul>
            <h3 style="background: rgb(51, 102, 255) none repeat scroll 0% 50%; -moz-background-clip: initial; -moz-background-origin: initial; -moz-background-inline-policy: initial; margin-right: 558.05pt;"><span style="font-size: 10pt; font-family: Arial; color: white;">4.1.0
- 03/01/2010</span></h3>
            <ol style="margin-top: 0in;" start="1" type="1">
              <li class="MsoNormal" style=""><b><i><span style="font-size: 10pt; font-family: Verdana;">General</span></i></b><i><span style="font-size: 10pt; font-family: Verdana;"> </span></i><i><span style="font-size: 10pt;"><o:p></o:p></span></i></li>
            </ol>
            <ul style="margin-top: 0in;" type="disc">
              <li class="MsoNormal" style=""><span style="font-size: 10pt; font-family: Verdana;">Add support
for&nbsp;<b>STM32 Low-density Value line (STM32F100x4/6) and
Medium-density Value line (STM32F100x8/B) devices</b>.</span></li>
              <li class="MsoNormal" style=""><span style="font-size: 10pt; font-family: Verdana;">Add support for the
STMicroelectronics STM32100B-EVAL evaluation board. </span><span style="font-size: 10pt;"><o:p></o:p></span></li>
            </ul>
            <ol style="margin-top: 0in;" start="2" type="1">
              <li class="MsoNormal" style=""><b><i><span style="font-size: 10pt; font-family: Verdana;">Utilities</span></i></b><b><i><span style="font-size: 10pt;"><o:p></o:p></span></i></b></li>
            </ol>
            <ul style="margin-top: 0in;" type="disc">
              <li class="MsoNormal" style=""><i><u><span style="font-size: 10pt; font-family: Verdana;">STM32_EVAL</span></u></i><u><span style="font-size: 10pt;"><o:p></o:p></span></u></li>
            </ul>
            <ul style="margin-top: 0in;" type="disc">
              <ul style="margin-top: 0in;" type="circle">
                <li class="MsoNormal" style=""><span style="font-size: 10pt; font-family: Verdana;"></span><span style="font-size: 10pt; font-family: Verdana;"></span><span style="font-size: 10pt; font-family: Verdana;">Add new directory
"Common" containing a common drivers for all STM32 evaluation boards:
fonts.h/.c, stm32_eval_i2c_ee.h/.c, </span><span style="font-size: 10pt; font-family: Verdana;">stm32_eval_spi_flash.h/.c,
                  </span><span style="font-size: 10pt; font-family: Verdana;">stm32_eval_i2c_tsensor.h/.c,
                  </span><span style="font-size: 10pt; font-family: Verdana;">stm32_eval_spi_sd.h/.c
and </span><span style="font-size: 10pt; font-family: Verdana;">stm32_eval_sdio_sd.h/.c</span></li>
                <li class="MsoNormal" style=""><span style="font-size: 10pt; font-family: Verdana;">Add new driver for the
STM32100B-EVAL managing Leds, push button and COM ports.</span></li>
                <li class="MsoNormal"><span style="font-size: 10pt; font-family: Verdana;">New HDMI CEC High level
driver.</span><br>
                </li>
                <li class="MsoNormal" style=""><span style="font-size: 10pt; font-family: Verdana;"><span style="font-style: italic;"></span>For all LCD drivers new fonts has
been added.</span></li>
                <li class="MsoNormal" style=""><span style="font-size: 10pt; font-family: Verdana;">New FSMC memories
drivers for STM3210E-EVAL board: stm3210e_eval_fsmc_sram.h/.c, </span><span style="font-size: 10pt; font-family: Verdana;">stm3210e_eval_fsmc_nor.h/.c
and </span><span style="font-size: 10pt; font-family: Verdana;">stm3210e_eval_fsmc_nand.h/.c.</span></li>
              </ul>
            </ul>
            <h2 style="background: rgb(51, 102, 255) none repeat scroll 0% 50%; -moz-background-clip: initial; -moz-background-origin: initial; -moz-background-inline-policy: initial;"><a name="License"></a><span style="font-size: 12pt; color: white;">License<o:p></o:p></span></h2>
            <p class="MsoNormal" style="margin: 4.5pt 0cm;"><span style="font-size: 10pt; font-family: Verdana; color: black;">The
enclosed firmware and all the related documentation are not covered by
a License Agreement, if you need such License you can contact your
local STMicroelectronics office.<u1:p></u1:p><o:p></o:p></span></p>

            <b><span style="font-size: 10pt; font-family: Verdana; color: black;">THE
PRESENT FIRMWARE WHICH IS FOR GUIDANCE ONLY AIMS AT PROVIDING CUSTOMERS
WITH CODING INFORMATION REGARDING THEIR PRODUCTS IN ORDER FOR THEM TO
SAVE TIME. AS A RESULT, STMICROELECTRONICS SHALL NOT BE HELD LIABLE FOR
ANY DIRECT, INDIRECT OR CONSEQUENTIAL DAMAGES WITH RESPECT TO ANY
CLAIMS ARISING FROM THE CONTENT OF SUCH FIRMWARE AND/OR THE USE MADE BY
CUSTOMERS OF THE CODING INFORMATION CONTAINED HEREIN IN CONNECTION WITH
THEIR PRODUCTS.</span></b>
            
            <div class="MsoNormal" style="text-align: center;" align="center"><span style="color: black;">
            <hr align="center" size="2" width="100%"></span></div>
            <p class="MsoNormal" style="margin: 4.5pt 0cm 4.5pt 18pt; text-align: center;" align="center"><span style="font-size: 10pt; font-family: Verdana; color: black;">For
complete documentation on </span><span style="font-size: 10pt; font-family: Verdana;">STMicroelectronics<span style="color: black;"> Microcontrollers visit </span><a target="_blank" href="http://www.st.com/internet/mcu/family/141.jsp"><u><span style="color: blue;">www.st.com</span></u></a></span><span style="font-size: 10pt; font-family: Verdana;"><u><span style="color: blue;"><a href="http://www.st.com/stm32l" target="_blank"></a></span></u></span><span style="color: black;"><o:p></o:p></span></p>
            </td>
          </tr>
        </tbody>
      </table>
      <p class="MsoNormal"><span style="font-size: 10pt;"><o:p></o:p></span></p>
      </td>
    </tr>
  </tbody>
</table>
</div>
<p class="MsoNormal"><o:p>&nbsp;</o:p></p>
</div>

</body></html>