<html>

<head>
<title>CMSIS Changes</title>
<meta http-equiv="Content-Type" content="text/html; charset=windows-1252">
<meta name="GENERATOR" content="Microsoft FrontPage 6.0">
<meta name="ProgId" content="FrontPage.Editor.Document">
<style>
<!--
/*-----------------------------------------------------------
Keil Software CHM Style Sheet
-----------------------------------------------------------*/
body         { color: #000000; background-color: #FFFFFF; font-size: 75%; font-family: 
               Verdana, Arial, 'Sans Serif' }
a:link       { color: #0000FF; text-decoration: underline }
a:visited    { color: #0000FF; text-decoration: underline }
a:active     { color: #FF0000; text-decoration: underline }
a:hover      { color: #FF0000; text-decoration: underline }
h1           { font-family: Verdana; font-size: 18pt; color: #000080; font-weight: bold; 
               text-align: Center; margin-right: 3 }
h2           { font-family: Verdana; font-size: 14pt; color: #000080; font-weight: bold; 
               background-color: #CCCCCC; margin-top: 24; margin-bottom: 3; 
               padding: 6 }
h3           { font-family: Verdana; font-size: 10pt; font-weight: bold; background-color: 
               #CCCCCC; margin-top: 24; margin-bottom: 3; padding: 6 }
pre          { font-family: Courier New; font-size: 10pt; background-color: #CCFFCC; 
               margin-left: 24; margin-right: 24 }
ul           { list-style-type: square; margin-top: 6pt; margin-bottom: 0 }
ol           { margin-top: 6pt; margin-bottom: 0 }
li           { clear: both; margin-bottom: 6pt }
table        { font-size: 100%; border-width: 0; padding: 0 }
th           { color: #FFFFFF; background-color: #000080; text-align: left; vertical-align: 
               bottom; padding-right: 6pt }
tr           { text-align: left; vertical-align: top }
td           { text-align: left; vertical-align: top; padding-right: 6pt }
.ToolT       { font-size: 8pt; color: #808080 }
.TinyT       { font-size: 8pt; text-align: Center }
code         { color: #000000; background-color: #E0E0E0; font-family: 'Courier New', Courier; 
               line-height: 120%; font-style: normal }
/*-----------------------------------------------------------
Notes
-----------------------------------------------------------*/
p.note       { font-weight: bold; clear: both; margin-bottom: 3pt; padding-top: 6pt }
/*-----------------------------------------------------------
Expanding/Contracting Divisions
-----------------------------------------------------------*/
#expand      { text-decoration: none; margin-bottom: 3pt }
img.expand   { border-style: none; border-width: medium }
div.expand   { display: none; margin-left: 9pt; margin-top: 0 }
/*-----------------------------------------------------------
Where List Tags
-----------------------------------------------------------*/
p.wh         { font-weight: bold; clear: both; margin-top: 6pt; margin-bottom: 3pt }
table.wh     { width: 100% }
td.whItem    { white-space: nowrap; font-style: italic; padding-right: 6pt; padding-bottom: 
               6pt }
td.whDesc    { padding-bottom: 6pt }
/*-----------------------------------------------------------
Keil Table Tags
-----------------------------------------------------------*/
table.kt     { border: 1pt solid #000000 }
th.kt        { white-space: nowrap; border-bottom: 1pt solid #000000; padding-left: 6pt; 
               padding-right: 6pt; padding-top: 4pt; padding-bottom: 4pt }
tr.kt        {  }
td.kt        { color: #000000; background-color: #E0E0E0; border-top: 1pt solid #A0A0A0; 
               padding-left: 6pt; padding-right: 6pt; padding-top: 2pt; 
               padding-bottom: 2pt }
/*-----------------------------------------------------------
-----------------------------------------------------------*/
-->

</style>
</head>

<body>

<h1>Changes to CMSIS version V1.20</h1>

<hr>

<h2>1. Removed CMSIS Middelware packages</h2>
<p>
  CMSIS Middleware is on hold from ARM side until a agreement between all CMSIS partners is found.
</p>

<h2>2. SystemFrequency renamed to SystemCoreClock</h2>
<p>
  The variable name <strong>SystemCoreClock</strong> is more precise than <strong>SystemFrequency</strong>
  because the variable holds the clock value at which the core is running.
</p>

<h2>3. Changed startup concept</h2>
<p>
  The old startup concept (calling SystemInit_ExtMemCtl from startup file and calling SystemInit 
  from main) has the weakness that it does not work for controllers which need a already 
  configuerd clock system to configure the external memory controller.
</p>

<h3>Changed startup concept</h3>
<ul>
  <li>
    SystemInit() is called from startup file before <strong>premain</strong>.
  </li>
  <li>
    <strong>SystemInit()</strong> configures the clock system and also configures
    an existing external memory controller.
  </li>
  <li>
    <strong>SystemInit()</strong> must not use global variables.
  </li>
  <li>
    <strong>SystemCoreClock</strong> is initialized with a correct predefined value.
  </li>
  <li>
    Additional function <strong>void SystemCoreClockUpdate (void)</strong> is provided.<br>
   <strong>SystemCoreClockUpdate()</strong> updates the variable <strong>SystemCoreClock</strong>
   and must be called whenever the core clock is changed.<br>
   <strong>SystemCoreClockUpdate()</strong> evaluates the clock register settings and calculates
   the current core clock.
  </li>
</ul>
      

<h2>4. Advanced Debug Functions</h2>
<p>
  ITM communication channel is only capable for OUT direction. To allow also communication for
  IN direction a simple concept is provided.
</p>
<ul>
  <li>
    Global variable <strong>volatile int ITM_RxBuffer</strong> used for IN data.
  </li>
  <li>
    Function <strong>int ITM_CheckChar (void)</strong> checks if a new character is available.
  </li>
  <li>
    Function <strong>int ITM_ReceiveChar (void)</strong> retrieves the new character.
  </li>
</ul>

<p>
  For detailed explanation see file <strong>CMSIS debug support.htm</strong>. 
</p>


<h2>5. Core Register Bit Definitions</h2>
<p>
  Files core_cm3.h and core_cm0.h contain now bit definitions for Core Registers. The name for the
  defines correspond with the Cortex-M Technical Reference Manual.  
</p>
<p>
  e.g. SysTick structure with bit definitions
</p>
<pre>
/** @addtogroup CMSIS_CM3_SysTick CMSIS CM3 SysTick
  memory mapped structure for SysTick
  @{
 */
typedef struct
{
  __IO uint32_t CTRL;                         /*!< Offset: 0x00  SysTick Control and Status Register */
  __IO uint32_t LOAD;                         /*!< Offset: 0x04  SysTick Reload Value Register       */
  __IO uint32_t VAL;                          /*!< Offset: 0x08  SysTick Current Value Register      */
  __I  uint32_t CALIB;                        /*!< Offset: 0x0C  SysTick Calibration Register        */
} SysTick_Type;

/* SysTick Control / Status Register Definitions */
#define SysTick_CTRL_COUNTFLAG_Pos         16                                             /*!< SysTick CTRL: COUNTFLAG Position */
#define SysTick_CTRL_COUNTFLAG_Msk         (1ul << SysTick_CTRL_COUNTFLAG_Pos)            /*!< SysTick CTRL: COUNTFLAG Mask */

#define SysTick_CTRL_CLKSOURCE_Pos          2                                             /*!< SysTick CTRL: CLKSOURCE Position */
#define SysTick_CTRL_CLKSOURCE_Msk         (1ul << SysTick_CTRL_CLKSOURCE_Pos)            /*!< SysTick CTRL: CLKSOURCE Mask */

#define SysTick_CTRL_TICKINT_Pos            1                                             /*!< SysTick CTRL: TICKINT Position */
#define SysTick_CTRL_TICKINT_Msk           (1ul << SysTick_CTRL_TICKINT_Pos)              /*!< SysTick CTRL: TICKINT Mask */

#define SysTick_CTRL_ENABLE_Pos             0                                             /*!< SysTick CTRL: ENABLE Position */
#define SysTick_CTRL_ENABLE_Msk            (1ul << SysTick_CTRL_ENABLE_Pos)               /*!< SysTick CTRL: ENABLE Mask */

/* SysTick Reload Register Definitions */
#define SysTick_LOAD_RELOAD_Pos             0                                             /*!< SysTick LOAD: RELOAD Position */
#define SysTick_LOAD_RELOAD_Msk            (0xFFFFFFul << SysTick_LOAD_RELOAD_Pos)        /*!< SysTick LOAD: RELOAD Mask */

/* SysTick Current Register Definitions */
#define SysTick_VAL_CURRENT_Pos             0                                             /*!< SysTick VAL: CURRENT Position */
#define SysTick_VAL_CURRENT_Msk            (0xFFFFFFul << SysTick_VAL_CURRENT_Pos)        /*!< SysTick VAL: CURRENT Mask */

/* SysTick Calibration Register Definitions */
#define SysTick_CALIB_NOREF_Pos            31                                             /*!< SysTick CALIB: NOREF Position */
#define SysTick_CALIB_NOREF_Msk            (1ul << SysTick_CALIB_NOREF_Pos)               /*!< SysTick CALIB: NOREF Mask */

#define SysTick_CALIB_SKEW_Pos             30                                             /*!< SysTick CALIB: SKEW Position */
#define SysTick_CALIB_SKEW_Msk             (1ul << SysTick_CALIB_SKEW_Pos)                /*!< SysTick CALIB: SKEW Mask */

#define SysTick_CALIB_TENMS_Pos             0                                             /*!< SysTick CALIB: TENMS Position */
#define SysTick_CALIB_TENMS_Msk            (0xFFFFFFul << SysTick_VAL_CURRENT_Pos)        /*!< SysTick CALIB: TENMS Mask */
/*@}*/ /* end of group CMSIS_CM3_SysTick */</pre>

<h2>7. DoxyGen Tags</h2>
<p>
  DoxyGen tags in files core_cm3.[c,h] and core_cm0.[c,h] are reworked to create proper documentation
  using DoxyGen.
</p>

<h2>8. Folder Structure</h2>
<p>
  The folder structure is changed to differentiate the single support packages.
</p>

  <ul>
    <li>CM0</li>
    <li>CM3
       <ul>
         <li>CoreSupport</li>
         <li>DeviceSupport</li>
           <ul>
             <li>Vendor 
               <ul>
                 <li>Device
                   <ul>
                      <li>Startup
                        <ul>
                          <li>Toolchain</li>
                          <li>Toolchain</li>
                          <li>...</li>
                        </ul>
                      </li>
                   </ul>
                 </li>
                 <li>Device</li>
                 <li>...</li>
               </ul>
             </li>
             <li>Vendor</li>
             <li>...</li>
           </ul>
         </li>
         <li>Example
           <ul>
             <li>Toolchain 
               <ul>
                 <li>Device</li>
                 <li>Device</li>
                 <li>...</li>
               </ul>
             </li>
             <li>Toolchain</li>
             <li>...</li>
           </ul>
         </li>
       </ul>
    </li>
     
    <li>Documentation</li>
  </ul>

<h2>9. Open Points</h2>
<p>
  Following points need to be clarified and solved:
</p>
<ul>
  <li>
    <p>
      Equivalent C and Assembler startup files.
    </p>
    <p>
      Is there a need for having C startup files although assembler startup files are
      very efficient and do not need to be changed?
    <p/>
  </li>
  <li>
    <p>
      Placing of HEAP in external RAM.
    </p>
    <p>
      It must be possible to place HEAP in external RAM if the device supports an 
      external memory controller.
    </p>
  </li>
  <li>
    <p>
      Placing of STACK /HEAP.
    </p>
    <p>
      STACK should always be placed at the end of internal RAM.
    </p>
    <p>
      If HEAP is placed in internal RAM than it should be placed after RW ZI section.
    </p>
  </li>
  <li>
    <p>
      Removing core_cm3.c and core_cm0.c.
    </p>
    <p>
      On a long term the functions in core_cm3.c and core_cm0.c must be replaced with 
      appropriate compiler intrinsics.
    </p>
  </li>
</ul>


<h2>10. Limitations</h2>
<p>
  The following limitations are not covered with the current CMSIS version:
</p>
<ul>
 <li>
  No <strong>C startup files</strong> for ARM toolchain are provided. 
 </li>
 <li>
  No <strong>C startup files</strong> for GNU toolchain are provided. 
 </li>
 <li>
  No <strong>C startup files</strong> for IAR toolchain are provided. 
 </li>
 <li>
  No <strong>Tasking</strong> projects are provided yet. 
 </li>
</ul>
