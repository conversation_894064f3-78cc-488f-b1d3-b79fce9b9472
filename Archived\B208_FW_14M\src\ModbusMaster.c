#include "ModbusMaster.h"

void ModDevComMon(ModStruct *ModX,ComModOpStruct *UXModOp,uint16_t DevNum)
{  //Device RS485 communication monitor routine
   if(((*ModX).Err & 0x8000)||((*ModX).Ret & 0x3000))
    {//current poll finished with or without error
     if((*ModX).Err & 0x8000)
      {//No response can be received (may be slaver off line)
        (*ModX).Err &=0x7fff ;
        (*UXModOp).sub_sta=0 ;
       } 

     (*UXModOp).sta++ ;//switch to do next device access

     if((*UXModOp).sta>DevNum)
        (*UXModOp).sta=0;  
     (*UXModOp).sub_sta=0 ;//switch to proces next device access request status
    }
    if((*ModX).Err & 0x4000)
      {
        (*ModX).Err &=0xbfff ; 
        ++(*UXModOp).ot_err ;//record the over time error
       }
    if((*ModX).Err & 0x0100)
      {
        (*ModX).Err &=0xfeff ; 
        ++(*UXModOp).crc_err ;//record the CRC error
       }
}

void ActiveAntenna(void)
{
 switch(U_Area.U1ModOp.sta)
 {
  case 0 : { //Access SLAVER--Output control data 
            if((U_Area.U1ModOp.sub_sta==0)&&(ModA.Sta==0))
             {
			    ModA.Add=HOST_ID ;//0x05 for monitor computer
			    ModA.Fun=ECS_MWRITE  ;//write slaver 
			    ModA.RegAdd=0x0000   ; //data write to Slaver(MODBUS address 40001)
			    ModA.DatNum=8     ; //output message length
			    ModA.SDp=(uint8_t *) &U_Area.PaDat_L ;//source data,received RF control data 
			    ModA.Sta=0x80 ;//switch to master poll status
			    ModA.Opc=0x80 ; 
			    ModA.Ret=0 ;//reset protocol return value to NULL status
			    ModA.Err=0 ;//clear previous error flag
			    U_Area.U1ModOp.Packages++ ; 
			    U_Area.U1ModOp.sub_sta=1 ;//switch to monitor status
              }
            else
             {
			   if((ModA.Err & 0x8000)||(ModA.Ret & 0x3000))
			    {//current poll finished with or without error
			     if(ModA.Ret & 0x2000)
			      {//response can be received (slaver on line)
			        OpCtrl |=CB_MDM_COM_OK ;//Set wired comunication OK flag
			       } 
			     else 
			      {//receive error or time out
			        OpCtrl &=~CB_MDM_COM_OK  ;//Clear response OK flag
			       }
			    }
                ModDevComMon(&ModA,&U_Area.U1ModOp,U1_DEV_NUM) ;
              }              
            break ;}
  case 1 : { //Access SLAVER--Readback status data 
            if((U_Area.U1ModOp.sub_sta==0)&&(ModA.Sta==0))
             {
			    ModA.Add=HOST_ID ;//0x05 for monitor computer
			    ModA.Fun=ECS_RDHR  ;//read slaver(control host) hold register
			    ModA.RegAdd=0x0010 ; //data Read from Slaver(MODBUS address 40017)
			    ModA.DatNum=20    ; //input message length
		        ModA.RSDp=(uint8_t *) &U_Area.DisplayDatBuf ;//Used to store,received data object
			    ModA.Sta=0x80 ;//switch to master poll status
			    ModA.Opc=0x80 ; 
			    ModA.Ret=0 ;//reset protocol return value to NULL status
			    ModA.Err=0 ;//clear previous error flag
			    U_Area.U1ModOp.Packages++ ; 
			    U_Area.U1ModOp.sub_sta=1 ;//switch to monitor status
              }
            else
             {
			   if((ModA.Err & 0x8000)||(ModA.Ret & 0x3000))
			    {//current poll finished with or without error
			     if(ModA.Ret & 0x2000)
			      {//response can be received (slaver on line)
			        OpCtrl |=CB_MDM_COM_OK ;//Set wired comunication OK flag
			       } 
			     else 
			      {//receive error or time out
			        OpCtrl &=~CB_MDM_COM_OK  ;//Clear response OK flag
			       }
			    }
                ModDevComMon(&ModA,&U_Area.U1ModOp,U1_DEV_NUM) ;
              }              
            break ;}
  default : {
             U_Area.U1ModOp.sta=0 ;
             }  
   }
}
//-----------
