/************F35Basic.h*******************/
#ifndef __F35_BASIC_H
#define __F35_BASIC_H
#include "stm32l1xx.h"
#include "ThisDevice.h"
#include "Timer.h"
#include "Display.h"
#include "RF_BasicFun.h"
#include "SysBasic.h"
#include "AD_Input.h"
#include "KeyAndMenu.h"
/* Extern variables functions----------------------------------------------------------*/

extern void  RealTimeFunction(void) ;

//-----------------------------------------------------------------------

void  Init108_F35Mode(void);//Init the 38108 board(main environment,like:STM32L15x )for Fyf35 mode

void  SysTickIsr_F35(void) ;

void  InitTIM9_F35(void ) ;

void  InitTIM11_F35(void ) ;

void  TIM9_ISR_F35(void ) ;

void  RfTickTimer_ISR_F35(void ) ;

void  TIM11_ISR_F35(void ) ;

#endif //__F35_BASIC_H
