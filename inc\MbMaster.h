#ifndef __MB_MASTER_H
#define __MB_MASTER_H

#include  "stm32l1xx.h"
#include  "ThisDevice.h"
#include  "Timer.h"
#include  "UartFun_A.h"
#include  "ModbusRTU_A.h"

#define  HOST_ID        5 //MODBUS monitor computer address
#define  U2_DEV_NUM     1 //total device access number in each access period of UART channel B

extern  tim_count U_Timer[U_TIM_NUM]  ;

extern  UARTStruct  UB  ;//

extern  ModStruct  ModB ;

extern  volatile   uint32_t   OpCtrl ;

extern  volatile   uint32_t   GFlag  ;

extern  SysAreaStruct    S_Area ;//Extern defined an object for system data --in file ".h"

extern  UserAreaStruct   U_Area ;//Extern defined an object for user data

void  ModDevComMon(ModStruct *ModX,ComModOpStruct *UXModOp,uint16_t DevNum) ;

void  ActiveAntenna(void) ;

#endif   //__MB_MASTER_H
//--------------------------------


