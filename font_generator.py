#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
汉字字库生成工具
生成12x12点阵字库数据，用于嵌入式LCD显示
"""

import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox, filedialog
from PIL import Image, ImageDraw, ImageFont
import numpy as np
import os

class FontGenerator:
    def __init__(self, root):
        self.root = root
        self.root.title("汉字字库生成工具 v1.0")
        self.root.geometry("800x700")
        
        # 字体设置
        self.font_size = 12
        self.font_path = None
        
        # 创建界面
        self.create_widgets()
        
        # 尝试加载默认字体
        self.load_default_font()
    
    def create_widgets(self):
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 字体选择区域
        font_frame = ttk.LabelFrame(main_frame, text="字体设置", padding="5")
        font_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Label(font_frame, text="字体文件:").grid(row=0, column=0, sticky=tk.W)
        self.font_path_var = tk.StringVar()
        ttk.Entry(font_frame, textvariable=self.font_path_var, width=50).grid(row=0, column=1, padx=(5, 5))
        ttk.Button(font_frame, text="选择字体", command=self.select_font).grid(row=0, column=2)
        
        # 输入区域
        input_frame = ttk.LabelFrame(main_frame, text="汉字输入", padding="5")
        input_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Label(input_frame, text="输入汉字:").grid(row=0, column=0, sticky=tk.W)
        self.char_entry = ttk.Entry(input_frame, width=20, font=("SimSun", 14))
        self.char_entry.grid(row=0, column=1, padx=(5, 5))
        self.char_entry.bind('<Return>', lambda e: self.generate_font())
        
        ttk.Button(input_frame, text="生成字库", command=self.generate_font).grid(row=0, column=2)
        ttk.Button(input_frame, text="批量生成", command=self.batch_generate).grid(row=0, column=3, padx=(5, 0))
        
        # 预览区域
        preview_frame = ttk.LabelFrame(main_frame, text="字符预览", padding="5")
        preview_frame.grid(row=2, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        
        # 创建Canvas用于显示点阵
        self.canvas = tk.Canvas(preview_frame, width=240, height=240, bg='white')
        self.canvas.grid(row=0, column=0, padx=(0, 10))
        
        # 输出区域
        output_frame = ttk.LabelFrame(main_frame, text="生成的代码", padding="5")
        output_frame.grid(row=2, column=1, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        
        self.output_text = scrolledtext.ScrolledText(output_frame, width=50, height=15, font=("Consolas", 9))
        self.output_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 控制按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=3, column=0, columnspan=2, pady=(10, 0))
        
        ttk.Button(button_frame, text="复制代码", command=self.copy_code).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="保存到文件", command=self.save_to_file).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="清空", command=self.clear_output).pack(side=tk.LEFT, padx=(0, 5))
        
        # 配置网格权重
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(2, weight=1)
        output_frame.columnconfigure(0, weight=1)
        output_frame.rowconfigure(0, weight=1)
    
    def load_default_font(self):
        """加载默认字体"""
        # 尝试常见的中文字体路径
        font_paths = [
            "C:/Windows/Fonts/simsun.ttc",  # 宋体
            "C:/Windows/Fonts/simhei.ttf",  # 黑体
            "C:/Windows/Fonts/msyh.ttc",    # 微软雅黑
            "/System/Library/Fonts/PingFang.ttc",  # macOS
            "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf",  # Linux
        ]
        
        for font_path in font_paths:
            if os.path.exists(font_path):
                self.font_path = font_path
                self.font_path_var.set(font_path)
                break
    
    def select_font(self):
        """选择字体文件"""
        file_path = filedialog.askopenfilename(
            title="选择字体文件",
            filetypes=[("字体文件", "*.ttf *.ttc *.otf"), ("所有文件", "*.*")]
        )
        if file_path:
            self.font_path = file_path
            self.font_path_var.set(file_path)
    
    def char_to_bitmap(self, char):
        """将字符转换为12x12位图"""
        if not self.font_path or not os.path.exists(self.font_path):
            messagebox.showerror("错误", "请先选择有效的字体文件")
            return None
        
        try:
            # 创建字体对象
            font = ImageFont.truetype(self.font_path, self.font_size)
            
            # 创建图像
            img = Image.new('L', (16, 16), 255)  # 稍大一点，便于居中
            draw = ImageDraw.Draw(img)
            
            # 获取字符边界框
            bbox = draw.textbbox((0, 0), char, font=font)
            text_width = bbox[2] - bbox[0]
            text_height = bbox[3] - bbox[1]
            
            # 计算居中位置
            x = (16 - text_width) // 2
            y = (16 - text_height) // 2 - bbox[1]
            
            # 绘制字符
            draw.text((x, y), char, font=font, fill=0)
            
            # 裁剪到12x12
            img = img.crop((2, 2, 14, 14))
            
            # 转换为二值图像
            img_array = np.array(img)
            bitmap = (img_array < 128).astype(np.uint8)
            
            return bitmap
            
        except Exception as e:
            messagebox.showerror("错误", f"生成字符位图失败: {str(e)}")
            return None
    
    def bitmap_to_bytes(self, bitmap):
        """将12x12位图转换为字节数组"""
        bytes_data = []

        # 每行12位，按照你的格式：高字节8位 + 低字节4位
        for row in bitmap:
            high_byte = 0
            low_byte = 0

            # 高字节：前8位
            for col in range(8):
                if row[col]:
                    high_byte |= (1 << (7 - col))

            # 低字节：后4位放在高4位
            for col in range(8, 12):
                if row[col]:
                    low_byte |= (1 << (7 - (col - 8)))

            bytes_data.extend([high_byte, low_byte])

        return bytes_data
    
    def generate_font(self):
        """生成单个字符的字库数据"""
        char = self.char_entry.get().strip()
        if not char:
            messagebox.showwarning("警告", "请输入要生成的汉字")
            return
        
        if len(char) > 1:
            char = char[0]  # 只取第一个字符
        
        # 生成位图
        bitmap = self.char_to_bitmap(char)
        if bitmap is None:
            return
        
        # 显示预览
        self.show_preview(bitmap, char)
        
        # 生成代码
        unicode_val = ord(char)
        bytes_data = self.bitmap_to_bytes(bitmap)
        
        code = self.generate_code(char, unicode_val, bytes_data)
        
        # 显示代码
        self.output_text.delete(1.0, tk.END)
        self.output_text.insert(tk.END, code)
    
    def show_preview(self, bitmap, char):
        """在Canvas上显示字符预览"""
        self.canvas.delete("all")
        
        # 绘制网格
        for i in range(13):
            self.canvas.create_line(i*20, 0, i*20, 240, fill="lightgray")
            self.canvas.create_line(0, i*20, 240, i*20, fill="lightgray")
        
        # 绘制像素
        for row in range(12):
            for col in range(12):
                if bitmap[row][col]:
                    x1, y1 = col*20, row*20
                    x2, y2 = x1+20, y1+20
                    self.canvas.create_rectangle(x1, y1, x2, y2, fill="black", outline="gray")
        
        # 显示字符信息
        self.canvas.create_text(120, 250, text=f"字符: {char} (Unicode: 0x{ord(char):04X})",
                               font=("SimSun", 12))

    def generate_code(self, char, unicode_val, bytes_data):
        """生成C语言代码"""
        # 生成CodePage条目
        codepage_entry = f"{{ 0x{unicode_val:04X}, 0x{unicode_val:04X}, 0x0XXX }}/* Segment XXX, 0x0001 Symbols */"

        # 生成字符数据
        char_data = f"// 字符: {char} (Unicode: 0x{unicode_val:04X})\n"
        char_data += "{{12, 12},{\n"

        for i in range(0, len(bytes_data), 2):
            high_byte = bytes_data[i]
            low_byte = bytes_data[i+1]

            # 生成注释（显示点阵图案）
            pattern = ""
            # 高字节8位
            for bit in range(8):
                if high_byte & (1 << (7-bit)):
                    pattern += "@"
                else:
                    pattern += "."
            # 低字节4位
            for bit in range(4):
                if low_byte & (1 << (7-bit)):
                    pattern += "@"
                else:
                    pattern += "."

            if i == len(bytes_data) - 2:  # 最后一行
                char_data += f"0x{high_byte:02X},    /*  {pattern}  */\n"
                char_data += f"\n"
                char_data += f"0x{low_byte:02X}     /*  {pattern}  */\n"
            else:
                char_data += f"0x{high_byte:02X},    /*  {pattern}  */\n"
                char_data += f"0x{low_byte:02X},    /*  {pattern}  */\n"
                if (i // 2 + 1) % 12 == 0 and i < len(bytes_data) - 2:
                    char_data += "\n"

        char_data += "}}"

        # 组合完整代码
        full_code = f"// CodePage条目 (需要插入到CodePage12.c中):\n"
        full_code += f"{codepage_entry}\n\n"
        full_code += f"// Unicode12数组条目 (需要插入到Unicode12.c中):\n"
        full_code += char_data

        return full_code

    def batch_generate(self):
        """批量生成多个字符"""
        batch_window = tk.Toplevel(self.root)
        batch_window.title("批量生成字库")
        batch_window.geometry("400x300")

        ttk.Label(batch_window, text="输入要生成的汉字（每行一个或连续输入）:").pack(pady=10)

        text_area = scrolledtext.ScrolledText(batch_window, width=40, height=10)
        text_area.pack(pady=10, padx=20, fill=tk.BOTH, expand=True)

        def do_batch_generate():
            chars = text_area.get(1.0, tk.END).strip()
            if not chars:
                messagebox.showwarning("警告", "请输入要生成的汉字")
                return

            # 去重并排序
            unique_chars = sorted(set(chars.replace('\n', '').replace(' ', '')))

            if not unique_chars:
                messagebox.showwarning("警告", "没有找到有效的汉字")
                return

            # 生成所有字符的代码
            all_code = f"// 批量生成的字库数据 - 共{len(unique_chars)}个字符\n"
            all_code += f"// 字符列表: {''.join(unique_chars)}\n\n"

            codepage_entries = []
            unicode_entries = []

            for i, char in enumerate(unique_chars):
                bitmap = self.char_to_bitmap(char)
                if bitmap is None:
                    continue

                unicode_val = ord(char)
                bytes_data = self.bitmap_to_bytes(bitmap)

                # CodePage条目
                codepage_entries.append(f"{{ 0x{unicode_val:04X}, 0x{unicode_val:04X}, 0x{i:04X} }}")

                # Unicode数据
                char_data = f"// {char} (0x{unicode_val:04X})\n"
                char_data += "{{12, 12},{\n"

                for j in range(0, len(bytes_data), 2):
                    high_byte = bytes_data[j]
                    low_byte = bytes_data[j+1]

                    pattern = ""
                    for bit in range(12):
                        if high_byte & (1 << (7-bit)):
                            pattern += "@"
                        elif bit < 4 and low_byte & (1 << (7-bit+4)):
                            pattern += "@"
                        else:
                            pattern += "."

                    if j == len(bytes_data) - 2:
                        char_data += f"0x{high_byte:02X},    /*  {pattern}  */\n"
                        char_data += f"\n0x{low_byte:02X}     /*  {pattern}  */\n"
                    else:
                        char_data += f"0x{high_byte:02X},    /*  {pattern}  */\n"
                        char_data += f"0x{low_byte:02X},    /*  {pattern}  */\n"
                        if (j // 2 + 1) % 12 == 0 and j < len(bytes_data) - 2:
                            char_data += "\n"

                char_data += "}}"
                unicode_entries.append(char_data)

            # 组合代码
            all_code += "// CodePage12.c 条目:\n"
            for entry in codepage_entries:
                all_code += f"{entry},/* Segment XXX, 0x0001 Symbols */\n"

            all_code += "\n// Unicode12.c 条目:\n"
            for i, entry in enumerate(unicode_entries):
                all_code += entry
                if i < len(unicode_entries) - 1:
                    all_code += ",\n"
                all_code += "\n"

            # 显示结果
            self.output_text.delete(1.0, tk.END)
            self.output_text.insert(tk.END, all_code)

            batch_window.destroy()
            messagebox.showinfo("完成", f"成功生成{len(unique_chars)}个字符的字库数据")

        ttk.Button(batch_window, text="生成", command=do_batch_generate).pack(pady=10)

    def copy_code(self):
        """复制代码到剪贴板"""
        code = self.output_text.get(1.0, tk.END).strip()
        if code:
            self.root.clipboard_clear()
            self.root.clipboard_append(code)
            messagebox.showinfo("成功", "代码已复制到剪贴板")
        else:
            messagebox.showwarning("警告", "没有可复制的代码")

    def save_to_file(self):
        """保存代码到文件"""
        code = self.output_text.get(1.0, tk.END).strip()
        if not code:
            messagebox.showwarning("警告", "没有可保存的代码")
            return

        file_path = filedialog.asksaveasfilename(
            title="保存字库代码",
            defaultextension=".c",
            filetypes=[("C文件", "*.c"), ("文本文件", "*.txt"), ("所有文件", "*.*")]
        )

        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(code)
                messagebox.showinfo("成功", f"代码已保存到: {file_path}")
            except Exception as e:
                messagebox.showerror("错误", f"保存失败: {str(e)}")

    def clear_output(self):
        """清空输出"""
        self.output_text.delete(1.0, tk.END)
        self.canvas.delete("all")

def main():
    root = tk.Tk()
    app = FontGenerator(root)
    root.mainloop()

if __name__ == "__main__":
    main()
