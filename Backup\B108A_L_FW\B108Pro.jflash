  AppVersion = 42014
[GENERAL]
  ConnectMode = 0
  CurrentFile = "F:\DQiu\NUD\B108_L_FW\RVMDK\Obj\B108_L_FW.hex"
  DataFileSAddr = 0x00000000
  GUIMode = 0
  HostName = ""
  TargetIF = 0
  USBPort = 0
  USBSerialNo = 0x00000000
[JTAG]
  IRLen = 0
  MultipleTargets = 0
  NumDevices = 0
  Speed0 = 5
  Speed1 = 4000
  TAP_Number = 0
  UseAdaptive0 = 0
  UseAdaptive1 = 0
  UseMaxSpeed0 = 0
  UseMaxSpeed1 = 1
[CPU]
  CheckCoreID = 1
  ChipName = "ST STM32L152VB"
  ClockSpeed = 0x00000000
  Core = 0x030000FF
  CoreID = 0x4BA00477
  DeviceFamily = 0x00000003
  EndianMode = 0
  HasInternalFlash = 1
  InitStep0_Action = "Reset"
  InitStep0_Comment = "Reset and halt target"
  InitStep0_Value0 = 0x00000000
  InitStep0_Value1 = 0x00000000
  NumInitSteps = 1
  RAMAddr = 0x20000000
  RAMSize = 0x00004000
  UseAutoSpeed = 0x00000000
  UseRAM = 1
[FLASH]
  NumBanks = 3
[FLASH0]
  aSectorSel[512] = 1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
  AutoDetect = 0
  BankName = ""
  BankSelMode = 1
  BaseAddr = 0x08000000
  CheckId = 0
  CustomRAMCode = ""
  DeviceName = "STM32L15xxB internal"
  EndBank = 511
  OrgNumBits = 32
  OrgNumChips = 1
  StartBank = 0
  UseCustomRAMCode = 0
[FLASH1]
  aSectorSel[0] = 
  AutoDetect = 1
  BankName = ""
  BankSelMode = 1
  BaseAddr = 0x08080000
  CheckId = 3
  CustomRAMCode = ""
  DeviceName = "Auto detected flash memory"
  EndBank = 8191
  OrgNumBits = 16
  OrgNumChips = 1
  StartBank = 0
  UseCustomRAMCode = 0
[FLASH2]
  aSectorSel[0] = 
  AutoDetect = 1
  BankName = ""
  BankSelMode = 1
  BaseAddr = 0x00000000
  CheckId = 3
  CustomRAMCode = ""
  DeviceName = "Auto detected flash memory"
  EndBank = 8191
  OrgNumBits = 16
  OrgNumChips = 1
  StartBank = 0
  UseCustomRAMCode = 0
[PRODUCTION]
  AutoPerformsErase = 1
  AutoPerformsHardLock = 0
  AutoPerformsHardUnlock = 0
  AutoPerformsProgram = 1
  AutoPerformsSecure = 1
  AutoPerformsSoftLock = 0
  AutoPerformsSoftUnlock = 1
  AutoPerformsStartApp = 1
  AutoPerformsUnsecure = 0
  AutoPerformsVerify = 1
  Delay = 0x000001F4
  EraseType = 2
  ProgramSN = 0
  SNAddr = 0x00000000
  SNInc = 0x00000001
  Threshold = 0x00000BB8
  VerifyType = 1
