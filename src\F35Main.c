#include "F35Main.h"
extern volatile   int16_t    daemon_lock ;

/*******************************************************************************
* Function Name  : __F35Main
* Description    : This is main function entry for work in  Fyf35 mode
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void  __F35Main(void) 
{
 __disable_irq();//Disable all interrupt
 //-------------------------------------------------------------------------------
 if((BootDats->f_key==F_KEY_FLAG)&&(BootDats->s_key==S_KEY_FLAG))
 { //system is hot boot
    BootDats->rst_count++ ;
    BootDats->bt_flag&=0x00ff ;
    BootDats->bt_flag|=HOTBOOT_FLAG ;//|=0x5a00
  }
 else //system is cool boot
 {
   BootDats->f_key=F_KEY_FLAG ;//set cool boot flag
   BootDats->s_key=S_KEY_FLAG ;
   BootDats->rst_count=0 ;//reset boot counter
   BootDats->bt_flag=0x0000 ;
  }
 //-----
 if(BootDats->switch_id==SWITCHED_ON_ID)
 {//Prevoius set flag is SWITCHED_ON_ID--this boot to enter shut off mode
   BootDats->switch_id = SWITCH_OFF_ID ;

  }
 else
 {//Prevoius set flag is SWITCH_OFF_ID or random data--this boot to run normal active mode
   BootDats->switch_id = SWITCHED_ON_ID;//Set switched ON flag,then enter switch ON operation
  }
 //-------------------------------------------------------------------------------
 PWR->CR|=0x00000100 ;//Set DBP--enable to access RTC backup register and RCC CSR 
 RCC->CSR=0x01000100 ;//LSE set to ON,Remove reset flag
 PWR->CR&=0xfffffeff ;//Reset DBP--disable to access RTC backup register and RCC CSR 
 //---------------------------------
 InitSysTimer()   ;//Initialize timers in kernel mode
 InitTimer() ;//Initialize  timer in user mode
 Init108_F35Mode() ;//Initialize memory,peripherals--GPIO ,set environment varivable 
 InitSTM32Lxx() ;//Initialize system clock variable and NVIC 
 __enable_irq();
 InitOnchipADC() ;
//------------------------
 InitRfEnvironment() ;//Initialize or setup the RF operation working environment
 if(Def_CRC_Seed==RIGHT_CRC_SEED)
 {
   Rx_021_Ch_p=(IC021RxSwitchReg *)&(APP_PC_p->Rx_Freq_Ch[APP_PA.RFIC_R_RxChNum]) ;
   Tx_021_Ch_p=(IC021TxSwitchReg *)&(APP_PC_p->Tx_Freq_Ch[APP_PA.RFIC_R_TxChNum]) ;
   Rx_01_Ch_p=(JF01SwitchReg *)&(APP_PD_p->Rx_Freq_Ch[APP_PA.RFIC_R_RxChNum]) ;
   Tx_01_Ch_p=(JF01SwitchReg *)&(APP_PD_p->Tx_Freq_Ch[APP_PA.RFIC_R_TxChNum]) ;
   U_Area.uc_str[5][0]=0xF353;//0x5200 ;//'R'
  }
 else
 {
   Rx_021_Ch_p=(IC021RxSwitchReg *)&(APP_PC_p->Rx_Freq_Ch[APP_PA.RFIC_L_RxChNum]) ;
   Tx_021_Ch_p=(IC021TxSwitchReg *)&(APP_PC_p->Tx_Freq_Ch[APP_PA.RFIC_L_TxChNum]) ;
   Rx_01_Ch_p=(JF01SwitchReg *)&(APP_PD_p->Rx_Freq_Ch[APP_PA.RFIC_L_RxChNum]) ;
   Tx_01_Ch_p=(JF01SwitchReg *)&(APP_PD_p->Tx_Freq_Ch[APP_PA.RFIC_L_TxChNum]) ;
   U_Area.uc_str[5][0]=0xE65D;//0x4C00 ;//'L"
  }
 //--
 InitSPI1_021() ;//Init SPI for IC021 access
//&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&
 RfDs.IC021Init.OpSta=NOT_INIT_STA ;
 RfDs.RF_State=RF_IDLE_STATUS ;
 RfDs.IC021Init.OpReq=SET_UP_RX+SET_CODE ;

 InitRfTickTimer();//Init a hardware RF tick timer(TIM10) 
 InitTIM11_F35();//Init hardware timer TIM11--Pd=0.9765mS
//------
 S_Area.TPool_p=0 ;//Reset Token pool pointer to First pool
 S_Area.RemainToken= RfTT_p->TokenPool[0].TokenNum ;
 S_Area.RFTickCount=RfTT_p->TokenPool[0].Interval-1;
 __disable_irq();
 IsrFlag |= IFG_KB_CHANGED ;
 __enable_irq();
 SYS_STA=LP_WAIT_STA ;
 do
 {//
   TimerProc() ;//user defined timer proccess
   RfDaemon_021() ;//daemon to service for IC021 initialization request
   ShrMachDatRxDaemon_021() ;
   LcdModuleInitDaemon();//process LCD module initialization request
   LcdPrintDaemon();//process LCD print(display) request
   ADC12Daemon() ;
   if(IsrFlag&IFG_GTOKEN_EN )
   {
     __disable_irq();
     IsrFlag&=~IFG_GTOKEN_EN ;
     __enable_irq();
     TxToken=1 ;
     S_Area.RemainToken-- ;
     if(S_Area.RemainToken<=0)
     {
       GFlag|=GF_SYS_NOTIFY_OK;
      }
    }
   switch(SYS_STA)
   {
     case LP_WAIT_STA:
	      if(TxToken>0)
	      {
	        SYS_STA=SETUP_TX_STA ;
	       }
	      break ;
     case SETUP_TX_STA :
          RfcTimer[RF_TIMER].csr.csword=0x0000 ;//disable RfcTimer[RF_TIMER]
          U_Area.uc_str[3][0]=0x1108 ;//"<->"TX/RX flag--Txing falg
          // U_Area.LcdCtrl.Set.Print_A3=1 ;
          NotifyTxSetup_021() ;
          SYS_STA=DO_TX_STA ;
     case DO_TX_STA :
          if(RfDs.RF_TxState==TX_FINISHED)
          {
            RfDs.RF_TxState=TX_IDLE ;
    	    __disable_irq();
	        TxToken-- ;//Use one token
	        __enable_irq();	
	        RfcTimer[RF_TIMER].pv=APP_PA.WaitPollTimeOut ;
	        RfcTimer[RF_TIMER].cv=0 ;
	        RfcTimer[RF_TIMER].csr.csword=0x4000 ;//enable RfcTimer[RF_TIMER]
	        SYS_STA=DO_RX_STA ;
	        U_Area.uc_str[3][0]=0x1008 ;//"<->"TX/RX flag--Rxing falg
	        // U_Area.LcdCtrl.Set.Print_A3=1 ;
	        SwitchTxToRx_021() ;
	       }
           break ;
     case SETUP_RX_STA :
          RfcTimer[RF_TIMER].pv = APP_PA.WaitPollTimeOut ;
          RfcTimer[RF_TIMER].cv = 0 ;
          RfcTimer[RF_TIMER].csr.csword=0x4000 ;//enable RfcTimer[RF_TIMER]
          U_Area.uc_str[3][0]=0x1008 ;//"<->"TX/RX flag--Rxing falg
          // U_Area.LcdCtrl.Set.Print_A3=1 ;
          SwitchTxToRx_021() ;
          SYS_STA=DO_RX_STA ;
     case DO_RX_STA :
		  if(RfDs.RF_RxState==RX_DP_FINISHED)
		  {//
		    if(TxToken)
		    {
		      RfDs.RF_RxState=RX_NO_INIT_IDLE;
		      SYS_STA=SETUP_TX_STA ;
		     }
		    else
		    {
		      SYS_STA=LP_WAIT_STA ;
		     }
		   }
		  else if((RfcTimer[RF_TIMER].csr.csbit.q==1)&&(RfDs.RF_RxState<RX_SEARCH_SOF2))
		  {//Wait poll time out
		    if(TxToken)
		    {
		     RfDs.RF_RxState=RX_NO_INIT_IDLE;
		     SYS_STA=SETUP_TX_STA ;
		     }
		    else
		    {
		     SYS_STA=LP_WAIT_STA ;
		     }
		   }
		  if((TxToken!=0)&&(SyncOpOkFlag)) //GPIOC->ODR's bit2 use as Rx/Tx clock Synchronizing operation OK flag
		  {//In sync time window
		    RfDs.RF_RxState=RX_NO_INIT_IDLE;
		    SYS_STA=SETUP_TX_STA ;
		   }
          break ;
     default:	;
     }
  }while(((GFlag&GF_SYS_NOTIFY_OK)==0)||(TxToken==1));
//--

//&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&
 RfDs.IC021Init.OpSta=NOT_INIT_STA ;
 RfDs.IC021Init.OpReq=0 ;
 RfDs.RF_State=RF_IDLE_STATUS ;
 RfDs.RF_RxState=RX_NO_INIT_IDLE;
 ResetRfTxEnPin ;//TxEn (021 "CE" pin to low
 ResetRfRxEnPin ;//Switch off Rx LNA
 if(BootDats->switch_id==SWITCHED_ON_ID)
 {
   #ifdef DEV_DEBUG
     USART_Config() ;//Use RS485 for debug
   #else
     SetSysClockMSI_4MHz() ;//Use 4.194MHz SYS_Clock to to redunce current consume
     SystemCoreClockUpdate() ;
     APB1_Clock = SystemCoreClock ;
     APB2_Clock = SystemCoreClock ;
   #endif
   SYS_STA=SETUP_LP_STA ;//Prepare enter low power wait status
  }
 else
 {
   DisableRfTickTimer() ;//Disable RF operation tick timer(99.945mS)
   ResetRfTxEnPin ;//TxEn (021 "CE" pin to low
   ResetRfRxEnPin ;//Switch off Rx LNA
   SYS_STA=POWER_DOWN_STA;
   __disable_irq() ;
   EnStandby_LCD_021() ;//Put the LCD to Sleep mode and MCU enter standby status-no inerrupt allowed
  }

 Timer10[OPC_TIMER].pv=25 ;
 Timer10[OPC_TIMER].cv=0 ;
 Timer10[OPC_TIMER].csr.csword=0x4000 ; //start Timer10[OPC_TIMER]
  
 //---------------Check to decide to enter RF test mode or Not
 InitTIM9_F35(); //Init hardware timer TIM9--Pd=1.000S
//----
 GetKeySta() ;//Repeat twice
 if(U_Area.KeyCodeRaw.cw==(SW13+SW14+SW15))
 {//SW11,SW13,SW14,SW15 be pressed at same time(simultanously)
   RfTestEntry_021() ;//To enter RF test mode ,Never return
  }
 else if(U_Area.KeyCodeRaw.cw)
 {
   GFlag|=GF_KB_ERROR ;//Set keyboard error flag
   S_Area.DeadKeyMask.cw=~U_Area.KeyCodeRaw.cw ;
   S_Area.DeadKeyMask.cw|=APP_PB.EStopCode ;//disable mask for emergnece stop key
   S_Area.KeyMask.cw&=S_Area.DeadKeyMask.cw ;
   U_Area.PSta.KeyBoardError=0x0001 ;//Send key board error flag to machine receiver
  }
 //LcdWelcomPrint() ;//Print welcom screen
 DisableTIM11() ;//Disable 1024Hz system operation tick interrupt
 S_Area.TPool_p=DEFAULT_POOL ;//Set default Token pool
 S_Area.RemainToken=RfTT_p->TokenPool[S_Area.TPool_p].TokenNum ;
 TxToken=0 ;
 Led_BL_off ;//turn off LCD back light
 U_Area.LcdCtrl.Set.Req=0 ;
 U_Area.LcdCtrl.Set.Print_A5=1 ;//Print current control channel flag
 GFlag |=GF_SYS_INIT_OK ;
 U_Area.PSta.InquiryPackage =0x0001 ;//Initially set InquiryPackage=1
 while (1)
 {//Average tun around periodical time about 120uS or more @8MHz CPU
   if(get_lock())
   {
     RealTimeFunction() ;//Main program function ,include RT task
     release_lock() ;
    }
   NON_RT_PROCESS();//Non realtime process
 }
}

//-------------------------------------------------
void  RealTimeFunction(void)
{//Run time about[50uS--100uS]@8MHz CPU
 GetKeySta();//Check key board periodically
 TxTokenGenProc_Bi() ;
//----	
 switch(SYS_STA)
 {
  //--------------------------------------------------------------------------
  case POWER_DOWN_STA :
       DisableRfTickTimer() ;//Disable RF operation tick timer(99.945mS)
       ResetRfTxEnPin ;//TxEn (021 "CE" pin to low
       ResetRfRxEnPin ;//Switch off Rx LNA
       RfDs.IC021Init.OpSta=NOT_INIT_STA ;
       RfDs.IC021Init.OpReq=0 ;
       RfDs.RF_State=RF_IDLE_STATUS ;
	   __disable_irq() ;
	   EnStandby_LCD_021() ;//Put the LCD to Sleep mode and MCU enter standby status-no inerrupt allowed
       break ;
  case LP_WAIT_STA :
       if(TxToken)
       {
         //ExitLowSpeedRunMode() ;
         EnableTIM11() ;//Enable Timer11 for 1mS interrupt
         SYS_STA=SETUP_TX_STA ;
        }
       if(IsrFlag&IFG_TO_BE_SHUTOFF)
       {
         SCB->AIRCR = 0x05fa0001;//do system & core reset,this will lead to device shut off
        }
       break ;
  case LCD_REFRESH_STA ://In this status, DCO set to on before
       if(TxToken)
       {//
         SYS_STA=SETUP_TX_STA ;
         break ;
        }
       if(U_Area.LcdCtrl.Set.Req==0)
       {//No display area need to be refresh,enter Low power wait status
         SYS_STA=SETUP_LP_STA ;
        }
       break ;
  case SETUP_RX_STA :
       RfcTimer[RF_TIMER].pv = APP_PA.WaitPollTimeOut ;
       RfcTimer[RF_TIMER].cv = 0 ;
       RfcTimer[RF_TIMER].csr.csword=0x4000 ;//enable RfcTimer[RF_TIMER]
       EnableTIM11() ;//Enable Timer11 for 1mS interrupt
       U_Area.uc_str[3][0]=0x1008 ;//"<->"TX/RX flag--Rxing falg
//       U_Area.LcdCtrl.Set.Print_A3=1 ;
       SwitchTxToRx_021() ;
       SYS_STA=DO_RX_STA ;
  case DO_RX_STA :
       ADC12Daemon() ;//process ADC operation request
       ShrMachDatRxDaemon_021() ;
       RfDaemon_021() ;
       WaitPollSta() ;
       break ;

  case SETUP_TX_STA :
       RfcTimer[RF_TIMER].csr.csword=0x0000 ;//disable RfcTimer[RF_TIMER]
       EnableTIM11() ;//Enable Timer11 for 1mS interrupt
       U_Area.uc_str[3][0]=0x1108 ;//"<->"TX/RX flag--Txing falg
//       U_Area.LcdCtrl.Set.Print_A3=1 ;
       FastSetupTx_021() ;
       SYS_STA=DO_TX_STA ;
  case DO_TX_STA :
       ADC12Daemon() ;//process ADC operation request
       ShrMachDatRxDaemon_021() ;
       RfDaemon_021() ;
       SyncTxSta() ;
       break ;
  case SETUP_LP_STA:
       RfcTimer[RF_TIMER].cv=0 ;
       RfcTimer[RF_TIMER].csr.csword=0x0000 ;//disable RfcTimer[RF_TIMER]
       ResetRfTxEnPin ;//TxEn (021 "CE" pin to low
       ResetRfRxEnPin ;//Switch off Rx LNA
       RfDs.IC021Init.OpSta=NOT_INIT_STA ;
       RfDs.IC021Init.OpReq=0 ;
       RfDs.RF_State=RF_IDLE_STATUS ;
       U_Area.uc_str[3][0]=0x0808 ;//TX/RX status flag to"  " Blank
//       U_Area.LcdCtrl.Set.Print_A3=1 ;
#ifndef NO_STANDBY
       DisableTIM11() ;//Enable Timer11 for 1mS interrupt
       //SetupLowSpeedRunMode() ;
#endif
       SYS_STA=LP_WAIT_STA ;
       break ;
  default : SYS_STA=SETUP_LP_STA ;
  }
}

//---------------------------------------------
void  NON_RT_PROCESS(void)
{//runtime at least [51uS--150uS--xxxuS ]@8MHzCPU
  uint32_t temp ;
  TimerProc() ;//user defined timer proccess
  if(SyncOpOkFlag) //GPIOC->ODR's bit2 use as Rx/Tx clock Synchronizing operation OK flag
    GFlag|=GF_SYNC_OK ;
  else
    GFlag&=~GF_SYNC_OK ;
  temp=GFlag_Pre^GFlag ;
  if(temp&GF_SYNC_OK)
  {
    if(GFlag&GF_SYNC_OK)
    { //Set in sync status code 
      U_Area.uc_str[1][0]=0x1208 ;
      U_Area.uc_str[1][1]=0x1308 ;
     }
    else
    { //Set out of sync status code
      U_Area.uc_str[1][0]=0x1408 ;
      U_Area.uc_str[1][1]=0x1508 ;
     }
    U_Area.LcdCtrl.Set.Print_A1=1 ;//Draw sync status indicator
   }
  if(temp&GF_CMD_CONFIRMED)
  {
     if(GFlag&GF_CMD_CONFIRMED)
     {  
       U_Area.uc_str[2][0]=0x0608 ;//Command confirmed flag
       U_Area.uc_str[2][1]=0x0708 ;
      }
     else
     {
       U_Area.uc_str[2][0]=0x0808 ;//Clear Command confirmed flag
       U_Area.uc_str[2][1]=0x0808 ;
      }
     U_Area.LcdCtrl.Set.Print_A2=1 ;//Print Command recive confirm Ok flag
   }
  GFlag_Pre=GFlag ;
  
  if(GFlag&GF_BATV_REFRESH)
  {
    GFlag&=~GF_BATV_REFRESH;
    if(U_Area.BatVoltage<APP_PA.BatLowLimit0)
    {//less than 3.025V
      U_Area.LcdCtrl.Set.Print_A0=1 ;
      U_Area.uc_str[0][0]=0x0408 ;
      U_Area.uc_str[0][1]=0x0508 ;
      GFlag|=GF_BAT_ALARM ;//Set battery low alarm flag
     }
    else if(U_Area.BatVoltage<APP_PA.BatLowLimit)
    {//less than 3.18V
      U_Area.LcdCtrl.Set.Print_A0=1 ;
      U_Area.uc_str[0][0]=0x0008 ;
      U_Area.uc_str[0][1]=0x0108 ;
      GFlag|=GF_BAT_ALARM ;//Set battery low alarm flag
     }
    else if(GFlag&GF_BAT_ALARM)
    {//Battery in normal status
      GFlag&=~GF_BAT_ALARM ;//Clear battery low alarm flag
      U_Area.LcdCtrl.Set.Print_A0=1 ;//Draw normal indicator
      U_Area.uc_str[0][0]=0x0808 ;
      U_Area.uc_str[0][1]=0x0808 ;
     }
   }
  if(GFlag&GF_RSSI_REFRESH)
  {//RF singnal indicator
    GFlag &=~(GF_RSSI_REFRESH+GF_MAREA1_V_U_OK+GF_MAREA2_V_U_OK) ;
    U_Area.PSta.RFLinkAlarm=0x0000 ;//Clear Panel revieve RFLink Alarm
    U_Area.uc_str[4][0]=0x0a08 ;
    if(RfDs.RSSI>=-78)
    {
      RssiDisSta=0x0b08 ;
     }
    else if(RfDs.RSSI>=-88)
    {
      RssiDisSta=0x0c08 ;
     }
    else if(RfDs.RSSI>=-98)
    {
      RssiDisSta=0x0d08 ;
     }
    else if(RfDs.RSSI>=-108)
    {
      RssiDisSta=0x0e08 ;
      U_Area.PSta.RFLinkAlarm=0x0001 ;//Set Panel revieve RFLinkAlarm
     }
    else
    {
      RssiDisSta=0x0f08 ;
     }
    if(U_Area.uc_str[4][1]!=RssiDisSta)
      U_Area.LcdCtrl.Set.Print_A4=1 ;
    U_Area.uc_str[4][1]=RssiDisSta ;
   }
  (*GetKeyID_fp)()  ;//Get Key ID for display
  LcdModuleInitDaemon();//process LCD module initialization request
  LcdPrintDaemon();//process LCD print(display) request
  if(GFlag&GF_ALARM_DIS_ON)
  {
    if(Timer10[OPC_TIMER].csr.csbit.q==1)
    {
      Timer10[OPC_TIMER].csr.csword=0x4000 ;
      Timer10[OPC_TIMER].cv=0 ;
	  //Flash the LCD backlight LED
	  if(Led_BL_sta)
		 Led_BL_off ;//Turn off the LCD backlight LED
	  else
		 Led_BL_on ;//Turn on the LCD backlight LED
     }
   }
  else if(S_Area.LCD_BL_Timer<=0)
  {
    Led_BL_off ;//Turn off the LCD backlight LED
   }
//--------
#ifdef DEV_DEBUG
  ModProcA(&UB,&ModB) ;//Modbus daemon  using channel B	--debug
  UXTxP() ;//--debug
#endif
//--------
}

//---------------------------------------------------
void  WaitPollSta(void)
{
  if(RssiCheckFlag)//RSSI check flag has been set
  {
    //----------------------------------------------------------------
    ClearRssiCheckFlag ;//GPIOC->ODR's bit1 as IC021 RSSI check request(or JF01 RSSI data avaliable) flag
    RfDs.RSSI_D=Read_RSSI_021() ;
    RSSI_Sum+=RfDs.RSSI_D ;
    RSSI_Sum-=RfDs.RSSI_BUF[RSSI_BufIndex_L] ;
    RfDs.RSSI_BUF[RSSI_BufIndex_L]=RfDs.RSSI_D ;
    RSSI_BufIndex_L++ ;
    RSSI_BufIndex_L&=0x0007 ;
    RfDs.RSSI=(RSSI_Sum>>3)+APP_PA.RSSIOffset ;
    GFlag|=GF_RSSI_REFRESH ;
    //----------------------------------------------------------------
   }
  if(RfDs.RF_RxState==RX_DP_FINISHED)
  {//
    if(TxToken)
    {
      RfDs.RF_RxState=RX_NO_INIT_IDLE;
      SYS_STA=SETUP_TX_STA ;
     }
    else
    {
      SYS_STA=SETUP_LP_STA ;
     }
    if((OpCtrl&CB_RX_CRC_OK)!=0)
    {
      OpCtrl&=~CB_RX_CRC_OK ;
	  OpCtrl |=CB_TUN_A8_PRINT_REQ ;//Request to print A8 text alarm area in tunneller mode
      if(U_Area.AlarmStatus==1)//Radio signal too weak alarm information string)
      {
        if(GFlag&GF_BAT_ALARM)
          U_Area.AlarmStatus=0 ;//Print battery low alarm
        else
          U_Area.AlarmStatus=0xffff ; //Clear alarm area 
       }
      S_Area.RFConfirmErrorCounter=0 ;//Reset confirm error counter
	 }
   }
  else if((RfcTimer[RF_TIMER].csr.csbit.q==1)&&(RfDs.RF_RxState<RX_SEARCH_SOF2))
  {//Wait poll time out
    if(TxToken)
    {
      RfDs.RF_RxState=RX_NO_INIT_IDLE;
      SYS_STA=SETUP_TX_STA ;
     }
    else
    {
      SYS_STA=SETUP_LP_STA ;
     }
    S_Area.RFConfirmErrorCounter++ ;
    if(S_Area.RFConfirmErrorCounter>=8)
    {
      S_Area.RFConfirmErrorCounter=1 ;
      U_Area.AlarmStatus=1 ;//Radio signal too weak alarm information string
	  OpCtrl |=CB_TUN_A8_PRINT_REQ ;//Request to print A8 text alarm area in tunneller mode
     }
   }
  if((TxToken!=0)&&(SyncOpOkFlag)) //GPIOC->ODR's bit2 use as Rx/Tx clock Synchronizing operation OK flag
  {//In sync time window
    RfDs.RF_RxState=RX_NO_INIT_IDLE;
    SYS_STA=SETUP_TX_STA ;
   }
}

void  SyncTxSta(void)
{
  if(RfDs.RF_TxState==TX_FINISHED)
  {
    RfDs.RF_TxState=TX_IDLE ;
	__disable_irq();
    TxToken-- ;//Use one token
    __enable_irq();	
    if(SysSetting.UnidirOpCtrl)//Request to work in unidirection mode
    {
      SYS_STA=SETUP_LP_STA ;
     }
    else//Normal Bidirection mode
    {
      RfcTimer[RF_TIMER].pv=APP_PA.WaitPollTimeOut ;
      RfcTimer[RF_TIMER].cv=0 ;
      RfcTimer[RF_TIMER].csr.csword=0x4000 ;//enable RfcTimer[RF_TIMER]
      SYS_STA=DO_RX_STA ;
      U_Area.uc_str[3][0]=0x1008 ;//"<->"TX/RX flag--Rxing falg
//      U_Area.LcdCtrl.Set.Print_A3=1 ;
      SwitchTxToRx_021() ;
     }
   }
}



//------------------------------------

void Proc10mS_F35(void) //Timed process at frequency=100Hz
{
  ;
}

void Proc100mS_F35(void)
{
  RfDs.DClkCntChk=RfDs.DClkCnt ;
  RfDs.DClkCnt=0;  
  if((RfDs.DClkCntChk<420)||(RfDs.DClkCntChk>520))
    RfDs.DClkCntChk=0 ;//Set to invalid
  HotKeyCheck() ;
}

void Proc1S_F35(void) //Timed process at frequency=1Hz
{
  ;
}

void Proc1H_F35(void) //Timed process at interval 1 hour
{
  ;
}


