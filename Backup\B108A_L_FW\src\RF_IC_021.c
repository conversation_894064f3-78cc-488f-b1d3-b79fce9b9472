#include "SysBasic.h"
#include "Timer.h"
#include "RF_BasicFun.h"
//----------------------------

extern  tim_count  RfcTimer[RFC_TIMER_NUM]  ;//system ISR process

extern  tim_count  RfcTimer10[RFC_TIMER10_NUM]  ;//user level timer(1 tick=10ms),RFC_TIMER10_NUM==2

extern  uint8_t   ShiftReg ;
extern  uint8_t   BitCounter;
extern  uint8_t   PreambleCount;
extern  uint8_t   PreambleError;
extern  uint8_t   ByteCounter;
extern  volatile  uint16_t  Def_CRC_Seed ;//default CRC seed for left or right side operation
extern  uint8_t   gain_correction[]; //For AGC and LAN 021
extern  LongWord  IC021Reg ;
extern  uint16_t  UserSetting1,UserSetting2,Display_p1,Display_p2 ;
extern  __IO uint16_t  PRandom ;
//--------
uint32_t  TestReg=0x0000010f ;

//----------------------------------------------------------------------------------------
void  ShrMachDatRxDaemon_021(void)
{
 uint16_t tmp , *tp1 ;
 uint16_t *tp2 ;
 if(RfcTimer10[AIR_TIMER].csr.csbit.q==1)
  {
    RfcTimer10[AIR_TIMER].csr.csword=0x0000 ;//Disable the monitor timer
    U_Area.ShrMachDat.WcrStaW=0x0000 ;//reset the Communication status
    S_Area.AlarmChange=0xfc00 ;//Guide to refresh all status indicator
    RfDs.RSSI=-128 ;
    GFlag |=GF_RSSI_REFRESH ;
    OpCtrl &=(~CB_RX_OK) ;
   }
 if(RfDs.RBuf_Sta&0x02)//there have valid data received in the buffer 0
 { 
   if(CRC16WithSeed(MACH_CRC_SEED,(uint16_t const *)&U_Area.ShrMachDat_RB0,sizeof(SHR_TX_DAT)/2))
   {
     RfDs.RF_RxErr++ ;//CRC error
    }
   else
   {
     tp1=(uint16_t *)&U_Area.ShrMachDat_RB0 ;
     tp2=(uint16_t *)&U_Area.ShrMachDat ;
     for(tmp=0;tmp<sizeof(SHR_TX_DAT)/2;tmp++)
       *(tp2++)=*(tp1++);//Copy received data to user data area
     if(U_Area.ShrMachDat.DatValid)
     {
       U_Area.DisplayDatBuf[U_Area.ShrMachDat.DatGID_1]=U_Area.ShrMachDat.Dat_1 ;
       U_Area.DisplayDatBuf[U_Area.ShrMachDat.DatGID_2]=U_Area.ShrMachDat.Dat_2 ;
      }
     S_Area.AlarmChange|=(S_Area.PreAlarmSta^U_Area.ShrMachDat.MachSta) ;
     S_Area.PreAlarmSta=U_Area.ShrMachDat.MachSta ;
     //-----------
     if((U_Area.ShrMachDat.LeftLinkOk!=0)&&(Def_CRC_Seed==LEFT_CRC_SEED))
     {
       GFlag|=GF_CMD_CONFIRMED ;
      }
     else if((U_Area.ShrMachDat.RightLinkOk!=0)&&(Def_CRC_Seed==RIGHT_CRC_SEED))
     {
       GFlag|=GF_CMD_CONFIRMED ;
      }
     else
     {
       GFlag&=~GF_CMD_CONFIRMED ;
      }
     RfcTimer10[AIR_TIMER].csr.csword=0x4000 ;//reset the monitor timer
     RfcTimer10[AIR_TIMER].cv=0 ;
     OpCtrl |= CB_RX_OK ;
    }
   __disable_irq() ;
   RfDs.RBuf_Sta&=0xfc ;//Reset data ready flag
   __enable_irq() ;
   GFlag&=~(GF_MAREA1_V_U_OK+GF_MAREA2_V_U_OK) ;//Guide to redraw monitor data and unit
 }
 if(RfDs.RBuf_Sta&=0x08)//there have valid data received in the buffer 1
 {
   if(CRC16WithSeed(MACH_CRC_SEED,(uint16_t const *)&U_Area.ShrMachDat_RB1,sizeof(SHR_TX_DAT)/2))
   {
     RfDs.RF_RxErr++ ;//CRC error
    }
   else
   {
     tp1=(uint16_t *)&U_Area.ShrMachDat_RB1 ;
     tp2=(uint16_t *)&U_Area.ShrMachDat ;
     for(tmp=0;tmp<sizeof(SHR_TX_DAT)/2;tmp++)
       *(tp2++)=*(tp1++);//Copy received data to user data area
     if(U_Area.ShrMachDat.DatValid)
     {
       U_Area.DisplayDatBuf[U_Area.ShrMachDat.DatGID_1]=U_Area.ShrMachDat.Dat_1 ;
       U_Area.DisplayDatBuf[U_Area.ShrMachDat.DatGID_2]=U_Area.ShrMachDat.Dat_2 ;
      }
     S_Area.AlarmChange|=(S_Area.PreAlarmSta^U_Area.ShrMachDat.MachSta) ;
     S_Area.PreAlarmSta=U_Area.ShrMachDat.MachSta ;
     //------------------
     if((U_Area.ShrMachDat.LeftLinkOk!=0)&&(Def_CRC_Seed==LEFT_CRC_SEED))
     {
       GFlag|=GF_CMD_CONFIRMED ;
      }
     else if((U_Area.ShrMachDat.RightLinkOk!=0)&&(Def_CRC_Seed==RIGHT_CRC_SEED))
     {
       GFlag|=GF_CMD_CONFIRMED ;
      }
     else
     {
       GFlag&=~GF_CMD_CONFIRMED ;
      }
     RfcTimer10[AIR_TIMER].csr.csword=0x4000 ;//reset the monitor timer
     RfcTimer10[AIR_TIMER].cv=0 ;
     OpCtrl |= CB_RX_OK ;
   }
   __disable_irq() ;
   RfDs.RBuf_Sta&=0xf3 ;//Reset data ready flag
   __enable_irq() ;
   GFlag&=~(GF_MAREA1_V_U_OK+GF_MAREA2_V_U_OK) ;//Guide to redraw monitor data and unit
 }
}
//--------------------------------------------------------------
//----------------------------------------------------------------------------------------
void  TunMachDatRxDaemon_021(void)
{
 uint16_t tmp , *tp1 ;
 uint16_t *tp2 ;
 if(RfcTimer10[AIR_TIMER].csr.csbit.q==1)
  {
    RfcTimer10[AIR_TIMER].csr.csword=0x0000 ;//Disable the monitor timer
    U_Area.TunMachDat.WcrStaW=0x0000 ;//reset the Communication status
    U_Area.TunMachDat.FB_CMD_D1=0x0000 ;
    U_Area.TunMachDat.FB_CMD_D2=0x0000 ;
    U_Area.TunMachDat.MachSpeed=0 ;
    RfDs.RSSI=-128 ;
    GFlag |=GF_RSSI_REFRESH ;
    OpCtrl &=(~CB_RX_OK) ;
   }
 if(RfDs.RBuf_Sta&0x02)//there have valid data received in the buffer 0
 { 
   if(CRC16WithSeed(MACH_CRC_SEED,(uint16_t const *)&U_Area.TunMachDat_RB0,sizeof(TUN_TX_DAT)/2))
   {
     RfDs.RF_RxErr++ ;//CRC error
    }
   else
   {
     tp1=(uint16_t *)&U_Area.TunMachDat_RB0 ;
     tp2=(uint16_t *)&U_Area.TunMachDat ;
     for(tmp=0;tmp<sizeof(TUN_TX_DAT)/2;tmp++)
       *(tp2++)=*(tp1++);//Copy received data to user data area
     RfcTimer10[AIR_TIMER].csr.csword=0x4000 ;//reset the monitor timer
     RfcTimer10[AIR_TIMER].cv=0 ;
     OpCtrl |= (CB_RX_OK+CB_RX_CRC_OK) ;
    }
   __disable_irq() ;
   RfDs.RBuf_Sta&=0xfc ;//Reset data ready flag
   __enable_irq() ;
 }
 if(RfDs.RBuf_Sta&=0x08)//there have valid data received in the buffer 1
 {
   if(CRC16WithSeed(MACH_CRC_SEED,(uint16_t const *)&U_Area.TunMachDat_RB1,sizeof(TUN_TX_DAT)/2))
   {
     RfDs.RF_RxErr++ ;//CRC error
    }
   else
   {
     tp1=(uint16_t *)&U_Area.TunMachDat_RB1 ;
     tp2=(uint16_t *)&U_Area.TunMachDat ;
     for(tmp=0;tmp<sizeof(TUN_TX_DAT)/2;tmp++)
       *(tp2++)=*(tp1++);//Copy received data to user data area
     RfcTimer10[AIR_TIMER].csr.csword=0x4000 ;//reset the monitor timer
     RfcTimer10[AIR_TIMER].cv=0 ;
     OpCtrl |= (CB_RX_OK+CB_RX_CRC_OK) ;
   }
   __disable_irq() ;
   RfDs.RBuf_Sta&=0xf3 ;//Reset data ready flag
   __enable_irq() ;
 }
}

//--------------------------------------------------------------
#ifdef USE_OLD_FRAME
void  ShrPanelDatTxLoad_021(void)
{
 uint16_t dlimit ;
 U_Area.PSet.SettingW&=0xc020 ;//clear bits other than reserved bits and UniDirCtrl flag
 dlimit=Display_p1 ;
 if(dlimit>=MAX_MACH_D_ITEM)
   dlimit=0 ;
 U_Area.PSet.SettingW|=(dlimit&0x001f)  ;
 dlimit=Display_p2 ;
 if(dlimit>=MAX_MACH_D_ITEM*256)
   dlimit=0 ;
 U_Area.PSet.SettingW|=(dlimit&0x1f00)+0xc000  ;//Fixed flag for old frame is 0xc000
 
 U_Area.PSta.StaW&=0x9dff ;//Preserve those will not affect flag bits(include:InGroupId,GroupId)
 U_Area.PanelOldDat_TB0.Payload.PanStaW&=0x0ff0 ;//Preserve those will not affect flag bits(include:InGroupId,GroupId)
 if(IsrFlag&IFG_TO_BE_SHUTOFF)
 {
   U_Area.PSta.ToBeShutoff=0x0001 ;
   U_Area.PanelOldDat_TB0.Payload.ToBeSleep=1 ;
  }
 if(OpCtrl&CB_RX_OK)
 {
   U_Area.PSta.RxDatOk=0x0001 ;//RxDatOk
   U_Area.PanelOldDat_TB0.Payload.RxDatOk=1 ; 
  }
 if(GFlag&GF_BAT_ALARM)
 {
   U_Area.PSta.BatteryAlarm=0x0001 ;//BatteryAlarm
   U_Area.PanelOldDat_TB0.Payload.BatteryAlarm=1 ; 
  }
 if(U_Area.PSta.InquiryPackage!=0)
   U_Area.PanelOldDat_TB0.Payload.InquiryPackage=1 ;
 if(U_Area.PSta.KeyBoardError!=0)
   U_Area.PanelOldDat_TB0.Payload.KeyBoardError=1 ;	 
 //---------
 U_Area.PanelOldDat_TB0.Payload.PanSettingW  =U_Area.PSet.SettingW;//
 //U_Area.PanelOldDat_TB0.Payload.PanStaW  =U_Area.PSta.StaW;//
 U_Area.PanelOldDat_TB0.Payload.KeyCode  =U_Area.KeyCode.cw ;// 
 U_Area.PanelOldDat_TB0.CRC_app  =CRC16WithSeed(Def_CRC_Seed,(const uint16_t *)&U_Area.PanelOldDat_TB0,(sizeof(PANEL_OLD_TX_DAT)/2-1));//Tx data load fixex to 2 words
}
//--------------------------------------------------------------
void  TunPanelDatTxLoad_021(void)
{
 uint16_t dlimit ;
 U_Area.PSet.SettingW&=0x2020 ;//clear bits other than reserved bits and UniDirCtrl flag
 dlimit=Display_p1 ;
 if(dlimit>=MAX_MACH_D_ITEM)
   dlimit=0 ;
 U_Area.PSet.SettingW|=(dlimit&0x001f)  ;
 dlimit=Display_p2 ;
 if(dlimit>=MAX_MACH_D_ITEM*256)
   dlimit=0 ;
 U_Area.PSet.SettingW|=(dlimit&0x1f00)+0xc000  ;
 dlimit=(SysTick->VAL<<6)&0x00c0 ;
 U_Area.PSet.SettingW|=dlimit ;
 
 U_Area.PSta.StaW&=0x9dff ;//Preserve those will not affect flag bits(include:InGroupId,GroupId)
 U_Area.PanelOldDat_TB0.Payload.PanStaW&=0x5ff4 ;
 if(U_Area.PSta.InquiryPackage!=0)
   U_Area.PanelOldDat_TB0.Payload.InquiryPackage=1 ;
 if(U_Area.PSta.KeyBoardError!=0)
   U_Area.PanelOldDat_TB0.Payload.KeyBoardError=0x0001 ;	 
 if(IsrFlag&IFG_TO_BE_SHUTOFF)
 {	 
   U_Area.PanelOldDat_TB0.Payload.ToBeSleep=0x0001 ;
   U_Area.PSta.ToBeShutoff=0x0001 ;
  }
 if(OpCtrl&CB_RX_OK)
 {
   U_Area.PanelOldDat_TB0.Payload.RxDatOk=0x0001 ;	 
   U_Area.PSta.RxDatOk=0x0001 ;//RxDatOk
  }
 if(GFlag&GF_BAT_ALARM)
 { 
   U_Area.PanelOldDat_TB0.Payload.BatteryAlarm=0x0001 ;	 
   U_Area.PSta.BatteryAlarm=0x0001 ;//BatteryAlarm
  }
 //---------
 U_Area.PanelOldDat_TB0.Payload.PanSettingW  =U_Area.PSet.SettingW;//
 //U_Area.PanelOldDat_TB0.Payload.PanStaW =0x0000;//
 U_Area.PanelOldDat_TB0.Payload.KeyCode  =U_Area.KeyCode.cw ;// 
 U_Area.PanelOldDat_TB0.CRC_app  =CRC16WithSeed(Def_CRC_Seed,(const uint16_t *)&U_Area.PanelOldDat_TB0,(sizeof(PANEL_TX_DAT)/2-1));//Tx data load fixex to 2 words
}
#else
void  ShrPanelDatTxLoad_021(void)
{
 uint16_t dlimit ;
 U_Area.PSet.SettingW&=0xc020 ;//clear bits other than reserved bits and UniDirCtrl flag
 dlimit=Display_p1 ;
 if(dlimit>=MAX_MACH_D_ITEM)
   dlimit=0 ;
 U_Area.PSet.SettingW|=(dlimit&0x001f)  ;
 dlimit=Display_p2 ;
 if(dlimit>=MAX_MACH_D_ITEM*256)
   dlimit=0 ;
 U_Area.PSet.SettingW|=(dlimit&0x1f00)+FIXED_FLAG+PRandom  ;
 U_Area.PSta.StaW&=0x9dff ;//Preserve those will not affect flag bits(include:InGroupId,GroupId)
 if(IsrFlag&IFG_TO_BE_SHUTOFF)
   U_Area.PSta.ToBeShutoff=0x0001 ;
 if(OpCtrl&CB_RX_OK)
   U_Area.PSta.RxDatOk=0x0001 ;//RxDatOk
 if(GFlag&GF_BAT_ALARM)
   U_Area.PSta.BatteryAlarm=0x0001 ;//BatteryAlarm
 //---------
 U_Area.PanelDat_TB0.Payload.PSet.SettingW  =U_Area.PSet.SettingW;//
 U_Area.PanelDat_TB0.Payload.PSta.StaW  =U_Area.PSta.StaW;//
 U_Area.PanelDat_TB0.Payload.KeyCode  =U_Area.KeyCode.cw ;// 
 U_Area.PanelDat_TB0.CRC_app  =CRC16WithSeed(Def_CRC_Seed,(const uint16_t *)&U_Area.PanelDat_TB0,(sizeof(PANEL_TX_DAT)/2-1));//Tx data load fixex to 2 words
}
//--------------------------------------------------------------
void  TunPanelDatTxLoad_021(void)
{
 uint16_t dlimit ;
 U_Area.PSet.SettingW&=0x2020 ;//clear bits other than reserved bits and UniDirCtrl flag
 dlimit=Display_p1 ;
 if(dlimit>=MAX_MACH_D_ITEM)
   dlimit=0 ;
 U_Area.PSet.SettingW|=(dlimit&0x001f)  ;
 dlimit=Display_p2 ;
 if(dlimit>=MAX_MACH_D_ITEM*256)
   dlimit=0 ;
 U_Area.PSet.SettingW|=(dlimit&0x1f00)+0xc000  ;
 dlimit=(SysTick->VAL<<6)&0x00c0 ;
 U_Area.PSet.SettingW|=dlimit ;
 
 U_Area.PSta.StaW&=0x9dff ;//Preserve those will not affect flag bits(include:InGroupId,GroupId)
 U_Area.PanelOldDat_TB0.Payload.PanStaW&=0x5ff4 ;
 if(U_Area.PSta.InquiryPackage!=0)
   U_Area.PanelOldDat_TB0.Payload.InquiryPackage=1 ;
 if(U_Area.PSta.KeyBoardError!=0)
   U_Area.PanelOldDat_TB0.Payload.KeyBoardError=0x0001 ;	 
 if(IsrFlag&IFG_TO_BE_SHUTOFF)
 {	 
   U_Area.PanelOldDat_TB0.Payload.ToBeSleep=0x0001 ;
   U_Area.PSta.ToBeShutoff=0x0001 ;
  }
 if(OpCtrl&CB_RX_OK)
 {
   U_Area.PanelOldDat_TB0.Payload.RxDatOk=0x0001 ;	 
   U_Area.PSta.RxDatOk=0x0001 ;//RxDatOk
  }
 if(GFlag&GF_BAT_ALARM)
 { 
   U_Area.PanelOldDat_TB0.Payload.BatteryAlarm=0x0001 ;	 
   U_Area.PSta.BatteryAlarm=0x0001 ;//BatteryAlarm
  }
 //---------
 U_Area.PanelOldDat_TB0.Payload.PanSettingW  =U_Area.PSet.SettingW;//
 //U_Area.PanelOldDat_TB0.Payload.PanStaW =0x0000;//
 U_Area.PanelOldDat_TB0.Payload.KeyCode  =U_Area.KeyCode.cw ;// 
 U_Area.PanelOldDat_TB0.CRC_app  =CRC16WithSeed(Def_CRC_Seed,(const uint16_t *)&U_Area.PanelOldDat_TB0,(sizeof(PANEL_TX_DAT)/2-1));//Tx data load fixex to 2 words
}
#endif

//--------------------------------------------------
void SwitchTxToRx_021(void)
{
  RfDs.RF_State=RF_IDLE_STATUS ;
  //write R5 to start IF filter cal
  WriteReg_021(&APP_PC_p->IC021Def.R5_IF_Filter);
  WaitSomeTime(TIME_200uS_V) ;//
  //write R11, configure sync word detect
  WriteReg_021(&APP_PC_p->IC021Def.R11_SYNC);//sync word = ;
  //write R12, start sync word detect
  WriteReg_021(&APP_PC_p->IC021Def.R12_SWD);//for sync word detect;
  //write R0, switch TX to RX and change LO
  WriteReg_021(&(Rx_021_Ch_p->R0_N_Rx));
  WaitSomeTime(TIME_40uS_V) ;//
  //write R4, turn on demodulation
  WriteReg_021(&APP_PC_p->IC021Def.R4_Demod);
  if (APP_PC_p->IC021Def.R10_AFC&0x00000010)//Required to use AFC
  {
   //write R10, turn AFC on
   WriteReg_021(&APP_PC_p->IC021Def.R10_AFC);
   }
  RfDs.IC021Init.OpSta=IN_RX_STA  ;//Set Rx setup ok status
  RfDs.IC021Init.OpReq=0 ;
  EnterRxMode_021() ;
}

void FastSetupTx_021(void)
{
  RfDs.RF_State=RF_IDLE_STATUS ;
  SetRfTxEnPin ;//TxEn (021 "CE" pin to high
  WaitSomeTime(TIME_1_5mS_V) ;//
 //write R1, turn on VCO
  WriteReg_021(&(Tx_021_Ch_p->R1_VCO_OSC_Tx));//
  WaitSomeTime(TIME_800uS_V) ;//
 //write R8, configure power down test register
  WriteReg_021(&APP_PC_p->IC021Def.R8_PDT);//Set internal Rx/Tx switch ;
  WriteReg_021(&APP_PC_p->IC021Def.R3_TxRx_Clk);//write R3, turn on TX/RX clocks
  WriteReg_021(&(Tx_021_Ch_p->R0_N_Tx));//write R0, turn on PLL
//  WaitSomeTime(TIME_20uS_V) ;//
  (*PanelDatTxLoad_fp)() ;//Need to wait 40uS
  WriteReg_021(&APP_PC_p->IC021Def.R2_Tx_Mod);//write R2, turn on PA
  RfDs.IC021Init.OpSta=IN_TX_STA  ;//Set tx setup ok status
  RfDs.IC021Init.OpReq=0 ;
  EnterTxMode_021() ;
}

void NotifyTxSetup_021(void)
{
  RfDs.RF_State=RF_IDLE_STATUS ;
  SetRfTxEnPin ;//TxEn (021 "CE" pin to high
  WaitSomeTime(TIME_1_5mS_V) ;//
  //write R1, turn on VCO
  WriteReg_021(&(Tx_021_Ch_p->R1_VCO_OSC_Tx));//
  WaitSomeTime(TIME_800uS_V) ;//
  //write R8, configure power down test register
  WriteReg_021(&APP_PC_p->IC021Def.R8_PDT);//Set internal Rx/Tx switch ;
  WriteReg_021(&APP_PC_p->IC021Def.R3_TxRx_Clk);//write R3, turn on TX/RX clocks
  WriteReg_021(&(Tx_021_Ch_p->R0_N_Tx));//write R0, turn on PLL
  //Prepare the PowerOn or Shutoff Notify Tx data frame	--expect to consume 40uS
  U_Area.PanelDat_TB0.Payload.PSet.SettingW=0x22c1;//(Enc_2,Enc_1,FixedBit)=1,DatGID_2=2,DatGID_1=1
  U_Area.PanelDat_TB0.Payload.PSta.StaW=0x0000 ;
  U_Area.PanelDat_TB0.Payload.PSta.StaW|=(APP_PA.GroupId_InGroupId&0x00ff) ;
  U_Area.PanelDat_TB0.Payload.PSta.InquiryPackage=1 ;
  if(BootDats->switch_id==SWITCH_OFF_ID)
  {
    U_Area.PanelDat_TB0.Payload.PSta.ToBeShutoff=1 ;
   }
  U_Area.PanelDat_TB0.Payload.KeyCode  =0x0000 ;//
  U_Area.PanelDat_TB0.CRC_app  = CRC16WithSeed(Def_CRC_Seed,(const uint16_t *)&U_Area.PanelDat_TB0,(sizeof(PANEL_TX_DAT)/2-1));//Tx data load fixex to 2 words
  //--   
  WriteReg_021(&APP_PC_p->IC021Def.R2_Tx_Mod);//write R2, turn on PA
  RfDs.IC021Init.OpSta=IN_TX_STA  ;//Set tx setup ok status
  RfDs.IC021Init.OpReq=0 ;
  EnterTxMode_021() ;
}

void SwitchRxToTx_021(void)
{
  RfDs.RF_State=RF_IDLE_STATUS ;
  //write R0, switch TX to RX and change LO
  WriteReg_021(&(Tx_021_Ch_p->R0_N_Tx));
//  WaitSomeTime(TIME_20uS_V) ;//
  (*PanelDatTxLoad_fp)() ;//Need to wait 40uS
  WriteReg_021(&APP_PC_p->IC021Def.R2_Tx_Mod);//write R2, turn on PA
  RfDs.IC021Init.OpSta=IN_TX_STA  ;//Set tx setup ok status
  RfDs.IC021Init.OpReq=0 ;
  EnterTxMode_021();
}

//----------------------------------------------------------------------------------------
void ShrPanelDatRx_021(void)
{
 uint16_t tmp,*tp1 ;
 uint16_t *tp2 ;
//-------------------
 if(RfcTimer10[RF_OP_TIMER_L].csr.csbit.q==1)
  {
    U_Area.PaDat_L.PSet.SettingW&=0xdf3f ;//reset the main panel setting status
    U_Area.PaDat_L.PSta.StaW &=0xf1ff ;//reset the panel status
    U_Area.PaDat_L.KeyCode =0 ;//reset the key operation status,except for F2nd,F3rd key
    OpCtrl &=~CB_RX_OK_L ;//Turn off the left side linkage OK LED
   }
 if(RfcTimer10[AIR_TIMER_L].csr.csbit.q==1)
  {
    OpCtrl &=~CB_T_ONLINE_L ;//Turn off the left side linkage OK flag
    U_Area.RSSI_L =-128 ;//RfDs.RSSI_Raw ;
   }
//----
 if(RfcTimer10[RF_OP_TIMER_R].csr.csbit.q==1)
  {
    U_Area.PaDat_R.PSet.SettingW&=0xdf3f;//reset main the panel setting status
    U_Area.PaDat_R.PSta.StaW &=0xf1ff ;//reset the panel status
    U_Area.PaDat_R.KeyCode =0 ;//reset the key operation status
    OpCtrl &=~CB_RX_OK_R ;//Turn off the right side linkage OK LED
   }
 if(RfcTimer10[AIR_TIMER_R].csr.csbit.q==1)
  {
    OpCtrl &=~CB_T_ONLINE_R ;//Turn off the right side linkage OK flag
    U_Area.RSSI_R=-128 ;//RfDs.RSSI_Raw ;
   }
//-------------------
 if(RfDs.RBuf_Sta&0x02)//begine to check and process RF receiver buffer 0
  {
    if(CRC16WithSeed(LEFT_CRC_SEED,(const uint16_t *) &U_Area.PanelDat_RB0,sizeof(PANEL_TX_DAT)/2)==0)
    {
      tp1=(uint16_t *) &U_Area.PanelDat_RB0 ;
      tp2=(uint16_t *) &U_Area.PaDat_L ;
      for(tmp=0 ;tmp<sizeof(PANEL_TX_DAT)/2;tmp++)
      {
       *tp2++=*tp1++ ;//copy valid data to output area
       }
      if((U_Area.PaDat_L.PSta.InquiryPackage!=0)||(U_Area.PaDat_L.PSet.FixedBit==0))
        U_Area.PaDat_L.KeyCode=0x0000 ;//reset the operation status
      // set air linkage valid flag
      RfDs.RF_ACK_Count_L=RF_ACK_NUM ;
      U_Area.DatReq_L=U_Area.PaDat_L.PSet.SettingW ;//Display setting
      OpCtrl |=CB_RX_OK_L+CB_T_ONLINE_L ;//Turn on the left side RF linkage OK flag
	  RfDs.RSSI_D_L=RfDs.Rssi_DB_021;
      __disable_irq();//Disable IRQ
	  IsrFlag|=IFG_RSSI_D_OK_L;
      __enable_irq();//Enable IRQ
      U_Area.RSSI_L=RfDs.RSSI_L ;
      if(RfDs.RSSI_L>=RSSI_LIMIT)
        GFlag |=GF_RSSI_GOOD_L ;
      else
        GFlag &=~GF_RSSI_GOOD_L ;
      //
	  if(U_Area.PaDat_L.PSta.InquiryPackage==0)
	  {
        RfcTimer10[RF_OP_TIMER_L].cv=0 ;
        RfcTimer10[RF_OP_TIMER_L].csr.csword=0x4000 ; //start RfcTimer10[RF_OP_TIMER_L]
	   }
      RfcTimer10[AIR_TIMER_L].csr.csword=0x4000 ;//reset the monitor timer
      RfcTimer10[AIR_TIMER_L].cv=0 ;
     }
    else if(CRC16WithSeed(RIGHT_CRC_SEED,(const uint16_t *) &U_Area.PanelDat_RB0,sizeof(PANEL_TX_DAT)/2)==0)
    {
      tp1=(uint16_t *) &U_Area.PanelDat_RB0 ;
      tp2=(uint16_t *) &U_Area.PaDat_R ;
      for(tmp=0 ;tmp<sizeof(PANEL_TX_DAT)/2;tmp++)
      {
       *tp2++=*tp1++ ;//copy valid data to output area
       }
      if((U_Area.PaDat_R.PSta.InquiryPackage!=0)||(U_Area.PaDat_R.PSet.FixedBit==0))
        U_Area.PaDat_R.KeyCode=0x0000 ;//reset the operation status
      // set air linkage valid flag
      RfDs.RF_ACK_Count_R=RF_ACK_NUM ;
      U_Area.DatReq_R=U_Area.PaDat_R.PSet.SettingW ;//Display setting
      OpCtrl |=CB_RX_OK_R+CB_T_ONLINE_R ;//Turn on the right side linkage OK flag
	  RfDs.RSSI_D_R=RfDs.Rssi_DB_021;
      __disable_irq();//Disable IRQ
	  IsrFlag|=IFG_RSSI_D_OK_R;
      __enable_irq();//Enable IRQ
      U_Area.RSSI_R=RfDs.RSSI_R ;
      if(RfDs.RSSI_R>=RSSI_LIMIT)
        GFlag |=GF_RSSI_GOOD_R ;
      else
        GFlag &=~GF_RSSI_GOOD_R ;
      //
	  if(U_Area.PaDat_R.PSta.InquiryPackage==0)
	  {
        RfcTimer10[RF_OP_TIMER_R].cv=0 ;
        RfcTimer10[RF_OP_TIMER_R].csr.csword=0x4000 ; //start RfcTimer10[RF_OP_TIMER_R]
	   }
      RfcTimer10[AIR_TIMER_R].csr.csword=0x4000 ;//reset the monitor timer
      RfcTimer10[AIR_TIMER_R].cv=0 ;
     }
    else
       RfDs.RF_RxErr++ ;//record the CRC error
    __disable_irq();//Disable IRQ
    RfDs.RBuf_Sta &=0xfd ;//reset the buffer to empty status
    __enable_irq();//Enable IRQ
  }
 if(RfDs.RBuf_Sta&0x08)//begine to check and process RF receiver buffer 1
  {
    if(CRC16WithSeed(LEFT_CRC_SEED,(const uint16_t *) &U_Area.PanelDat_RB1,sizeof(PANEL_TX_DAT)/2)==0)
    {
      tp1=(uint16_t *) &U_Area.PanelDat_RB1 ;
      tp2=(uint16_t *) &U_Area.PaDat_L ;
      for(tmp=0 ;tmp<sizeof(PANEL_TX_DAT)/2;tmp++)
      {
       *tp2++=*tp1++ ;//copy valid data to output area
       }
      if((U_Area.PaDat_L.PSta.InquiryPackage!=0)||(U_Area.PaDat_L.PSet.FixedBit==0))
        U_Area.PaDat_L.KeyCode=0x0000 ;//reset the operation status
      // set air linkage valid flag
      RfDs.RF_ACK_Count_L=RF_ACK_NUM ;
      U_Area.DatReq_L=U_Area.PaDat_L.PSet.SettingW ;//Display setting
      OpCtrl |=CB_RX_OK_L+CB_T_ONLINE_L ;//Turn on the left side RF linkage OK flag
	  RfDs.RSSI_D_L=RfDs.Rssi_DB_021;
      __disable_irq();//Disable IRQ
	  IsrFlag|=IFG_RSSI_D_OK_L;
      __enable_irq();//Enable IRQ
      U_Area.RSSI_L=RfDs.RSSI_L ;
      if(RfDs.RSSI_L>=RSSI_LIMIT)
        GFlag |=GF_RSSI_GOOD_L ;
      else
        GFlag &=~GF_RSSI_GOOD_L ;
      //
	  if(U_Area.PaDat_L.PSta.InquiryPackage==0)
	  {
        RfcTimer10[RF_OP_TIMER_L].cv=0 ;
        RfcTimer10[RF_OP_TIMER_L].csr.csword=0x4000 ; //start RfcTimer10[RF_OP_TIMER_L]
	   }
      RfcTimer10[AIR_TIMER_L].csr.csword=0x4000 ;//reset the monitor timer
      RfcTimer10[AIR_TIMER_L].cv=0 ;
     }
    else if(CRC16WithSeed(RIGHT_CRC_SEED,(const uint16_t *) &U_Area.PanelDat_RB1,sizeof(PANEL_TX_DAT)/2)==0)
    {
      tp1=(uint16_t *) &U_Area.PanelDat_RB1 ;
      tp2=(uint16_t *) &U_Area.PaDat_R ;
      for(tmp=0 ;tmp<sizeof(PANEL_TX_DAT)/2;tmp++)
      {
       *tp2++=*tp1++ ;//copy valid data to output area
       }
      if((U_Area.PaDat_R.PSta.InquiryPackage!=0)||(U_Area.PaDat_R.PSet.FixedBit==0))
        U_Area.PaDat_R.KeyCode=0x0000 ;//reset the operation status
      // set air linkage valid flag
      RfDs.RF_ACK_Count_R=RF_ACK_NUM ;
      U_Area.DatReq_R=U_Area.PaDat_R.PSet.SettingW ;//Display setting
      OpCtrl |=CB_RX_OK_R+CB_T_ONLINE_R ;//Turn on the right side linkage OK flag
	  RfDs.RSSI_D_R=RfDs.Rssi_DB_021;
      __disable_irq();//Disable IRQ
	  IsrFlag|=IFG_RSSI_D_OK_R;
      __enable_irq();//Enable IRQ
      U_Area.RSSI_R=RfDs.RSSI_R ;
      if(RfDs.RSSI_R>=RSSI_LIMIT)
        GFlag |=GF_RSSI_GOOD_R ;
      else
        GFlag &=~GF_RSSI_GOOD_R ;
      //
	  if(U_Area.PaDat_R.PSta.InquiryPackage==0)
	  {
        RfcTimer10[RF_OP_TIMER_R].cv=0 ;
        RfcTimer10[RF_OP_TIMER_R].csr.csword=0x4000 ; //start RfcTimer10[RF_OP_TIMER_R]
	   }
      RfcTimer10[AIR_TIMER_R].csr.csword=0x4000 ;//reset the monitor timer
      RfcTimer10[AIR_TIMER_R].cv=0 ;
     }
    else
      RfDs.RF_RxErr++ ;//record the CRC error
    __disable_irq();//Disable IRQ
    RfDs.RBuf_Sta &=0xf7 ;//reset the buffer to empty status
    __enable_irq();//Enable IRQ
  }
}

//--------------------------------------------------------------


void  RfRxTestDaemon_021(void)
{
  uint16_t tmp,*tp1 ;
  uint16_t tmp3 ;
  //check the received data ==0x55 ==0xaa or not
  if((RfDs.char_count>64)&(RfDs.flag==0))
    RfDs.flag=1 ;
  if((RfDs.char_count<64)&(RfDs.flag==2))
    RfDs.flag=0 ;
  if(RfDs.flag==1)
  {
    tp1=U_Area.Ultra_BUF ;
    RfDs.flag=2 ;
    tmp3=0 ;
    for(tmp=0 ;tmp<32 ;tmp++)
    {
     tmp3 |=*tp1++ ;
     }
    if((tmp3==0x5555)||(tmp3==0xaaaa))
      GFlag |=GF_TEST_RX_OK ;//Guide to turn on the right side linkage OK LED
    else
      GFlag &=~GF_TEST_RX_OK ;//Guide to turn off the right side linkage OK LED
   }
  return ;
}

//-------------------------------------
void  RfDaemon_021(void)
{		  
//Process IC021 Initalization operation
 switch (RfDs.IC021Init.OpSta)
 {
  case NOT_INIT_STA :
    if((RfDs.IC021Init.OpReq&0xff00)==SET_CODE )
	{
      RfDs.IC021Init.OpSta =RfDs.IC021Init.OpReq&0x00ff ;
	  RfDs.IC021Init.OpReq=0;
	  SetRfTxEnPin ;//ADF7021 CE to high
	  __disable_irq();
      RfcTimer[RF_TIMER].pv=OSC_TIMEOUT ;
      RfcTimer[RF_TIMER].cv=0 ;
      RfcTimer[RF_TIMER].csr.csword=0x4000 ;//enable RfcTimer[RF_TIMER]
      __enable_irq();
	 }
    break ;
  case IN_RX_STA :
    if(RfDs.IC021Init.OpReq==SET_UP_TX)
    {
      RfDs.IC021Init.OpReq=0x0000 ;
	  __disable_irq();
      RfcTimer[RF_TIMER].pv=MIN_TIMEOUT ;
      RfcTimer[RF_TIMER].cv=0 ;
      RfcTimer[RF_TIMER].csr.csword=0x4000 ;//enable RfcTimer[RF_TIMER]
      __enable_irq();
 	  //write R0, switch RX to TX and change LO
	  WriteReg_021(&(Tx_021_Ch_p->R0_N_Tx));
      RfDs.IC021Init.OpSta=SET_UP_TX_WAIT2 ;
     }
    break ;
  case IN_TX_STA :
    if(RfDs.IC021Init.OpReq==SET_UP_RX)
    {
      RfDs.IC021Init.OpReq=0x0000 ;
	  __disable_irq();
      RfcTimer[RF_TIMER].pv=MIN_TIMEOUT ;
      RfcTimer[RF_TIMER].cv=0 ;
      RfcTimer[RF_TIMER].csr.csword=0x4000 ;//enable RfcTimer[RF_TIMER]
      __enable_irq();
      //write R0, switch TX to RX and change LO
	  WriteReg_021(&(Rx_021_Ch_p->R0_N_Rx));
      RfDs.IC021Init.OpSta=SET_UP_RX_WAIT3 ;
     }
    break ;
  case SET_UP_RX :
    SetRfTxEnPin ;//TxEn (021 "CE" pin to high
    RfcTimer[RF_TIMER].pv=OSC_TIMEOUT ;
    RfcTimer[RF_TIMER].cv=0 ;
    RfcTimer[RF_TIMER].csr.csword=0x4000 ;//enable RfcTimer[RF_TIMER]
    RfDs.IC021Init.OpSta=SET_UP_RX_WAIT0 ;
    break ;
  case SET_UP_RX_WAIT0 :
    if(RfcTimer[RF_TIMER].csr.csbit.q==1)
    {
      //GPIOC->BSRR=0x00000800 ;//if output pin PC.11='0',set it to '1'	--debug
	  __disable_irq();
      RfcTimer[RF_TIMER].pv=MIN_TIMEOUT ;
      RfcTimer[RF_TIMER].cv=0 ;
      RfcTimer[RF_TIMER].csr.csword=0x4000 ;//enable RfcTimer[RF_TIMER]
      __enable_irq();
	  WriteReg_021(&(Rx_021_Ch_p->R1_VCO_OSC_Rx));//write R1, turn on VCO
      RfDs.IC021Init.OpSta=SET_UP_RX_WAIT1 ;
	 }
	 break ;
  case SET_UP_RX_WAIT1 :
    if(RfcTimer[RF_TIMER].csr.csbit.q==1)
    {
	  //write R3, turn on TX/RX clocks
	  WriteReg_021(&APP_PC_p->IC021Def.R3_TxRx_Clk);
      if(GFlag&GF_CALIBRATION_OK)
      {
        WriteReg_021(&RfDs.IC021Init.FilterCalReadback);//write R5 to start
        RfcTimer[RF_TIMER].csr.csword=0x5000 ;//enable RfcTimer[RF_TIMER] and .q=1
       }
      else if(((GFlag&GF_CALIBRATION_OK)==0)&&(APP_PC_p->IC021Def.R6_IF_Cal&0x00000010))
      { //Configured require IF Filter Fine calibration
	    __disable_irq();
        RfcTimer[RF_TIMER].pv=IF_CAL_FINE_TIME ;
        RfcTimer[RF_TIMER].cv=0 ;
        RfcTimer[RF_TIMER].csr.csword=0x4000 ;//enable RfcTimer[RF_TIMER]
        __enable_irq();
        WriteReg_021(&APP_PC_p->IC021Def.R6_IF_Cal);//write R6 here, if fine IF filter cal is wanted
	    WriteReg_021(&APP_PC_p->IC021Def.R5_IF_Filter);//write R5 to start IF filter cal
       }
      else
      {//Do coarse calibration
	    __disable_irq();
        RfcTimer[RF_TIMER].pv=MIN_TIMEOUT ;
        RfcTimer[RF_TIMER].cv=0 ;
        RfcTimer[RF_TIMER].csr.csword=0x4000 ;//enable RfcTimer[RF_TIMER]
        __enable_irq();
        WriteReg_021(&APP_PC_p->IC021Def.R5_IF_Filter);//write R5 to start IF filter cal
       }
      RfDs.IC021Init.OpSta=SET_UP_RX_WAIT2 ;
    }
    break ;
  case SET_UP_RX_WAIT2 :
    if(RfcTimer[RF_TIMER].csr.csbit.q==1)
    {
      if(((GFlag&GF_CALIBRATION_OK)==0)&&(APP_PC_p->IC021Def.R6_IF_Cal&0x00000010))
      { //Configured require IF Filter Fine calibration
        GFlag|=GF_CALIBRATION_OK ;
        RfDs.IC021Init.FilterCalRaw=ReadFilterCal_021() ;
        RfDs.IC021Init.CalTemp=ReadTemperature_021() ;
       }
	  __disable_irq();
      RfcTimer[RF_TIMER].pv=MIN_TIMEOUT ;
      RfcTimer[RF_TIMER].cv=0 ;
      RfcTimer[RF_TIMER].csr.csword=0x4000 ;//enable RfcTimer[RF_TIMER]
      __enable_irq();
	  //write R8, configure power down test register
	  WriteReg_021(&APP_PC_p->IC021Def.R8_PDT);//Set internal Rx/Tx switch ;
	  //write R11, configure sync word detect
	  WriteReg_021(&APP_PC_p->IC021Def.R11_SYNC);//sync word = ;
 	  //write R12, start sync word detect
	  WriteReg_021(&APP_PC_p->IC021Def.R12_SWD);//for sync word detect;
   	  //write R0, turn on PLL
	  WriteReg_021(&(Rx_021_Ch_p->R0_N_Rx));
      RfDs.IC021Init.OpSta=SET_UP_RX_WAIT3 ;
    }
    break ;
  case SET_UP_RX_WAIT3 :
    if(RfcTimer[RF_TIMER].csr.csbit.q==1)
    {
	  __disable_irq();
	  RfcTimer[RF_TIMER].csr.csword=0x0000 ;//disable timer
	  RfcTimer[RF_TIMER].cv=0 ;
      __enable_irq();
      //write R4, turn on demodulation
	  WriteReg_021(&APP_PC_p->IC021Def.R4_Demod);
  	  if (APP_PC_p->IC021Def.R10_AFC&0x00000010)//Required to use AFC
	  {
		//write R10, turn AFC on
		WriteReg_021(&APP_PC_p->IC021Def.R10_AFC);
	   }
      if((TestCtrlW&0xff00)==0x8000)
        RfDs.IC021Init.OpSta=RX_TEST_STA ;//Set RX test status
      else
        RfDs.IC021Init.OpSta=IN_RX_STA ;//Set rx setup ok status
      RfDs.IC021Init.OpReq=0 ;
      EnterRxMode_021() ;
    }
    break ;
  case SET_UP_TX : //Only used in FYS30 mode
    SetRfTxEnPin ;//TxEn (021 "CE" pin to high
    RfcTimer[RF_TIMER].pv=OSC_TIMEOUT ;
    RfcTimer[RF_TIMER].cv=0 ;
    RfcTimer[RF_TIMER].csr.csword=0x4000 ;//enable RfcTimer[RF_TIMER]
    RfDs.IC021Init.OpSta=SET_UP_TX_WAIT0 ;
    break ;
  case SET_UP_TX_WAIT0 :
    if(RfcTimer[RF_TIMER].csr.csbit.q==1)
    {
      //write R1, turn on VCO
	  WriteReg_021(&(Tx_021_Ch_p->R1_VCO_OSC_Tx));//
	  __disable_irq();
      RfcTimer[RF_TIMER].pv=MIN_TIMEOUT ;//delay about 800us
      RfcTimer[RF_TIMER].cv=0 ;
      RfcTimer[RF_TIMER].csr.csword=0x4000 ;//enable RfcTimer[RF_TIMER]
      __enable_irq();
      RfDs.IC021Init.OpSta=SET_UP_TX_WAIT1 ;
	 }
  case SET_UP_TX_WAIT1 :
    if(RfcTimer[RF_TIMER].csr.csbit.q==1)
    {
	  __disable_irq();
      RfcTimer[RF_TIMER].pv=MIN_TIMEOUT ;//require to delay about 40us
      RfcTimer[RF_TIMER].cv=0 ;
      RfcTimer[RF_TIMER].csr.csword=0x4000 ;//enable RfcTimer[RF_TIMER]
      __enable_irq();
	  //write R8, configure power down test register
	  WriteReg_021(&APP_PC_p->IC021Def.R8_PDT);//Set internal Rx/Tx switch off ;
	  WriteReg_021(&APP_PC_p->IC021Def.R3_TxRx_Clk);//write R3, turn on TX/RX clocks
	  WriteReg_021(&(Tx_021_Ch_p->R0_N_Tx));//write R0, turn on PLL
      RfDs.IC021Init.OpSta=SET_UP_TX_WAIT2 ;
    }
    break ;
  case SET_UP_TX_WAIT2 :
    if(RfcTimer[RF_TIMER].csr.csbit.q==1)
    {
	  __disable_irq();
	  RfcTimer[RF_TIMER].csr.csword=0x0000 ;//disable timer
	  RfcTimer[RF_TIMER].cv=0 ;
      __enable_irq();
	  WriteReg_021(&APP_PC_p->IC021Def.R2_Tx_Mod);//write R2, turn on PA
      if((TestCtrlW&0xfc00)==0xc000)
      {
//---------
        if((TestCtrlW&0xff00)==0xc100)//
	      WriteReg_021(&TestReg);//write Test register R15, to Output the carrier only
//---------
        RfDs.IC021Init.OpSta=TX_TEST_STA  ;//Enter Tx test status
        RfDs.IC021Init.OpReq=0 ;
        EnterTxMode_021() ;//Enter TX test status
        break ;
      }
      ShrMachDatTxLoad_021() ;//Need to change #########
	  //TunMachDatTxLoad_021 ;
      RfDs.IC021Init.OpSta=IN_TX_STA  ;//Set tx setup ok status
      RfDs.IC021Init.OpReq=0 ;
      EnterTxMode_021() ;
    }
    break ;
  case TX_TEST_STA :
  case RX_TEST_STA :
    RfRxTestDaemon_021() ;
    break ;
  case IR_CAL_REQ :
    RfDs.IC021Init.OpSta=NOT_INIT_STA ;
    RfDs.IC021Init.OpReq=0 ;
    break ;
  case IN_SLEEP_STA :
  default ://There must have enter wrong status
    {
	  RfDs.IC021Init.OpReq=0 ;
      RfDs.IC021Init.OpSta=NOT_INIT_STA ;
      RfDs.RF_State=RF_IDLE_STATUS ;
	  }
  }
}

//----------------------------------------------------------------------
void  ShrMachDatTxLoad_021(void)
{
 U_Area.ShrMachDat_TB0.WcrStaW=0x0000 ;
 // set air linkage valid flag
 if(RfDs.RF_ACK_Count_L)
 {
   RfDs.RF_ACK_Count_L-- ;
   if((U_Area.PaDat_L.PSta.InquiryPackage==0)||(U_Area.PaDat_L.KeyCode!=0))
     U_Area.ShrMachDat_TB0.LeftLinkOk=1;
  }
 if(RfDs.RF_ACK_Count_R)
 {
   RfDs.RF_ACK_Count_R-- ;
   if((U_Area.PaDat_R.PSta.InquiryPackage==0)||(U_Area.PaDat_R.KeyCode!=0))
     U_Area.ShrMachDat_TB0.RightLinkOk=1;
  }
 //--------------
 if(U_Area.RSSI_L>=RSSI_LIMIT)
   U_Area.ShrMachDat_TB0.RSSI_Ok_L=1 ;
 if(U_Area.RSSI_R>=RSSI_LIMIT)
   U_Area.ShrMachDat_TB0.RSSI_Ok_R=1 ;
 //----------------
 if(RfDs.RfPollNum&0x0001)
   U_Area.ShrMachDat_TB0.ToggleBit=1 ;
 //----------------
 if(OpCtrl &(CB_MDM_COM_OK+CB_MDS_COM_OK))//Rx station module communicate with HOST(CAN bus)or Demo module OK
   U_Area.ShrMachDat_TB0.DatValid=1 ;
 //----------------
 if(U_Area.DatReq_CP)//machine Rx station will output request for right side
 {
   U_Area.DatReq_CP=0 ;
   if(OpCtrl&CB_T_ONLINE_R)
   {
     U_Area.DatGID_C=U_Area.DatReq_R&0x1f1f ;
    }
   else
   {
     U_Area.DatGID_C=U_Area.DatReq_L&0x1f1f ;
    }
 }
 else
 {
   U_Area.DatReq_CP=1 ;
   if(OpCtrl&CB_T_ONLINE_L)
   {
     U_Area.DatGID_C=U_Area.DatReq_L&0x1f1f ;
    }
   else
   {
     U_Area.DatGID_C=U_Area.DatReq_R&0x1f1f ;
    }
 }
 U_Area.ShrMachDat_TB0.WcrStaW |= U_Area.DatGID_C&0x001f;//Set replayed display data GID0
 U_Area.ShrMachDat_TB0.WcrStaW |= (U_Area.DatGID_C>>3)&0x03e0;//Set replayed display data GID1
 U_Area.DisplayDatBuf[26]=U_Area.RSSI_L ;
 U_Area.DisplayDatBuf[27]=U_Area.RSSI_R ;
 U_Area.ShrMachDat_TB0.MachSta  = U_Area.DisplayDatBuf[0] ;//For machine status data
 U_Area.ShrMachDat_TB0.Dat_1 = U_Area.DisplayDatBuf[U_Area.DatGID_C&0x001f] ;
 U_Area.ShrMachDat_TB0.Dat_2 = U_Area.DisplayDatBuf[((U_Area.DatGID_C>>8)&0x001f)] ;
 
 //---------
 U_Area.ShrMachDat_TB0.CRC_app = CRC16WithSeed(MACH_CRC_SEED,(const uint16_t *)&U_Area.ShrMachDat_TB0,(sizeof(SHR_TX_DAT)/2-1));//Tx data load fixed to 4 words
}
//------------------------
void  TunMachDatTxLoad_021(void)
{
 U_Area.TunMachDat_TB0.WcrStaW=0x0000 ;
 // set air linkage valid flag
 if(RfDs.RF_ACK_Count_L)
 {
   RfDs.RF_ACK_Count_L-- ;
   if((U_Area.PaDat_L.PSta.InquiryPackage==0)||(U_Area.PaDat_L.KeyCode!=0))
     U_Area.TunMachDat_TB0.LeftLinkOk=1;
  }
 if(RfDs.RF_ACK_Count_R)
 {
   RfDs.RF_ACK_Count_R-- ;
   if((U_Area.PaDat_R.PSta.InquiryPackage==0)||(U_Area.PaDat_R.KeyCode!=0))
     U_Area.TunMachDat_TB0.RightLinkOk=1;
  }
 //--------------
 if(U_Area.RSSI_L>=RSSI_LIMIT)
   U_Area.TunMachDat_TB0.RSSI_Ok_L=1 ;
 if(U_Area.RSSI_R>=RSSI_LIMIT)
   U_Area.TunMachDat_TB0.RSSI_Ok_R=1 ;
 //----------------
 if(RfDs.RfPollNum&0x0001)
   U_Area.ShrMachDat_TB0.ToggleBit=1 ;
 //----------------
 if(OpCtrl &(CB_MDM_COM_OK+CB_MDS_COM_OK))//Rx station module communicate with HOST(CAN bus)or Demo module OK
   U_Area.TunMachDat_TB0.DatValid=1 ;
 //----------------
 if(U_Area.DatReq_CP)//machine Rx station will output request for right side
 {
   U_Area.DatReq_CP=0 ;
   if(OpCtrl&CB_T_ONLINE_R)
   {
     U_Area.DatGID_C=U_Area.DatReq_R&0x1f1f ;
    }
   else
   {
     U_Area.DatGID_C=U_Area.DatReq_L&0x1f1f ;
    }
 }
 else
 {
   U_Area.DatReq_CP=1 ;
   if(OpCtrl&CB_T_ONLINE_L)
   {
     U_Area.DatGID_C=U_Area.DatReq_L&0x1f1f ;
    }
   else
   {
     U_Area.DatGID_C=U_Area.DatReq_R&0x1f1f ;
    }
 }
 U_Area.TunMachDat_TB0.WcrStaW |= U_Area.DatGID_C&0x001f;//Set replayed display data GID0
 U_Area.TunMachDat_TB0.WcrStaW |= (U_Area.DatGID_C>>3)&0x03e0;//Set replayed display data GID1
 //---------For debug
 if(U_Area.TestCtrlW==0)
 {
   U_Area.TunMachDat_TB0.FB_CMD_D1 =U_Area.PaDat_L.KeyCode ;//For debug
   U_Area.TunMachDat_TB0.FB_CMD_D2 =U_Area.PaDat_R.KeyCode ;//For debug
  }
 //---------
 U_Area.TunMachDat_TB0.CRC_app = CRC16WithSeed(MACH_CRC_SEED,(const uint16_t *)&U_Area.TunMachDat_TB0,(sizeof(TUN_TX_DAT)/2-1));//Tx data load fixed to 4 words
}

//-----------------------
void  GetRSSI_021(void)
{
 if(RssiCheckFlag)//RSSI check flag has been set
 {
  //----------------------------------------------------------------
  ClearRssiCheckFlag ;//GPIOC->ODR's bit1 as IC021 RSSI check request(or JF01 RSSI data avaliable) flag
  RfDs.Rssi_DB_021=Read_RSSI_021() ;
  }
}
/*Init SPI1 interface to access IC021 @4Mbps ,16 bits character,use poll mode
  And set proper run condition(varible init)
*/
void InitSPI1_021(void)
{
 /* Enable SPI1  clocks */
  RCC_APB2PeriphClockCmd(RCC_APB2Periph_SPI1 , ENABLE);
  RCC_APB2PeriphResetCmd(RCC_APB2Periph_SPI1 , ENABLE);  /* Reset SPI1 First */
  RCC_APB2PeriphResetCmd(RCC_APB2Periph_SPI1 , DISABLE); /*SPI1 Out of reset  status */
 /*SPI interface to access RF chip @ 4Mbps ,8 bits character,use poll mode*/
  GPIOE->MODER&=0x00ffffff ;
  GPIOE->MODER|=0xa9000000 ;//set SCLK,MOSI to AF OUT mode,MISO to Input mode,bit12-NSS to GPIO output mode
 // APB2_CLK=8000000 Hz
  SPI1->CR1 = 0x0b04 ;// nSS internal mode 1,Master Mode,16 bit data length, disabled,CPHA=0,CPOL=0
                      //8MHz APB2 clock(8MHz CPU)/2=4 Mbps(0x0b0c--2Mbps)
  SPI1->CR2  =0x0000 ;//No interrupt,No DMA
 /* Enable SPI1 */
  SPI1->CR1 |=0x0040 ;
//-----------------------
}
//-----------------------------------------------------------------------------
void  EXTI15_10_ISR_021(void)
{//ADF7021_DCLK interrupt
 if((EXTI->PR)&0x00000800)
 {//EXTI Line 11 (PE.11) interrupt
  EXTI->PR=0x00000800 ;//Clear the interrupt
  RfDs.DClkCnt++  ;//MD20068
  switch (RfDs.RF_State) {
    case TX_STATUS:  //TX_STATE
			      if(BitCounter==0)
			      {
                    switch (RfDs.RF_TxState)
                    {
                      case TX_IDLE    : break ;//
                      case TX_1SEND_PREAMBLE_SEG : //prepare to output preamble status
                                  RfDs.RF_TxState=TX_1SEND_SOF_ID1 ;//switch to continue output preamble status
                                  PreambleCount=1 ;
                                  ShiftReg=(uint8_t)0x55 ;
                                  break ;
                      case TX_1SEND_SOF_ID1 : //continue to output the preamble character
                                  if(PreambleCount>=APP_PA.TxPreLen)
                                  {
                                    PreambleCount=0 ;//for debug
                                    SetStartAdcFlag ;//Use PC.0 as ADC convertion start request flag and set it to guide to do VCC measurement --####
                                    RfDs.RF_TxState=TX_1SEND_SOF_ID2 ;//switch to prepare to output first SOF identifier status
                                    ShiftReg=(uint8_t)SOF_ID1 ;//Output first SOF identifier
                                   }
                                  else
                                  {
                                    PreambleCount++ ;
                                    ShiftReg=(uint8_t)0x55 ;//continue to output preamble
                                   }
                                  break ;
                      case TX_1SEND_SOF_ID2 : //
                                  RfDs.RF_TxState=TX_1SEND_DP ;//switch to prepare to output data frame status
                                  ByteCounter=0 ;
                                  RfDat_tp=(uint8_t *) U_Area.RfTxChBuf0;
                                  ShiftReg=(uint8_t)SOF_ID2 ;//Output second SOF identifier
                                  break ;
                      case TX_1SEND_DP : //
                                  ShiftReg=*(RfDat_tp+ByteCounter++);//TxBuffer[ByteCounter++]^0xaa ;
                                  ShiftReg^=(uint8_t)0xaa ;
                                  if(ByteCounter>=RfDs.TxPackageLen)//ByteCounter>=DATAFRAME_LEN)
                                  {
                                    RfDs.RF_TxState=TX_WAIT_FOR_END ;//switch to wait last charater send out status
                                    ByteCounter=0 ;                   //in this status the TX buffer can be rewrite now
                                    RfDs.RF_TxPackage++ ;
                                   }
                                  break ;
                      case TX_FINISHED : //finish to send all bytes
                                  ShiftReg=(uint8_t)0x55 ;//
                                  EXTI->IMR  &=0x007ff7ff ;//Disable PE.11 to generate interrupt
                                  GPIOE->MODER &=0xffcfffff ;//Set PE.10(DIO) to input float status
                                  break ;
                      case TX_WAIT_FOR_END : //wait RFIC021 internal package process delay
                                  RfDs.RF_TxState=TX_WAIT_FOR_END2 ;//switch to Wait finish status 2
                                  ShiftReg=(uint8_t)0x55 ;//
                                  break ;
                      case TX_WAIT_FOR_END2 : //
                                  RfDs.RF_TxState=TX_WAIT_FOR_END3 ;//switch to Wait finish status 3
                                  ShiftReg=(uint8_t)0x55 ;//
                                  break ;
                      case TX_WAIT_FOR_END3 : //
                                  RfDs.RF_TxState=TX_FINISHED ;//switch to TX finish status
                                  ShiftReg=(uint8_t)0x55 ;//
                                  break ;
                      case TX_RF_TEST :
                                  if((TestCtrlW&0xfe00)==(uint16_t)0xc200) //
                                  {
                                    if(TestCtrlW&(uint16_t)0x0100)//
                                      ShiftReg=(uint8_t)0xff ;
                                    else
                                      ShiftReg=(uint8_t)0x00 ;
                                   }
                                  else
                                    ShiftReg=(uint8_t)0x55 ;
                                  RfDs.char_count++ ;
                                 if((RfDs.char_count&(uint16_t)0x001f)==0)
                                  {
                                    RfDs.char_count=0 ;
                                    SetStartAdcFlag ;//Use PC.0 as ADC convertion start request flag,Guide to measure supply voltage --####
                                   }
                                  break ;
                      default :   RfDs.RF_TxState=TX_IDLE ;
                     }
                   }
                  if(ShiftReg&(uint8_t)0x80)
                  {
                    GPIOE->BSRRL =0x0400  ;//set to high--DIO
                  }
                  else
                  {
                    GPIOE->BSRRH =0x0400  ;//set to low--DIO
                  }
			      ShiftReg=ShiftReg<<1;
			      BitCounter++;
			      if(BitCounter>=8) BitCounter=0 ;
			      break;
   case RX_STATUS :  //RX_STATE
                  /* Read data from ADF7021 */
			      ShiftReg=ShiftReg<<1;
			      if(GPIOE->IDR &0x0400) ShiftReg|=(uint8_t)0x01 ;//DIO pin = high
			      BitCounter++;
                  switch (RfDs.RF_RxState) {
                    case RX_NO_INIT_IDLE : break ;
                    case RX_SEARCH_PREAMBLE : //search for a vaild preamble status
					       /* If valid preamble, increase counter */
					       if ((ShiftReg==(uint8_t)0x55)||(ShiftReg==(uint8_t)0xAA))
                             PreambleCount++;
					       /* If not, reset counter */
					       else
						     PreambleCount=0;
					       /* If preamble requirement has been reached, declare that preamble */
					       /* has been found */
					       if (PreambleCount>=PREAMBLE_REQ)
					       {
//					 SetRssiCheckFlag ;//GPIOC->ODR's bit1 as IC021 RSSI check request(or JF01 RSSI data avaliable) flag
							 RfDs.RF_RxState=RX_SEARCH_SOF1;//switch to preamble found status
							 PreambleError=0;
				            }
                           break ;
                    case RX_SEARCH_SOF1 ://vailid preable be found already
                           //here must start receiving monitor timer#####################
                           //after receiving last data byte,must disable receiving monitor timer
                           //overflow of the timer,will reset the receiver daemon

			               /* Look for SOF/unique identifier */
						   if (ShiftReg==(uint8_t)SOF_ID1)
                           {
							// If SOF found, go into RX mode
							BitCounter=0 ;
							RfDs.RF_RxState=RX_SEARCH_SOF2;//first SOF already received status
                            //GPIOx->BSRR=0x00004000 ;//if PB.14='0',set it to '1'
							}
							/* Are we still receiving the preamble? */
						   else if ((ShiftReg==(uint8_t)0x55)||(ShiftReg==(uint8_t)0xAA))
                           {
							 PreambleCount++; /* If so, do nothing */
							}
							/* If we are not receiving a correct preamble, declare an error */
						   else if (PreambleError==0)
                           {
							PreambleError++;
							}
								
						   if (PreambleError>0)
							/* Increase the error counter regardless of bits read if */
							/* we have found en error */
							   PreambleError++;
								
							/* Once an error condition has occurred, a correct SOF must be */
							/* found within 9 bits (we increment by 2 at the first bit), */
							/* otherwise we abort and start looking for the preamble again */
						   if (PreambleError>10)
                           {
							RfDs.RF_RxState=RX_SEARCH_PREAMBLE;//switch to search for preamble status
                            PreambleCount=0 ;//reset PreambleCount
							}
                           break ;
                    case RX_SEARCH_SOF2 : //
		                    /* Byte received? */
		                    if (BitCounter>=8)
		                      {
				                 BitCounter=0;
							     if (ShiftReg==(uint8_t)SOF_ID2)
							     {//clear receive buffer ,reset pointer,ready to receiving datapackage
							       if((RfDs.RBuf_Sta&0x02)==0)
						           {//Buffer 0 is free
							         RfDat_rp=(uint8_t *) U_Area.RfRxBuf0 ;
							         RfDs.RBuf_Sta &=(uint8_t)0xfc ;//
                                     RfDs.RBuf_Sta |=(uint8_t)0x01 ;//Indicate buffer 0 be used
							        }
							       else
							       {//force to use buffer1
							         RfDat_rp=(uint8_t *) U_Area.RfRxBuf1 ;
							         RfDs.RBuf_Sta &=(uint8_t)0xf3 ;//
                                     RfDs.RBuf_Sta |=(uint8_t)0x04 ;//Indicate buffer 1 be used
							         }
                                   if(PreambleCount>VALID_SYNC_PREAMBLE)
                                   {
                                     SetSyncOpOkFlag ;//Use PC.2 as Rx/Tx clock synchronize operation OK flag--####
                                     S_Area.SyncTimeOut = APP_PA.SyncTimeOut ;
                                     AdjustRfTickTimer(RfTT_p->SyncTimeSet) ; //Sync the loacal Rf tick Timer to Rx station's clock
                                    }
    	     			   	       SetRssiCheckFlag ;//GPIOC->ODR's bit1 as IC021 RSSI check request(or JF01 RSSI data avaliable) flag
							       ByteCounter=0 ;
							       RfDs.RF_RxState=RX_RECEIVE_DP;//switch to receiving data package status
							      }
							     else
							     {
							       RfDs.RF_RxState=RX_SEARCH_PREAMBLE;//switch to search for preamble status
                                   PreambleCount=0 ;//reset PreambleCount
							       }
							    }
                            break ;
                    case RX_RECEIVE_DP : //
		                    /* Byte received? */
		                    if (BitCounter>=8)
		                      {
				                 BitCounter=0;
				                 *(RfDat_rp+ByteCounter++)=ShiftReg^(uint8_t)0xaa;//RxBuffer[ByteCounter++]=ShiftReg^0xaa;
				                 if(ByteCounter>=RfDs.RxPackageLen)
				                   {
							         if(RfDat_rp==(uint8_t *)U_Area.RfRxBuf0)
                                     {
							           RfDs.RBuf_Sta &=(uint8_t)0xfe ;
                                       RfDs.RBuf_Sta |=(uint8_t)0x02 ;//set data buffer0 valid flag
                                     }
							         else
                                     {
							           RfDs.RBuf_Sta &=(uint8_t)0xfb ;
                                       RfDs.RBuf_Sta |=(uint8_t)0x08 ;//set data buffer1 valid flag
                                     }
                                     if((APP_PA.work_mode&DEVICE_MODE_MASK)==FYS30_021_MODE)
                                     {//This device be configured to work in FYS30 shearer mode
							           RfDs.RF_RxState=RX_SEARCH_PREAMBLE;//enter to search new data frame status
                                      }
                                     else
                                     {//This device be configured to work in FYF35/FYF25 mode
                                       RfDs.RF_RxState=RX_DP_FINISHED;//Switch status to wait for upper level software operation
                                      }
                                     RfDs.RF_RxPackage++ ;
                                     PreambleCount=0 ;//reset PreambleCount
				                    }
                               }
                            break ;
                    case RX_DP_FINISHED:
		                    if (BitCounter>=8) BitCounter=0;
                            break ;
                    case RX_RF_TEST_INIT:
					       if ((ShiftReg==(uint8_t)0x55)||(ShiftReg==(uint8_t)0xAA))
                           {
                             RfDs.char_count=0 ;
                             RfDat_rp=(uint8_t*) U_Area.Ultra_BUF ;
	                         RfDs.RF_RxState=RX_RF_TEST;//switch to RX test status
                            }
                           break ;
                    case RX_RF_TEST:
                           if (BitCounter==8)
                           {
                             BitCounter=0 ;
                             if((ShiftReg!=0x55)&&(ShiftReg!=0xaa))
                               RfDs.BitError++ ;
                             *RfDat_rp=ShiftReg ;
                             RfDat_rp++ ;
                             RfDs.char_count++ ;
                             if((RfDs.char_count&(uint16_t)0x001f)==0)
							   SetRssiCheckFlag ;//GPIOC->ODR's bit1 as IC021 RSSI check request(or JF01 RSSI data avaliable) flag
                             if(RfDs.char_count>=96)
                              {
                                RfDs.char_count=0 ;
                                RfDat_rp=(uint8_t*)  U_Area.Ultra_BUF ;
                               }
                            }
                            break ;
			        default :
							RfDs.RF_RxState=RX_SEARCH_PREAMBLE;//enter data ready flag status,guide RX daemon to process
							                          //the received data
					}
					break ;
	default :{
	           RfDs.RF_State=RF_IDLE_STATUS ;//=0x0000
               EXTI->IMR  &=0xfffff7ff  ; // disable DCLK signal to generate interrupt in current state
              }
  }
 }
}

//Function related to SRD IC 021
uint16_t  ReadReg_021(uint8_t readback_config )
{
  uint32_t regv32;
  uint16_t i,regv;
/* Write readback and ADC control value */
  regv32 = ((uint32_t)readback_config & 0x0000001F) << 4;
  regv32 |=0x00000007; // Address the readback setup register
  WriteReg_021(&regv32) ;//After this call L_SLE=1
  __disable_irq();
  GPIOE->BSRRH=0xe000 ;//SCLK,MOSI,MISO ODR to 0
  GPIOE->MODER&=0x03ffffff ;//
  GPIOE->MODER|=SPI1Pins_GPIO_CONFIG ;//Set PE.13,14,15(SCLK,MISO,MOSI) to GPIO status
  //Clock in first bit and discard
  GPIOE->BSRR=0x80003000 ;//SLE=1,SDATA=0,SCLK=1,SREAD input dir
  regv = 0; // Slight pulse extend
  GPIOE->BSRR=0xa0001000 ;//SLE=1,SDATA=SCLK=0,SREAD input dir
  // Clock in data MSbit first
  for (i=16; i>0;i--)
  {
    GPIOE->BSRRL=0x2000 ;//SLE=1,SDATA=0,SCLK=1,SREAD input dir
    regv <<= 1; // left shift 1
    GPIOE->BSRRH=0x2000 ;//SLE=1,SDATA=SCLK=0,SREAD input dir
    if (GPIOE->IDR &0x4000)
       regv |= 1;//SREAD=1
   }
//Generate additional clock pulse
  GPIOE->BSRRL=0x2000 ;//SLE=1,SDATA=0,SCLK=1,SREAD input dir
  GPIOE->BSRRH =0x2000 ;//SLE=1,SDATA=SCLK=0,SREAD input dir
  // All port lines left low ?
  GPIOE->MODER&=0x03ffffff ;
  GPIOE->MODER|=SPI1Pins_SPI_CONFIG ;//Set PE.13,14,15(SCLK,MISO,MOSI) to SPI status
  __enable_irq();
  return regv;
}

void WriteReg_021(uint32_t *regv_p)
{//The read back data all be ignored
  uint16_t dummy ;
  IC021Reg.w32=*regv_p ;
  while((SPI1->SR&0x0080)!=0) ;//Wait the SPI1 to idle
  dummy=SPI1->DR ;
  GPIOE->BSRRH =0x1000;//Reset PE.12  force SLE to '0' 
  __disable_irq();
  SPI1->DR=IC021Reg.w16[1]; // Write high half word to SPI1
  while ((SPI1->SR&0x0002)==0)  ;//Wait the Tx buffer to empty to idle
  SPI1->DR=IC021Reg.w16[0]; // Write low half word to SPI1
  dummy=SPI1->DR ;
  // Wait until data is written
  while ((SPI1->SR&0x0080)!=0)  ;//Wait the SPI1 to idle
  dummy=SPI1->DR ;
  __enable_irq();//Enable IRQ
  GPIOE->BSRRL =0x1000 ;// Set PSEL(021 SLE) to High latch the data
  dummy=dummy ;
}
__inline void EnterTxMode_021(void)
{
  __disable_irq();
  BitCounter=0;
  PreambleCount=0;
  RfDs.RF_State=TX_STATUS ;//Set to TX state
  if((TestCtrlW&0xfc00)==0xc000)
    RfDs.RF_TxState=TX_RF_TEST ;//Enter RF TX Test status
  else
    RfDs.RF_TxState=TX_1SEND_PREAMBLE_SEG ;//prepare to output preamble status
  RfDs.RF_RxState=RX_NO_INIT_IDLE ;//
  ResetRfRxEnPin ;//Switch off Rx LNA and open TX signal path
  GPIOE->MODER &=0xffcfffff ;
  GPIOE->MODER |=0x00100000 ;//Set PE.10(DIO) to PP output status
  EXTI->RTSR &=0x003ff7ff ;//Disable Rising trigger
  EXTI->FTSR |=0x00000800 ;//Using Falling trigger (for TX mode,push data on high-to-low transition )
  EXTI->PR    =0x00000800 ;//clear pending ext interrupt line11# PE.11(DCLK)
  EXTI->IMR  |=0x00000800 ;//Allow PE.11 to generate interrupt
  RfDs.EntTxCnt++ ;
  __enable_irq();
}
__inline void EnterRxMode_021(void)
{
  __disable_irq();
  ShiftReg=0 ;
  BitCounter=0;
  RfDs.RF_State=RX_STATUS ;//Set to RX state
  if((TestCtrlW&0xff00)==0x8000)
    RfDs.RF_RxState=RX_RF_TEST_INIT ;//Prepar to enter RX TEST status
  else
    RfDs.RF_RxState=RX_SEARCH_PREAMBLE ;//search for a vaild preamble status
  RfDs.RF_TxState=TX_IDLE ;
  GPIOE->MODER &=0xffcfffff ;//Set PE.10(DIO) to input float status
  SetRfRxEnPin ;//Enable LNA and open RX signal path
  EXTI->RTSR |=0x00000800 ;//using Rising trigger(for RX mode,read received data on low-to-high transition )
  EXTI->FTSR &=0x003ff7ff ;//Disable Falling trigger
  EXTI->PR    =0x00000800 ;//clear pending ext interrupt line11# PE.11(DCLK)
  EXTI->IMR  |=0x00000800 ;//Allow PA.3 to generate interrupt
  RfDs.EntRxCnt++ ;
  __enable_irq();
}
/****************************************************************************
 *                   		   readback functions                           *
 ***************************************************************************/
__inline uint16_t Read_AFC_021(void)
{
  if( RfDs.IC021Init.OpSta==IN_RX_STA)
    return ReadReg_021(0x10);
  else
    return 0 ;
}
int16_t  ReadTemperature_021(void)
{
  int32_t temp = -45;
  uint32_t tmp ;
  uint16_t TMP_value ;
  if(RfDs.IC021Init.OpSta==IN_TX_STA)
  {//I assume the ADC enabled in Tx mode
    TMP_value=ReadReg_021(0x16);
    TMP_value&=0x007f ;
    TMP_value *=10 ;
    temp=(684-TMP_value)*932 ;
    temp/=1000 ;
    temp-=40 ;
    return (int16_t)temp ;
  }
  else
  {
    tmp=APP_PC_p->IC021Def.R9_AGC|0x00080000 ;
	WriteReg_021(&tmp);//Disable(Freeze) the AGC function
    TMP_value=ReadReg_021(0x16);
	WriteReg_021(&APP_PC_p->IC021Def.R9_AGC);//re_enable the AGC function
    TMP_value&=0x007f ;
    TMP_value *=10 ;
    temp=(684-TMP_value)*932 ;
    temp/=1000 ;
    temp-=40 ;
    return (int16_t)temp ;
  }
}
int16_t Read_RSSI_021(void)
{
  int16_t rssi = 0;
  uint16_t RSSI_value;
  if((RfDs.IC021Init.OpSta==IN_RX_STA)||(RfDs.IC021Init.OpSta==RX_TEST_STA))
  {
	RSSI_value = ReadReg_021(0x14)&0x07ff;
    rssi = RSSI_value>>7 ;
    RSSI_value&=0x007f ;//only use last 7 bits
	RSSI_value += gain_correction[rssi&0x0F] ;
    rssi = RSSI_value>>1 ;//*0.5
    return (rssi-130) ;//RSSI(dBm) = rssi + 130
   }
  else
    return -128 ;//Should set to invalid value
}

uint16_t ReadFilterCal_021(void)
{
  uint16_t vreturn ;
  vreturn=ReadReg_021(0x18) ;
  RfDs.IC021Init.FilterCalReadback=vreturn-128 ;
  RfDs.IC021Init.FilterCalReadback<<=14 ;
  RfDs.IC021Init.FilterCalReadback&=0x000fc000 ;
  RfDs.IC021Init.FilterCalReadback|=APP_PC_p->IC021Def.R5_IF_Filter ;
  RfDs.IC021Init.FilterCalReadback&=0xffffffef ;//Clear calibration req bit
  return(vreturn);
}

uint16_t ReadVersion_021(void)
{
  //IC021 PC=0x211 ,current revision code should >=0x1
  return(ReadReg_021(0x1c));//So readback value >=0x2104
}

/* EOF */

