#include "S30Main_01.h"
//-------------------------
/*
   After power up reset , in order to indicate current device worked in left or right mode,
   if device work in right side mode, onboard Led 1 and Led 4 will be functional exchanged about 10 seconds
*/
/*******************************************************************************
* Function Name  : __S30Main_01
* Description    : This is main function entry for work in  Fys30 OPS with RF01 mode
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void  __S30Main_01(void)
{
 __disable_irq();
 PWR->CR|=0x00000100 ;//Set DBP--enable to access RTC backup register and RCC CSR 
 RCC->CSR=0x01000100 ;//LSE set to ON,Remove reset flag
 PWR->CR&=0xfffffeff ;//Reset DBP--disable to access RTC backup register and RCC CSR 
 //---------------------------------
 InitSysTimer()   ;//Init timers in kernel mode
 InitTimer() ;//Init timer in user mode
 Init108_S30Mode() ;
 InitSTM32Lxx() ;//Initialize system clock variable and NVIC 
 __enable_irq();
//--
 InitTIM9_S30(); //Init hardware timer TIM9--Pd=1.000S
 InitRfTickTimer();//Init a hardware RF tick timer(TIM10) 
 InitTIM11_S30();//Init hardware timer TIM11--Pd=0.9765mS
//--
 InitRfEnvironment() ;//Initialize or setup the RF operation working environment
//------------------------
 if(Def_CRC_Seed==RIGHT_CRC_SEED)
 {
   Rx_01_Ch_p=(JF01SwitchReg *)&(APP_PD_p->Rx_Freq_Ch[APP_PA.RFIC_R_TxChNum]) ;
   Tx_01_Ch_p=(JF01SwitchReg *)&(APP_PD_p->Tx_Freq_Ch[APP_PA.RFIC_R_RxChNum]) ;
   APP_PB.U2Config.mod_add= ID_OPS_R; 
  }
 else
 {
   Rx_01_Ch_p=(JF01SwitchReg *)&(APP_PD_p->Rx_Freq_Ch[APP_PA.RFIC_L_TxChNum]) ;
   Tx_01_Ch_p=(JF01SwitchReg *)&(APP_PD_p->Tx_Freq_Ch[APP_PA.RFIC_L_RxChNum]) ;
   APP_PB.U2Config.mod_add= ID_OPS_L; 
  }
//-----------------
 InitSPI1_01() ;//Init SPI for JF01 access
 ResetRfTxEnPin ;//TxEn pin to low
 ResetRfRxEnPin ;//Switch off Rx LNA
 JF01CmdStrobe(JF01_SPWD) ;//Put the JF101 to sleep status
 JF01Reset();//After this chip enter IDLE status
 InitJF01Regiters() ;//Init configuration register to default value
 JF01WritePATable(DefPATable,PA_TAB_LEN) ;//I will only use DefPATable[0] normally
 JF01WriteReg(JF01_IOCFG0,0x02) ;// Set GDO0 to be TX FIFO threshold ALARM signal
 JF01WriteReg(JF01_IOCFG2,0x06) ;// Set GDO2 to packet sent signal
 JF01WriteReg(JF01_MCSM1,0x0f)  ;// CCA mode=Always,RXOFF_MODE->Stay in RX,TXOFF_MODE->RX mode
 SetupJF01PD() ;//Enter sleep status

//-----------------  
 APP_PB.U2Config.sci_speed=19200 ;
 APP_PB.U2Config.parity=2 ;
 APP_PB.U2Config.tx_delay=3 ;
 APP_PB.U2Config.rx_t_out=40 ;
 APP_PB.U2Config.retry=0 ; 
 //----------------
  
#ifndef PROG_DEBUG  
 InitWatchDog() ;//Time out value about 100ms
#endif
  
 USART_Config() ;//Use RS485 for OPS communication
 InitOnchipADC() ;
 //------------------------
 Timer10[OPC_TIMER].pv=10 ;
 Timer10[OPC_TIMER].cv=0 ;
 Timer10[OPC_TIMER].csr.csword=0x4000 ; //start Timer10[OPC_TIMER]
 while(Timer10[OPC_TIMER].csr.csbit.q==0)
 {
   TimerProc() ;//user defined timer proccess
   GetKeySta() ;//Get and set local operation key status
#ifndef PROG_DEBUG
	 KickWatchDog();//kick the watchdog
#endif	 
  }	 
 //Need to wait about 100mS befor actual check the keyboard status 
 //----------------
 Timer10[OPC_TIMER].pv=95 ;
 Timer10[OPC_TIMER].cv=0 ;
 Timer10[OPC_TIMER].csr.csword=0x4000 ; //start Timer10[OPC_TIMER]
 Timer10[CM_TIMER].pv=APP_PB.U2Config.rx_t_out ;//Setup modbus slaver mode communication monitor
 Timer10[CM_TIMER].cv=0 ;
 Timer10[CM_TIMER].csr.csword=0x4000 ; //start Timer10[CM_TIMER]
 //------------------------
 GetKeySta() ;//Get and set local operation key status
 if(U_Area.KeyCodeRaw.cw==(SW13+SW14+SW15))
 {//SW11,SW13,SW14,SW15 be pressed at same time(simultanously)
   RfTestEntry_01() ;//To enter RF test mode ,Never return
  }
 else if(U_Area.KeyCodeRaw.cw)
 {
   GFlag|=GF_KB_ERROR ;//Set keyboard error flag
   S_Area.DeadKeyMask.cw=~U_Area.KeyCodeRaw.cw ;
   S_Area.DeadKeyMask.cw|=APP_PB.EStopCode ;//disable mask for emergnece stop key
   S_Area.KeyMask.cw&=S_Area.DeadKeyMask.cw ;
  }
 Led_1x2_on() ;//Use LED1 as power indicator
 //U_Area.HW_Version=ReadVersion_021() ;
 U_Area.AreaFlag100=0xF100 ;//MD100
 SYS_STA=UNI_INIT_STA ;//Enter RX only RF init status
 while(1)
 {
  TimerProc() ;//user defined timer proccess
  ADC12Daemon() ;//process ADC operation request
  //--------------------------------------
  switch(SYS_STA)
  {
  case DUPLEX_INIT_STA :
    RfDs.JF01Init.OpReq =SET_CODE+SET_UP_RX ;//Request to do Rx setup operation
    RfDs.JF01Init.OpSta =NOT_INIT_STA;//
    SYS_STA=DUPLEX_RX_STA ;
    break ;
  case DUPLEX_RX_STA :
    if((IsrFlag&IFG_GTOKEN_EN))
    {
	  if(RfDs.RF_TxState==TX_IDLE)
	  {
        __disable_irq();//Disable IRQ
        IsrFlag &=~IFG_GTOKEN_EN ;
        RfDs.RF_State=RF_IDLE_STATUS ;//Set to Idle state
        __enable_irq();//Enable IRQ
        if(RfDs.JF01Init.OpSta!=IN_RX_STA)
        {
          RfDs.JF01Init.OpSta=NOT_INIT_STA ;
          RfDs.JF01Init.OpReq=SET_CODE+SET_UP_TX ;
         }
        else
          RfDs.JF01Init.OpReq=SET_UP_TX ;//
        SYS_STA=DUPLEX_TX_STA ;
	   }
     }
    break ;
  case DUPLEX_TX_STA :
    if(RfDs.RF_TxState==TX_FINISHED)
    {
      RfDs.RF_State=RF_IDLE_STATUS ;//Set to Idle state
      RfDs.RF_TxState=TX_IDLE ;
      if(RfDs.JF01Init.OpSta!=IN_TX_STA)
      {
        RfDs.JF01Init.OpSta=NOT_INIT_STA ;
        RfDs.JF01Init.OpReq=SET_CODE+SET_UP_RX ;//
       }
      else
        RfDs.JF01Init.OpReq=SET_UP_RX ;//
      SYS_STA=DUPLEX_RX_STA ;
     }
    break ;
  case UNI_INIT_STA :
	RfDs.JF01Init.OpReq =SET_CODE+SET_UP_RX ;//Request to do Rx setup operation
    SYS_STA=UNI_RX_STA ;
    break ;
  case UNI_RX_STA :
    /*
	if(RfDs.JF01Init.OpSta!=IN_RX_STA)
	{//The RF chip may be in wrong status
       RfDs.JF01Init.OpSta=NOT_INIT_IDLE ;
       RfDs.JF01Init.OpReq=SET_CODE+SET_UP_RX ;
	 }
	 */
    break ;
  case WAIT_RFIC_READY_STA:

    break ;
  default :
      SCB->AIRCR = 0x05fa0001;//in wrong status--do system & core reset
  }
  //--------------------------------------
  ModProcA(&UB,&ModB) ;//Modbus daemon  using channel B
  UXTxP() ;//
  //--------------------------------------
  RfDaemon_01() ;
  PanelDatRxDaemon_01() ;	 
  RSSI_Filter_01() ;  
  //
  if(Timer10[CM_TIMER].csr.csbit.q==1)
  {
    Timer10[CM_TIMER].cv=0 ;
    Timer10[CM_TIMER].csr.csword=0x4000 ; //restart Timer10[CM_TIMER]
    OpCtrl&=~CB_MDS_COM_OK ;//Wired communication lost error --this flag set by modbus daemon
   }
  if(Timer10[OPC_TIMER].csr.csbit.q==1)
  {
   if(Led_4_sta)
   {
     if(OpCtrl&(CB_MDM_COM_OK+CB_MDS_COM_OK))//Modbus Master or Slaver communication OK
       Timer10[OPC_TIMER].pv=5 ;
     else
       Timer10[OPC_TIMER].pv=95 ;
     Led_4_off ;//
    }
   else
   {
     Timer10[OPC_TIMER].pv=5 ;
     Led_4_on ;//
    } 
   Timer10[OPC_TIMER].cv=0 ;
   Timer10[OPC_TIMER].csr.csword=0x4000 ; //start Timer10[OPC_TIMER]
  }
  //--
  if((U_Area.PaDat_L.KeyCode!=0)||(U_Area.PaDat_R.KeyCode!=0))
  {
    Led_3_on ;//Wireless operation exist indicator
   }
  else
    Led_3_off ;//Wireless operation exist indicator
  //--
  if(U_Area.KeyCode.cw)
    Led_2x1_on() ;
  else
    Led_2x1_off() ;
  //----------------------------------------
  SysConfigDaemon() ;//For parameter change IAP
#ifndef PROG_DEBUG
  KickWatchDog();//kick the watchdog
#endif  
 }
}
//---------
void Proc10mS_S30_01(void) //Timed process at frequency=100Hz
{
  GetKeySta() ;//Get and set local operation key status
  U_Area.Counter10ms++ ;
  //------------
  if(Def_CRC_Seed==LEFT_CRC_SEED)
  {
    U_Area.KeyComb=U_Area.KeyCode.cw|U_Area.PaDat_L.KeyCode ;
    U_Area.OpsDat_L.OpsStaW=0x0000;
    U_Area.OpsDat_L.OpsStaW|=(U_Area.Counter10ms&0x00ff) ;
    if(U_Area.KeyComb&0x0200)//F2nd be pressed
       U_Area.OpsDat_L.KeyCode=U_Area.KeyComb ;
    else
       U_Area.OpsDat_L.KeyCode=U_Area.KeyComb&0xff3f ;//disable right side arm operation
	//--
	U_Area.OpsDat_L.KeyCode=U_Area.KeyComb ;//for debug test
	//--
    U_Area.OpsDat_L.RandomNum=TIM11->CNT;
    U_Area.OpsDat_L.CRC_app=CRC16WithSeed(LEFT_CRC_SEED,(const uint16_t *) &U_Area.OpsDat_L.OpsStaW,(sizeof(OPS_TX_DAT)/2-1));
    U_Area.OpsDat_L.OpsStaW^=U_Area.OpsDat_L.CRC_app;
    U_Area.OpsDat_L.KeyCode^=U_Area.OpsDat_L.CRC_app;
    U_Area.OpsDat_L.RandomNum^=U_Area.OpsDat_L.CRC_app;
   }
  else//Def_CRC_Seed==RIGHT_CRC_SEED
  {
    U_Area.KeyComb=U_Area.KeyCode.cw|U_Area.PaDat_R.KeyCode ;
    U_Area.OpsDat_R.OpsStaW=0x0000;
    U_Area.OpsDat_R.OpsStaW|=(U_Area.Counter10ms&0x00ff) ;
    if(U_Area.KeyComb&0x0200)//F2nd be pressed
       U_Area.OpsDat_R.KeyCode=U_Area.KeyComb ;
    else
       U_Area.OpsDat_R.KeyCode=U_Area.KeyComb&0xfffc ;//disable left side arm operation
	//--
	U_Area.OpsDat_R.KeyCode=U_Area.KeyComb ;//for debug test
	//--
    U_Area.OpsDat_R.RandomNum=TIM11->CNT;
    U_Area.OpsDat_R.CRC_app=CRC16WithSeed(RIGHT_CRC_SEED,(const uint16_t *) &U_Area.OpsDat_R.OpsStaW,(sizeof(OPS_TX_DAT)/2-1));
    U_Area.OpsDat_R.OpsStaW^=U_Area.OpsDat_R.CRC_app ;
    U_Area.OpsDat_R.KeyCode^=U_Area.OpsDat_R.CRC_app ;
    U_Area.OpsDat_R.RandomNum^=U_Area.OpsDat_R.CRC_app ;
   }
}
void Proc100mS_S30_01(void)
{
  if(GFlag&GF_KB_ERROR)
  {//Keyboard error flag be set,use Led 1 as alarm indicator
	if(Led_1x2_sta())
	  Led_1x2_off() ;
	else
	  Led_1x2_on() ;
   }
}
void Proc1S_S30_01(void) //Timed process at frequency=1Hz
{
  ;
}

void Proc1H_S30_01(void) //Timed process at interval 1 hour
{
  ;
}

