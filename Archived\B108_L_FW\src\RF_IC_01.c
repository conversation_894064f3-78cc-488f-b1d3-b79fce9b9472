#include "SysBasic.h"
#include "Timer.h"
#include "RF_BasicFun.h"
//----------------------------------------------------------------------------------------
extern  tim_count  RfcTimer[RFC_TIMER_NUM]  ;//system ISR process

extern  tim_count  RfcTimer10[RFC_TIMER10_NUM]  ;//user level timer(1 tick=10ms),RFC_TIMER10_NUM==2

extern  uint8_t   ShiftReg ;
extern  uint8_t   BitCounter;
extern  uint8_t   PreambleCount;
extern  uint8_t   PreambleError;
extern  uint8_t   ByteCounter;

extern  uint8_t   *test_p ;//pointer used to store current testoutput poistion of RX data

extern  uint8_t   *RfDat_rp  ;//pointer used for RF receiveing  data
extern  uint8_t   *RfDat_tp  ;//pointer used for RF Transmitte data 
extern  __IO uint16_t  PRandom ;

//----------------------------------------------------------------------------------------
__inline void PanelDatTxLoad_01(void)
{
  //-----------
  U_Area.PSet.SettingW &=  0xc020 ;//clear bits other than rev_bit1,rev_bit2,UniDirCtrl
  U_Area.PSet.SettingW |= (0x0201+FIXED_FLAG+PRandom) ;//Set the fixed bit(bit 7)flag and default display request

  U_Area.PSta.StaW&=0x9dff ;//Preserve those will not affect flag bits(include:InGroupId,GroupId)
  if(IsrFlag&IFG_TO_BE_SHUTOFF)
    U_Area.PSta.ToBeShutoff=0x0001 ;
  if(GFlag&GF_BAT_ALARM)
    U_Area.PSta.BatteryAlarm=0x0001 ;
  if(OpCtrl&CB_RX_OK)
    U_Area.PSta.RxDatOk=0x0001 ;//RxDatOk

  U_Area.PanelDat.Payload.PSet.SettingW=U_Area.PSet.SettingW;
  U_Area.PanelDat.Payload.PSta.StaW=U_Area.PSta.StaW ;
  U_Area.PanelDat.Payload.KeyCode  =U_Area.KeyCode.cw ;//
  U_Area.PanelDat.CRC_app  = CRC16WithSeed(Def_CRC_Seed,(const uint16_t *)&U_Area.PanelDat,(sizeof(PANEL_TX_DAT)/2-1));
  //-----
  U_Area.PanelDat_TB0.Payload.PSet.SettingW=U_Area.PanelDat.Payload.PSet.SettingW^0xaaaa ;
  U_Area.PanelDat_TB0.Payload.PSta.StaW=U_Area.PanelDat.Payload.PSta.StaW^0xaaaa ;
  U_Area.PanelDat_TB0.Payload.KeyCode=U_Area.PanelDat.Payload.KeyCode^0xaaaa ;
  U_Area.PanelDat_TB0.CRC_app=U_Area.PanelDat.CRC_app^0xaaaa ;
}

//--------------------------------------------------------------------------
void  ShrMachDatRxDaemon_01(void)
{
  uint16_t tmp , *tp1 ,*tp2;
  uint8_t *ch_tp,sta ;

  if(RfcTimer10[AIR_TIMER_L].csr.csbit.q==1)
  {
    RfcTimer10[AIR_TIMER_L].csr.csword=0x0000 ;//Disable the monitor timer
    U_Area.ShrMachDat.WcrStaW=0x0000 ;//reset the Communication status
    S_Area.AlarmChange=0xfc00 ;//Guide to refresh all status indicator
    RfDs.RSSI=-128 ;
    GFlag |=GF_RSSI_REFRESH ;
    OpCtrl &=(~CB_RX_OK) ;
   }
  if(IsrFlag & IFG_JF01_RX_FINISHED)
  {
    __disable_irq();//Disable IRQ
    IsrFlag &=(~IFG_JF01_RX_FINISHED) ;
    __enable_irq();//Enable IRQ
    //---------------Begin to read out data in Rx FIFO
    EnableJF01Chip ;//nCS='0'
    while((GPIOE->IDR&0x4000)!=0)  ;//Wait for chip to ready
    SPI1->DR=JF01_RXFIFO+JF01_READ_BURST ;//Issue the address byte
    ch_tp=(uint8_t *) U_Area.RfRxChBuf0 ;
    while((SPI1->SR&0x0001)==0)  ;//Wait for the data be output
    sta=SPI1->DR ;//Get returned JF01 sta byte
    for(tmp=0 ;tmp<RfDs.RxPackageLen ;tmp++)
    {
      SPI1->DR=JF01_RXFIFO+JF01_READ_BURST ;//Dummy data
      while((SPI1->SR&0x0001)==0)  ;//Wait for the data be output
      *ch_tp=(uint8_t)SPI1->DR^0xaa ;//Get the data ;
      ch_tp++ ;
     }
    SPI1->DR=JF01_RXFIFO+JF01_READ_BURST ;//Dummy data
    while((SPI1->SR&0x0001)==0)  ;//Wait for the data be output
    *ch_tp=(uint8_t)SPI1->DR ;//Get the data--Append_RSSI_L
    ch_tp++ ;
    SPI1->DR=JF01_RXFIFO+JF01_READ_BURST ;//Dummy data
    while((SPI1->SR&0x0001)==0)  ;//Wait for the data be output
    *ch_tp=(uint8_t)SPI1->DR;//Get the data --Append_CRC_LQI_L
    DisableJF01Chip ;  // Set /nCS to High 
    sta=sta ;
    RfDs.RF_RxState=RX_DP_FINISHED ;//switch to RX finish status
	RfDs.RSSI_D_L=Read_RSSI_01();//(int16_t) U_Area.RfRxChBuf0[RfDs.RxPackageLen] ;
//-------------------------------
    if(CRC16WithSeed(MACH_CRC_SEED,(uint16_t const *) U_Area.RfRxBuf0,sizeof(SHR_TX_DAT)/2))
    {
      RfDs.RF_RxErr++ ;//CRC error
     }
    else
    {
      tp1=(uint16_t *)&U_Area.RfRxBuf0 ;
      tp2=(uint16_t *)&U_Area.ShrMachDat ;
      for(tmp=0;tmp<sizeof(SHR_TX_DAT)/2;tmp++)
        *(tp2++)=*(tp1++);//Copy received data to user data area
      if(U_Area.ShrMachDat.DatValid)
      {
        U_Area.DisplayDatBuf[U_Area.ShrMachDat.DatGID_1]=U_Area.ShrMachDat.Dat_1 ;
        U_Area.DisplayDatBuf[U_Area.ShrMachDat.DatGID_2]=U_Area.ShrMachDat.Dat_2 ;
       }
      S_Area.AlarmChange|=(S_Area.PreAlarmSta^U_Area.ShrMachDat.MachSta) ;
      S_Area.PreAlarmSta=U_Area.ShrMachDat.MachSta ;
      //-----------
      if((U_Area.ShrMachDat.LeftLinkOk!=0)&&(Def_CRC_Seed==LEFT_CRC_SEED))
      {
        GFlag|=GF_CMD_CONFIRMED ;
       }
      else if((U_Area.ShrMachDat.RightLinkOk!=0)&&(Def_CRC_Seed==RIGHT_CRC_SEED))
      {
        GFlag|=GF_CMD_CONFIRMED ;
       }
      else
      {
        GFlag&=~GF_CMD_CONFIRMED ;
       }
      RfcTimer10[AIR_TIMER_L].csr.csword=0x4000 ;//reset the monitor timer
      RfcTimer10[AIR_TIMER_L].cv=0 ;
      OpCtrl |= CB_RX_OK ;
     }
    GFlag&=~(GF_MAREA1_V_U_OK+GF_MAREA2_V_U_OK) ;//Guide to redraw monitor data and unit
   }
 }
//----------------------------------------------------------------------------------------
void PanelDatRxDaemon_01(void)
{
 uint16_t  tmp,*tp1 ;
 uint16_t  *tp2 ;
 uint8_t   sta,*ch_tp ;
 if(RfcTimer10[RF_OP_TIMER_L].csr.csbit.q==1)
  {
    U_Area.PaDat_L.PSet.SettingW&=0xdf3f ;//reset the pannel setting byte
    U_Area.PaDat_L.PSta.StaW =0x0000 ;//reset the pannel status
    U_Area.PaDat_L.KeyCode =0x0000 ;//reset the key operation status,except for F2nd,F3rd key
    OpCtrl &=~CB_RX_OK_L ;//Turn off the left side  operation received OK flag
   }
 if(RfcTimer10[AIR_TIMER_L].csr.csbit.q==1)
  {
    OpCtrl &=~CB_T_ONLINE_L ;//Turn off the left side linkage OK flag
    U_Area.RSSI_L =-128 ;//RfDs.RSSI_L ;
   }
 if(RfcTimer10[RF_OP_TIMER_R].csr.csbit.q==1)
  {
    U_Area.PaDat_R.PSet.SettingW&=0xdf3f  ;//reset the pannel status
    U_Area.PaDat_R.PSta.StaW =0x0000 ;//reset the pannel status
    U_Area.PaDat_R.KeyCode =0x0000 ;//reset the key operation status,except for F2nd,F3rd key
    OpCtrl &=~CB_RX_OK_R ;//Turn off the right side operation received OK flag
   }
 if(RfcTimer10[AIR_TIMER_R].csr.csbit.q==1)
  {
    OpCtrl &=~CB_T_ONLINE_R ;//Turn off the right side linkage OK flag
    U_Area.RSSI_R=-128 ;//RfDs.RSSI_R ;
   }
  if(IsrFlag & IFG_JF01_RX_FINISHED)
  {
	RfcTimer[RF_TIMER].cv=0 ;//Reset RX attenuation monitor timer 
    __disable_irq();//Disable IRQ
    IsrFlag &=(~IFG_JF01_RX_FINISHED) ;
    __enable_irq();//Enable IRQ
    //---------------Begin to read out data in Rx FIFO
    EnableJF01Chip ;//nCS='0'
    while((GPIOE->IDR&0x4000)!=0)  ;//Wait for chip to ready
    SPI1->DR=JF01_RXFIFO+JF01_READ_BURST ;//Issue the address byte
    ch_tp=(uint8_t *) U_Area.RfRxChBuf0 ;
    while((SPI1->SR&0x0001)==0)  ;//Wait for the data be output
    sta=SPI1->DR ;//Get returned JF01 sta byte
    for(tmp=0 ;tmp<RfDs.RxPackageLen ;tmp++)
    {
      SPI1->DR=JF01_RXFIFO+JF01_READ_BURST ;//Dummy data
      while((SPI1->SR&0x0001)==0)  ;//Wait for the data be output
      *ch_tp=(uint8_t)SPI1->DR^0xaa ;//Get the data ;
      ch_tp++ ;
     }
    SPI1->DR=JF01_RXFIFO+JF01_READ_BURST ;//Dummy data
    while((SPI1->SR&0x0001)==0)  ;//Wait for the data be output
    *ch_tp=(uint8_t)SPI1->DR ;//Get the data--Append_RSSI_L
    ch_tp++ ;
    SPI1->DR=JF01_RXFIFO+JF01_READ_BURST ;//Dummy data
    while((SPI1->SR&0x0001)==0)  ;//Wait for the data be output
    *ch_tp=(uint8_t)SPI1->DR;//Get the data --Append_CRC_LQI_L
    DisableJF01Chip ;  // Set /nCS to High 
    sta=sta ;
   if(CRC16WithSeed(LEFT_CRC_SEED,(const uint16_t *) &U_Area.RfRxBuf0,RfDs.RxPackageLen/2)==0)
   {
     tp1=(uint16_t  *) &U_Area.RfRxBuf0 ;
     tp2=(uint16_t  *) &U_Area.PaDat_L ;
     for(tmp=0 ;tmp<RfDs.RxPackageLen/2;tmp++)
     {
       *tp2++=*tp1++ ;//copy valid data to output area
      }
     if((U_Area.PaDat_L.PSta.InquiryPackage!=0)||(U_Area.PaDat_L.PSet.FixedBit!=0x0001))
       U_Area.PaDat_L.KeyCode=0x0000 ;//reset the operation status
     // set air linkage valid flag
     RfDs.RF_ACK_Count_L=RF_ACK_NUM ;
     U_Area.DatReq_L=U_Area.PaDat_L.PSta.StaW ;
	 RfDs.RSSI_D_L=Read_RSSI_01();//(int16_t) U_Area.RfRxChBuf0[RfDs.RxPackageLen] ;
     __disable_irq();//Disable IRQ
	 IsrFlag|=IFG_JF01_RSSI_OK_L;
     __enable_irq();//Enable IRQ
     OpCtrl |=CB_RX_OK_L+CB_T_ONLINE_L ;//Turn on the left side operation received and linkage OK flag
     U_Area.RSSI_L=RfDs.RSSI_L ;
     if(RfDs.RSSI_L>=RSSI_LIMIT)
       GFlag |=GF_RSSI_GOOD_L ;
     else
       GFlag &=~GF_RSSI_GOOD_L ;
      //
	 if(U_Area.PaDat_L.PSta.InquiryPackage==0)
	 {
       RfcTimer10[RF_OP_TIMER_L].cv=0 ;
       RfcTimer10[RF_OP_TIMER_L].csr.csword=0x4000 ; //start RfcTimer10[RF_OP_TIMER_L]
	  }
     RfcTimer10[AIR_TIMER_L].csr.csword=0x4000 ;//reset the monitor timer
     RfcTimer10[AIR_TIMER_L].cv=0 ;
    }
   else if(CRC16WithSeed(RIGHT_CRC_SEED,(const uint16_t  *)U_Area.RfRxBuf0,RfDs.RxPackageLen/2)==0)
   {
     tp1=(uint16_t  *) U_Area.RfRxBuf0 ;
     tp2=(uint16_t  *) &U_Area.PaDat_R ;//P_RX_R_p ;//
     for(tmp=0 ;tmp<RfDs.RxPackageLen/2;tmp++)
     {
      *tp2++=*tp1++ ;//copy valid data to output area
      }
     if((U_Area.PaDat_R.PSta.InquiryPackage!=0)||(U_Area.PaDat_R.PSet.FixedBit!=0x0001))
       U_Area.PaDat_R.KeyCode=0x0000 ;//reset the operation status
     RfDs.RF_ACK_Count_R=RF_ACK_NUM ;
     U_Area.DatReq_R=U_Area.PaDat_R.PSta.StaW ;//
     OpCtrl |=CB_RX_OK_R+CB_T_ONLINE_R ;//Turn on the right side  operation received and linkage OK LED
	 RfDs.RSSI_D_R=Read_RSSI_01();//
     __disable_irq();//Disable IRQ
	 IsrFlag|=IFG_JF01_RSSI_OK_R;
     __enable_irq();//Enable IRQ
     U_Area.RSSI_R=RfDs.RSSI_R ;
     if(RfDs.RSSI_R>=RSSI_LIMIT)
       GFlag |=GF_RSSI_GOOD_R ;
     else
       GFlag &=~GF_RSSI_GOOD_R ;
      //
	 if(U_Area.PaDat_R.PSta.InquiryPackage==0)
	 {
       RfcTimer10[RF_OP_TIMER_R].cv=0 ;
       RfcTimer10[RF_OP_TIMER_R].csr.csword=0x4000 ; //start RfcTimer10[RF_OP_TIMER_R]
	  }
     RfcTimer10[AIR_TIMER_R].csr.csword=0x4000 ;//reset the monitor timer
     RfcTimer10[AIR_TIMER_R].cv=0 ;
     }
   else
     RfDs.RF_RxErr++ ;//record the CRC error
  }
 if((GPIOE->IDR&0x00000800)&&(RfDs.RxAttenFlag!=0x5a5a))//(L_GDO0,DCLK)the CS signal asserted
 {
   if((int8_t)JF01ReadRSSIReg()>70)
   {
     JF01WriteReg(JF01_FIFOTHR, 0x77);//Set to Rx signal anttenuation to 18dB
     RfDs.RxAttenFlag=0x5a5a ;
     RfcTimer[RF_TIMER].csr.csword=0x4000 ;
     RfcTimer[RF_TIMER].cv=0 ;
    }
  }
 if(RfDs.RxAttenFlag==0x5a5a)
 {
   if(GPIOE->IDR&0x00000800==0)//(L_GDO0,DCLK)the CS signal de-asserted
   {
     RfcTimer[RF_TIMER].csr.csword=0x0000 ;
     RfcTimer[RF_TIMER].cv=0 ;
     JF01WriteReg(JF01_FIFOTHR, 0x47);//Set to default setting,no RF attenuation
     RfDs.RxAttenFlag=0x0000 ;
    }
  }
 if(RfcTimer[RF_TIMER].csr.csbit.q==1)
 {
   RfcTimer[RF_TIMER].csr.csword=0x0000 ;
   RfcTimer[RF_TIMER].cv=0 ;
   JF01WriteReg(JF01_FIFOTHR, 0x47);//Set to default setting,no RF attenuation
   RfDs.RxAttenFlag=0x0000 ;
  }
}
//----------------------------------------------------------------------------------------
void RfDaemon_01(void)
{
  uint8_t i,sta ;
//Process JF01 Initalization operation
 switch (RfDs.JF01Init.OpSta)
 {
  case IN_SLEEP_STA :
    if((RfDs.JF01Init.OpReq&0xff00)==SET_CODE )
    {
      JF01CmdStrobe(JF01_SIDLE);//Put the chip to IDLE status
      RfDs.JF01Init.OpSta =RfDs.JF01Init.OpReq&0x00ff ;
      RfDs.JF01Init.OpReq=0x0000 ;
      JF01WriteReg(JF01_TEST2, APP_PD_p->JF01Def.test2); // Various test settings,lost in sleep status.
      JF01WriteReg(JF01_TEST1, APP_PD_p->JF01Def.test1); // Various test settings,lost in sleep status.
      JF01WriteReg(JF01_TEST0, APP_PD_p->JF01Def.test0); // Various test settings,lost in sleep status.
     }
    break ;
  case IN_RX_STA :
    if((RfDs.JF01Init.OpReq&0xff00)==SET_CODE)
    {
      JF01CmdStrobe(JF01_SIDLE);//Put the chip to IDLE status
      //Check chip status
      RfDs.JF01Init.OpSta =RfDs.JF01Init.OpReq&0x00ff ;
      RfDs.JF01Init.OpReq=0x0000 ;
      break ;
     }
    if(IsrFlag & IFG_JF01_FIFO_ALARM)
    {
      __disable_irq();//Disable IRQ
      IsrFlag &=~IFG_JF01_FIFO_ALARM ;//Clear flag
      __enable_irq();//Enable IRQ
      //---------------Begin to read out data in Rx FIFO
      EnableJF01Chip ;//nCS='0'
      while((GPIOE->IDR&0x4000)!=0)  ;//Wait for chip to ready
      SPI1->DR=JF01_RXFIFO+JF01_READ_BURST ;//Issue the address byte
      //test_p=(uint8_t *) &U_Area.Ultra_BUF ;
      while((SPI1->SR&0x0001)==0)  ;//Wait for the data be output
      sta=SPI1->DR ;//Get returned JF01 sta byte
      for(i=0 ;i<FIFO_THRESHOLD_BYTES-1 ;i++)
      {
        SPI1->DR=JF01_RXFIFO+JF01_READ_BURST ;//Dummy data
        while((SPI1->SR&0x0001)==0)  ;//Wait for the data be output
        *test_p=SPI1->DR ;//Get the data ;
        test_p++ ;
        RfDs.char_count++ ;
        if(RfDs.char_count>=96)
        {
          RfDs.char_count=0 ;
          test_p=(uint8_t *) &U_Area.Ultra_BUF ;
         }
       }
      DisableJF01Chip ;  // Set /nCS to High 
      sta=sta ;
     }
    break ;
  case IN_TX_STA :
    if((RfDs.JF01Init.OpReq&0xff00)==SET_CODE)
    {
      JF01CmdStrobe(JF01_SIDLE);//Put the chip to IDLE status
      //Check chip status
      RfDs.JF01Init.OpSta =RfDs.JF01Init.OpReq&0x00ff ;
      RfDs.JF01Init.OpReq=0x0000 ;
      break ;
     }
    break ;
  case SET_UP_RX :
    JF01WriteReg(JF01_FREQ2,Rx_01_Ch_p->freq2); // Frequency control word, high byte.
    JF01WriteReg(JF01_FREQ1,Rx_01_Ch_p->freq1); // Frequency control word, middle byte.
    JF01WriteReg(JF01_FREQ0,Rx_01_Ch_p->freq0); // Frequency control word, low byte.
//    JF01WriteReg(JF01_IOCFG0,0x00) ;// Set GDO0 to be Asserts when RX FIFO is filled at or above threshold
    JF01WriteReg(JF01_IOCFG0,0x0e) ;// Set GDO0 to be Asserts when CS(RSSI above threshold)
    JF01WriteReg(JF01_IOCFG2,0x06) ;// Set GDO2 to assert when sync word has been received
    JF01WriteReg(JF01_PKTLEN,RfDs.RxPackageLen); // Set RX Packet length.
    RfDs.JF01Init.OpSta=IN_RX_STA ;
    EnterRxMode_01() ;
    JF01CmdStrobe(JF01_SRX);//
    break ;
   
  case SET_UP_TX :
    JF01WriteReg(JF01_FREQ2,Tx_01_Ch_p->freq2); // Frequency control word, high byte.
    JF01WriteReg(JF01_FREQ1,Tx_01_Ch_p->freq1); // Frequency control word, middle byte.
    JF01WriteReg(JF01_FREQ0,Tx_01_Ch_p->freq0); // Frequency control word, low byte.
    PanelDatTxLoad_01() ;
    JF01WriteReg(JF01_IOCFG0,0x02) ;// Set GDO0 to be TX FIFO threshold ALARM signal
    JF01WriteReg(JF01_IOCFG2,0x06) ;// Set GDO2 to packet sent signal
    //Prepare and issue the Tx data
    JF01WriteReg(JF01_PKTLEN,RfDs.TxPackageLen); // Set TX Packet length.
    JF01WriteFIFO(U_Area.RfTxChBuf0,RfDs.TxPackageLen) ;
    RfDs.JF01Init.OpSta=IN_TX_STA ;
    EnterTxMode_01() ;
    JF01CmdStrobe(JF01_STX);//
    break ;
  case SET_UP_TEST :

    break ;
  case TX_TEST_STA :

    break ;
  case RX_TEST_STA :

    break ;
  case NOT_INIT_STA :
    JF01Reset();//After this chip enter IDLE status
    InitJF01Regiters() ;//Init configuration register to default value
    JF01WriteReg(JF01_MCSM1,0x0f);//Main Radio Cntrl State Machine config,CCA_MODE,RXOFF_MODE->Stay in RX,TXOFF_MODE->RX mode
    JF01WritePATable(DefPATable,PA_TAB_LEN) ;//I will only use DefPATable[0] normally
    //       JF01_B_Read(JF01_IOCFG2,(uint8_t *) U_Area.Ultra_BUF,48) ;//---MD20049HB
    SetupJF01PD() ;//Enter sleep status
    break ;
  default : ;
 }
}
//--------------------------
void FastSetupTx_01(void ) 
{
  RfDs.JF01Init.OpReq=0x0000 ;
  //Check chip status
  JF01CmdStrobe(JF01_SIDLE);//Put the chip to IDLE status
  JF01WriteReg(JF01_TEST2, APP_PD_p->JF01Def.test2); // Various test settings,lost in sleep status.
  JF01WriteReg(JF01_TEST1, APP_PD_p->JF01Def.test1); // Various test settings,lost in sleep status.
  JF01WriteReg(JF01_TEST0, APP_PD_p->JF01Def.test0); // Various test settings,lost in sleep status.
  JF01WriteReg(JF01_FREQ2,Tx_01_Ch_p->freq2); // Frequency control word, high byte.
  JF01WriteReg(JF01_FREQ1,Tx_01_Ch_p->freq1); // Frequency control word, middle byte.
  JF01WriteReg(JF01_FREQ0,Tx_01_Ch_p->freq0); // Frequency control word, low byte.
//  JF01WriteReg(JF01_FIFOTHR,APP_PD_p->JF01Def.fifothr) ;// Set TX FIFO threshold:Bytes in TX buffer=61,Bytes in RX FIFO =4
  JF01CmdStrobe(JF01_SFTX);//Flush TX FIFO
  JF01WriteReg(JF01_IOCFG0,0x02) ;// Set GDO0 to be TX FIFO threshold ALARM signal
  JF01WriteReg(JF01_IOCFG2,0x06) ;// Set GDO2 to packet sent signal
  //Prepare and issue the Tx data
  PanelDatTxLoad_01() ;
  JF01WriteReg(JF01_PKTLEN,RfDs.TxPackageLen); // Set TX Packet length.
  JF01WriteFIFO(U_Area.RfTxChBuf0,RfDs.TxPackageLen) ;
//   JF01_B_Read(JF01_MARCSTATE,(uint8_t *) &U_Area.Ultra_BUF[0],1) ;//---MD20049HB --debug
//   JF01_B_Read(JF01_TXBYTES,(uint8_t *) &U_Area.Ultra_BUF[1],1) ;//---MD20049HB	--debug
  RfDs.JF01Init.OpSta=IN_TX_STA ;
  EnterTxMode_01() ;
  JF01CmdStrobe(JF01_STX);//
}

//------------------------------------------------------------------------------------------------
void SwitchTxToRx_01(void )
{
  RfDs.JF01Init.OpReq=0x0000 ;
  //Check chip status
  //JF01CmdStrobe(JF01_SIDLE);//Put the chip to IDLE status
  JF01WriteReg(JF01_FREQ2,Rx_01_Ch_p->freq2); // Frequency control word, high byte.
  JF01WriteReg(JF01_FREQ1,Rx_01_Ch_p->freq1); // Frequency control word, middle byte.
  JF01WriteReg(JF01_FREQ0,Rx_01_Ch_p->freq0); // Frequency control word, low byte.
  //JF01WriteReg(JF01_IOCFG0,0x00) ;// Set GDO0 to be Asserts when RX FIFO is filled at or above threshold
  JF01WriteReg(JF01_IOCFG0,0x0e) ;// Set GDO0 to be Asserts when CS(RSSI above threshold)
  JF01WriteReg(JF01_IOCFG2,0x06) ;// Set GDO2 to assert when sync word has been received
  JF01WriteReg(JF01_PKTLEN,RfDs.RxPackageLen);     // Packet length.
  RfDs.char_count=0 ;
  test_p=(uint8_t *) U_Area.Ultra_BUF ;
  RfDs.JF01Init.OpSta=IN_RX_STA ;
  EnterRxMode_01() ;
  JF01CmdStrobe(JF01_SRX);//  
}

//------------------------------------------------------------------------------------------------

int16_t Read_RSSI_01(void)
{
 uint8_t ctmp ;
 int16_t  stmp ;
// ctmp=JF01ReadStaReg(JF01_RSSI) ;
 ctmp=(uint8_t) U_Area.RfRxChBuf0[RfDs.RxPackageLen] ;//Must to read append RSSI value in receive buffer
 if(ctmp>128)
 {
   stmp=((uint16_t)ctmp-256)>>1 ;
   stmp-=RSSI_OFFSET ;
 }
 else
 {
   stmp=(ctmp>>1)-RSSI_OFFSET ;
 }
 return stmp ;
}

/**********************************************************************************/
void EnterTxMode_01(void)
{
  __disable_irq();//Disable IRQ
  RfDs.RF_State=TX_STATUS ;//Set to TX state
  RfDs.RF_RxState=RX_NO_INIT_IDLE ;//
  GPIOB->BSRR=0x00600080 ;//PB.7->Tx_EN='1' Open PA output path, PB.5,6->Rx_EN='0' shut off LNA
  RfDs.RF_TxState=TX_READY ;//Set to sending data status
  EXTI->RTSR |= 0x00000400 ;//PE.10-->GDO2(DIO) generate interrupt on low-to-high transition(GDO2-->sync be sent out)
  EXTI->FTSR &= 0x003ff3ff ;//disable PE.10-->GDO2 Falling trigger
  EXTI->PR    = 0x00000400 ;//clear pending ext interrupt line PE.10-->GDO2
  EXTI->IMR  |= 0x00000400 ;//Allow PE.10 to generate interrupt
  __enable_irq();//Enable IRQ
}
__inline void EnterRxMode_01(void)
{
  __disable_irq();//Disable IRQ
  RfDs.RF_State=RX_STATUS ;//Set to RX state
  RfDs.RF_RxState=RX_READY ;//search for a vaild preamble & receiving DP status
  RfDs.RF_TxState=TX_IDLE ;//
  GPIOB->BSRR=0x00800060 ;//PB.7->Tx_EN='0' Switch off RF output (PA) path,PB.5,6->Rx_EN='1' Switch on RF LNA ,enter receive status
  EXTI->RTSR |= 0x00000400 ;//PE.10-->GDO2(DIO) generate interrupt on low-to-high transition((sync have been received))
  EXTI->FTSR &= 0x003ff3ff ;//disable PE.10-->GDO2 Falling trigger
  EXTI->PR    = 0x00000400 ;//clear pending ext interrupt line PE.10-->GDO2
  EXTI->IMR  |= 0x00000400 ;//Allow PE.10 to generate interrupt
  __enable_irq();//Enable IRQ
}
void SetupJF01PD(void)
{
  __disable_irq();//Disable IRQ
  GPIOB->BSRRH=0x00e0 ;//Set PB.7->Tx_EN='0',PB.5,6->Rx_EN='0'
  RfDs.RF_State=RF_IDLE_STATUS ;
  RfDs.RF_TxState=TX_IDLE ;//
  RfDs.RF_RxState=RX_NO_INIT_IDLE ;//
  JF01CmdStrobe(JF01_SPWD);//Set JF01 to power down(sleep mode)
  RfDs.JF01Init.OpReq=0 ;
  RfDs.JF01Init.OpSta=IN_SLEEP_STA ;//Enter sleep status
  EXTI->PR   =0x00000c00 ;//clear current pending interrupt flag #############&&&&&&&&&&&&&&&
  EXTI->IMR &=0x003ff3ff ;//disable (GDO2,GDO0) signal to generate interrupt
  __enable_irq();//Enable IRQ
}

//----------------------------------------------------------------------------------------

void JF01Reset(void)
{
  uint8_t sta ;
  //To reset the JF01
  GPIOE->BSRR=0x80006000 ;//SCLK=1,MOSI,MISO=0
  GPIOE->MODER&=0x03ffffff ;//
  GPIOE->MODER|=SPI1Pins_GPIO_CONFIG ;//Set PE.13,14,15(SCLK,MISO,MOSI) to GPIO status
  WaitSomeTime(TIME_20uS_V) ;
  EnableJF01Chip ;//Set JF01 nCS Pin to low(PE.12-PSEL)
  WaitSomeTime(TIME_40uS_V) ;
  DisableJF01Chip  ;//JF01 nCS Pin to high,/nCS=1,MISO=0,SCLK=1
  WaitSomeTime(TIME_40uS_V) ;
  GPIOE->MODER&=0x03ffffff ;
  GPIOE->MODER|=SPI1Pins_SPI_CONFIG ;//Set PE.13,14,15(SCLK,MISO,MOSI) to SPI status
  EnableJF01Chip ;//Set nCS Pin to '0'
  while((GPIOE->IDR&0x4000)!=0)   ;//Wait for JF01 SO to low(ready for SPI operation)
  SPI1->DR=JF01_SRES ;//Issue a command strobe SRES -reset the chip
  while((SPI1->SR&0x0001)==0);//Wait for the data be output
  sta=SPI1->DR ;
  while((GPIOE->IDR&0x4000)!=0) ;//Wait for JF01 SO to low(ready for SPI operation)
  DisableJF01Chip ;//Set nCS Pin to '1'
  sta=sta ;
}

uint8_t JF01_B_Read(uint8_t Add, uint8_t *Data_p,uint8_t Len)
{
  uint8_t i,sta ;
  EnableJF01Chip ;//nCS='0'
  while((SPI1->SR&0x0002)==0x0000) ;//Wait the Tx FIFO to empty
  while(SPI1->SR&0x0001)
  { //empty the receive FIFO
    sta=(uint8_t) SPI1->DR ;//
   } 
  while((GPIOE->IDR&0x4000)!=0)  ;//Wait for chip to ready
  __disable_irq();//Disable all interrupt ,enable __enable_irq()
  SPI1->DR=Add+JF01_READ_BURST ;//Issue the address byte
  while((SPI1->SR&0x0001)==0)  ;//Wait for the data be output
  sta=SPI1->DR ;//Get returned JF01 sta byte
  for(i=0 ;i<Len ;i++)
  {
    SPI1->DR=0x00 ;//Dummy data
    while((SPI1->SR&0x0001)==0)  ;//Wait for the data be output
    *Data_p++=SPI1->DR ;//Get the data
   }
  DisableJF01Chip  ;//nCS='1'
  __enable_irq();
  return sta ;
}

uint8_t JF01ReadReg(uint8_t Add, uint8_t *Data_p)
{
  uint8_t sta ;
  EnableJF01Chip ;//nCS='0'
  while((SPI1->SR&0x0002)==0x0000) ;//Wait the Tx FIFO to empty
  while(SPI1->SR&0x0001)
  { //empty the receive FIFO
    sta=(uint8_t) SPI1->DR ;//
  } 
  while((GPIOE->IDR&0x4000)!=0)  ;//Wait for chip to ready
  __disable_irq();//Disable all interrupt
  SPI1->DR=Add+JF01_READ_SINGLE ;//Issue the address byte
  while((SPI1->SR&0x0001)==0) ;//Wait for the data be output
  sta=SPI1->DR ;//Get returned JF01 sta byte
  SPI1->DR=0x00 ;//Dummy data
  while((SPI1->SR&0x0001)==0)  ;//Wait for the data be output
  *Data_p=SPI1->DR ;//Get the data
  DisableJF01Chip ;  // Set /nCS to High 
  __enable_irq();
  return sta ;
}

uint8_t  JF01ReadRSSIReg(void)
{
  uint8_t sta ;
  EnableJF01Chip ;//nCS='0'
  while((GPIOE->IDR&0x4000)!=0)  ;//Wait for chip to ready
  __disable_irq();//Disable all interrupt
  SPI1->DR=JF01_RSSI+JF01_READ_BURST ;//Issue the address byte
  while((SPI1->SR&0x0001)==0)  ;//Wait for the data be output
  sta=SPI1->DR ;//Get the JF01 status byte
  SPI1->DR=0x00 ;//Issue the dummy byte
  while((SPI1->SR&0x0001)==0)  ;//Wait for the data be output
  sta=SPI1->DR ;//Get the JF01 status register content
  DisableJF01Chip ;  // Set /nCS to High 
  __enable_irq();
  return sta ;
}

uint8_t JF01ReadStaReg(uint8_t Add)
{
  uint8_t sta ;
  EnableJF01Chip ;//nCS='0'
  while((SPI1->SR&0x0002)==0x0000) ;//Wait the Tx FIFO to empty
  while(SPI1->SR&0x0001)
  { //empty the receive FIFO
    sta=(uint8_t) SPI1->DR ;//
  } 
  while((GPIOE->IDR&0x4000)!=0)  ;//Wait for chip to ready
  __disable_irq();//Disable all interrupt
  SPI1->DR=Add+JF01_READ_BURST ;//Issue the address byte
  while((SPI1->SR&0x0001)==0)  ;//Wait for the data be output
  sta=SPI1->DR ;//Get the JF01 status byte
  SPI1->DR=0x00 ;//Issue the dummy byte
  while((SPI1->SR&0x0001)==0)  ;//Wait for the data be output
  sta=SPI1->DR ;//Get the JF01 status register content
  DisableJF01Chip ;  // Set /nCS to High 
  __enable_irq();
  return sta ;
}

uint8_t JF01GetRxSta(void)
{
  uint8_t pre_sta,sta ;
  EnableJF01Chip ;//nCS='0'
  while((SPI1->SR&0x0002)==0x0000) ;//Wait the Tx FIFO to empty
  while(SPI1->SR&0x0001)
  { //empty the receive FIFO
    pre_sta=(uint8_t) SPI1->DR ;//
   } 
  while((GPIOE->IDR&0x4000)!=0)  ;//Wait for chip to ready
  __disable_irq();//Disable all interrupt
  SPI1->DR=JF01_SNOP+JF01_READ_SINGLE ;////Issue Nop operation cmd
  while((SPI1->SR&0x0001)==0)  ;//Wait for the data be output
  sta=SPI1->DR ;//Get the JF01 status byte
  do{
      pre_sta=sta ;
      SPI1->DR=JF01_SNOP+JF01_READ_SINGLE ;//Issue Nop operation cmd
      while((SPI1->SR&0x0001)==0)  ;//Wait for the data be output
      sta=SPI1->DR ;//Get the JF01 status byte
     } while(pre_sta!=sta) ;
  DisableJF01Chip ;  // Set /nCS to High 
  __enable_irq();
  return sta ;
}

uint8_t JF01GetTxSta(void)
{
  uint8_t pre_sta,sta ;
  EnableJF01Chip ;//nCS='0'
  while((SPI1->SR&0x0002)==0x0000) ;//Wait the Tx FIFO to empty
  while(SPI1->SR&0x0001)
  { //empty the receive FIFO
    pre_sta=(uint8_t) SPI1->DR ;//
   } 
  while((GPIOE->IDR&0x4000)!=0)  ;//Wait for chip to ready
  __disable_irq();//Disable all interrupt
  SPI1->DR=JF01_SNOP ;////Issue Nop operation cmd
  while((SPI1->SR&0x0001)==0)  ;//Wait for the data be output
  sta=SPI1->DR ;//Get the JF01 status byte
  do{
      pre_sta=sta ;
      SPI1->DR=JF01_SNOP ;//Issue Nop operation cmd
      while((SPI1->SR&0x0001)==0)  ;//Wait for the data be output
      sta=SPI1->DR ;//Get the JF01 status byte
     } while(pre_sta!=sta) ;
  DisableJF01Chip ;  // Set /nCS to High 
  __enable_irq();
  return sta ;
}

uint8_t JF01_B_Write(uint8_t Add, uint8_t *Data_p,uint8_t Len)
{
  uint8_t i,sta,tmp ;
  tmp=0 ;
  EnableJF01Chip ;//nCS='0'
  while((SPI1->SR&0x0002)==0x0000) ;//Wait the Tx FIFO to empty
  while(SPI1->SR&0x0001)
  { //empty the receive FIFO
    sta=(uint8_t) SPI1->DR ;//
   } 
  while((GPIOE->IDR&0x4000)!=0)  ;//Wait for chip to ready
  __disable_irq();//Disable all interrupt
  SPI1->DR=Add+JF01_WRITE_BURST ;//Issue the address byte
  while((SPI1->SR&0x0001)==0)  ;//Wait for the data be output
  sta=SPI1->DR ;//Get returned JF01 sta byte
  for(i=0 ;i<Len ;i++)
  {
    SPI1->DR=*Data_p++ ;//Issue the write data
    while((SPI1->SR&0x0001)==0)  ;//Wait for the data be output
    tmp=SPI1->DR ;//Dummy read
   }
  DisableJF01Chip ;  // Set /nCS to High 
  __enable_irq();
  tmp=tmp ;
  return sta ;
}

void JF01WriteReg(uint8_t Add, uint8_t Data)
{
  uint8_t sta ;
  EnableJF01Chip ;//nCS='0'
  while((SPI1->SR&0x0002)==0x0000) ;//Wait the Tx FIFO to empty
  while(SPI1->SR&0x0001)
  { //empty the receive FIFO
    sta=(uint8_t) SPI1->DR ;//
   } 
  while((GPIOE->IDR&0x4000)!=0)  ;//Wait for chip to ready
  __disable_irq();//Disable all interrupt
  SPI1->DR=Add ;//Issue the address byte
  while((SPI1->SR&0x0001)==0)  ;//Wait for the data be output
  sta=SPI1->DR ;//Get returned JF01 sta byte
  SPI1->DR=Data ;//Issue the write data
  while((SPI1->SR&0x0001)==0)  ;//Wait for the data be output
  sta=SPI1->DR ;//Get returned JF01 sta byte
  DisableJF01Chip ;  // Set /nCS to High 
  __enable_irq();
  sta=sta ;
}

uint8_t JF01CmdStrobe(uint8_t cmd)
{
  uint8_t sta ;
  EnableJF01Chip ;//nCS='0'
  while((SPI1->SR&0x0002)==0x0000) ;//Wait the Tx FIFO to empty
  while(SPI1->SR&0x0001)
  { //empty the receive FIFO
    sta=(uint8_t) SPI1->DR ;//
   } 
  while((GPIOE->IDR&0x4000)!=0)  ;//Wait for chip to ready
  __disable_irq();//Disable all interrupt
  SPI1->DR=cmd ;//Issue the address byte
  while((SPI1->SR&0x0001)==0)  ;//Wait for the data be output
  sta=SPI1->DR ;//Get the JF01 status byte
  DisableJF01Chip ;  // Set /nCS to High 
  __enable_irq();
  return sta ;
}

uint8_t JF01ReadFIFO(uint8_t *Data_p ,uint8_t Len)
{
  uint8_t i,sta ;
  EnableJF01Chip ;//nCS='0'
  while((SPI1->SR&0x0002)==0x0000) ;//Wait the Tx FIFO to empty
  while(SPI1->SR&0x0001)
  { //empty the receive FIFO
    sta=(uint8_t) SPI1->DR ;//
   } 
  while((GPIOE->IDR&0x4000)!=0)  ;//Wait for chip to ready
  __disable_irq();//Disable all interrupt
  SPI1->DR=JF01_RXFIFO+JF01_READ_BURST ;//Issue the address byte
  while((SPI1->SR&0x0001)==0)  ;//Wait for the data be output
  sta=SPI1->DR ;//Get returned JF01 sta byte
  for(i=0 ;i<Len ;i++)
  {
    SPI1->DR=JF01_RXFIFO+JF01_READ_BURST ;//Dummy data
    while((SPI1->SR&0x0001)==0)  ;//Wait for the data be output
    *Data_p++=SPI1->DR ;//Get the data
   }
  DisableJF01Chip ;  // Set /nCS to High 
  __enable_irq();
  return sta ;
}

uint8_t JF01WriteFIFO(uint8_t *Data_p ,uint8_t Len)
{
  uint8_t i,sta ;
  EnableJF01Chip ;//nCS='0'
  while((SPI1->SR&0x0002)==0x0000) ;//Wait the Tx FIFO to empty
  while(SPI1->SR&0x0001)
  { //empty the receive FIFO
    sta=(uint8_t) SPI1->DR ;//
   } 
  while((GPIOE->IDR&0x4000)!=0)  ;//Wait for chip to ready
  __disable_irq();//Disable all interrupt
  SPI1->DR=JF01_TXFIFO+JF01_WRITE_BURST ;//Issue the address byte
  while((SPI1->SR&0x0001)==0)  ;//Wait for the data be output
  sta=SPI1->DR ;//Get returned JF01 sta byte
  for(i=0 ;i<Len ;i++)
  {
    SPI1->DR=*Data_p++ ;//Write the data
    while((SPI1->SR&0x0001)==0)  ;//Wait for the data be output
    sta=SPI1->DR ;//dummy read
   }
  DisableJF01Chip ;  // Set /nCS to High 
  __enable_irq();
  return sta ;
}

uint8_t JF01ReadPATable(uint8_t *Data_p ,uint8_t Len)
{
  uint8_t i,sta ;
  EnableJF01Chip ;//nCS='0'
  while((SPI1->SR&0x0002)==0x0000) ;//Wait the Tx FIFO to empty
  while(SPI1->SR&0x0001)
  { //empty the receive FIFO
    sta=(uint8_t) SPI1->DR ;//
   } 
  while((GPIOE->IDR&0x4000)!=0)  ;//Wait for chip to ready
  __disable_irq();//Disable all interrupt
  SPI1->DR=JF01_PATABLE+JF01_READ_BURST ;//Issue the address byte
  while((SPI1->SR&0x0001)==0)  ;//Wait for the data be output
  sta=SPI1->DR ;//Get returned JF01 sta byte
  for(i=0 ;i<Len ;i++)
  {
    SPI1->DR=JF01_PATABLE+JF01_READ_BURST ;//Dummy data
    while((SPI1->SR&0x0001)==0)  ;//Wait for the data be output
    *Data_p++=SPI1->DR ;//Get the data
   }
  DisableJF01Chip ;  // Set /nCS to High 
  __enable_irq();
  return sta ;
}

uint8_t JF01WritePATable(uint8_t *Data_p ,uint8_t Len)
{
  uint8_t i,sta,tmp ;
  tmp=0;
  EnableJF01Chip ;//nCS='0'
  while((SPI1->SR&0x0002)==0x0000) ;//Wait the Tx FIFO to empty
  while(SPI1->SR&0x0001)
  { //empty the receive FIFO
    sta=(uint8_t) SPI1->DR ;//
   } 
  while((GPIOE->IDR&0x4000)!=0)  ;//Wait for chip to ready
  __disable_irq();//Disable all interrupt
  SPI1->DR=JF01_PATABLE+JF01_WRITE_BURST ;//Issue the address byte
  while((SPI1->SR&0x0001)==0)  ;//Wait for the data be output
  sta=SPI1->DR ;//Get returned JF01 sta byte
  for(i=0 ;i<Len ;i++)
  {
    SPI1->DR=*Data_p++ ;//Write the data
    while((SPI1->SR&0x0001)==0)  ;//Wait for the data be output
    tmp=SPI1->DR ;//dummy read
   }
  DisableJF01Chip ;  // Set /nCS to High 
  __enable_irq();
  tmp=tmp ;
  return sta ;
}
//-------------------------------------------------
uint8_t ReadJF01FSCal(uint8_t *Data_p)
{//read back he Frequency synthesizer calibration result
  return(JF01_B_Read(JF01_FSCAL3,Data_p,3)) ;
}

//-------------------------------------------------
void InitJF01Regiters(void)
{
  JF01WriteReg(JF01_FSCTRL1,  APP_PD_p->JF01Def.fsctrl1);    // Frequency synthesizer control.
  JF01WriteReg(JF01_FSCTRL0,  APP_PD_p->JF01Def.fsctrl0);    // Frequency synthesizer control.
  JF01WriteReg(JF01_FREQ2,    Tx_01_Ch_p->freq2);      // Frequency control word, high byte.
  JF01WriteReg(JF01_FREQ1,    Tx_01_Ch_p->freq1);      // Frequency control word, middle byte.
  JF01WriteReg(JF01_FREQ0,    Tx_01_Ch_p->freq0);      // Frequency control word, low byte.
  JF01WriteReg(JF01_MDMCFG4,  APP_PD_p->JF01Def.mdmcfg4);    // Modem configuration.
  JF01WriteReg(JF01_MDMCFG3,  APP_PD_p->JF01Def.mdmcfg3);    // Modem configuration.
  JF01WriteReg(JF01_MDMCFG2,  APP_PD_p->JF01Def.mdmcfg2);    // Modem configuration.
  JF01WriteReg(JF01_MDMCFG1,  APP_PD_p->JF01Def.mdmcfg1);    // Modem configuration.
  JF01WriteReg(JF01_MDMCFG0,  APP_PD_p->JF01Def.mdmcfg0);    // Modem configuration.
  JF01WriteReg(JF01_CHANNR,   APP_PD_p->JF01Def.channr);     // Channel number.
  JF01WriteReg(JF01_DEVIATN,  APP_PD_p->JF01Def.deviatn);    // Modem deviation setting (when FSK modulation is enabled).
  JF01WriteReg(JF01_FREND1,   APP_PD_p->JF01Def.frend1);     // Front end RX configuration.
  JF01WriteReg(JF01_FREND0,   APP_PD_p->JF01Def.frend0);     // Front end RX configuration.
  JF01WriteReg(JF01_MCSM0,    APP_PD_p->JF01Def.mcsm0);      // Main Radio Control State Machine configuration.
  JF01WriteReg(JF01_FOCCFG,   APP_PD_p->JF01Def.foccfg);     // Frequency Offset Compensation Configuration.
  JF01WriteReg(JF01_BSCFG,    APP_PD_p->JF01Def.bscfg);      // Bit synchronization Configuration.
  JF01WriteReg(JF01_AGCCTRL2, APP_PD_p->JF01Def.agcctrl2);   // AGC control.
  JF01WriteReg(JF01_AGCCTRL1, APP_PD_p->JF01Def.agcctrl1);   // AGC control.
  JF01WriteReg(JF01_AGCCTRL0, APP_PD_p->JF01Def.agcctrl0);   // AGC control.
  JF01WriteReg(JF01_FSCAL3,   APP_PD_p->JF01Def.fscal3);     // Frequency synthesizer calibration.
  JF01WriteReg(JF01_FSCAL2,   APP_PD_p->JF01Def.fscal2);     // Frequency synthesizer calibration.
  JF01WriteReg(JF01_FSCAL1,   APP_PD_p->JF01Def.fscal1);     // Frequency synthesizer calibration.
  JF01WriteReg(JF01_FSCAL0,   APP_PD_p->JF01Def.fscal0);     // Frequency synthesizer calibration.
  JF01WriteReg(JF01_FSTEST,   APP_PD_p->JF01Def.fstest);     // Frequency synthesizer calibration.
  JF01WriteReg(JF01_FIFOTHR,  APP_PD_p->JF01Def.fifothr);    // Set TX FIFO threshold:Bytes in TX buffer=61,Bytes in RX FIFO =4
  JF01WriteReg(JF01_IOCFG2,   APP_PD_p->JF01Def.iocfg2);     // GDO2 output pin configuration.
  JF01WriteReg(JF01_IOCFG0,   APP_PD_p->JF01Def.iocfg0);     // GDO0 output pin configuration.
  JF01WriteReg(JF01_MCSM1,    APP_PD_p->JF01Def.mcsm1) ;     // CCA mode=Always,RXOFF_MODE=IDLE,TXOFF_MODE=Switch to RX status
  JF01WriteReg(JF01_SYNC1,    APP_PD_p->JF01Def.sync1) ;     //0xcc
  JF01WriteReg(JF01_SYNC0,    APP_PD_p->JF01Def.sync0) ;     //0x33
  JF01WriteReg(JF01_PKTCTRL1, APP_PD_p->JF01Def.pktctrl1);   // Packet automation control.
  JF01WriteReg(JF01_PKTCTRL0, APP_PD_p->JF01Def.pktctrl0);   // Packet automation control.
//JF01WriteReg(JF01_ADDR, APP_PD_p->JF01Def.addr);  // Device address.
}

//---------------------------------------------------------------------------------------
/*Init SPI1 interface to access JF01 @4Mbps ,8 bits character,use poll mode
  And set proper run condition(varible init)
*/
void InitSPI1_01(void)
{
 /* Enable SPI1  clocks */
  RCC_APB2PeriphClockCmd(RCC_APB2Periph_SPI1 , ENABLE);
  RCC_APB2PeriphResetCmd(RCC_APB2Periph_SPI1 , ENABLE);  /* Reset SPI1 First */
  RCC_APB2PeriphResetCmd(RCC_APB2Periph_SPI1 , DISABLE); /*SPI1 Out of reset  status */
 /*SPI interface to access RF chip @ 4Mbps ,8 bits character,use poll mode*/
  GPIOE->MODER&=0x00ffffff ;
  GPIOE->MODER|=0xa9000000 ;//set SCLK,MOSI to AF OUT mode,MISO to Input mode,bit12-NSS to GPIO output mode
  SPI1->CR1 = 0x030c ;//0x0b04 nSS internal mode 1,Master Mode,16 bit data length, disabled,CPHA=0,CPOL=0
                      //8MHz APB2 clock(8MHz CPU)/4=2 Mbps(0x0b04)
  SPI1->CR2  =0x0000 ;//No interrupt,No DMA
 /* Enable SPI1 */
  SPI1->CR1 |=0x0040 ;
//---------------------------
}

//-------------------------------
void  EXTI15_10_ISR_01(void)
{
 if((EXTI->PR)&0x00000400)
 {//EXTI Line 10 (PE.10) interrupt ,GDO2 interrupt(Named "DIO")
   EXTI->PR=0x00000400 ;//Clear the interrupt pending interrupt flag #############&&&&&&&&&&&&&&&
   switch (RfDs.RF_State)
   {
    case TX_STATUS:  //TX_STATE
         switch (RfDs.RF_TxState)
         {
           case TX_IDLE :
                IsrFlag |=IFG_JF01_INT_ERR ;
                break ;
           case TX_FINISHED : 
                IsrFlag |=IFG_JF01_INT_ERR ;
                break ;
           case TX_SENDING :
                RfDs.RF_TxPackage++ ;
                RfDs.RF_TxState=TX_FINISHED ;//switch to TX finish status
                break ;
           case TX_READY :
                EXTI->FTSR |= 0x00000400 ;//Switch the PE.10(GDO2(DIO)generate interrupt on high-to-low transition(whole package be sent out)
				EXTI->RTSR &= 0x003ffbff ;//
                EXTI->PR=0x00000400 ;//Clear the interrupt pending interrupt flag #############&&&&&&&&&&&&&&&
                RfDs.RF_TxState=TX_SENDING ;//switch to TX data package status
				SetStartAdcFlag ;//to check battery output voltage
                IsrFlag |=IFG_JF01_TX_SYNC ;
                break ;
           case TX_RF_TEST :
                IsrFlag |=IFG_JF01_INT_ERR ;
                break ;
           default :
                RfDs.RF_TxState=TX_IDLE ;
          }
         break;
    case RX_STATUS :  //RX_STATE
         switch (RfDs.RF_RxState)
         {
           case RX_NO_INIT_IDLE :
                IsrFlag |=IFG_JF01_INT_ERR ;
                break ;
           case RX_DP_FINISHED : 
                IsrFlag |=IFG_JF01_INT_ERR ;
                break ;
           case RX_RECEIVING :
                RfDs.RF_RxPackage++ ;
                IsrFlag |=IFG_JF01_RX_FINISHED ;
                if((APP_PA.work_mode&DEVICE_MODE_MASK)==FYS30_01_MODE)
                {//This device be configured to work in FYS30 shearer mode
                  EXTI->RTSR |= 0x00000400 ;//PE.10-->GDO2(DIO) generate interrupt on low-to-high transition((sync have been received))
                  EXTI->FTSR &= 0x003ff3ff ;//disable PE.10-->GDO2 Falling trigger
                  EXTI->PR    = 0x00000400 ;//clear pending ext interrupt line PE.10-->GDO2
				  RfDs.RF_RxState=RX_READY;//enter to search new data frame status
                 }
                else
                {//This device be configured to work in FYF35/FYF25 mode
                  RfDs.RF_RxState=RX_DP_FINISHED;//Switch status to wait for upper level software operation
                 }
                break ;
           case RX_READY :
                EXTI->FTSR |= 0x00000400 ;//Switch the PE.10(GDO2(DIO)generate interrupt on high-to-low transition(whole package be received)
				EXTI->RTSR &= 0x003ffbff ;//
                RfDs.RF_RxState=RX_RECEIVING ;
                IsrFlag |=IFG_JF01_RX_SYNC ;
                EXTI->PR=0x00000400 ;//Clear the interrupt pending interrupt flag #############&&&&&&&&&&&&&&&
                break ;
           case RX_RF_TEST :
                IsrFlag |=IFG_JF01_INT_ERR ;
                break ;
           default :
                RfDs.RF_RxState=RX_NO_INIT_IDLE ;
          }
		 break ;
    case RF_IDLE_STATUS :

         break ;
	default :{
	     RfDs.RF_State=RF_IDLE_STATUS ;//=0x0000
         EXTI->IMR &=0xfffffbff ;//disable (GDO2) signal to generate interrupt in current state
	    }
	}
  }
 else if((EXTI->PR)&0x00000800)
 {//EXTI Line 11 (PE.11) interrupt , GDO0 interrupt(Named "DCLK")
   EXTI->PR=0x00000800 ;//Clear the interrupt pending interrupt flag #############&&&&&&&&&&&&&&&
   switch (RfDs.RF_State)
   {
    case TX_STATUS:  //TX_STATE
         switch (RfDs.RF_TxState)
         {
           case TX_IDLE :
                IsrFlag |=IFG_JF01_INT_ERR ;
                break ;
           case TX_FINISHED : 
                IsrFlag |=IFG_JF01_INT_ERR ;
                break ;
           case TX_SENDING :
                IsrFlag |=IFG_JF01_FIFO_ALARM ;//Set JF01 FIFO alarm flag
                break ;
           case TX_READY :
                IsrFlag |=IFG_JF01_INT_ERR ;
                break ;
           case TX_RF_TEST :
				SetStartAdcFlag ;//to check battery output voltage
                IsrFlag |=IFG_JF01_FIFO_ALARM ;//Set JF01 FIFO alarm flag
                break ;
           default :
                RfDs.RF_TxState=TX_IDLE ;
          }
         break;
    case RX_STATUS :  //RX_STATE
         switch (RfDs.RF_RxState)
         {
           case RX_NO_INIT_IDLE :
                IsrFlag |=IFG_JF01_INT_ERR ;
                break ;
           case RX_DP_FINISHED : 
                IsrFlag |=IFG_JF01_INT_ERR ;
                break ;
           case RX_RECEIVING :
                IsrFlag |=IFG_JF01_FIFO_ALARM ;//Set JF01 FIFO alarm flag
                break ;
           case RX_READY :
                IsrFlag |=IFG_JF01_INT_ERR ;
                break ;
           case RX_RF_TEST :
				SetStartAdcFlag ;//to check battery output voltage
                IsrFlag |=IFG_JF01_FIFO_ALARM ;//Set JF01 FIFO alarm flag
                break ;
           default :
                RfDs.RF_RxState=RX_NO_INIT_IDLE ;
          }
		 break ;
    case RF_IDLE_STATUS :

         break ;
	default :{
	     RfDs.RF_State=RF_IDLE_STATUS ;//=0x0000
         EXTI->IMR &=0xfffff7ff ;//disable (DCLK->GDO0) signal to generate interrupt in current state
	    }
	}  
  }			
}
//---------------
