/******Timer function define*****/
#include "Timer.h"
extern void  InitUartTimer(void) ;//Init the system timer for UART operation
extern __inline void  UartTimerFun(void) ;//This function must be called in system tick ISR for proper uart(modbus RTU)

extern void  InitRfcTimer(void) ;//Init the system timer for CAN_bx operation
extern void  InitRfcTimer10(void) ;//Init the user mode timer for CAN device monitor operation
extern __inline void  RfcTimer10Fun(void) ;//This function must be called ervery 10 ms  for proper CAN_bx(RTM)
extern __inline void  RfcTimerFun(void) ;//This function must be called in system tick ISR for proper CAN_bx(RTM)

//---------------
extern void (*Proc10mS_fp)(void) ;//Define function pointer for periodical called timer function--10mS
extern void (*Proc100mS_fp)(void) ;
extern void (*Proc1S_fp)(void) ;
extern void (*Proc1H_fp)(void) ;

//--------------Timer related varibale  define

tim_count   Timer[TIM_NUM]  ;

tim_count   Timer10[TIM10ms_NUM]  ;//user defined timer(1 tick=10ms)

//tim_count   Timer1k[TIM1000_NUM] ;//user defined timer(1 tick=1000ms)

uint16_t    tenth_tick=0   ;//used to generate midium timer (1 tick=100 ms)
uint16_t    hundred_tick=0 ;//
uint16_t    second_tick=0  ;/*used to generate second timer */
uint16_t    hour_tick=0    ; /*used to generate hour timer  */
int16_t    Timer10_flag=0 ;//global flag of timer10
int16_t    Timer1k_flag=0 ;//global flag of timer in second
//--------------

void InitTimer(void)
{
  uint16_t i ;
  hour_tick=0 ;
  for(i=0;i<TIM10ms_NUM ;i++)
  {
    Timer10[i].pv=Timer10[i].cv=Timer10[i].csr.csword=0 ;
    }
  /*
  for(i=0;i<TIM1000_NUM ;i++)
  {
    Timer1k[i].pv=Timer1k[i].cv=Timer1k[i].csr.csword=0 ;
    }
   */
  InitRfcTimer10() ;//Init timer used for RF device monitor
}
void TimerProc(void)
{
  uint16_t  i ;
  if(Timer10_flag==1)
   {
     Timer10_flag=0 ;
     for(i=0;i<TIM10ms_NUM;i++)
     {
       if(Timer10[i].csr.csbit.en==1)
        {
          if(Timer10[i].cv<65535) ++(Timer10[i].cv) ;
          if(Timer10[i].cv>=Timer10[i].pv)
          {
            Timer10[i].csr.csbit.q=1 ;
           }
         }
      }
	 //--------------------------
	 RfcTimer10Fun() ;//For proper can device monitor
     //-------------------- 
     (*Proc10mS_fp)() ;//Timed process at freqency=100Hz
     hundred_tick++ ;
     if(hundred_tick>=10)
     {
       hundred_tick=0 ;
       (*Proc100mS_fp)() ;//Timed process at freqency=10Hz
     }
    }
  if(Timer1k_flag==1)
   {
     Timer1k_flag=0 ;
     hour_tick++ ;
	 /*
     for(i=0;i<TIM1000_NUM;i++)
     {
       if(Timer1k[i].csr.csbit.en==1)
       {
         if(Timer1k[i].cv<65535) ++(Timer1k[i].cv) ;
         if(Timer1k[i].cv>=Timer1k[i].pv)
         {
           Timer1k[i].csr.csbit.q=1 ;
           }
        }
       }
	  */
     (*Proc1S_fp)() ;//process task at frequency=1 Hz
     if(hour_tick>=3600)
     {
       hour_tick=0;
       (*Proc1H_fp)() ;//process task 1 hour interval
      }
   }
}
void InitSysTimer(void)
{
  uint16_t i ;
  tenth_tick=0 ;
  second_tick=0 ;
  Timer10_flag=0 ;
  Timer1k_flag=0 ;
  for(i=0;i<TIM_NUM ;i++)
  {
    Timer[i].pv=0 ;
    Timer[i].cv=0 ;
    Timer[i].csr.csword=0 ;
  }
  InitUartTimer() ;//For timers used in uart function
  InitRfcTimer() ;//For timers used in CAN function
}

void TBM_ISR(void) 
{ //run time about [11uS-25uS]@8MHz
   uint16_t  i ;
   tenth_tick++ ;
   if(tenth_tick>=10)
   {
     tenth_tick=0 ;
     Timer10_flag=1 ;
   }
   second_tick++ ;
   if(second_tick>=1000)
   {
      second_tick=0;
      Timer1k_flag=1 ;
    }
   for(i=0;i<TIM_NUM;i++)
   {
     if(Timer[i].csr.csbit.en==1)
     {
       if(Timer[i].cv<65535) ++(Timer[i].cv) ;
       if(Timer[i].cv>=Timer[i].pv)
       {
          Timer[i].csr.csbit.q=1 ;
         }
      }
    }
   UartTimerFun() ;//For proper UART function to run
   RfcTimerFun() ;//For proper CAN function to run
}

