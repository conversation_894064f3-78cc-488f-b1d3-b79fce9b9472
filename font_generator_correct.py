#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
正确的字库生成工具
根据实际格式：前12字节(上2/3部分8行) + 后12字节(下1/3部分4行)
"""

import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox, filedialog
from PIL import Image, ImageDraw, ImageFont
import numpy as np

class FontGeneratorCorrect:
    def __init__(self, root):
        self.root = root
        self.root.title("正确格式字库生成工具 v2.0")
        self.root.geometry("1000x800")
        
        self.font_path = None
        self.create_widgets()
    
    def create_widgets(self):
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 标题
        title_label = ttk.Label(main_frame, text="正确格式字库生成工具", 
                               font=("SimSun", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))
        
        # 字体选择
        font_frame = ttk.LabelFrame(main_frame, text="字体设置", padding="10")
        font_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Button(font_frame, text="选择字体文件", command=self.select_font).pack(side=tk.LEFT, padx=(0, 10))
        self.font_label = ttk.Label(font_frame, text="未选择字体")
        self.font_label.pack(side=tk.LEFT)
        
        # 输入区域
        input_frame = ttk.LabelFrame(main_frame, text="字符输入", padding="10")
        input_frame.grid(row=2, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 5))
        
        ttk.Label(input_frame, text="输入汉字:").grid(row=0, column=0, sticky=tk.W, pady=(0, 5))
        self.char_entry = ttk.Entry(input_frame, width=10, font=("SimSun", 14))
        self.char_entry.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        self.char_entry.bind('<Return>', lambda e: self.generate_font())
        
        ttk.Button(input_frame, text="生成字库", command=self.generate_font).grid(row=2, column=0, pady=(0, 10))
        ttk.Button(input_frame, text="分析切字", command=self.analyze_qie_char).grid(row=3, column=0, pady=(0, 5))
        
        # 预览区域
        preview_frame = ttk.LabelFrame(input_frame, text="12x12预览", padding="5")
        preview_frame.grid(row=4, column=0, sticky=(tk.W, tk.E), pady=(10, 0))
        
        self.canvas = tk.Canvas(preview_frame, width=240, height=240, bg='white')
        self.canvas.grid(row=0, column=0)
        
        # 输出区域
        output_frame = ttk.LabelFrame(main_frame, text="生成的字库代码", padding="10")
        output_frame.grid(row=2, column=1, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(5, 0))
        
        self.output_text = scrolledtext.ScrolledText(output_frame, width=60, height=30, font=("Consolas", 9))
        self.output_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 控制按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=3, column=0, columnspan=2, pady=(10, 0))
        
        ttk.Button(button_frame, text="复制代码", command=self.copy_code).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="清空", command=self.clear_all).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="验证格式", command=self.verify_format).pack(side=tk.LEFT, padx=(0, 5))
        
        # 配置网格权重
        main_frame.columnconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=2)
        main_frame.rowconfigure(2, weight=1)
        input_frame.columnconfigure(0, weight=1)
        input_frame.rowconfigure(4, weight=1)
        output_frame.columnconfigure(0, weight=1)
        output_frame.rowconfigure(0, weight=1)
    
    def select_font(self):
        """选择字体文件"""
        font_path = filedialog.askopenfilename(
            title="选择字体文件",
            filetypes=[("字体文件", "*.ttf *.ttc *.otf"), ("所有文件", "*.*")]
        )
        if font_path:
            self.font_path = font_path
            self.font_label.config(text=f"已选择: {font_path.split('/')[-1]}")
    
    def char_to_bitmap(self, char):
        """将字符转换为12x12位图"""
        try:
            # 创建16x16图像用于渲染
            img = Image.new('L', (16, 16), 255)
            draw = ImageDraw.Draw(img)
            
            # 选择字体
            if self.font_path:
                font = ImageFont.truetype(self.font_path, 12)
            else:
                # 使用系统默认字体
                try:
                    font = ImageFont.truetype("simsun.ttc", 12)
                except:
                    font = ImageFont.load_default()
            
            # 获取文字尺寸
            bbox = draw.textbbox((0, 0), char, font=font)
            text_width = bbox[2] - bbox[0]
            text_height = bbox[3] - bbox[1]
            
            # 计算居中位置
            x = (16 - text_width) // 2
            y = (16 - text_height) // 2 - bbox[1]
            
            # 绘制字符
            draw.text((x, y), char, font=font, fill=0)
            
            # 裁剪到12x12
            img = img.crop((2, 2, 14, 14))
            
            # 转换为二值图像
            img_array = np.array(img)
            bitmap = (img_array < 128).astype(np.uint8)
            
            return bitmap
            
        except Exception as e:
            messagebox.showerror("错误", f"生成字符位图失败: {str(e)}")
            return None
    
    def bitmap_to_bytes_correct(self, bitmap):
        """将12x12位图转换为正确的字节数组格式"""
        bytes_data = []
        
        # 前12字节：每行的左8位（上2/3部分，实际是前8行的完整12位中的左8位）
        for row in range(12):
            byte_val = 0
            for col in range(8):
                if bitmap[row][col]:
                    byte_val |= (1 << (7 - col))
            bytes_data.append(byte_val)
        
        # 后12字节：每行的右4位（存储在字节的高4位）
        for row in range(12):
            byte_val = 0
            for col in range(8, 12):
                if bitmap[row][col]:
                    byte_val |= (1 << (7 - (col - 8)))
            bytes_data.append(byte_val)
        
        return bytes_data
    
    def generate_font(self):
        """生成字库数据"""
        char = self.char_entry.get().strip()
        if not char:
            messagebox.showwarning("警告", "请输入汉字")
            return
        
        if len(char) != 1:
            messagebox.showwarning("警告", "请输入单个汉字")
            return
        
        # 生成位图
        bitmap = self.char_to_bitmap(char)
        if bitmap is None:
            return
        
        # 显示预览
        self.show_bitmap_preview(bitmap)
        
        # 转换为字节数组
        bytes_data = self.bitmap_to_bytes_correct(bitmap)
        
        # 生成代码
        unicode_val = ord(char)
        code = self.generate_code(char, unicode_val, bytes_data, bitmap)
        
        # 显示代码
        self.output_text.delete(1.0, tk.END)
        self.output_text.insert(tk.END, code)
    
    def generate_code(self, char, unicode_val, bytes_data, bitmap):
        """生成C代码"""
        code = f"// 字符: {char} (Unicode: 0x{unicode_val:04X})\n\n"
        
        # CodePage条目
        code += f"// CodePage条目 (需要插入到CodePage12.c中):\n"
        code += f"{{ 0x{unicode_val:04X}, 0x{unicode_val:04X}, 0xXXXX }}/* Segment XXX, 0x0001 Symbols */\n\n"
        
        # Unicode12数组条目
        code += f"// Unicode12数组条目 (需要插入到Unicode12.c中):\n"
        code += f"{{{{12, 12}},{{\n"
        
        # 前12字节，注释显示完整12位
        for i in range(12):
            left_byte = bytes_data[i]
            right_byte = bytes_data[i + 12]
            
            # 生成完整12位注释
            pattern = ""
            for bit in range(8):
                pattern += "@" if (left_byte & (1 << (7-bit))) else "."
            for bit in range(4):
                pattern += "@" if (right_byte & (1 << (7-bit))) else "."
            
            if i == 11:
                code += f"0x{left_byte:02X},    /*  {pattern}  */\n\n"
            else:
                code += f"0x{left_byte:02X},    /*  {pattern}  */\n"
        
        # 后12字节，注释显示完整12位
        for i in range(12):
            left_byte = bytes_data[i]
            right_byte = bytes_data[i + 12]
            
            # 生成完整12位注释
            pattern = ""
            for bit in range(8):
                pattern += "@" if (left_byte & (1 << (7-bit))) else "."
            for bit in range(4):
                pattern += "@" if (right_byte & (1 << (7-bit))) else "."
            
            if i == 11:
                code += f"0x{right_byte:02X}     /*  {pattern}  */\n"
            else:
                code += f"0x{right_byte:02X},    /*  {pattern}  */\n"
        
        code += f"}}}}\n\n"
        
        # 添加格式说明
        code += f"// 格式说明:\n"
        code += f"// 前12字节: 每行的左8位\n"
        code += f"// 后12字节: 每行的右4位(存储在字节的高4位)\n"
        code += f"// 注释显示: 完整的12位图案(左8位+右4位)\n"
        
        return code
    
    def show_bitmap_preview(self, bitmap):
        """显示位图预览"""
        self.canvas.delete("all")
        
        cell_size = 20
        
        # 绘制网格
        for i in range(13):
            self.canvas.create_line(i*cell_size, 0, i*cell_size, 12*cell_size, fill="lightgray")
            self.canvas.create_line(0, i*cell_size, 12*cell_size, i*cell_size, fill="lightgray")
        
        # 绘制像素
        for row in range(12):
            for col in range(12):
                if bitmap[row][col]:
                    x1, y1 = col*cell_size, row*cell_size
                    x2, y2 = x1+cell_size, y1+cell_size
                    self.canvas.create_rectangle(x1, y1, x2, y2, fill="black", outline="gray")
    
    def analyze_qie_char(self):
        """分析'切'字的数据"""
        # "切"字的实际数据
        left_bytes = [0x10, 0x10, 0xFF, 0x08, 0x08, 0x02, 0x02, 0xFE, 0x02, 0x02, 0xFE, 0x00]
        right_bytes = [0x00, 0x00, 0x07, 0x02, 0x09, 0x04, 0x03, 0x00, 0x08, 0x08, 0x07, 0x00]
        
        # 重建12x12位图
        bitmap = []
        for row in range(12):
            row_data = []
            # 左8位
            for bit in range(8):
                row_data.append(1 if (left_bytes[row] & (1 << (7-bit))) else 0)
            # 右4位
            for bit in range(4):
                row_data.append(1 if (right_bytes[row] & (1 << (7-bit))) else 0)
            bitmap.append(row_data)
        
        # 显示预览
        self.show_bitmap_preview(bitmap)
        
        # 生成分析代码
        code = "// '切'字数据分析\n\n"
        code += "12x12点阵图案:\n"
        for row in range(12):
            pattern = ""
            for col in range(12):
                pattern += "█" if bitmap[row][col] else "·"
            code += f"行{row:2d}: {pattern}\n"
        
        code += "\n原始字库格式:\n"
        code += "{{12, 12},{\n"
        
        # 显示原始格式
        for i in range(12):
            left_byte = left_bytes[i]
            right_byte = right_bytes[i]
            
            pattern = ""
            for bit in range(8):
                pattern += "@" if (left_byte & (1 << (7-bit))) else "."
            for bit in range(4):
                pattern += "@" if (right_byte & (1 << (7-bit))) else "."
            
            if i == 11:
                code += f"0x{left_byte:02X},    /*  {pattern}  */\n\n"
            else:
                code += f"0x{left_byte:02X},    /*  {pattern}  */\n"
        
        for i in range(12):
            left_byte = left_bytes[i]
            right_byte = right_bytes[i]
            
            pattern = ""
            for bit in range(8):
                pattern += "@" if (left_byte & (1 << (7-bit))) else "."
            for bit in range(4):
                pattern += "@" if (right_byte & (1 << (7-bit))) else "."
            
            if i == 11:
                code += f"0x{right_byte:02X}     /*  {pattern}  */\n"
            else:
                code += f"0x{right_byte:02X},    /*  {pattern}  */\n"
        
        code += "}}\n"
        
        self.output_text.delete(1.0, tk.END)
        self.output_text.insert(tk.END, code)
    
    def verify_format(self):
        """验证生成的格式是否正确"""
        char = self.char_entry.get().strip()
        if not char:
            self.analyze_qie_char()
            return
        
        # 重新生成并验证
        self.generate_font()
        messagebox.showinfo("验证", "格式验证完成，请检查生成的代码是否符合项目格式")
    
    def copy_code(self):
        """复制代码到剪贴板"""
        code = self.output_text.get(1.0, tk.END).strip()
        if code:
            self.root.clipboard_clear()
            self.root.clipboard_append(code)
            messagebox.showinfo("成功", "代码已复制到剪贴板")
        else:
            messagebox.showwarning("警告", "没有可复制的代码")
    
    def clear_all(self):
        """清空所有内容"""
        self.char_entry.delete(0, tk.END)
        self.output_text.delete(1.0, tk.END)
        self.canvas.delete("all")

def main():
    root = tk.Tk()
    app = FontGeneratorCorrect(root)
    root.mainloop()

if __name__ == "__main__":
    main()
