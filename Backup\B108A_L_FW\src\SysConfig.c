//-----------------
#include "SysConfig.h"
#include "Timer.h"
#include "ThisDevice.h"
#include "SysBasic.h"
#include "RF_BasicFun.h"
//
//#define  DEVICE_MODE_MASK     (uint16_t)0xf000
//#define  IC_SEL_CONFIG_BIT	(uint16_t)0x8000
//#define  FYF35_SHR_MODE       (uint16_t)0x0000
//#define  FYF35_TUN_MODE  	    (uint16_t)0x1000
//#define  FYS30_021_MODE    	(uint16_t)0x4000
//#define  FYF25_MODE  	        (uint16_t)0x8000
//#define  FYS30_01_MODE    	(uint16_t)0xc000
//#define  HEART_BEAT_EN_BIT 	(uint16_t)0x0800

//------------------------------
const uint16_t SysConfigADef[] __attribute__((at(PARA_GA_MEM_BASE))) ={
// .Parameter ID=0x0041 ,LEN=32
   0x0041 ,//Px0101,Parameter's identifier
       32 ,//Px0102,configuration data length
   0xffff ,//Px0103,reserved
   0xffff ,//Px0104,reserved   ,+4

   0x0800 ,//Px0105,work mode of this device.
	       //--Bit_12-15(DEVICE_MODE)  , Bit15(IC_SEL_CONFIG_BIT--'0' use IC021,='1' use JF01),
	       //  Bit14--Transmitter or Receiver mode select--='0' FYFxx mode,='1'FYSxx mode) 
	       //  =0x0xxx-0x7xxx->for IC021 type transmitter/receiver(0x0xxx-0x3xxx for FYF35,0x4xxx-0x7xxx for FYS30(38403B type)) 
           //  =0x8xxx-0xfxxx->for JF01 type transmitter/receiver(0x8xxx-0xbxxx for FYF25,0xcxxx-0xfxxx for FYS30(38404B type))
           //--Bit_11(HEART_BEAT_EN_BIT) ='1' use heart beat signal, ='0' not use heart beat signal
           //--Bit_10(NO_AUTO_SHUTOFF) ='1' disable auto switch off function,='0' use it
		   //--Bit_8(LNA_EXIST_01) =1 there have a LNA used in 38404B RF front board,=0 no LNA used in 38404B RF front board
           //--Bit_0-3(MACHINE_TYPE)--> =0x0 for S260T1,=0x1 for S260T2,when in FYF35 tunneller mode
	0x0004 ,//Px0106,Bit0-1[0-3]set this panel  in group id,Bit2-7[0-63] group id ,When the device work as panel transmitter
        10 ,//Px0107,LCD black light automatic shut off time when idle(in second,10S )
      3600 ,//Px0108,System active status wait time before enter sleep status(in second,1800S) 
    
#ifndef SHR_S160_MODE		   
        2 ,//Px0109,Left side RF IC Receive working frequecy channel number[0-5] 
        1 ,//Px0110,Left side RF IC Transmitte working frequecy channel number[0-5] 
        2 ,//Px0111,Right side RF IC Receive working frequecy channel number[0-5] 
        3 ,//Px0112,Right side RF IC Transmitte working frequecy channel number[0-5]  ,+4  
#else
        3 ,//Px0109,Left side RF IC Receive working frequecy channel number[0-5] 
        0 ,//Px0110,Left side RF IC Transmitte working frequecy channel number[0-5] 
        3 ,//Px0111,Right side RF IC Receive working frequecy channel number[0-5] 
        4 ,//Px0112,Right side RF IC Transmitte working frequecy channel number[0-5]  ,+4  
#endif
      250 ,//Px0113, AirLinkageTO ,time out value for air linkage rx activ,220*10ms about 2200mS
	  220 ,//Px0114, Operation key code keep time for rf command(used in receiver mode),OpcKeepTime=90*10 mS ,0.9 Seconds	
      150 ,//Px0115, (75 for 9600bps)Wait for poll status time out value in Sync mode(ms)
       12 ,//Px0116, RF Tx frame first preamble segment length in bytes   

      400 ,//Px0117,SyncTimeOut-Synchronous status keep time setting (in RF Tx Ticks)
   0xffff ,//Px0118,reserved word 1,+4
       55 ,//Px0119,JF01 chip Rx attenuation status monitor time out value --in mS 						   
       30 ,//Px0120,Determine the FYF25(Unidirection mode) Led flash interval --in RfTick numbers
	       //       When battery endup voltage be detect this value will extend to 3 times of this setting.,+4				   

		   0xffff , //Px0121,reserved
                9 , //Px0122,RF output power level setting 10 step [0..9],this will index to power level control table for each device
  (uint16_t)(-17) , //Px0123, RSSI voltage input channel left  
  (uint16_t)(-17) , //Px0124, RSSI voltage input channel right ,+4	
         

        990 ,//Px0125,Scale coefficient used to adjust the calculated battery voltage  x1000 
          0 ,//Px0126,Offset used to adjust the calculated battery output voltage
       3100 ,//Px0127,low voltage alarm
       2900 ,//Px0128,use to indicate battery end up
		  
       2060 ,//Px0129,typical temperature coefficient (1.66x4095/3300)*1000 
        263 ,//Px0130,typical temperature offset of on chip temperature sensor @273k(0 celsuis degree)
     0xffff ,//Px0131, Reserved word 3 
     0xffff  //Px0132, crc check value  +4		 
} ;

//------------------------------
const  uint16_t  SysConfigBDef[] __attribute__((at(PARA_GB_MEM_BASE))) ={
// .Parameter ID=0x0042 ,LEN=56
   0x0042 ,//Px0101,Parameter's identifier
       56 ,//Px0102,configuration data length
   0xffff ,//Px0103,module UID low word16
   0xffff ,//Px0104,module UID hight word16   ,+4
//USART 2 port configuration --Maybe for debug connection
  (uint16_t)0x00+((uint16_t)0x03<<8) , //Px0105, work mode of the port(reserved),
                                       //=0x01,Modbus slaver  address
                      (uint16_t)19200 , //Px0106, Communication speed (baud rate,allow:2400 4800,9600,14400,19200,28800,38400,default 19200)
  (uint16_t)0x02+((uint16_t)4<<8) , //Px0107, =0x00 no parity, 01 odd parity (default 01 odd parity),02 even parity
	  			       //=5, in ms,tx delay time of slaver reponse(default 5 ms),minimal value=2ms
  (uint16_t)100+((uint16_t)1<<8) , //Px0108, in ms,when act as master the max wait time(in ms) for slaver respone,minimal value=60ms 
                       //=0, retry number  when there are no response form slaver
 //Lo_Add ,Hi_Add ,   Attr  --For UART 1
        0 ,     7 ,   READ_ONLY , //Px0109-0111, 
        8 ,   127 ,  READ_WRITE , //Px0112-0114,
      128 , 19999 ,   NO_ACCESS , //Px0115-0117,
    20000 , 20255 ,  READ_WRITE , //Px0118-0120,
           
        0 ,//Px0121, number of segment in this control table(=0, no control) 
   0xffff ,//Px0122,Globle operation key mask code,if any bit=0,the coresponing key will be mask out,no operation will output
   0xffff ,//Px0123,Left side mode operation key mask code
   0xffff ,//Px0124,Right side mode operation key mask  code,+4

        0 ,//Px0125,CAN bus interface working mode 
      250 ,//Px0126,CAN bus working speed setting 125kbps,250kbps,500kbps,1Mbps
        5 ,//Px0127,CANOpen mode Node ID 0-127
   0xffff ,//Px0128, reserved(CAN bus output message object ID setting) ,+4	

//Following are RF tick and token setting for bidirection mode setting
//     1305 ,//Px0129,Reset value for RF timer ,when synchronization signal be captured(for Fyf35 use 2320)
//     1638 ,//Px0130,Preload counter cycle value for the RF tick timer(determine the RF tick long)
//           //       for Fyf35 use 9600bpd (20.0048Hz,49.98748mS)
////Interval,TokenNum(if TokenNum=-1 ,means this status have infinite token numbers)	 
//        1 , 4   ,//Px0143-144,Interval- represent Rf tick number between two token generation
//       10 , 16  ,//Px0145-146,(10-->2Hz interval when any key be pressed on)
//       20 , 30  ,//Px0147-148,(40*20*0.049987S=30S)
//       30 , 1200 ,//Px0149-150,(1200*1.5=1800S)  
//       40 , (uint16_t)-1 ,//Px0151-152,TOKENPOOLS --fixed to 5, +12 
     2200 ,//Px0129,Reset value for RF timer ,when synchronization signal be captured(for Fyf35 use 2320)
     3275 ,//Px0130,Preload counter cycle value for the RF tick timer(determine the RF tick long)
           //       for Fyf35 use 3275(10.005Hz,99.945mS)
  //Interval,TokenNum(if TokenNum=-1 ,means this status have infinite token numbers)	 
        1  , 4   ,//Px0131-132,Interval- represent Rf tick number between two token generation
        10 , 16  ,//Px0133-134,
        10 , 120 ,//Px0135-136,(120*1.0S=120S)
        20 , 21600 ,//Px0137-138,(21600*2S=12Hours)
        50 , 28800 ,//Px0139-140,5S,40Hours,TOKENPOOLS -fixed to 5, +12 

//Following are RF tick and token setting for unidirection mode setting
     1305 ,//Px0141,Reset value for RF timer ,when synchronization signal be captured(not used for Fyf25)
     1638 ,//Px0142,Preload counter cycle value for the RF tick timer(determine the RF tick long)
           //       for Fyf25 use 1638(20.0048Hz,49.98748mS)
//Interval,TokenNum(if TokenNum=-1 ,means this status have infinite token numbers)	 
        1 , 4    ,//Px0143-144,Interval- represent Rf tick number between two token generation
       20 , 16   ,//Px0145-146,(16-->0.8S interval when any key be pressed on)
       20 , 120  ,//Px0147-148,(120*20*0.049987S=120S)
       40 , 21600 ,//Px0149-150,(21600*2=12 Hours)  
      100 , 28800 ,//Px0151-152,5S,40Hours,TOKENPOOLS --fixed to 5, +12 
//------------------------		   	
   0xffff ,//Px0153, Reserved
   0xffff ,//Px0154, Reserved
   0x0008 ,//Px0155,define the application specified emergence stop key code
   0xffff  //Px0156, crc check value  +4		 
} ;

//-----------------------------------------------------------------------------
__align(4) const uint32_t  SysConfigCDef[] __attribute__((at(PARA_GC_MEM_BASE))) ={
// .Parameter ID=0x0043 ,LEN=84
     0x00540043 ,//Parameter's identifier
//uint16_t  len ;//configuration data length
     0x00000000 ,//Rev_wa[2]
//----------------------------
/*
     //Following data for 7021
     0x09560000 ,//R0 ,N Register PLL 8-bit Integer-N 15-Bit Fractional-N,for 315.2872MHz Rx mode
     0x021b7021 ,//R1 ,VCO/Oscillator Register for 315.2872MHz Rx
     0x00b7f882 ,//R2 ,Transmit Modulation register, PA on,for 2FSK ,Fdev=2.475kHz,
     0x2a4c80d3 ,//R3 ,Transmit/Receive clock register DEMOD_CLK=4.9152,Data rate=2.4kHz,DISCRIMLATOR_BW=516
     0x8053d814 ,//R4 ,Demodulator setup register For 25 kHz IF BW ,Discrim B/W=2x
     0x000024f5 ,//R5 ,IF filter setup register
     0x05070e06 ,//R6 ,IF fine calibration setup register for stander IF cal
     0x00000fd8 ,//R8 ,Power down test register,internal Rx/Tx switch control
     0x00a631e9 , //R9 ,AGC setup register##Should set LNA gain to max ,and disable AGC
     0x1896473a ,//R10 ,AFC setup register
     0x00cc331b ,//R11 ,SYNC word detect register
     0x00000aac ,//R12 ,SWD/Threshold setup register
     0x0000000d ,//R13 ,3FSK Demod register
     0x0000000e ,//R14 ,Test DAC register
     0x0000000f ,//R15 ,Test register
*/
   //Following data for 7021-N
     0x09560010 ,//R0 ,N Register PLL 8-bit Integer-N 15-Bit Fractional-N,for 315.2872MHz Rx mode
     0x021b7021 ,//R1 ,VCO/Oscillator Register for 315.2872MHz Rx
     0x00b7f882 ,//R2 ,Transmit Modulation register, PA on, for 2FSK,Fdev=2.475kHz
     0x364c80d3 ,//R3 ,Transmit/Receive clock register DEMOD_CLK=4.9152MHz,Data rate=4800
     0x8053d814 ,//R4 ,Demodulator setup register For 18.5 kHz IF BW,Discrim B/W=2x(K value=20),PostDemodulator BW=3.8197kHz 
//     0x0127f882 ,//R2 ,Transmit Modulation register, PA on, for 2FSK,Fdev=4.0kHz
//     0x364c6093 ,//R3 ,Transmit/Receive clock register DEMOD_CLK=4.9152MHz,Data rate=9600
//     0x80773694 ,//R4 ,Demodulator setup register For 18.5 kHz IF BW,Discrim B/W=1x(K value=22),PostDemodulator BW=8.0214kHz 
     0x000024f5 ,//R5 ,IF filter setup register
     0x0a07cbc6 ,//R6 ,IF fine calibration setup register for stander IF cal    0x05070e06
     0x00000fd8 ,//R8 ,Power down test register,internal Rx/Tx switch control
     0x00a631e9 , //R9 ,AGC setup register,Manual AGC ,use max LNA&Filter GAIN
//     0x1296473a ,//R10 ,AFC setup register,AFC on,9kHz(-9~+9kHz) AFC range,KP=4,KI=11,AFC scaling factor=569
     0x12d0473a ,//R10 **,AFC setup register,AFC on,9kHz(-9~+9kHz) AFC range,KP=6,KI=8,AFC scaling factor=569
     0x00cc331b ,//R11 ,SYNC word detect register
     0x00000aac ,//R12 ,SWD/Threshold setup register
     0x0000000d ,//R13 ,3FSK Demod register
     0x0000000e ,//R14 ,Test DAC register
     0x0000000f ,//R15 ,Test register

//--Rx frequency channel 0 register setting
     0x0954e440 , //R0 for 314.2657MHz Rx mode,      
     0x021b7021 , //R1 ,VCO/Oscillator Register Rx
//--Rx frequency channel 1 register setting
     0x09550010 , //R0 for 314.3656MHz Rx mode,      
     0x021b7021 , //R1 ,VCO/Oscillator Register Rx
//--Rx frequency channel 2 register setting
     0x095578b0 , //R0 for 314.8000MHz Rx mode,      
     0x021b7021 , //R1 ,VCO/Oscillator Register Rx
//--Rx frequency channel 3 register setting
     0x09560010 , //R0 for 315.2872MHz Rx mode,      
     0x021b7021 , //R1 ,VCO/Oscillator Register Rx
//--Rx frequency channel 4 register setting
     0x09568e70 , //R0 for 315.8000MHz Rx mode,      
     0x021b7021 , //R1 ,VCO/Oscillator Register Rx
//--Rx frequency channel 5 register setting
     0x09570010 , //R0 for 316.2088MHz Rx mode,      
     0x021b7021 , //R1 ,VCO/Oscillator Register Rx
//----------------------------
//--Tx frequency channel 0 register setting
     0x01550010 , //R0 for 314.2656MHz Tx mode
     0x021b7021 , //R1 ,VCO/Oscillator Register Tx
//--Tx frequency channel 1 register setting
     0x01551bc0 , //R0 for 314.3656MHz Tx mode
     0x021b7021 , //R1 ,VCO/Oscillator Register Tx
//--Tx frequency channel 2 register setting
     0x01559470 , //R0 for 314.8000MHz Tx mode
     0x021b7021 , //R1 ,VCO/Oscillator Register Tx
//--Tx frequency channel 3 register setting
     0x01561bc0 , //R0 for 315.2872MHz Tx mode
     0x021b7021 , //R1 ,VCO/Oscillator Register Tx
//--Tx frequency channel 4 register setting
     0x0156aa40 , //R0 for 315.8000MHz Tx mode
     0x021b7021 , //R1 ,VCO/Oscillator Register Tx
//--Tx frequency channel 5 register setting
     0x01571bc0 , //R0 for 316.2088MHz Tx mode
     0x021b7021 , //R1 ,VCO/Oscillator Register Tx
//--------------------------
     0xaaaa5555 , //Module UID
     0x0000ffff  //reserved loc ,Check sum of this basic parameter  block  ,+4
} ;

const uint8_t  SysConfigDDef[] __attribute__((at(PARA_GD_MEM_BASE))) ={
// .Parameter ID=0x0044 ,LEN=40
   (uint8_t) (0x0044%256) ,//Parameter's identifier
   (uint8_t) (0x0044>>8) ,//Parameter's identifier
   (uint8_t) (40%256),//configuration data length
   (uint8_t) (40>>8) ,//configuration data length
   (uint8_t) (0x0000%256) ,//Rev_wa[0]
   (uint8_t) (0x0000>>8) ,//
   (uint8_t) (0x0000%256) ,//Rev_wa[1] +4 
   (uint8_t) (0x0000>>8) ,// 
//------------------------------------------------ 
//JF01 Default setting
  (uint8_t)0x29,//00,IOCFG2    GDO2 output pin configuration,CHIP_RDYn
  (uint8_t)0x2E,//01,IOCFG1    GDO1 output pin configuration,High impedance 3state
  (uint8_t)0x02,//02,IOCFG0    GDO0 output pin configuration,TX FIFO  full
  (uint8_t)0x47,//03,FIFOTHR   RX FIFO threshold=32 bytes ,and TX FIFO thresholds=33 bytes
  (uint8_t)0xCC,//04,SYNC1     Sync word, high byte
  (uint8_t)0x33,//05,SYNC0     Sync word, low byte
  (uint8_t)0x08,//06,PKTLEN    Packet length
  (uint8_t)0x84,//07,PKTCTRL1  Packet automation control,PQT,CRC_AUTOFLUSH,APPEND_STATUS,ADR_CHK

  (uint8_t)0x00,//08,PKTCTRL0  Packet automation control,WHITE_DATA,PKT_FORMAT,CRC_EN,LENGTH_CONFIG
  (uint8_t)0x01,//09,ADDR      Device address
  (uint8_t)0x00,//0A,CHANNR    Channel number
  (uint8_t)0x06,//0B,FSCTRL1   Frequency synthesizer control,FREQ_IF
  (uint8_t)0x00,//0C,FSCTRL0   Frequency synthesizer control,FREQOFF
  (uint8_t)0x0C,//0D,FREQ2     Frequency control word, high byte--For Frequency=314.020538MHz
  (uint8_t)0x13,//0E,FREQ1     Frequency control word, middle byte
  (uint8_t)0xE5,//0F,FREQ0     Frequency control word, low byte

  (uint8_t)0xc7,//10,MDMCFG4   Modem configuration,For 102kHz IF BW-CHANBW_E=3,CHANBW_M=0,for 162kHz-BW-CHANBW_E=2,CHANBW_M=1
                             //,For 4.797935kbps-DRATE_E=7(for 9.595871kbps,DRATE_E=8)
  (uint8_t)0x83,//11,MDMCFG3   Modem configuration,For 4.797935kbps-DRATE_M =0x83
                             //(for 9.595871kbps,DRATE_M =0x83)
  (uint8_t)0x02,//12,MDMCFG2   Modem configuration,DEM_DCFILT_OFF,MOD_FORMAT,MANCHESTER_EN,SYNC_MODE
  (uint8_t)0x33,//13,MDMCFG1   Modem configuration,FEC_EN,NUM_PREAMBLE-->6 bytes preamble,CHANSPC_E
  (uint8_t)0xF8,//14,MDMCFG0   Modem configuration,CHANSPC_M
  (uint8_t)0x40,//15,DEVIATN   Modem deviation setting,DEVIATION_E,Fpr deviation=31.738281kHz-DEVIATION_M=0x42
                //(for deviation=25.390625kHz-DEVIATION_M=0x40)(for deviation=28.564453kHz-DEVIATION_M=0x41)
  (uint8_t)0x07,//16,MCSM2     Main Radio Cntrl State Machine config,RX_TIME_RSSI,RX_TIME_QUAL,RX_TIME
  (uint8_t)0x00,//17,MCSM1     Main Radio Cntrl State Machine config,CCA_MODE,RXOFF_MODE->IDLE,TXOFF_MODE->IDLE mode
  
  (uint8_t)0x18,//18,MCSM0     Main Radio Cntrl State Machine config,FS_AUTOCAL,PO_TIMEOUT,PIN_CTRL_EN,XOSC_FORCE_ON
  (uint8_t)0x16,//19,FOCCFG    Frequency Offset Compensation config,FOC_BS_CS_GATE,FOC_PRE_K,FOC_POST_K,FOC_LIMIT
  (uint8_t)0x6C,//1A,BSCFG     Bit Synchronization configuration,BS_PRE_KI,BS_PRE_KP,BS_POST_KI,BS_POST_KP,BS_LIMIT
  (uint8_t)0x43,//1B,AGCCTRL2  AGC control,MAX_DVGA_GAIN,MAX_LNA_GAIN,MAGN_TARGET
  (uint8_t)0x40,//1C,AGCCTRL1  AGC control,AGC_LNA_PRIORITY,CARRIER_SENSE_REL_THR,CARRIER_SENSE_ABS_THR
  (uint8_t)0x91,//1D,AGCCTRL0  AGC control,HYST_LEVEL,WAIT_TIME,AGC_FREEZE,FILTER_LENGTH
  (uint8_t)0x87,//1E,WOREVT1   High byte Event 0 timeout
  (uint8_t)0x6B,//1F,WOREVT0   Low byte Event 0 timeout
  
  (uint8_t)0xF8,//20,WORCTRL   Wake On Radio control,RC_PD,EVENT1,RC_CAL,WOR_RES
  (uint8_t)0x56,//21,FREND1    Front end RX configuration,LNA_CURRENT,LNA2MIX_CURRENT,LODIV_BUF_CURRENT_RX,MIX_CURRENT
  (uint8_t)0x10,//22,FREND0    Front end TX configuration,LODIV_BUF_CURRENT_TX,PA_POWER
  (uint8_t)0xE9,//23,FSCAL3    Frequency synthesizer calibration,FSCAL3[7:6],CHP_CURR_CAL_EN,FSCAL3[3:0]
  (uint8_t)0x0A,//24,FSCAL2    Frequency synthesizer calibration,VCO_CORE_H_EN,FSCAL2
  (uint8_t)0x00,//25,FSCAL1    Frequency synthesizer calibration,FSCAL1
  (uint8_t)0x1F,//26,FSCAL0    Frequency synthesizer calibration,FSCAL0
  (uint8_t)0x41,//27,RCCTRL1   RC oscillator configuration

  (uint8_t)0x00,//28,RCCTRL0   RC oscillator configuration
  (uint8_t)0x59,//29,FSTEST    Frequency synthesizer cal control,For test only.Do not write to this register
  (uint8_t)0x7F,//2A,PTEST     Production test,Write 0xbf for on-chip temperature in the IDLE state,otherwise use default value 0x7f
  (uint8_t)0x3F,//2B,AGCTEST   AGC test,Do not write to this register
  (uint8_t)0x81,//2C,**TEST2     Various test settings
  (uint8_t)0x35,//2D,**TEST1     Various test settings
  (uint8_t)0x0B,//2E,TEST0     Various test settings,TEST0[7:2],VCO_SEL_CAL_EN,TEST0[0]
  (uint8_t)0xff,//Reserved location

//--Rx frequency channel 0 register setting
  (uint8_t)0x0C,//0D	FREQ2 (x)--For Frequency=314.020538MHz
  (uint8_t)0x13,//0E	FREQ1 (x)
  (uint8_t)0xe5,//0F	FREQ0 (x)
  (uint8_t)0x0B,//2E,TEST0     Various test settings,TEST0[7:2],VCO_SEL_CAL_EN,TEST0[0]
//--Rx frequency channel 1 register setting
  (uint8_t)0x0C,//0D	FREQ2 (x)--For Frequency=315.073853MHz
  (uint8_t)0x1e,//0E	FREQ1 (x)
  (uint8_t)0x44,//0F	FREQ0 (x)
  (uint8_t)0x0B,//2E,TEST0     Various test settings,TEST0[7:2],VCO_SEL_CAL_EN,TEST0[0]
//--Rx frequency channel 2 register setting
  (uint8_t)0x0C,//0D	FREQ2 (x)--For Frequency=316.046123MHz
  (uint8_t)0x27,//0E	FREQ1 (x)
  (uint8_t)0xd8,//0F	FREQ0 (x)
  (uint8_t)0x0B,//2E,TEST0     Various test settings,TEST0[7:2],VCO_SEL_CAL_EN,TEST0[0]
//--Rx frequency channel 3 register setting
  (uint8_t)0x0C,//0D	FREQ2 (x)--For Frequency=317.180084MHz
  (uint8_t)0x33,//0E	FREQ1 (x)
  (uint8_t)0x01,//0F	FREQ0 (x)
  (uint8_t)0x0B,//2E,TEST0     Various test settings,TEST0[7:2],VCO_SEL_CAL_EN,TEST0[0]
//--Rx frequency channel 4 register setting
  (uint8_t)0x0C,//0D	FREQ2 (x)--For Frequency=318.233398MHz
  (uint8_t)0x3d,//0E	FREQ1 (x)
  (uint8_t)0x60,//0F	FREQ0 (x)
  (uint8_t)0x0B,//2E,TEST0     Various test settings,TEST0[7:2],VCO_SEL_CAL_EN,TEST0[0]
//--Rx frequency channel 5 register setting
  (uint8_t)0x0C,//0D	FREQ2 (x)--For Frequency=319.286713MHz
  (uint8_t)0x47,//0E	FREQ1 (x)
  (uint8_t)0xbf,//0F	FREQ0 (x)
  (uint8_t)0x0B,//2E,TEST0     Various test settings,TEST0[7:2],VCO_SEL_CAL_EN,TEST0[0]
//-------------------------------------------------------------------------------------
//--Tx frequency channel 0 register setting
  (uint8_t)0x0C,//0D	FREQ2 (x)--For Frequency=314.020538MHz
  (uint8_t)0x13,//0E	FREQ1 (x)
  (uint8_t)0xe5,//0F	FREQ0 (x)
  (uint8_t)0x0B,//2E,TEST0     Various test settings,TEST0[7:2],VCO_SEL_CAL_EN,TEST0[0]
//--Tx frequency channel 1 register setting
  (uint8_t)0x0C,//0D	FREQ2 (x)--For Frequency=315.073853MHz
  (uint8_t)0x1e,//0E	FREQ1 (x)
  (uint8_t)0x44,//0F	FREQ0 (x)
  (uint8_t)0x0B,//2E,TEST0     Various test settings,TEST0[7:2],VCO_SEL_CAL_EN,TEST0[0]
//--Tx frequency channel 2 register setting
  (uint8_t)0x0C,//0D	FREQ2 (x)--For Frequency=316.046123MHz
  (uint8_t)0x27,//0E	FREQ1 (x)
  (uint8_t)0xd8,//0F	FREQ0 (x)
  (uint8_t)0x0B,//2E,TEST0     Various test settings,TEST0[7:2],VCO_SEL_CAL_EN,TEST0[0]
//--Tx frequency channel 3 register setting
  (uint8_t)0x0C,//0D	FREQ2 (x)--For Frequency=317.180084MHz
  (uint8_t)0x33,//0E	FREQ1 (x)
  (uint8_t)0x01,//0F	FREQ0 (x)
  (uint8_t)0x0B,//2E,TEST0     Various test settings,TEST0[7:2],VCO_SEL_CAL_EN,TEST0[0]
//--Tx frequency channel 4 register setting
  (uint8_t)0x0C,//0D	FREQ2 (x)--For Frequency=318.233398MHz
  (uint8_t)0x3d,//0E	FREQ1 (x)
  (uint8_t)0x60,//0F	FREQ0 (x)
  (uint8_t)0x0B,//2E,TEST0     Various test settings,TEST0[7:2],VCO_SEL_CAL_EN,TEST0[0]
//--Tx frequency channel 5 register setting
  (uint8_t)0x0C,//0D	FREQ2 (x)--For Frequency=319.286713MHz
  (uint8_t)0x47,//0E	FREQ1 (x)
  (uint8_t)0xbf,//0F	FREQ0 (x)
  (uint8_t)0x0B,//2E,TEST0     Various test settings,TEST0[7:2],VCO_SEL_CAL_EN,TEST0[0]
//--------------------------------------------
  (uint8_t)(0xfa05%256) ,//reserved data word-flag(Low Byte)
  (uint8_t)(0xfa05>>8) ,//reserved data word-flag(High Byte)
  (uint8_t)0xff ,
  (uint8_t)0xff  //Check sum of this basic parameter  block  ,+4
} ;
 
SysConfigA_t   APP_PA ;//System application parameter Group A, ID=0x0041

SysConfigB_t   APP_PB ;//System application parameter Group B, ID=0x0042

SysConfigC_t   *APP_PC_p ;//System application parameter Group C pointer, ID=0x0043

SysConfigD_t   *APP_PD_p ;//System application parameter Group D pointer, ID=0x0044
  
void  SysConfigInit(void) 
{
  uint16_t *tp,*tp2,tmp ;
  uint32_t  ltmp ;
/*******************set configuration data here******************************/
  RfDs.PAccStaS.UIDCode=CalReqCode() ;//Get Authentication request code for this module
  tp=(uint16_t *)&APP_PA ;
  tp2=(uint16_t *) SysConfigADef ;
  for(tmp=0 ;tmp<sizeof(SysConfigA_t)/2 ;tmp++)
  {
    *tp++=*tp2++ ;
   }
  if(APP_PA.RFIC_L_RxChNum>5)	APP_PA.RFIC_L_RxChNum=3 ;
  if(APP_PA.RFIC_R_RxChNum>5)	APP_PA.RFIC_R_RxChNum=4 ;
  if(APP_PA.RFIC_L_TxChNum>5)	APP_PA.RFIC_L_TxChNum=3 ;
  if(APP_PA.RFIC_R_TxChNum>5)	APP_PA.RFIC_R_TxChNum=3 ;
  tp=(uint16_t *)&APP_PB ;
  tp2=(uint16_t *) SysConfigBDef ;
  for(tmp=0 ;tmp<sizeof(SysConfigB_t)/2 ;tmp++)
  {
    *tp++=*tp2++ ;
   }
  ltmp=APP_PB.ACode_h ;
  ltmp<<=16 ;
  ltmp+=(uint32_t) APP_PB.ACode_l ; 
  if(VerifyAuthCode(ltmp))
  {//Module may not auth  to sale(use) 
    U_Area.Err=0xfa02 ;//Set Error flag
   }
  if((APP_PB.BiTickToken.TokenPool[0].Interval<1)||(APP_PB.BiTickToken.TokenPool[0].Interval>5))//Key status changed token generation interval
    APP_PB.BiTickToken.TokenPool[0].Interval=1 ;
  if(APP_PB.BiTickToken.TokenPool[0].TokenNum<2)//Key status changed token setting
    APP_PB.BiTickToken.TokenPool[0].TokenNum=3 ;
  if((APP_PB.BiTickToken.TokenPool[1].Interval<1)||(APP_PB.BiTickToken.TokenPool[1].Interval>50))//Key status changed token generation interval
    APP_PB.BiTickToken.TokenPool[1].Interval=5 ;
  if(APP_PB.BiTickToken.TokenPool[1].TokenNum<2)//Key holded(keeped on) token setting
    APP_PB.BiTickToken.TokenPool[1].TokenNum=4 ;

  if((APP_PB.UniTickToken.TokenPool[0].Interval<1)||(APP_PB.UniTickToken.TokenPool[0].Interval>5))//Key status changed token generation interval
    APP_PB.UniTickToken.TokenPool[0].Interval=1 ;
  if(APP_PB.UniTickToken.TokenPool[0].TokenNum<2)//Key status changed token setting
    APP_PB.UniTickToken.TokenPool[0].TokenNum=3 ;
  if((APP_PB.UniTickToken.TokenPool[1].Interval<1)||(APP_PB.UniTickToken.TokenPool[1].Interval>50))//Key status changed token generation interval
    APP_PB.UniTickToken.TokenPool[1].Interval=10 ;
  if(APP_PB.UniTickToken.TokenPool[1].TokenNum<2)//Key holded(keeped on) token setting
    APP_PB.UniTickToken.TokenPool[1].TokenNum=4 ;

  if((APP_PA.work_mode&HEART_BEAT_EN_BIT)==0)
  {
    APP_PB.UniTickToken.TokenPool[2].Interval=32767 ;//almost diable the token generate
    APP_PB.UniTickToken.TokenPool[2].TokenNum=32000 ;
   }
  APP_PC_p=(SysConfigC_t *) SysConfigCDef ;//
  APP_PD_p=(SysConfigD_t *) SysConfigDDef ;//
 }
//------------------------------------------------------------
void  SysConfigDaemon(void) 
{
 int16_t  tmp , *tp,*tp2 ;
 switch(RfDs.PAccStaS.sta)
 {
  case 0x0000: {
                if(RfDs.PAccDs.req==READ_PARA_REQ)
                {//begin to read specified parameter from RAM buffer
	                 if(RfDs.PAccDs.pid==0x0041)
	                 { //be request to read 0x0041 parameter structure 
	                   tp =(int16_t *) &APP_PA  ;
	                   RfDs.PAccStaS.pid=0x0041 ;
	                   RfDs.PAccStaS.len=sizeof(SysConfigA_t)/2 ;  //SysConfigA_t
                       }
	                 else if(RfDs.PAccDs.pid==0x0042)
	                 { //be request to read 0x0042 parameter structure 
	                   tp =(int16_t *) &APP_PB  ;
	                   RfDs.PAccStaS.pid=0x0042 ;
	                   RfDs.PAccStaS.len=sizeof(SysConfigB_t)/2 ;  //SysConfigB_t
                       }
	                 else
	                 {
	                   RfDs.PAccStaS.ack=READ_INVALIDED ;//set read operation invalid acknowledge 
	                   break ;
	                  }
	                 tp2 =(int16_t *) RfDs.PARA_M_BUF ;//point to modify buffer
	                 for(tmp=0 ;tmp<RfDs.PAccStaS.len; tmp++)
	                 {
	                   *(tp2++)=*(tp++)  ;
	                  }//output parameter to modify buffer
                     RfDs.PARA_M_BUF[1]=RfDs.PAccStaS.len ;//Set acture data length
	                 RfDs.PAccStaS.ack=READ_FULFILLED ;//set read operation fulfilles acknowledge
	                 RfDs.PAccStaS.sta=0xaa51 ;//switch to allow write back operation status
                 }
                break ;}
  case 0xaa51: {             
                 if(RfDs.PAccDs.req==WRITE_PARA_REQ||RfDs.PAccDs.req==CLEAR_PARA_REQ)
                 {
                   RfDs.PAccStaS.sta=0xaa52 ;//switch to allow write back operation status
                  }
                 else if(RfDs.PAccDs.req==EXIT_PARA_REQ)
                 {
                   RfDs.PAccDs.req=0 ;
                   RfDs.PAccDs.pid=0 ;//Set to invalid
                   RfDs.PAccStaS.sta=0 ;
                  }
                 else if(RfDs.PAccDs.req!=READ_PARA_REQ)
                 {//no valid request pending switch to idle status
                   RfDs.PAccStaS.sta=0 ;
                  }        
                break ;}
                         
  case 0xaa52: {             
                 if(RfDs.PAccStaS.pid==0x0041)
                 {
                   tp  =(int16_t *) &APP_PA  ;
	               RfDs.PAccStaS.len = sizeof(SysConfigA_t)/2 ; 
				   if((RfDs.TArray[0]==0xfedc)&&(RfDs.TArray[1]==0xba98)&&(RfDs.TArray[2]==0x7654))
				   {  
				     RfDs.PARA_M_BUF[2]=(uint16_t ) GenAuthCode(CalReqCode()) ;//ACode_l 
				     RfDs.PARA_M_BUF[3]=(uint16_t ) (GenAuthCode(CalReqCode())>>16) ;//ACode_h 
					}
	              }    
                 else if(RfDs.PAccStaS.pid==0x0042)
                 {
                   tp  =(int16_t *) &APP_PB  ;
	               RfDs.PAccStaS.len = sizeof(SysConfigB_t)/2 ; 
				  }
                 else
                 {
                   RfDs.PAccDs.req=0 ;
                   RfDs.PAccDs.pid=0 ;//Set to invalid
                   RfDs.PAccStaS.sta=0 ;//reset to initial status 
                   break ;
                  }
                 tp2 =(int16_t *) RfDs.PARA_M_BUF ;//point to modfie buffer
                 if(RfDs.PAccDs.req==WRITE_PARA_REQ)
                 {//Be required to write back parameter to FLASH memory
                   *tp2++=RfDs.PAccStaS.pid ;
                   *tp2++=RfDs.PAccStaS.len ;//set parameter data block length
                   tp2 =(int16_t *) RfDs.PARA_M_BUF ;//pointer to modfie buffer
                   for(tmp=0 ;tmp<RfDs.PAccStaS.len ;tmp++)
                   {
                     *(tp++)=*(tp2++) ;//copy new password&parameter setting
                    }
                   ModifyPara(RfDs.PAccStaS.pid,0x0010) ;//Force to save specified parameter block
                   RfDs.PAccDs.req=0 ;
                   RfDs.PAccDs.pid=0 ;//Set to invalid
                   RfDs.PAccStaS.ack=WRITE_FULFILLED ;
                   RfDs.PAccStaS.sta=0 ;//reset to initial status 
                  }
                 else //be required to clear the parameter block
                 {//Will not clear Parameter struct header
                   RfDs.PARA_M_BUF[0]=RfDs.PAccStaS.pid ;
                   RfDs.PARA_M_BUF[1]=RfDs.PAccStaS.len ;
	               tp2 =(int16_t *) &RfDs.PARA_M_BUF[2] ;//point to first parameter data in modfie buffer
	               for(tmp=0 ;tmp<(RfDs.PAccStaS.len-2) ;tmp++)
	               {
	                 *(tp2++)=0 ;//clear all parameter
	                } 
                   ModifyPara(RfDs.PAccStaS.pid,0x0055) ;//Force to clear specified parameter block                   RfDs.PAccDs.req=0 ;
                   RfDs.PAccDs.pid=0 ;//Set to invalid
	               RfDs.PAccStaS.ack=CLEAR_FULFILLED ;
	               RfDs.PAccStaS.sta=0 ;//reset to initial status
                  }  
                break ;}
  default :   ;                       
 
 }    
}

//------------------------------------------------------------
void  ModifyPara(uint16_t PGroup,uint16_t Opc)
{//Modify parameter group in EEPROM
/*
 */
}

//-----------------
