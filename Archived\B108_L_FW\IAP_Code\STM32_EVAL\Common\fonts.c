/**
  ******************************************************************************
  * @file    fonts.c
  * <AUTHOR> Application Team
  * @version V4.5.0
  * @date    07-March-2011
  * @brief   This file provides text fonts for STM32xx-E<PERSON><PERSON>'s LCD driver. 
  ******************************************************************************
  * @attention
  *
  * THE PRESENT FIRMWARE WHICH IS FOR GUIDANCE ONLY AIMS AT PROVIDING CUSTOMERS
  * WITH CODING INFORMATION REGARDING THEIR PRODUCTS IN ORDER FOR THEM TO SAVE
  * TIME. AS A RESULT, STMICROELECTRONICS SHALL NOT BE HELD LIABLE FOR ANY
  * DIRECT, INDIRECT OR CONSEQUENTIAL DAMAGES WITH RESPECT TO ANY CLAIMS ARISING
  * FROM THE CONTENT OF SUCH FIRMWARE AND/OR THE USE MADE BY CUSTOMERS OF THE
  * CODING INFORMATION CONTAINED HEREIN IN CONNECTION WITH THEIR PRODUCTS.
  *
  * <h2><center>&copy; COPYRIGHT 2011 STMicroelectronics</center></h2>
  ******************************************************************************  
  */ 
  
/* Includes ------------------------------------------------------------------*/
#include "fonts.h"

/** @addtogroup Utilities
  * @{
  */
  
/** @addtogroup STM32_EVAL
  * @{
  */ 

/** @addtogroup Common
  * @{
  */

/** @addtogroup FONTS
  * @brief      This file includes the Fonts driver of STM32-EVAL boards.
  * @{
  */  

/** @defgroup FONTS_Private_Types
  * @{
  */ 
/**
  * @}
  */ 


/** @defgroup FONTS_Private_Defines
  * @{
  */
/**
  * @}
  */ 


/** @defgroup FONTS_Private_Macros
  * @{
  */
/**
  * @}
  */ 
  

/** @defgroup FONTS_Private_Variables
  * @{
  */
const uint16_t ASCII16x24_Table [] = {
/** 
  * @brief        Space ' '  
  */ 
         0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
         0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
         0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
/** 
  * @brief        '!'  
  */ 
         0x0000, 0x0180, 0x0180, 0x0180, 0x0180, 0x0180, 0x0180, 0x0180,
         0x0180, 0x0180, 0x0180, 0x0180, 0x0180, 0x0180, 0x0000, 0x0000,
         0x0180, 0x0180, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
/** 
  * @brief        '"'  
  */ 
         0x0000, 0x0000, 0x00CC, 0x00CC, 0x00CC, 0x00CC, 0x00CC, 0x00CC,
         0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
         0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
/** 
  * @brief        '#'  
  */ 
         0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0C60, 0x0C60,
         0x0C60, 0x0630, 0x0630, 0x1FFE, 0x1FFE, 0x0630, 0x0738, 0x0318,
         0x1FFE, 0x1FFE, 0x0318, 0x0318, 0x018C, 0x018C, 0x018C, 0x0000,
/** 
  * @brief        '$'  
  */ 
         0x0000, 0x0080, 0x03E0, 0x0FF8, 0x0E9C, 0x1C8C, 0x188C, 0x008C,
         0x0098, 0x01F8, 0x07E0, 0x0E80, 0x1C80, 0x188C, 0x188C, 0x189C,
         0x0CB8, 0x0FF0, 0x03E0, 0x0080, 0x0080, 0x0000, 0x0000, 0x0000,
/** 
  * @brief        '%'  
  */ 
         0x0000, 0x0000, 0x0000, 0x180E, 0x0C1B, 0x0C11, 0x0611, 0x0611,
         0x0311, 0x0311, 0x019B, 0x018E, 0x38C0, 0x6CC0, 0x4460, 0x4460,
         0x4430, 0x4430, 0x4418, 0x6C18, 0x380C, 0x0000, 0x0000, 0x0000,
/** 
  * @brief        '&'  
  */ 
         0x0000, 0x01E0, 0x03F0, 0x0738, 0x0618, 0x0618, 0x0330, 0x01F0,
         0x00F0, 0x00F8, 0x319C, 0x330E, 0x1E06, 0x1C06, 0x1C06, 0x3F06,
         0x73FC, 0x21F0, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
/** 
  * @brief        '''  
  */ 
         0x0000, 0x0000, 0x000C, 0x000C, 0x000C, 0x000C, 0x000C, 0x000C,
         0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
         0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
/** 
  * @brief        '('  
  */ 
         0x0000, 0x0200, 0x0300, 0x0180, 0x00C0, 0x00C0, 0x0060, 0x0060,
         0x0030, 0x0030, 0x0030, 0x0030, 0x0030, 0x0030, 0x0030, 0x0030,
         0x0060, 0x0060, 0x00C0, 0x00C0, 0x0180, 0x0300, 0x0200, 0x0000,
/** 
  * @brief        ')'  
  */ 
         0x0000, 0x0020, 0x0060, 0x00C0, 0x0180, 0x0180, 0x0300, 0x0300,
         0x0600, 0x0600, 0x0600, 0x0600, 0x0600, 0x0600, 0x0600, 0x0600,
         0x0300, 0x0300, 0x0180, 0x0180, 0x00C0, 0x0060, 0x0020, 0x0000,
/** 
  * @brief        '*'  
  */ 
         0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x00C0, 0x00C0,
         0x06D8, 0x07F8, 0x01E0, 0x0330, 0x0738, 0x0000, 0x0000, 0x0000,
         0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
/** 
  * @brief        '+'  
  */ 
         0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0180, 0x0180,
         0x0180, 0x0180, 0x0180, 0x3FFC, 0x3FFC, 0x0180, 0x0180, 0x0180,
         0x0180, 0x0180, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
/** 
  * @brief        ','  
  */ 
         0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
         0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
         0x0000, 0x0180, 0x0180, 0x0100, 0x0100, 0x0080, 0x0000, 0x0000,
/** 
  * @brief        '-'  
  */ 
         0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
         0x0000, 0x0000, 0x0000, 0x0000, 0x07E0, 0x07E0, 0x0000, 0x0000,
         0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
/** 
  * @brief        '.'  
  */ 
         0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
         0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
         0x0000, 0x00C0, 0x00C0, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
/** 
  * @brief        '/'  
  */ 
         0x0000, 0x0C00, 0x0C00, 0x0600, 0x0600, 0x0600, 0x0300, 0x0300,
         0x0300, 0x0380, 0x0180, 0x0180, 0x0180, 0x00C0, 0x00C0, 0x00C0,
         0x0060, 0x0060, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
/** 
  * @brief        '0'  
  */ 
         0x0000, 0x03E0, 0x07F0, 0x0E38, 0x0C18, 0x180C, 0x180C, 0x180C,
         0x180C, 0x180C, 0x180C, 0x180C, 0x180C, 0x180C, 0x0C18, 0x0E38,
         0x07F0, 0x03E0, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
/** 
  * @brief        '1'  
  */ 
         0x0000, 0x0100, 0x0180, 0x01C0, 0x01F0, 0x0198, 0x0188, 0x0180,
         0x0180, 0x0180, 0x0180, 0x0180, 0x0180, 0x0180, 0x0180, 0x0180,
         0x0180, 0x0180, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
/** 
  * @brief        '2'  
  */ 
         0x0000, 0x03E0, 0x0FF8, 0x0C18, 0x180C, 0x180C, 0x1800, 0x1800,
         0x0C00, 0x0600, 0x0300, 0x0180, 0x00C0, 0x0060, 0x0030, 0x0018,
         0x1FFC, 0x1FFC, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
/** 
  * @brief        '3'  
  */ 
         0x0000, 0x01E0, 0x07F8, 0x0E18, 0x0C0C, 0x0C0C, 0x0C00, 0x0600,
         0x03C0, 0x07C0, 0x0C00, 0x1800, 0x1800, 0x180C, 0x180C, 0x0C18,
         0x07F8, 0x03E0, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
/** 
  * @brief        '4'  
  */ 
         0x0000, 0x0C00, 0x0E00, 0x0F00, 0x0F00, 0x0D80, 0x0CC0, 0x0C60,
         0x0C60, 0x0C30, 0x0C18, 0x0C0C, 0x3FFC, 0x3FFC, 0x0C00, 0x0C00,
         0x0C00, 0x0C00, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
/** 
  * @brief        '5'  
  */ 
         0x0000, 0x0FF8, 0x0FF8, 0x0018, 0x0018, 0x000C, 0x03EC, 0x07FC,
         0x0E1C, 0x1C00, 0x1800, 0x1800, 0x1800, 0x180C, 0x0C1C, 0x0E18,
         0x07F8, 0x03E0, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
/** 
  * @brief        '6'  
  */ 
         0x0000, 0x07C0, 0x0FF0, 0x1C38, 0x1818, 0x0018, 0x000C, 0x03CC,
         0x0FEC, 0x0E3C, 0x1C1C, 0x180C, 0x180C, 0x180C, 0x1C18, 0x0E38,
         0x07F0, 0x03E0, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
/** 
  * @brief        '7'  
  */ 
         0x0000, 0x1FFC, 0x1FFC, 0x0C00, 0x0600, 0x0600, 0x0300, 0x0380,
         0x0180, 0x01C0, 0x00C0, 0x00E0, 0x0060, 0x0060, 0x0070, 0x0030,
         0x0030, 0x0030, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
/** 
  * @brief        '8'  
  */ 
         0x0000, 0x03E0, 0x07F0, 0x0E38, 0x0C18, 0x0C18, 0x0C18, 0x0638,
         0x07F0, 0x07F0, 0x0C18, 0x180C, 0x180C, 0x180C, 0x180C, 0x0C38,
         0x0FF8, 0x03E0, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
/** 
  * @brief        '9'  
  */ 
         0x0000, 0x03E0, 0x07F0, 0x0E38, 0x0C1C, 0x180C, 0x180C, 0x180C,
         0x1C1C, 0x1E38, 0x1BF8, 0x19E0, 0x1800, 0x0C00, 0x0C00, 0x0E1C,
         0x07F8, 0x01F0, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
/** 
  * @brief        ':'  
  */ 
         0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0180, 0x0180,
         0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
         0x0180, 0x0180, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
/** 
  * @brief        ';'  
  */ 
         0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0180, 0x0180,
         0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
         0x0180, 0x0180, 0x0100, 0x0100, 0x0080, 0x0000, 0x0000, 0x0000,
/** 
  * @brief        '<'  
  */ 
         0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
         0x1000, 0x1C00, 0x0F80, 0x03E0, 0x00F8, 0x0018, 0x00F8, 0x03E0,
         0x0F80, 0x1C00, 0x1000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
/** 
  * @brief        '='  
  */ 
         0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
         0x1FF8, 0x0000, 0x0000, 0x0000, 0x1FF8, 0x0000, 0x0000, 0x0000,
         0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
/** 
  * @brief        '>'  
  */ 
         0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
         0x0008, 0x0038, 0x01F0, 0x07C0, 0x1F00, 0x1800, 0x1F00, 0x07C0,
         0x01F0, 0x0038, 0x0008, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
/** 
  * @brief        '?'  
  */ 
         0x0000, 0x03E0, 0x0FF8, 0x0C18, 0x180C, 0x180C, 0x1800, 0x0C00,
         0x0600, 0x0300, 0x0180, 0x00C0, 0x00C0, 0x00C0, 0x0000, 0x0000,
         0x00C0, 0x00C0, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
/** 
  * @brief        '@'  
  */ 
         0x0000, 0x0000, 0x07E0, 0x1818, 0x2004, 0x29C2, 0x4A22, 0x4411,
         0x4409, 0x4409, 0x4409, 0x2209, 0x1311, 0x0CE2, 0x4002, 0x2004,
         0x1818, 0x07E0, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
/** 
  * @brief        'A'  
  */ 
         0x0000, 0x0380, 0x0380, 0x06C0, 0x06C0, 0x06C0, 0x0C60, 0x0C60,
         0x1830, 0x1830, 0x1830, 0x3FF8, 0x3FF8, 0x701C, 0x600C, 0x600C,
         0xC006, 0xC006, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
/** 
  * @brief        'B'  
  */ 
         0x0000, 0x03FC, 0x0FFC, 0x0C0C, 0x180C, 0x180C, 0x180C, 0x0C0C,
         0x07FC, 0x0FFC, 0x180C, 0x300C, 0x300C, 0x300C, 0x300C, 0x180C,
         0x1FFC, 0x07FC, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
/** 
  * @brief        'C'  
  */ 
         0x0000, 0x07C0, 0x1FF0, 0x3838, 0x301C, 0x700C, 0x6006, 0x0006,
         0x0006, 0x0006, 0x0006, 0x0006, 0x0006, 0x6006, 0x700C, 0x301C,
         0x1FF0, 0x07E0, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
/** 
  * @brief        'D'  
  */ 
         0x0000, 0x03FE, 0x0FFE, 0x0E06, 0x1806, 0x1806, 0x3006, 0x3006,
         0x3006, 0x3006, 0x3006, 0x3006, 0x3006, 0x1806, 0x1806, 0x0E06,
         0x0FFE, 0x03FE, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
/** 
  * @brief        'E'  
  */ 
         0x0000, 0x3FFC, 0x3FFC, 0x000C, 0x000C, 0x000C, 0x000C, 0x000C,
         0x1FFC, 0x1FFC, 0x000C, 0x000C, 0x000C, 0x000C, 0x000C, 0x000C,
         0x3FFC, 0x3FFC, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
/** 
  * @brief        'F'  
  */ 
         0x0000, 0x3FF8, 0x3FF8, 0x0018, 0x0018, 0x0018, 0x0018, 0x0018,
         0x1FF8, 0x1FF8, 0x0018, 0x0018, 0x0018, 0x0018, 0x0018, 0x0018,
         0x0018, 0x0018, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
/** 
  * @brief        'G'  
  */ 
         0x0000, 0x0FE0, 0x3FF8, 0x783C, 0x600E, 0xE006, 0xC007, 0x0003,
         0x0003, 0xFE03, 0xFE03, 0xC003, 0xC007, 0xC006, 0xC00E, 0xF03C,
         0x3FF8, 0x0FE0, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
/** 
  * @brief        'H'  
  */ 
         0x0000, 0x300C, 0x300C, 0x300C, 0x300C, 0x300C, 0x300C, 0x300C,
         0x3FFC, 0x3FFC, 0x300C, 0x300C, 0x300C, 0x300C, 0x300C, 0x300C,
         0x300C, 0x300C, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
/** 
  * @brief        'I'  
  */ 
         0x0000, 0x0180, 0x0180, 0x0180, 0x0180, 0x0180, 0x0180, 0x0180,
         0x0180, 0x0180, 0x0180, 0x0180, 0x0180, 0x0180, 0x0180, 0x0180,
         0x0180, 0x0180, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
/** 
  * @brief        'J'  
  */ 
         0x0000, 0x0600, 0x0600, 0x0600, 0x0600, 0x0600, 0x0600, 0x0600,
         0x0600, 0x0600, 0x0600, 0x0600, 0x0600, 0x0618, 0x0618, 0x0738,
         0x03F0, 0x01E0, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
/** 
  * @brief        'K'  
  */ 
         0x0000, 0x3006, 0x1806, 0x0C06, 0x0606, 0x0306, 0x0186, 0x00C6,
         0x0066, 0x0076, 0x00DE, 0x018E, 0x0306, 0x0606, 0x0C06, 0x1806,
         0x3006, 0x6006, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
/** 
  * @brief        'L'  
  */ 
         0x0000, 0x0018, 0x0018, 0x0018, 0x0018, 0x0018, 0x0018, 0x0018,
         0x0018, 0x0018, 0x0018, 0x0018, 0x0018, 0x0018, 0x0018, 0x0018,
         0x1FF8, 0x1FF8, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
/** 
  * @brief        'M'  
  */ 
         0x0000, 0xE00E, 0xF01E, 0xF01E, 0xF01E, 0xD836, 0xD836, 0xD836,
         0xD836, 0xCC66, 0xCC66, 0xCC66, 0xC6C6, 0xC6C6, 0xC6C6, 0xC6C6,
         0xC386, 0xC386, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
/** 
  * @brief        'N'  
  */ 
         0x0000, 0x300C, 0x301C, 0x303C, 0x303C, 0x306C, 0x306C, 0x30CC,
         0x30CC, 0x318C, 0x330C, 0x330C, 0x360C, 0x360C, 0x3C0C, 0x3C0C,
         0x380C, 0x300C, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
/** 
  * @brief        'O'  
  */ 
         0x0000, 0x07E0, 0x1FF8, 0x381C, 0x700E, 0x6006, 0xC003, 0xC003,
         0xC003, 0xC003, 0xC003, 0xC003, 0xC003, 0x6006, 0x700E, 0x381C,
         0x1FF8, 0x07E0, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
/** 
  * @brief        'P'  
  */ 
         0x0000, 0x0FFC, 0x1FFC, 0x380C, 0x300C, 0x300C, 0x300C, 0x300C,
         0x180C, 0x1FFC, 0x07FC, 0x000C, 0x000C, 0x000C, 0x000C, 0x000C,
         0x000C, 0x000C, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
/** 
  * @brief        'Q'  
  */ 
         0x0000, 0x07E0, 0x1FF8, 0x381C, 0x700E, 0x6006, 0xE003, 0xC003,
         0xC003, 0xC003, 0xC003, 0xC003, 0xE007, 0x6306, 0x3F0E, 0x3C1C,
         0x3FF8, 0xF7E0, 0xC000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
/** 
  * @brief        'R'  
  */ 
         0x0000, 0x0FFE, 0x1FFE, 0x3806, 0x3006, 0x3006, 0x3006, 0x3806,
         0x1FFE, 0x07FE, 0x0306, 0x0606, 0x0C06, 0x1806, 0x1806, 0x3006,
         0x3006, 0x6006, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
/** 
  * @brief        'S'  
  */ 
         0x0000, 0x03E0, 0x0FF8, 0x0C1C, 0x180C, 0x180C, 0x000C, 0x001C,
         0x03F8, 0x0FE0, 0x1E00, 0x3800, 0x3006, 0x3006, 0x300E, 0x1C1C,
         0x0FF8, 0x07E0, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
/** 
  * @brief        'T'  
  */ 
         0x0000, 0x7FFE, 0x7FFE, 0x0180, 0x0180, 0x0180, 0x0180, 0x0180,
         0x0180, 0x0180, 0x0180, 0x0180, 0x0180, 0x0180, 0x0180, 0x0180,
         0x0180, 0x0180, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
/** 
  * @brief        'U'  
  */ 
         0x0000, 0x300C, 0x300C, 0x300C, 0x300C, 0x300C, 0x300C, 0x300C,
         0x300C, 0x300C, 0x300C, 0x300C, 0x300C, 0x300C, 0x300C, 0x1818,
         0x1FF8, 0x07E0, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
/** 
  * @brief        'V'  
  */ 
         0x0000, 0x6003, 0x3006, 0x3006, 0x3006, 0x180C, 0x180C, 0x180C,
         0x0C18, 0x0C18, 0x0E38, 0x0630, 0x0630, 0x0770, 0x0360, 0x0360,
         0x01C0, 0x01C0, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
/** 
  * @brief        'W'  
  */ 
         0x0000, 0x6003, 0x61C3, 0x61C3, 0x61C3, 0x3366, 0x3366, 0x3366,
         0x3366, 0x3366, 0x3366, 0x1B6C, 0x1B6C, 0x1B6C, 0x1A2C, 0x1E3C,
         0x0E38, 0x0E38, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
/** 
  * @brief        'X'  
  */ 
         0x0000, 0xE00F, 0x700C, 0x3018, 0x1830, 0x0C70, 0x0E60, 0x07C0,
         0x0380, 0x0380, 0x03C0, 0x06E0, 0x0C70, 0x1C30, 0x1818, 0x300C,
         0x600E, 0xE007, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
/** 
  * @brief        'Y'  
  */ 
         0x0000, 0xC003, 0x6006, 0x300C, 0x381C, 0x1838, 0x0C30, 0x0660,
         0x07E0, 0x03C0, 0x0180, 0x0180, 0x0180, 0x0180, 0x0180, 0x0180,
         0x0180, 0x0180, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
/** 
  * @brief        'Z'  
  */ 
         0x0000, 0x7FFC, 0x7FFC, 0x6000, 0x3000, 0x1800, 0x0C00, 0x0600,
         0x0300, 0x0180, 0x00C0, 0x0060, 0x0030, 0x0018, 0x000C, 0x0006,
         0x7FFE, 0x7FFE, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
/** 
  * @brief        '['  
  */ 
         0x0000, 0x03E0, 0x03E0, 0x0060, 0x0060, 0x0060, 0x0060, 0x0060,
         0x0060, 0x0060, 0x0060, 0x0060, 0x0060, 0x0060, 0x0060, 0x0060,
         0x0060, 0x0060, 0x0060, 0x0060, 0x0060, 0x03E0, 0x03E0, 0x0000,
/** 
  * @brief        '\'  
  */ 
         0x0000, 0x0030, 0x0030, 0x0060, 0x0060, 0x0060, 0x00C0, 0x00C0,
         0x00C0, 0x01C0, 0x0180, 0x0180, 0x0180, 0x0300, 0x0300, 0x0300,
         0x0600, 0x0600, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
/** 
  * @brief        ']'  
  */ 
         0x0000, 0x03E0, 0x03E0, 0x0300, 0x0300, 0x0300, 0x0300, 0x0300,
         0x0300, 0x0300, 0x0300, 0x0300, 0x0300, 0x0300, 0x0300, 0x0300,
         0x0300, 0x0300, 0x0300, 0x0300, 0x0300, 0x03E0, 0x03E0, 0x0000,
/** 
  * @brief        '^'  
  */ 
         0x0000, 0x0000, 0x01C0, 0x01C0, 0x0360, 0x0360, 0x0360, 0x0630,
         0x0630, 0x0C18, 0x0C18, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
         0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
/** 
  * @brief        '_'  
  */ 
         0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
         0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
         0x0000, 0xFFFF, 0xFFFF, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
/** 
  * @brief        '''  
  */ 
         0x0000, 0x000C, 0x000C, 0x000C, 0x000C, 0x000C, 0x000C, 0x0000,
         0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
         0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
/** 
  * @brief        'a'  
  */ 
         0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x03F0, 0x07F8,
         0x0C1C, 0x0C0C, 0x0F00, 0x0FF0, 0x0CF8, 0x0C0C, 0x0C0C, 0x0F1C,
         0x0FF8, 0x18F0, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
/** 
  * @brief        'b'  
  */ 
         0x0000, 0x0018, 0x0018, 0x0018, 0x0018, 0x0018, 0x03D8, 0x0FF8,
         0x0C38, 0x1818, 0x1818, 0x1818, 0x1818, 0x1818, 0x1818, 0x0C38,
         0x0FF8, 0x03D8, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
/** 
  * @brief        'c'  
  */ 
         0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x03C0, 0x07F0,
         0x0E30, 0x0C18, 0x0018, 0x0018, 0x0018, 0x0018, 0x0C18, 0x0E30,
         0x07F0, 0x03C0, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
/** 
  * @brief        'd'  
  */ 
         0x0000, 0x1800, 0x1800, 0x1800, 0x1800, 0x1800, 0x1BC0, 0x1FF0,
         0x1C30, 0x1818, 0x1818, 0x1818, 0x1818, 0x1818, 0x1818, 0x1C30,
         0x1FF0, 0x1BC0, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
/** 
  * @brief        'e'  
  */ 
         0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x03C0, 0x0FF0,
         0x0C30, 0x1818, 0x1FF8, 0x1FF8, 0x0018, 0x0018, 0x1838, 0x1C30,
         0x0FF0, 0x07C0, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
/** 
  * @brief        'f'  
  */ 
         0x0000, 0x0F80, 0x0FC0, 0x00C0, 0x00C0, 0x00C0, 0x07F0, 0x07F0,
         0x00C0, 0x00C0, 0x00C0, 0x00C0, 0x00C0, 0x00C0, 0x00C0, 0x00C0,
         0x00C0, 0x00C0, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
/** 
  * @brief        'g'  
  */ 
         0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0DE0, 0x0FF8,
         0x0E18, 0x0C0C, 0x0C0C, 0x0C0C, 0x0C0C, 0x0C0C, 0x0C0C, 0x0E18,
         0x0FF8, 0x0DE0, 0x0C00, 0x0C0C, 0x061C, 0x07F8, 0x01F0, 0x0000,
/** 
  * @brief        'h'  
  */ 
         0x0000, 0x0018, 0x0018, 0x0018, 0x0018, 0x0018, 0x07D8, 0x0FF8,
         0x1C38, 0x1818, 0x1818, 0x1818, 0x1818, 0x1818, 0x1818, 0x1818,
         0x1818, 0x1818, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
/** 
  * @brief        'i'  
  */ 
         0x0000, 0x00C0, 0x00C0, 0x0000, 0x0000, 0x0000, 0x00C0, 0x00C0,
         0x00C0, 0x00C0, 0x00C0, 0x00C0, 0x00C0, 0x00C0, 0x00C0, 0x00C0,
         0x00C0, 0x00C0, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
/** 
  * @brief        'j'  
  */ 
         0x0000, 0x00C0, 0x00C0, 0x0000, 0x0000, 0x0000, 0x00C0, 0x00C0,
         0x00C0, 0x00C0, 0x00C0, 0x00C0, 0x00C0, 0x00C0, 0x00C0, 0x00C0,
         0x00C0, 0x00C0, 0x00C0, 0x00C0, 0x00C0, 0x00F8, 0x0078, 0x0000,
/** 
  * @brief        'k'  
  */ 
         0x0000, 0x000C, 0x000C, 0x000C, 0x000C, 0x000C, 0x0C0C, 0x060C,
         0x030C, 0x018C, 0x00CC, 0x006C, 0x00FC, 0x019C, 0x038C, 0x030C,
         0x060C, 0x0C0C, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
/** 
  * @brief        'l'  
  */ 
         0x0000, 0x00C0, 0x00C0, 0x00C0, 0x00C0, 0x00C0, 0x00C0, 0x00C0,
         0x00C0, 0x00C0, 0x00C0, 0x00C0, 0x00C0, 0x00C0, 0x00C0, 0x00C0,
         0x00C0, 0x00C0, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
/** 
  * @brief        'm'  
  */ 
         0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x3C7C, 0x7EFF,
         0xE3C7, 0xC183, 0xC183, 0xC183, 0xC183, 0xC183, 0xC183, 0xC183,
         0xC183, 0xC183, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
/** 
  * @brief        'n'  
  */ 
         0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0798, 0x0FF8,
         0x1C38, 0x1818, 0x1818, 0x1818, 0x1818, 0x1818, 0x1818, 0x1818,
         0x1818, 0x1818, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
/** 
  * @brief        'o'  
  */ 
         0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x03C0, 0x0FF0,
         0x0C30, 0x1818, 0x1818, 0x1818, 0x1818, 0x1818, 0x1818, 0x0C30,
         0x0FF0, 0x03C0, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
/** 
  * @brief        'p'  
  */ 
         0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x03D8, 0x0FF8,
         0x0C38, 0x1818, 0x1818, 0x1818, 0x1818, 0x1818, 0x1818, 0x0C38,
         0x0FF8, 0x03D8, 0x0018, 0x0018, 0x0018, 0x0018, 0x0018, 0x0000,
/** 
  * @brief        'q'  
  */ 
         0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x1BC0, 0x1FF0,
         0x1C30, 0x1818, 0x1818, 0x1818, 0x1818, 0x1818, 0x1818, 0x1C30,
         0x1FF0, 0x1BC0, 0x1800, 0x1800, 0x1800, 0x1800, 0x1800, 0x0000,
/** 
  * @brief        'r'  
  */ 
         0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x07B0, 0x03F0,
         0x0070, 0x0030, 0x0030, 0x0030, 0x0030, 0x0030, 0x0030, 0x0030,
         0x0030, 0x0030, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
/** 
  * @brief        's'  
  */ 
         0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x03E0, 0x03F0,
         0x0E38, 0x0C18, 0x0038, 0x03F0, 0x07C0, 0x0C00, 0x0C18, 0x0E38,
         0x07F0, 0x03E0, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
/** 
  * @brief        't'  
  */ 
         0x0000, 0x0000, 0x0080, 0x00C0, 0x00C0, 0x00C0, 0x07F0, 0x07F0,
         0x00C0, 0x00C0, 0x00C0, 0x00C0, 0x00C0, 0x00C0, 0x00C0, 0x00C0,
         0x07C0, 0x0780, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
/** 
  * @brief        'u'  
  */ 
         0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x1818, 0x1818,
         0x1818, 0x1818, 0x1818, 0x1818, 0x1818, 0x1818, 0x1818, 0x1C38,
         0x1FF0, 0x19E0, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
/** 
  * @brief        'v'  
  */ 
         0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x180C, 0x0C18,
         0x0C18, 0x0C18, 0x0630, 0x0630, 0x0630, 0x0360, 0x0360, 0x0360,
         0x01C0, 0x01C0, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
/** 
  * @brief        'w'  
  */ 
         0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x41C1, 0x41C1,
         0x61C3, 0x6363, 0x6363, 0x6363, 0x3636, 0x3636, 0x3636, 0x1C1C,
         0x1C1C, 0x1C1C, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
/** 
  * @brief        'x'  
  */ 
         0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x381C, 0x1C38,
         0x0C30, 0x0660, 0x0360, 0x0360, 0x0360, 0x0360, 0x0660, 0x0C30,
         0x1C38, 0x381C, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
/** 
  * @brief        'y'  
  */ 
         0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x3018, 0x1830,
         0x1830, 0x1870, 0x0C60, 0x0C60, 0x0CE0, 0x06C0, 0x06C0, 0x0380,
         0x0380, 0x0380, 0x0180, 0x0180, 0x01C0, 0x00F0, 0x0070, 0x0000,
/** 
  * @brief        'z'  
  */ 
         0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x1FFC, 0x1FFC,
         0x0C00, 0x0600, 0x0300, 0x0180, 0x00C0, 0x0060, 0x0030, 0x0018,
         0x1FFC, 0x1FFC, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
/** 
  * @brief        '{'  
  */ 
         0x0000, 0x0300, 0x0180, 0x00C0, 0x00C0, 0x00C0, 0x00C0, 0x00C0,
         0x00C0, 0x0060, 0x0060, 0x0030, 0x0060, 0x0040, 0x00C0, 0x00C0,
         0x00C0, 0x00C0, 0x00C0, 0x00C0, 0x0180, 0x0300, 0x0000, 0x0000,
/** 
  * @brief        '|'  
  */ 
         0x0000, 0x0180, 0x0180, 0x0180, 0x0180, 0x0180, 0x0180, 0x0180,
         0x0180, 0x0180, 0x0180, 0x0180, 0x0180, 0x0180, 0x0180, 0x0180,
         0x0180, 0x0180, 0x0180, 0x0180, 0x0180, 0x0180, 0x0180, 0x0000,
/** 
  * @brief        '}'  
  */ 
         0x0000, 0x0060, 0x00C0, 0x01C0, 0x0180, 0x0180, 0x0180, 0x0180,
         0x0180, 0x0300, 0x0300, 0x0600, 0x0300, 0x0100, 0x0180, 0x0180,
         0x0180, 0x0180, 0x0180, 0x0180, 0x00C0, 0x0060, 0x0000, 0x0000,
/** 
  * @brief        '~'  
  */ 
         0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
         0x10F0, 0x1FF8, 0x0F08, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
         0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000};

const uint16_t ASCII12x12_Table [] = {
    0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,
    0x0000,0x2000,0x2000,0x2000,0x2000,0x2000,0x2000,0x2000,0x0000,0x2000,0x0000,0x0000,
    0x0000,0x5000,0x5000,0x5000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,
    0x0000,0x0900,0x0900,0x1200,0x7f00,0x1200,0x7f00,0x1200,0x2400,0x2400,0x0000,0x0000,
    0x1000,0x3800,0x5400,0x5000,0x5000,0x3800,0x1400,0x5400,0x5400,0x3800,0x1000,0x0000,
    0x0000,0x3080,0x4900,0x4900,0x4a00,0x32c0,0x0520,0x0920,0x0920,0x10c0,0x0000,0x0000,
    0x0000,0x0c00,0x1200,0x1200,0x1400,0x1800,0x2500,0x2300,0x2300,0x1d80,0x0000,0x0000,
    0x0000,0x4000,0x4000,0x4000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,
    0x0000,0x0800,0x1000,0x1000,0x2000,0x2000,0x2000,0x2000,0x2000,0x2000,0x1000,0x1000,
    0x0000,0x4000,0x2000,0x2000,0x1000,0x1000,0x1000,0x1000,0x1000,0x1000,0x2000,0x2000,
    0x0000,0x2000,0x7000,0x2000,0x5000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,
    0x0000,0x0000,0x0000,0x0800,0x0800,0x7f00,0x0800,0x0800,0x0000,0x0000,0x0000,0x0000,
    0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x2000,0x2000,0x4000,
    0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x7000,0x0000,0x0000,0x0000,0x0000,0x0000,
    0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x2000,0x0000,0x0000,
    0x0000,0x1000,0x1000,0x1000,0x2000,0x2000,0x2000,0x2000,0x4000,0x4000,0x0000,0x0000,
    0x0000,0x1000,0x2800,0x4400,0x4400,0x4400,0x4400,0x4400,0x2800,0x1000,0x0000,0x0000,
    0x0000,0x1000,0x3000,0x5000,0x1000,0x1000,0x1000,0x1000,0x1000,0x1000,0x0000,0x0000,
    0x0000,0x3000,0x4800,0x4400,0x0400,0x0800,0x1000,0x2000,0x4000,0x7c00,0x0000,0x0000,
    0x0000,0x3000,0x4800,0x0400,0x0800,0x1000,0x0800,0x4400,0x4800,0x3000,0x0000,0x0000,
    0x0000,0x0800,0x1800,0x1800,0x2800,0x2800,0x4800,0x7c00,0x0800,0x0800,0x0000,0x0000,
    0x0000,0x3c00,0x2000,0x4000,0x7000,0x4800,0x0400,0x4400,0x4800,0x3000,0x0000,0x0000,
    0x0000,0x1800,0x2400,0x4000,0x5000,0x6800,0x4400,0x4400,0x2800,0x1000,0x0000,0x0000,
    0x0000,0x7c00,0x0400,0x0800,0x1000,0x1000,0x1000,0x2000,0x2000,0x2000,0x0000,0x0000,
    0x0000,0x1000,0x2800,0x4400,0x2800,0x1000,0x2800,0x4400,0x2800,0x1000,0x0000,0x0000,
    0x0000,0x1000,0x2800,0x4400,0x4400,0x2c00,0x1400,0x0400,0x4800,0x3000,0x0000,0x0000,
    0x0000,0x0000,0x0000,0x2000,0x0000,0x0000,0x0000,0x0000,0x0000,0x2000,0x0000,0x0000,
    0x0000,0x0000,0x0000,0x2000,0x0000,0x0000,0x0000,0x0000,0x0000,0x2000,0x2000,0x4000,
    0x0000,0x0000,0x0400,0x0800,0x3000,0x4000,0x3000,0x0800,0x0400,0x0000,0x0000,0x0000,
    0x0000,0x0000,0x0000,0x7c00,0x0000,0x0000,0x7c00,0x0000,0x0000,0x0000,0x0000,0x0000,
    0x0000,0x0000,0x4000,0x2000,0x1800,0x0400,0x1800,0x2000,0x4000,0x0000,0x0000,0x0000,
    0x0000,0x3800,0x6400,0x4400,0x0400,0x0800,0x1000,0x1000,0x0000,0x1000,0x0000,0x0000,
    0x0000,0x0f80,0x1040,0x2ea0,0x51a0,0x5120,0x5120,0x5120,0x5320,0x4dc0,0x2020,0x1040,
    0x0000,0x0800,0x1400,0x1400,0x1400,0x2200,0x3e00,0x2200,0x4100,0x4100,0x0000,0x0000,
    0x0000,0x3c00,0x2200,0x2200,0x2200,0x3c00,0x2200,0x2200,0x2200,0x3c00,0x0000,0x0000,
    0x0000,0x0e00,0x1100,0x2100,0x2000,0x2000,0x2000,0x2100,0x1100,0x0e00,0x0000,0x0000,
    0x0000,0x3c00,0x2200,0x2100,0x2100,0x2100,0x2100,0x2100,0x2200,0x3c00,0x0000,0x0000,
    0x0000,0x3e00,0x2000,0x2000,0x2000,0x3e00,0x2000,0x2000,0x2000,0x3e00,0x0000,0x0000,
    0x0000,0x3e00,0x2000,0x2000,0x2000,0x3c00,0x2000,0x2000,0x2000,0x2000,0x0000,0x0000,
    0x0000,0x0e00,0x1100,0x2100,0x2000,0x2700,0x2100,0x2100,0x1100,0x0e00,0x0000,0x0000,
    0x0000,0x2100,0x2100,0x2100,0x2100,0x3f00,0x2100,0x2100,0x2100,0x2100,0x0000,0x0000,
    0x0000,0x2000,0x2000,0x2000,0x2000,0x2000,0x2000,0x2000,0x2000,0x2000,0x0000,0x0000,
    0x0000,0x0800,0x0800,0x0800,0x0800,0x0800,0x0800,0x4800,0x4800,0x3000,0x0000,0x0000,
    0x0000,0x2200,0x2400,0x2800,0x2800,0x3800,0x2800,0x2400,0x2400,0x2200,0x0000,0x0000,
    0x0000,0x2000,0x2000,0x2000,0x2000,0x2000,0x2000,0x2000,0x2000,0x3e00,0x0000,0x0000,
    0x0000,0x2080,0x3180,0x3180,0x3180,0x2a80,0x2a80,0x2a80,0x2a80,0x2480,0x0000,0x0000,
    0x0000,0x2100,0x3100,0x3100,0x2900,0x2900,0x2500,0x2300,0x2300,0x2100,0x0000,0x0000,
    0x0000,0x0c00,0x1200,0x2100,0x2100,0x2100,0x2100,0x2100,0x1200,0x0c00,0x0000,0x0000,
    0x0000,0x3c00,0x2200,0x2200,0x2200,0x3c00,0x2000,0x2000,0x2000,0x2000,0x0000,0x0000,
    0x0000,0x0c00,0x1200,0x2100,0x2100,0x2100,0x2100,0x2100,0x1600,0x0d00,0x0100,0x0000,
    0x0000,0x3e00,0x2100,0x2100,0x2100,0x3e00,0x2400,0x2200,0x2100,0x2080,0x0000,0x0000,
    0x0000,0x1c00,0x2200,0x2200,0x2000,0x1c00,0x0200,0x2200,0x2200,0x1c00,0x0000,0x0000,
    0x0000,0x3e00,0x0800,0x0800,0x0800,0x0800,0x0800,0x0800,0x0800,0x0800,0x0000,0x0000,
    0x0000,0x2100,0x2100,0x2100,0x2100,0x2100,0x2100,0x2100,0x1200,0x0c00,0x0000,0x0000,
    0x0000,0x4100,0x4100,0x2200,0x2200,0x2200,0x1400,0x1400,0x1400,0x0800,0x0000,0x0000,
    0x0000,0x4440,0x4a40,0x2a40,0x2a80,0x2a80,0x2a80,0x2a80,0x2a80,0x1100,0x0000,0x0000,
    0x0000,0x4100,0x2200,0x1400,0x1400,0x0800,0x1400,0x1400,0x2200,0x4100,0x0000,0x0000,
    0x0000,0x4100,0x2200,0x2200,0x1400,0x0800,0x0800,0x0800,0x0800,0x0800,0x0000,0x0000,
    0x0000,0x7e00,0x0200,0x0400,0x0800,0x1000,0x1000,0x2000,0x4000,0x7e00,0x0000,0x0000,
    0x0000,0x3000,0x2000,0x2000,0x2000,0x2000,0x2000,0x2000,0x2000,0x2000,0x2000,0x2000,
    0x0000,0x4000,0x4000,0x2000,0x2000,0x2000,0x2000,0x2000,0x1000,0x1000,0x0000,0x0000,
    0x0000,0x6000,0x2000,0x2000,0x2000,0x2000,0x2000,0x2000,0x2000,0x2000,0x2000,0x2000,
    0x0000,0x1000,0x2800,0x2800,0x2800,0x4400,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,
    0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x7e00,
    0x4000,0x2000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,
    0x0000,0x0000,0x0000,0x3800,0x4400,0x0400,0x3c00,0x4400,0x4400,0x3c00,0x0000,0x0000,
    0x0000,0x4000,0x4000,0x5800,0x6400,0x4400,0x4400,0x4400,0x6400,0x5800,0x0000,0x0000,
    0x0000,0x0000,0x0000,0x3000,0x4800,0x4000,0x4000,0x4000,0x4800,0x3000,0x0000,0x0000,
    0x0000,0x0400,0x0400,0x3400,0x4c00,0x4400,0x4400,0x4400,0x4c00,0x3400,0x0000,0x0000,
    0x0000,0x0000,0x0000,0x3800,0x4400,0x4400,0x7c00,0x4000,0x4400,0x3800,0x0000,0x0000,
    0x0000,0x6000,0x4000,0xe000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x0000,0x0000,
    0x0000,0x0000,0x0000,0x3400,0x4c00,0x4400,0x4400,0x4400,0x4c00,0x3400,0x0400,0x4400,
    0x0000,0x4000,0x4000,0x5800,0x6400,0x4400,0x4400,0x4400,0x4400,0x4400,0x0000,0x0000,
    0x0000,0x4000,0x0000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x0000,0x0000,
    0x0000,0x4000,0x0000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,
    0x0000,0x4000,0x4000,0x4800,0x5000,0x6000,0x5000,0x5000,0x4800,0x4800,0x0000,0x0000,
    0x0000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x0000,0x0000,
    0x0000,0x0000,0x0000,0x5200,0x6d00,0x4900,0x4900,0x4900,0x4900,0x4900,0x0000,0x0000,
    0x0000,0x0000,0x0000,0x5800,0x6400,0x4400,0x4400,0x4400,0x4400,0x4400,0x0000,0x0000,
    0x0000,0x0000,0x0000,0x3800,0x4400,0x4400,0x4400,0x4400,0x4400,0x3800,0x0000,0x0000,
    0x0000,0x0000,0x0000,0x5800,0x6400,0x4400,0x4400,0x4400,0x6400,0x5800,0x4000,0x4000,
    0x0000,0x0000,0x0000,0x3400,0x4c00,0x4400,0x4400,0x4400,0x4c00,0x3400,0x0400,0x0400,
    0x0000,0x0000,0x0000,0x5000,0x6000,0x4000,0x4000,0x4000,0x4000,0x4000,0x0000,0x0000,
    0x0000,0x0000,0x0000,0x3000,0x4800,0x4000,0x3000,0x0800,0x4800,0x3000,0x0000,0x0000,
    0x0000,0x4000,0x4000,0xe000,0x4000,0x4000,0x4000,0x4000,0x4000,0x6000,0x0000,0x0000,
    0x0000,0x0000,0x0000,0x4400,0x4400,0x4400,0x4400,0x4400,0x4c00,0x3400,0x0000,0x0000,
    0x0000,0x0000,0x0000,0x4400,0x4400,0x2800,0x2800,0x2800,0x2800,0x1000,0x0000,0x0000,
    0x0000,0x0000,0x0000,0x4900,0x4900,0x5500,0x5500,0x5500,0x5500,0x2200,0x0000,0x0000,
    0x0000,0x0000,0x0000,0x4400,0x2800,0x2800,0x1000,0x2800,0x2800,0x4400,0x0000,0x0000,
    0x0000,0x0000,0x0000,0x4400,0x4400,0x2800,0x2800,0x2800,0x1000,0x1000,0x1000,0x1000,
    0x0000,0x0000,0x0000,0x7800,0x0800,0x1000,0x2000,0x2000,0x4000,0x7800,0x0000,0x0000,
    0x0000,0x1000,0x2000,0x2000,0x2000,0x2000,0x4000,0x2000,0x2000,0x2000,0x2000,0x2000,
    0x0000,0x2000,0x2000,0x2000,0x2000,0x2000,0x2000,0x2000,0x2000,0x2000,0x2000,0x2000,
    0x0000,0x4000,0x2000,0x2000,0x2000,0x2000,0x1000,0x2000,0x2000,0x2000,0x2000,0x2000,
    0x0000,0x0000,0x0000,0x0000,0x7400,0x5800,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,
    0x0000,0x0000,0x7000,0x5000,0x5000,0x5000,0x5000,0x5000,0x5000,0x7000,0x0000,0x0000};

const uint16_t ASCII8x12_Table [] = {
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x10,0x10,0x10,0x10,0x10,0x10,0x00,0x10,0x00,
    0x00,0x00,0x00,0x28,0x28,0x28,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x14,0x14,0x3e,0x14,0x28,0x7c,0x28,0x28,0x00,
    0x00,0x00,0x10,0x38,0x54,0x50,0x38,0x14,0x14,0x54,0x38,0x10,
    0x00,0x00,0x00,0x44,0xa8,0xa8,0x50,0x14,0x1a,0x2a,0x24,0x00,
    0x00,0x00,0x00,0x20,0x50,0x50,0x20,0xe8,0x98,0x98,0x60,0x00,
    0x00,0x00,0x00,0x80,0x80,0x80,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x40,0x80,0x80,0x80,0x80,0x80,0x80,0x80,0x80,
    0x00,0x00,0x00,0x80,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x40,
    0x00,0x00,0x00,0x40,0xe0,0x40,0xa0,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x20,0x20,0xf8,0x20,0x20,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x40,0x40,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xc0,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x40,0x00,
    0x00,0x00,0x00,0x20,0x20,0x20,0x40,0x40,0x80,0x80,0x80,0x00,
    0x00,0x00,0x00,0x60,0x90,0x90,0x90,0x90,0x90,0x90,0x60,0x00,
    0x00,0x00,0x00,0x20,0x60,0xa0,0x20,0x20,0x20,0x20,0x20,0x00,
    0x00,0x00,0x00,0x60,0x90,0x10,0x10,0x20,0x40,0x80,0xf0,0x00,
    0x00,0x00,0x00,0x60,0x90,0x10,0x60,0x10,0x10,0x90,0x60,0x00,
    0x00,0x00,0x00,0x10,0x30,0x50,0x50,0x90,0xf8,0x10,0x10,0x00,
    0x00,0x00,0x00,0x70,0x40,0x80,0xe0,0x10,0x10,0x90,0x60,0x00,
    0x00,0x00,0x00,0x60,0x90,0x80,0xa0,0xd0,0x90,0x90,0x60,0x00,
    0x00,0x00,0x00,0xf0,0x10,0x20,0x20,0x20,0x40,0x40,0x40,0x00,
    0x00,0x00,0x00,0x60,0x90,0x90,0x60,0x90,0x90,0x90,0x60,0x00,
    0x00,0x00,0x00,0x60,0x90,0x90,0xb0,0x50,0x10,0x90,0x60,0x00,
    0x00,0x00,0x00,0x00,0x00,0x40,0x00,0x00,0x00,0x00,0x40,0x00,
    0x00,0x00,0x00,0x00,0x00,0x40,0x00,0x00,0x00,0x00,0x40,0x40,
    0x00,0x00,0x00,0x00,0x00,0x10,0x60,0x80,0x60,0x10,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0xf0,0x00,0xf0,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x80,0x60,0x10,0x60,0x80,0x00,0x00,
    0x00,0x00,0x00,0x60,0x90,0x10,0x20,0x40,0x40,0x00,0x40,0x00,
    0x00,0x00,0x00,0x1c,0x22,0x5b,0xa5,0xa5,0xa5,0xa5,0x9e,0x41,
    0x00,0x00,0x00,0x20,0x50,0x50,0x50,0x50,0x70,0x88,0x88,0x00,
    0x00,0x00,0x00,0xf0,0x88,0x88,0xf0,0x88,0x88,0x88,0xf0,0x00,
    0x00,0x00,0x00,0x38,0x44,0x84,0x80,0x80,0x84,0x44,0x38,0x00,
    0x00,0x00,0x00,0xe0,0x90,0x88,0x88,0x88,0x88,0x90,0xe0,0x00,
    0x00,0x00,0x00,0xf8,0x80,0x80,0xf8,0x80,0x80,0x80,0xf8,0x00,
    0x00,0x00,0x00,0x78,0x40,0x40,0x70,0x40,0x40,0x40,0x40,0x00,
    0x00,0x00,0x00,0x38,0x44,0x84,0x80,0x9c,0x84,0x44,0x38,0x00,
    0x00,0x00,0x00,0x88,0x88,0x88,0xf8,0x88,0x88,0x88,0x88,0x00,
    0x00,0x00,0x00,0x80,0x80,0x80,0x80,0x80,0x80,0x80,0x80,0x00,
    0x00,0x00,0x00,0x10,0x10,0x10,0x10,0x10,0x90,0x90,0x60,0x00,
    0x00,0x00,0x00,0x88,0x90,0xa0,0xe0,0xa0,0x90,0x90,0x88,0x00,
    0x00,0x00,0x00,0x80,0x80,0x80,0x80,0x80,0x80,0x80,0xf0,0x00,
    0x00,0x00,0x00,0x82,0xc6,0xc6,0xaa,0xaa,0xaa,0xaa,0x92,0x00,
    0x00,0x00,0x00,0x84,0xc4,0xa4,0xa4,0x94,0x94,0x8c,0x84,0x00,
    0x00,0x00,0x00,0x30,0x48,0x84,0x84,0x84,0x84,0x48,0x30,0x00,
    0x00,0x00,0x00,0xf0,0x88,0x88,0x88,0xf0,0x80,0x80,0x80,0x00,
    0x00,0x00,0x00,0x30,0x48,0x84,0x84,0x84,0x84,0x58,0x34,0x04,
    0x00,0x00,0x00,0x78,0x44,0x44,0x78,0x50,0x48,0x44,0x42,0x00,
    0x00,0x00,0x00,0x70,0x88,0x80,0x70,0x08,0x88,0x88,0x70,0x00,
    0x00,0x00,0x00,0xf8,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x00,
    0x00,0x00,0x00,0x84,0x84,0x84,0x84,0x84,0x84,0x48,0x30,0x00,
    0x00,0x00,0x00,0x88,0x88,0x50,0x50,0x50,0x50,0x50,0x20,0x00,
    0x00,0x00,0x00,0x92,0xaa,0xaa,0xaa,0xaa,0xaa,0xaa,0x44,0x00,
    0x00,0x00,0x00,0x84,0x48,0x48,0x30,0x30,0x48,0x48,0x84,0x00,
    0x00,0x00,0x00,0x88,0x50,0x50,0x20,0x20,0x20,0x20,0x20,0x00,
    0x00,0x00,0x00,0xf8,0x08,0x10,0x20,0x20,0x40,0x80,0xf8,0x00,
    0x00,0x00,0x00,0xc0,0x80,0x80,0x80,0x80,0x80,0x80,0x80,0x80,
    0x00,0x00,0x00,0x80,0x80,0x40,0x40,0x40,0x40,0x20,0x20,0x00,
    0x00,0x00,0x00,0xc0,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x40,
    0x00,0x00,0x00,0x40,0xa0,0xa0,0xa0,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xf8,
    0x00,0x00,0x00,0x80,0x40,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0xe0,0x10,0x70,0x90,0x90,0x70,0x00,
    0x00,0x00,0x00,0x80,0x80,0xa0,0xd0,0x90,0x90,0xd0,0xa0,0x00,
    0x00,0x00,0x00,0x00,0x00,0x60,0x90,0x80,0x80,0x90,0x60,0x00,
    0x00,0x00,0x00,0x10,0x10,0x50,0xb0,0x90,0x90,0xb0,0x50,0x00,
    0x00,0x00,0x00,0x00,0x00,0x60,0x90,0xf0,0x80,0x90,0x60,0x00,
    0x00,0x00,0x00,0xc0,0x80,0xc0,0x80,0x80,0x80,0x80,0x80,0x00,
    0x00,0x00,0x00,0x00,0x00,0x50,0xb0,0x90,0x90,0xb0,0x50,0x10,
    0x00,0x00,0x00,0x80,0x80,0xa0,0xd0,0x90,0x90,0x90,0x90,0x00,
    0x00,0x00,0x00,0x80,0x00,0x80,0x80,0x80,0x80,0x80,0x80,0x00,
    0x00,0x00,0x00,0x80,0x00,0x80,0x80,0x80,0x80,0x80,0x80,0x80,
    0x00,0x00,0x00,0x80,0x80,0x90,0xa0,0xc0,0xa0,0x90,0x90,0x00,
    0x00,0x00,0x00,0x80,0x80,0x80,0x80,0x80,0x80,0x80,0x80,0x00,
    0x00,0x00,0x00,0x00,0x00,0xa6,0xda,0x92,0x92,0x92,0x92,0x00,
    0x00,0x00,0x00,0x00,0x00,0xa0,0xd0,0x90,0x90,0x90,0x90,0x00,
    0x00,0x00,0x00,0x00,0x00,0x60,0x90,0x90,0x90,0x90,0x60,0x00,
    0x00,0x00,0x00,0x00,0x00,0xa0,0xd0,0x90,0x90,0xd0,0xa0,0x80,
    0x00,0x00,0x00,0x00,0x00,0x50,0xb0,0x90,0x90,0xb0,0x50,0x10,
    0x00,0x00,0x00,0x00,0x00,0xa0,0xc0,0x80,0x80,0x80,0x80,0x00,
    0x00,0x00,0x00,0x00,0x00,0xe0,0x90,0x40,0x20,0x90,0x60,0x00,
    0x00,0x00,0x00,0x80,0x80,0xc0,0x80,0x80,0x80,0x80,0xc0,0x00,
    0x00,0x00,0x00,0x00,0x00,0x90,0x90,0x90,0x90,0xb0,0x50,0x00,
    0x00,0x00,0x00,0x00,0x00,0x88,0x88,0x50,0x50,0x50,0x20,0x00,
    0x00,0x00,0x00,0x00,0x00,0x92,0xaa,0xaa,0xaa,0xaa,0x44,0x00,
    0x00,0x00,0x00,0x00,0x00,0x88,0x50,0x20,0x20,0x50,0x88,0x00,
    0x00,0x00,0x00,0x00,0x00,0x88,0x50,0x50,0x50,0x20,0x20,0x20,
    0x00,0x00,0x00,0x00,0x00,0xf0,0x10,0x20,0x40,0x80,0xf0,0x00,
    0x00,0x00,0x00,0xc0,0x80,0x80,0x80,0x00,0x80,0x80,0x80,0x80,
    0x00,0x00,0x00,0x80,0x80,0x80,0x80,0x80,0x80,0x80,0x80,0x80,
    0x00,0x00,0x00,0xc0,0x40,0x40,0x40,0x20,0x40,0x40,0x40,0x40,
    0x00,0x00,0x00,0x00,0x00,0x00,0xe8,0xb0,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0xe0,0xa0,0xa0,0xa0,0xa0,0xa0,0xe0,0x00};

const uint16_t ASCII8x8_Table [] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x40, 0x40, 0x40, 0x40, 0x40, 0x40, 0x00, 0x40,
    0xa0, 0xa0, 0xa0, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x24, 0x24, 0xfe, 0x48, 0xfc, 0x48, 0x48,
    0x38, 0x54, 0x50, 0x38, 0x14, 0x14, 0x54, 0x38,
    0x44, 0xa8, 0xa8, 0x50, 0x14, 0x1a, 0x2a, 0x24,
    0x10, 0x28, 0x28, 0x10, 0x74, 0x4c, 0x4c, 0x30,
    0x10, 0x10, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x08, 0x10, 0x10, 0x10, 0x10, 0x10, 0x10, 0x08,
    0x10, 0x08, 0x08, 0x08, 0x08, 0x08, 0x08, 0x10,
    0x00, 0x00, 0x24, 0x18, 0x3c, 0x18, 0x24, 0x00,
    0x00, 0x00, 0x10, 0x10, 0x7c, 0x10, 0x10, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0x08, 0x10,
    0x00, 0x00, 0x00, 0x00, 0x3c, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x18, 0x18,
    0x08, 0x08, 0x08, 0x10, 0x10, 0x20, 0x20, 0x20,
    0x18, 0x24, 0x24, 0x24, 0x24, 0x24, 0x24, 0x18,
    0x08, 0x18, 0x28, 0x08, 0x08, 0x08, 0x08, 0x08,
    0x38, 0x44, 0x00, 0x04, 0x08, 0x10, 0x20, 0x7c,
    0x18, 0x24, 0x04, 0x18, 0x04, 0x04, 0x24, 0x18,
    0x04, 0x0c, 0x14, 0x24, 0x44, 0x7e, 0x04, 0x04,
    0x3c, 0x20, 0x20, 0x38, 0x04, 0x04, 0x24, 0x18,
    0x18, 0x24, 0x20, 0x38, 0x24, 0x24, 0x24, 0x18,
    0x3c, 0x04, 0x08, 0x08, 0x08, 0x10, 0x10, 0x10,
    0x18, 0x24, 0x24, 0x18, 0x24, 0x24, 0x24, 0x18,
    0x18, 0x24, 0x24, 0x24, 0x1c, 0x04, 0x24, 0x18,
    0x00, 0x00, 0x10, 0x00, 0x00, 0x10, 0x00, 0x00,
    0x00, 0x00, 0x08, 0x00, 0x00, 0x08, 0x10, 0x00,
    0x00, 0x00, 0x04, 0x18, 0x20, 0x18, 0x04, 0x00,
    0x00, 0x00, 0x00, 0x3c, 0x00, 0x3c, 0x00, 0x00,
    0x00, 0x00, 0x20, 0x18, 0x04, 0x18, 0x20, 0x00,
    0x18, 0x24, 0x04, 0x08, 0x10, 0x10, 0x00, 0x10,
    0x3c, 0x42, 0x99, 0xa5, 0xa5, 0x9d, 0x42, 0x38,
    0x38, 0x44, 0x44, 0x44, 0x7c, 0x44, 0x44, 0x44,
    0x78, 0x44, 0x44, 0x78, 0x44, 0x44, 0x44, 0x78,
    0x1c, 0x22, 0x42, 0x40, 0x40, 0x42, 0x22, 0x1c,
    0x70, 0x48, 0x44, 0x44, 0x44, 0x44, 0x48, 0x70,
    0x7c, 0x40, 0x40, 0x7c, 0x40, 0x40, 0x40, 0x7c,
    0x3c, 0x20, 0x20, 0x38, 0x20, 0x20, 0x20, 0x20,
    0x1c, 0x22, 0x42, 0x40, 0x4e, 0x42, 0x22, 0x1c,
    0x44, 0x44, 0x44, 0x7c, 0x44, 0x44, 0x44, 0x44,
    0x10, 0x10, 0x10, 0x10, 0x10, 0x10, 0x10, 0x10,
    0x04, 0x04, 0x04, 0x04, 0x04, 0x24, 0x24, 0x18,
    0x44, 0x48, 0x50, 0x70, 0x50, 0x48, 0x48, 0x44,
    0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x3c,
    0x82, 0xc6, 0xc6, 0xaa, 0xaa, 0xaa, 0xaa, 0x92,
    0x42, 0x62, 0x52, 0x52, 0x4a, 0x4a, 0x46, 0x42,
    0x18, 0x24, 0x42, 0x42, 0x42, 0x42, 0x24, 0x18,
    0x78, 0x44, 0x44, 0x44, 0x78, 0x40, 0x40, 0x40,
    0x18, 0x24, 0x42, 0x42, 0x42, 0x42, 0x2c, 0x1a,
    0x78, 0x44, 0x44, 0x78, 0x50, 0x48, 0x44, 0x42,
    0x38, 0x44, 0x40, 0x38, 0x04, 0x44, 0x44, 0x38,
    0x7c, 0x10, 0x10, 0x10, 0x10, 0x10, 0x10, 0x10,
    0x42, 0x42, 0x42, 0x42, 0x42, 0x42, 0x24, 0x18,
    0x44, 0x44, 0x28, 0x28, 0x28, 0x28, 0x28, 0x10,
    0x92, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0x44,
    0x42, 0x24, 0x24, 0x18, 0x18, 0x24, 0x24, 0x42,
    0x44, 0x28, 0x28, 0x10, 0x10, 0x10, 0x10, 0x10,
    0x7c, 0x04, 0x08, 0x10, 0x10, 0x20, 0x40, 0x7c,
    0x1c, 0x10, 0x10, 0x10, 0x10, 0x10, 0x10, 0x1c,
    0x10, 0x10, 0x08, 0x08, 0x08, 0x08, 0x04, 0x04,
    0x1c, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x1c,
    0x10, 0x28, 0x44, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x20, 0x10, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x18, 0x04, 0x1c, 0x24, 0x24, 0x1c,
    0x20, 0x20, 0x28, 0x34, 0x24, 0x24, 0x34, 0x28,
    0x00, 0x00, 0x18, 0x24, 0x20, 0x20, 0x24, 0x18,
    0x04, 0x04, 0x14, 0x2c, 0x24, 0x24, 0x2c, 0x14,
    0x00, 0x00, 0x18, 0x24, 0x3c, 0x20, 0x24, 0x18,
    0x00, 0x18, 0x10, 0x10, 0x18, 0x10, 0x10, 0x10,
    0x00, 0x18, 0x24, 0x24, 0x18, 0x04, 0x24, 0x18,
    0x20, 0x20, 0x28, 0x34, 0x24, 0x24, 0x24, 0x24,
    0x10, 0x00, 0x10, 0x10, 0x10, 0x10, 0x10, 0x10,
    0x08, 0x00, 0x08, 0x08, 0x08, 0x08, 0x28, 0x10,
    0x20, 0x20, 0x24, 0x28, 0x30, 0x28, 0x24, 0x24,
    0x10, 0x10, 0x10, 0x10, 0x10, 0x10, 0x10, 0x10,
    0x00, 0x00, 0xa6, 0xda, 0x92, 0x92, 0x92, 0x92,
    0x00, 0x00, 0x28, 0x34, 0x24, 0x24, 0x24, 0x24,
    0x00, 0x00, 0x18, 0x24, 0x24, 0x24, 0x24, 0x18,
    0x00, 0x28, 0x34, 0x24, 0x38, 0x20, 0x20, 0x20,
    0x00, 0x14, 0x2c, 0x24, 0x1c, 0x04, 0x04, 0x04,
    0x00, 0x00, 0x2c, 0x30, 0x20, 0x20, 0x20, 0x20,
    0x00, 0x00, 0x18, 0x24, 0x10, 0x08, 0x24, 0x18,
    0x00, 0x10, 0x38, 0x10, 0x10, 0x10, 0x10, 0x18,
    0x00, 0x00, 0x24, 0x24, 0x24, 0x24, 0x2c, 0x14,
    0x00, 0x00, 0x44, 0x44, 0x28, 0x28, 0x28, 0x10,
    0x00, 0x00, 0x92, 0xaa, 0xaa, 0xaa, 0xaa, 0x44,
    0x00, 0x00, 0x44, 0x28, 0x10, 0x10, 0x28, 0x44,
    0x00, 0x28, 0x28, 0x28, 0x10, 0x10, 0x10, 0x10,
    0x00, 0x00, 0x3c, 0x04, 0x08, 0x10, 0x20, 0x3c,
    0x00, 0x08, 0x10, 0x10, 0x20, 0x10, 0x10, 0x08,
    0x10, 0x10, 0x10, 0x10, 0x10, 0x10, 0x10, 0x10,
    0x00, 0x10, 0x08, 0x08, 0x04, 0x08, 0x08, 0x10,
    0x00, 0x00, 0x00, 0x60, 0x92, 0x0c, 0x00, 0x00,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff};


sFONT Font16x24 = {
  ASCII16x24_Table,
  16, /* Width */
  24, /* Height */
};

sFONT Font12x12 = {
  ASCII12x12_Table,
  12, /* Width */
  12, /* Height */
};

sFONT Font8x12 = {
  ASCII8x12_Table,
  8, /* Width */
  12, /* Height */
};


sFONT Font8x8 = {
  ASCII8x8_Table,
  8, /* Width */
  8, /* Height */
};
   
/**
  * @}
  */ 


/** @defgroup FONTS_Private_Function_Prototypes
  * @{
  */ 
/**
  * @}
  */ 


/** @defgroup FONTS_Private_Functions
  * @{
  */
    
/**
  * @}
  */
  
/**
  * @}
  */ 

/**
  * @}
  */

/**
  * @}
  */

/**
  * @}
  */  
/******************* (C) COPYRIGHT 2011 STMicroelectronics *****END OF FILE****/
