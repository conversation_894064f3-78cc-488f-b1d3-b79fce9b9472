/************F35Main.h*******************/
#ifndef __F35_MAIN_H
#define __F35_MAIN_H
#include "F35Basic.h"
//---------------------------------------
extern  tim_count  RfcTimer[RFC_TIMER_NUM] ;//system ISR process

extern  tim_count  RfcTimer10[RFC_TIMER10_NUM] ;//user level timer(1 tick=10ms),RFC_TIMER10_NUM==2

extern  uint16_t  RssiDisSta ;

extern  volatile  int16_t   RSSI_Sum	;

extern  UARTStruct   UB ;//

extern  ModStruct  ModB ;

extern  uint32_t  ADC_STA ;

//---------------------------------------------

void  RealTimeFunction(void) ;

void  SyncTxSta(void) ;

void  WaitPollSta(void)	;

void  NON_RT_PROCESS(void) ;

void  __F35Main(void) __attribute__ ((noreturn));

void  Proc10mS_F35(void); //Timed process at frequency=100Hz

void  Proc100mS_F35(void);//Timed process at frequency=10Hz

void  Proc1S_F35(void);//Timed process at frequency=1Hz

void  Proc1H_F35(void); //Timed process at interval 1 hour

#endif //__F35_MAIN_H
