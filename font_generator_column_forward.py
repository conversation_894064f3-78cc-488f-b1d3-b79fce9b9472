#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
列行式顺向取模字库生成工具
格式：前12字节(12列×8位，顺向) + 后12字节(12列×4位，顺向)
"""

import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox, filedialog
from PIL import Image, ImageDraw, ImageFont
import numpy as np

class FontGeneratorColumnForward:
    def __init__(self, root):
        self.root = root
        self.root.title("列行式顺向取模字库生成工具 v1.0")
        self.root.geometry("1200x900")
        
        self.font_path = None
        self.create_widgets()
    
    def create_widgets(self):
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 标题
        title_label = ttk.Label(main_frame, text="列行式顺向取模字库生成工具", 
                               font=("SimSun", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # 字体选择
        font_frame = ttk.LabelFrame(main_frame, text="字体设置", padding="10")
        font_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Button(font_frame, text="选择字体文件", command=self.select_font).pack(side=tk.LEFT, padx=(0, 10))
        self.font_label = ttk.Label(font_frame, text="未选择字体")
        self.font_label.pack(side=tk.LEFT)
        
        # 输入区域
        input_frame = ttk.LabelFrame(main_frame, text="字符输入", padding="10")
        input_frame.grid(row=2, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 5))
        
        ttk.Label(input_frame, text="输入汉字:").grid(row=0, column=0, sticky=tk.W, pady=(0, 5))
        self.char_entry = ttk.Entry(input_frame, width=10, font=("SimSun", 14))
        self.char_entry.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        self.char_entry.bind('<Return>', lambda e: self.generate_font())
        
        ttk.Button(input_frame, text="生成字库", command=self.generate_font).grid(row=2, column=0, pady=(0, 10))
        ttk.Button(input_frame, text="分析切字", command=self.analyze_qie_char).grid(row=3, column=0, pady=(0, 5))
        
        # 预览区域
        preview_frame = ttk.LabelFrame(input_frame, text="12x12预览", padding="5")
        preview_frame.grid(row=4, column=0, sticky=(tk.W, tk.E), pady=(10, 0))
        
        self.canvas = tk.Canvas(preview_frame, width=300, height=300, bg='white')
        self.canvas.grid(row=0, column=0)
        
        # 列数据分析区域
        column_frame = ttk.LabelFrame(main_frame, text="列数据分析", padding="10")
        column_frame.grid(row=2, column=1, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(5, 5))
        
        self.column_text = scrolledtext.ScrolledText(column_frame, width=40, height=25, font=("Consolas", 9))
        self.column_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 输出区域
        output_frame = ttk.LabelFrame(main_frame, text="生成的字库代码", padding="10")
        output_frame.grid(row=2, column=2, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(5, 0))
        
        self.output_text = scrolledtext.ScrolledText(output_frame, width=50, height=25, font=("Consolas", 9))
        self.output_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 控制按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=3, column=0, columnspan=3, pady=(10, 0))
        
        ttk.Button(button_frame, text="复制代码", command=self.copy_code).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="复制列分析", command=self.copy_column).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="清空", command=self.clear_all).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="验证格式", command=self.verify_format).pack(side=tk.LEFT, padx=(0, 5))
        
        # 配置网格权重
        main_frame.columnconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.columnconfigure(2, weight=2)
        main_frame.rowconfigure(2, weight=1)
        input_frame.columnconfigure(0, weight=1)
        input_frame.rowconfigure(4, weight=1)
        column_frame.columnconfigure(0, weight=1)
        column_frame.rowconfigure(0, weight=1)
        output_frame.columnconfigure(0, weight=1)
        output_frame.rowconfigure(0, weight=1)
    
    def select_font(self):
        """选择字体文件"""
        font_path = filedialog.askopenfilename(
            title="选择字体文件",
            filetypes=[("字体文件", "*.ttf *.ttc *.otf"), ("所有文件", "*.*")]
        )
        if font_path:
            self.font_path = font_path
            self.font_label.config(text=f"已选择: {font_path.split('/')[-1]}")
    
    def char_to_bitmap(self, char):
        """将字符转换为12x12位图"""
        try:
            # 创建16x16图像用于渲染
            img = Image.new('L', (16, 16), 255)
            draw = ImageDraw.Draw(img)
            
            # 选择字体
            if self.font_path:
                font = ImageFont.truetype(self.font_path, 12)
            else:
                # 使用系统默认字体
                try:
                    font = ImageFont.truetype("simsun.ttc", 12)
                except:
                    font = ImageFont.load_default()
            
            # 获取文字尺寸
            bbox = draw.textbbox((0, 0), char, font=font)
            text_width = bbox[2] - bbox[0]
            text_height = bbox[3] - bbox[1]
            
            # 计算居中位置
            x = (16 - text_width) // 2
            y = (16 - text_height) // 2 - bbox[1]
            
            # 绘制字符
            draw.text((x, y), char, font=font, fill=0)
            
            # 裁剪到12x12
            img = img.crop((2, 2, 14, 14))
            
            # 转换为二值图像
            img_array = np.array(img)
            bitmap = (img_array < 128).astype(np.uint8)
            
            return bitmap
            
        except Exception as e:
            messagebox.showerror("错误", f"生成字符位图失败: {str(e)}")
            return None
    
    def bitmap_to_bytes_column_forward(self, bitmap):
        """将12x12位图转换为列行式顺向取模字节数组"""
        bytes_data = []
        
        # 前12字节：12列，每列前8行（顺向取模）
        for col in range(12):
            byte_val = 0
            for row in range(8):  # 前8行
                if bitmap[row][col]:
                    byte_val |= (1 << (7 - row))  # 顺向：第0行对应最高位
            bytes_data.append(byte_val)
        
        # 后12字节：12列，每列后4行（顺向取模）
        for col in range(12):
            byte_val = 0
            for row in range(8, 12):  # 后4行
                if bitmap[row][col]:
                    byte_val |= (1 << (7 - (row - 8)))  # 顺向：第8行对应最高位
            bytes_data.append(byte_val)
        
        return bytes_data
    
    def analyze_column_data(self, bitmap, bytes_data):
        """分析列数据"""
        analysis = "列行式顺向取模数据分析:\n"
        analysis += "=" * 40 + "\n\n"
        
        # 分析前12字节（前8行）
        analysis += "前12字节（12列×前8行）:\n"
        for col in range(12):
            byte_val = bytes_data[col]
            analysis += f"列{col:2d}: 0x{byte_val:02X} = "
            
            # 显示该列的8位数据
            col_pattern = ""
            for row in range(8):
                if bitmap[row][col]:
                    col_pattern += "█"
                else:
                    col_pattern += "·"
            analysis += f"{col_pattern}\n"
        
        analysis += "\n后12字节（12列×后4行）:\n"
        for col in range(12):
            byte_val = bytes_data[col + 12]
            analysis += f"列{col:2d}: 0x{byte_val:02X} = "
            
            # 显示该列的4位数据
            col_pattern = ""
            for row in range(8, 12):
                if bitmap[row][col]:
                    col_pattern += "█"
                else:
                    col_pattern += "·"
            col_pattern += "    "  # 补齐显示
            analysis += f"{col_pattern}\n"
        
        analysis += "\n取模方式说明:\n"
        analysis += "- 顺向取模：第0行对应字节的最高位(bit7)\n"
        analysis += "- 第1行对应bit6，第2行对应bit5，以此类推\n"
        analysis += "- 每列独立存储，共12列\n"
        analysis += "- 前8行存储在前12字节，后4行存储在后12字节\n"
        
        return analysis
    
    def generate_font(self):
        """生成字库数据"""
        char = self.char_entry.get().strip()
        if not char:
            messagebox.showwarning("警告", "请输入汉字")
            return
        
        if len(char) != 1:
            messagebox.showwarning("警告", "请输入单个汉字")
            return
        
        # 生成位图
        bitmap = self.char_to_bitmap(char)
        if bitmap is None:
            return
        
        # 显示预览
        self.show_bitmap_preview(bitmap)
        
        # 转换为字节数组
        bytes_data = self.bitmap_to_bytes_column_forward(bitmap)
        
        # 分析列数据
        column_analysis = self.analyze_column_data(bitmap, bytes_data)
        self.column_text.delete(1.0, tk.END)
        self.column_text.insert(tk.END, column_analysis)
        
        # 生成代码
        unicode_val = ord(char)
        code = self.generate_code(char, unicode_val, bytes_data, bitmap)
        
        # 显示代码
        self.output_text.delete(1.0, tk.END)
        self.output_text.insert(tk.END, code)
    
    def generate_code(self, char, unicode_val, bytes_data, bitmap):
        """生成C代码"""
        code = f"// 字符: {char} (Unicode: 0x{unicode_val:04X}) - 列行式顺向取模\n\n"
        
        # CodePage条目
        code += f"// CodePage条目 (需要插入到CodePage12.c中):\n"
        code += f"{{ 0x{unicode_val:04X}, 0x{unicode_val:04X}, 0xXXXX }}/* Segment XXX, 0x0001 Symbols */\n\n"
        
        # Unicode12数组条目
        code += f"// Unicode12数组条目 (需要插入到Unicode12.c中):\n"
        code += f"{{{{12, 12}},{{\n"
        
        # 前12字节（12列×前8行）
        for i in range(12):
            col_byte = bytes_data[i]
            
            # 生成注释（显示该列前8行的图案）
            pattern = ""
            for row in range(8):
                pattern += "@" if (col_byte & (1 << (7-row))) else "."
            
            if i == 11:
                code += f"0x{col_byte:02X},    /*  {pattern}  */\n\n"
            else:
                code += f"0x{col_byte:02X},    /*  {pattern}  */\n"
        
        # 后12字节（12列×后4行）
        for i in range(12, 24):
            col_byte = bytes_data[i]
            
            # 生成注释（显示该列后4行的图案）
            pattern = ""
            for row in range(4):
                pattern += "@" if (col_byte & (1 << (7-row))) else "."
            pattern += "    "  # 补齐显示
            
            if i == 23:
                code += f"0x{col_byte:02X}     /*  {pattern}  */\n"
            else:
                code += f"0x{col_byte:02X},    /*  {pattern}  */\n"
        
        code += f"}}}}\n\n"
        
        # 添加格式说明
        code += f"// 列行式顺向取模格式说明:\n"
        code += f"// 前12字节: 12列，每列前8行（顺向取模）\n"
        code += f"// 后12字节: 12列，每列后4行（顺向取模）\n"
        code += f"// 顺向取模: 第0行→bit7, 第1行→bit6, ..., 第7行→bit0\n\n"
        
        # 添加完整12x12预览
        code += f"// 完整12x12点阵预览:\n"
        for row in range(12):
            pattern = ""
            for col in range(12):
                pattern += "@" if bitmap[row][col] else "."
            code += f"// 行{row:2d}: {pattern}\n"
        
        return code
    
    def show_bitmap_preview(self, bitmap):
        """显示位图预览"""
        self.canvas.delete("all")
        
        cell_size = 25
        
        # 绘制网格
        for i in range(13):
            self.canvas.create_line(i*cell_size, 0, i*cell_size, 12*cell_size, fill="lightgray")
            self.canvas.create_line(0, i*cell_size, 12*cell_size, i*cell_size, fill="lightgray")
        
        # 绘制像素
        for row in range(12):
            for col in range(12):
                if bitmap[row][col]:
                    x1, y1 = col*cell_size, row*cell_size
                    x2, y2 = x1+cell_size, y1+cell_size
                    self.canvas.create_rectangle(x1, y1, x2, y2, fill="black", outline="gray")
        
        # 添加行列标号
        for i in range(12):
            # 列标号
            self.canvas.create_text(i*cell_size + cell_size//2, -10, text=str(i), font=("Arial", 8))
            # 行标号
            self.canvas.create_text(-15, i*cell_size + cell_size//2, text=str(i), font=("Arial", 8))
    
    def analyze_qie_char(self):
        """分析'切'字的列行式顺向取模数据"""
        # "切"字的列行式顺向取模数据
        col_bytes_8bit = [0x10, 0x10, 0xFF, 0x08, 0x08, 0x02, 0x02, 0xFE, 0x02, 0x02, 0xFE, 0x00]
        col_bytes_4bit = [0x00, 0x00, 0x07, 0x02, 0x09, 0x04, 0x03, 0x00, 0x08, 0x08, 0x07, 0x00]
        
        # 重建12x12位图
        bitmap = [[0 for _ in range(12)] for _ in range(12)]
        
        # 从列行式顺向取模数据重建位图
        for col in range(12):
            # 前8行
            col_byte_8 = col_bytes_8bit[col]
            for row in range(8):
                if col_byte_8 & (1 << (7-row)):  # 顺向取模
                    bitmap[row][col] = 1
            
            # 后4行
            col_byte_4 = col_bytes_4bit[col]
            for row in range(8, 12):
                if col_byte_4 & (1 << (7-(row-8))):  # 顺向取模
                    bitmap[row][col] = 1
        
        # 显示预览
        self.show_bitmap_preview(bitmap)
        
        # 分析列数据
        bytes_data = col_bytes_8bit + col_bytes_4bit
        column_analysis = self.analyze_column_data(bitmap, bytes_data)
        self.column_text.delete(1.0, tk.END)
        self.column_text.insert(tk.END, column_analysis)
        
        # 生成分析代码
        code = "// '切'字列行式顺向取模数据分析\n\n"
        
        code += "// 完整12x12点阵图案:\n"
        for row in range(12):
            pattern = ""
            for col in range(12):
                pattern += "█" if bitmap[row][col] else "·"
            code += f"// 行{row:2d}: {pattern}\n"
        
        code += "\n// 标准字库格式:\n"
        code += "{{12, 12},{\n"
        
        # 前12字节（12列×前8行）
        for i in range(12):
            col_byte = col_bytes_8bit[i]
            pattern = ""
            for row in range(8):
                pattern += "@" if (col_byte & (1 << (7-row))) else "."
            
            if i == 11:
                code += f"0x{col_byte:02X},    /*  {pattern}  */\n\n"
            else:
                code += f"0x{col_byte:02X},    /*  {pattern}  */\n"
        
        # 后12字节（12列×后4行）
        for i in range(12):
            col_byte = col_bytes_4bit[i]
            pattern = ""
            for row in range(4):
                pattern += "@" if (col_byte & (1 << (7-row))) else "."
            pattern += "    "
            
            if i == 11:
                code += f"0x{col_byte:02X}     /*  {pattern}  */\n"
            else:
                code += f"0x{col_byte:02X},    /*  {pattern}  */\n"
        
        code += "}}\n\n"
        
        code += "// 列行式顺向取模格式说明:\n"
        code += "// 数据按列存储，每列从上到下顺向取模\n"
        code += "// 前12字节: 第0-11列的前8行数据\n"
        code += "// 后12字节: 第0-11列的后4行数据\n"
        code += "// 顺向取模: 第0行→bit7, 第1行→bit6, ..., 第7行→bit0\n"
        
        self.output_text.delete(1.0, tk.END)
        self.output_text.insert(tk.END, code)
    
    def verify_format(self):
        """验证生成的格式是否正确"""
        char = self.char_entry.get().strip()
        if not char:
            self.analyze_qie_char()
            return
        
        # 重新生成并验证
        self.generate_font()
        messagebox.showinfo("验证", "列行式顺向取模格式验证完成")
    
    def copy_code(self):
        """复制代码到剪贴板"""
        code = self.output_text.get(1.0, tk.END).strip()
        if code:
            self.root.clipboard_clear()
            self.root.clipboard_append(code)
            messagebox.showinfo("成功", "代码已复制到剪贴板")
        else:
            messagebox.showwarning("警告", "没有可复制的代码")
    
    def copy_column(self):
        """复制列分析到剪贴板"""
        analysis = self.column_text.get(1.0, tk.END).strip()
        if analysis:
            self.root.clipboard_clear()
            self.root.clipboard_append(analysis)
            messagebox.showinfo("成功", "列分析已复制到剪贴板")
        else:
            messagebox.showwarning("警告", "没有可复制的列分析")
    
    def clear_all(self):
        """清空所有内容"""
        self.char_entry.delete(0, tk.END)
        self.output_text.delete(1.0, tk.END)
        self.column_text.delete(1.0, tk.END)
        self.canvas.delete("all")

def main():
    root = tk.Tk()
    app = FontGeneratorColumnForward(root)
    root.mainloop()

if __name__ == "__main__":
    main()
