#include "MbMaster.h"
#include "UartFun_A.h"

extern ModStruct  ModB  ;

//ComModOpStruct U2ModOp ;//SCI communiction object

void ModDevComMon(ModStruct *ModX,ComModOpStruct *UXModOp,uint16_t DevNum)
{  //Device RS485 communication monitor routine
   if(((*ModX).Err & 0x8000)||((*ModX).Ret & 0x3000))
    {//current poll finished with or without error
     if((*ModX).Err & 0x8000)
      {//No response can be received (may be slaver off line)
        (*ModX).Err &=0x7fff ;
        (*UXModOp).sub_sta=0 ;
       } 

     (*UXModOp).sta++ ;//switch to do next device access

     if((*UXModOp).sta>DevNum)
        (*UXModOp).sta=0;  
     (*UXModOp).sub_sta=0 ;//switch to proces next device access request status
    }
    if((*ModX).Err & 0x4000)
      {
        (*ModX).Err &=0xbfff ; 
        ++(*UXModOp).ot_err ;//record the over time error
       }
    if((*ModX).Err & 0x0100)
      {
        (*ModX).Err &=0xfeff ; 
        ++(*UXModOp).crc_err ;//record the CRC error
       }
}

void ActiveAntenna(void)
{
 switch(U_Area.U2ModOp.sta)
 {
  case 0 : { //Send message 
            if((U_Area.U2ModOp.sub_sta==0)&&(ModB.Sta==0))
             {
			    ModB.Add=HOST_ID ;//0x05 for monitor computer
			    ModB.Fun=ECS_MWRITE  ;//write slaver 
			    ModB.RegAdd=0x0000   ; //data write to Slaver(MODBUS address 40001)
			    ModB.DatNum=8     ; //output message length
			    ModB.SDp=(uint8_t *) &U_Area.PaDat_L ;//source data,received RF control data 
			    ModB.Sta=0x80 ;//switch to master poll status
			    ModB.Opc=0x80 ; 
			    ModB.Ret=0 ;//reset protocol return value to NULL status
			    ModB.Err=0 ;//clear previous error flag
			    U_Area.U2ModOp.Packages++ ; 
			    U_Area.U2ModOp.sub_sta=1 ;//switch to monitor status
              }
            else
             {
			   if((ModB.Err & 0x8000)||(ModB.Ret & 0x3000))
			    {//current poll finished with or without error
			     if(ModB.Ret & 0x2000)
			      {//response can be received (slaver on line)
			        OpCtrl |=CB_MDM_COM_OK ;//Set wired comunication OK flag
			       } 
			     else 
			      {//receive error or time out
			        OpCtrl &=~CB_MDM_COM_OK  ;//Clear response OK flag
			       }
			    }
                ModDevComMon(&ModB,&U_Area.U2ModOp,U2_DEV_NUM) ;
              }              
            break ;}
  case 1 : { //Send message 
            if((U_Area.U2ModOp.sub_sta==0)&&(ModB.Sta==0))
             {
			    ModB.Add=HOST_ID ;//0x05 for monitor computer
			    ModB.Fun=ECS_RDHR  ;//read slaver(control host) hold register
			    ModB.RegAdd=0x0010 ; //data Read from Slaver(MODBUS address 40017)
			    ModB.DatNum=20    ; //input message length
		        ModB.RSDp=(uint8_t *) &U_Area.DisplayDatBuf ;//Used to store,received data object
			    ModB.Sta=0x80 ;//switch to master poll status
			    ModB.Opc=0x80 ; 
			    ModB.Ret=0 ;//reset protocol return value to NULL status
			    ModB.Err=0 ;//clear previous error flag
			    U_Area.U2ModOp.Packages++ ; 
			    U_Area.U2ModOp.sub_sta=1 ;//switch to monitor status
              }
            else
             {
			   if((ModB.Err & 0x8000)||(ModB.Ret & 0x3000))
			    {//current poll finished with or without error
			     if(ModB.Ret & 0x2000)
			      {//response can be received (slaver on line)
			        OpCtrl |=CB_MDM_COM_OK ;//Set wired comunication OK flag
			       } 
			     else 
			      {//receive error or time out
			        OpCtrl &=~CB_MDM_COM_OK  ;//Clear response OK flag
			       }
			    }
                ModDevComMon(&ModB,&U_Area.U2ModOp,U2_DEV_NUM) ;
              }              
            break ;}
  default : {
             U_Area.U2ModOp.sta=0 ;
             }  
   }
}
//-----------
