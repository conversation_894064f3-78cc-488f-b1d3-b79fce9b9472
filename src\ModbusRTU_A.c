#include "ModbusRTU_A.h"

extern const uint16_t wCRCTable[] ;

extern tim_count U_Timer[U_TIM_NUM]  ;

/*All words tranmittied with Hi byte first ,then Lo Byte ,except for CRC16 value used in modbus */

/*********Function related to use modbus communication ******/
/*******This file define a implimention of Modbus RTU Master and Slaver Function********/

void ModProcA(UARTStruct *UX , ModStruct *ModX)
{
 __IO uint8_t *TDp,*RDp;//Temp data pointer,receive routine pointer,
 char RMax ;//Temp var
 unsigned char tmp_c ; //char used to calculate CRC
 uint16_t cWord,i,tmp ; //a temp varible,and a return value,
  tmp=0 ;
  if(((*ModX).Sta & 0x88)!=0) // (*ModX).sta bit 7 or 3 !=0, there have some trans request
  {
   if(((*UX).tcs==U_TX_IDLE)||((*UX).tcs==U_TX_WAIT_FOR_SEND))
	{
	  TDp=(*UX).TxBuf ;
	  *TDp++=(*ModX).Add ;//set address field
	  *TDp++=(*ModX).Fun ;//set function code
	  (*ModX).Length=0 ;
	  if(((*ModX).Opc>=0x80)&&((*ModX).Sta==0x80)) //in master status
	    {
		   switch((*ModX).Fun)
			 {
		       case 0x03 : ;
		       case 0x04 : { /* read mutiple register in the slaver */
		                       *TDp++=((*ModX).RegAdd)>>8  ;
		                       *TDp++=(*ModX).RegAdd ;
		                       if((*ModX).DatNum>(U_DB_MAX/2-3)) (*ModX).DatNum=(U_DB_MAX/2-3) ;//max value allow to read each time
		                       *TDp++=((*ModX).DatNum)>>8 ;
		                       *TDp++=(*ModX).DatNum ;
		                       (*ModX).Length=8 ;//Set total length of the modbus protocol(in bytes)
		                       break ;}
		       case 0x06 : { /* write single register in the slaver */
		                       *TDp++=((*ModX).RegAdd)>>8 ;
		                       *TDp++=(*ModX).RegAdd ;
		                       *TDp++=((*ModX).DatNum)>>8  ;//((*ModX).DatNum)>>8 set the data that want to output to slaver
		                       *TDp++=(*ModX).DatNum ;
		                       (*ModX).Length=8 ;//Set total length of the modbus protocol(in bytes)
		                       break ;}
		       case 0x10 : { /* write mutiple register in the slaver */
		                       *TDp++=(*ModX).RegAdd>>8  ;
		                       *TDp++=(*ModX).RegAdd ;
		                       if((*ModX).DatNum>(U_DB_MAX/2-5)) (*ModX).DatNum=(U_DB_MAX/2-5) ;//max value allow to write each time
		                       *TDp++=(*ModX).DatNum>>8 ;
		                       *TDp++=(*ModX).DatNum ;
		                       (*ModX).ByteNum=((*ModX).DatNum)<<1 ;//byte number = word number*2
		                       *TDp++=(*ModX).ByteNum ;
		                       for(i=0 ;i<(*ModX).DatNum ;i++)
		                        {
		                          tmp_c=*(*ModX).SDp ;
                                  ++(*ModX).SDp ;
                                  *TDp++=*(*ModX).SDp ;
		                          *TDp++=tmp_c ;
		                          ++(*ModX).SDp ;
		                         }
		                       (*ModX).SDp -=(*ModX).ByteNum ;//reset pointer
		                       (*ModX).Length=(*ModX).ByteNum + 9 ;//Set total length of the modbus protocol(in bytes)
		                       break ;}
		       case 0x17 : {/* read and write  specified slaver */
		                       *TDp++=(*ModX).RAdd>>8;           //before use this function ,you need set receive address in pointer "RSDp"
		                       *TDp++=(*ModX).RAdd;
		                       if((*ModX).RNum>U_DB_MAX/2-3) (*ModX).RNum=U_DB_MAX/2-3 ;//max value allow to read each time
		                       *TDp++=(*ModX).RNum>>8 ;
		                       *TDp++=(*ModX).RNum ;
		                       ///////////////////////
		                       *TDp++=(*ModX).RegAdd>>8  ;
		                       *TDp++=(*ModX).RegAdd ;
		                       if((*ModX).DatNum>(U_DB_MAX/2-5)) (*ModX).DatNum=(U_DB_MAX/2-5) ;//max value allow to write each time
		                       *TDp++=(*ModX).DatNum>>8 ;
		                       *TDp++=(*ModX).DatNum ;
		                       (*ModX).ByteNum=((*ModX).DatNum)<<1 ;//byte number = word number*2
		                       *TDp++=(*ModX).ByteNum ;
		                       for(i=0 ;i<(*ModX).DatNum ;i++)
		                        {
		                          tmp_c=*(*ModX).SDp ;
                                  ++(*ModX).SDp ;
                                  *TDp++=*(*ModX).SDp ;
		                          *TDp++=tmp_c ;
		                          ++(*ModX).SDp ;
		                         }
		                       (*ModX).SDp -=(*ModX).ByteNum ;//reset pointer
		                       (*ModX).Length=(*ModX).ByteNum + 13 ;//Set total length of the modbus protocol(in bytes)
		                      break ;}
		       default     :   (*ModX).Length=0 ; //not support function ,do nothing
		
			}
		   if((*ModX).Length!=0)  (*ModX).Sta=0x40 ;//switch to initiate trans status
		   else               (*ModX).Sta=0x00 ;//switch to idle status
	    }
	  if(((*ModX).Opc<0x80)&&((*ModX).Sta==0x08))             //in slaver mode
	   {
	    switch((*ModX).Fun) //slaver reponse function
	      {		       	

	       case 0x03 : ;
	       case 0x04 : {/* Normal response read command from master */
	                       if((*ModX).DatNum>(U_DB_MAX/2-3)) (*ModX).DatNum=(U_DB_MAX/2-3) ;//max value allow to read each time
	                       (*ModX).ByteNum=((*ModX).DatNum)<<1  ;
	                       *TDp++=(*ModX).ByteNum ;//set data number in bytes accordding to protocol requirement
	                       for(i=0 ;i<(*ModX).DatNum ;i++)
	                        {
		                       tmp_c=*(*ModX).SDp ;
                               ++(*ModX).SDp ;
                               *TDp++=*(*ModX).SDp ;
		                       *TDp++=tmp_c ;
		                       ++(*ModX).SDp ;
	                         }
							
	                       (*ModX).SDp -= (*ModX).ByteNum;//reset pointer
	                       (*ModX).Length=(*ModX).ByteNum+5 ; //Set total length of the modbus protocol(in bytes)
	                       break ;}
           case 0x06 :
           case 0x08 : {/* Normal response of loop back test from master */
	                       *TDp++=(*ModX).RegAdd>>8  ;
	                       *TDp++=(*ModX).RegAdd ;
	                       *TDp++=(*ModX).DatNum>>8 ;
	                       *TDp++=(*ModX).DatNum ;
	                       (*ModX).Length=8 ; //Set total length of the modbus protocol(in bytes)
	                       break ;}       //echo data have been saved in TX buffer by receiving routine
	       case 0x10 : {/* Normal response write command from master */
	                       *TDp++=(*ModX).RegAdd>>8  ;
	                       *TDp++=(*ModX).RegAdd ;
	                       *TDp++=(*ModX).DatNum>>8 ;
	                       *TDp++=(*ModX).DatNum ;
	                       (*ModX).Length=8 ; //Set total length of the modbus protocol(in bytes)
	                       break ;}
           case 0x0017 : {
	                       if((*ModX).RNum>(U_DB_MAX/2-5)) (*ModX).RNum=(U_DB_MAX/2-5) ;//max value allow to read each time
	                       *TDp++=(*ModX).RNum<<1 ;//set data number in bytes accordding to protocol requirement
	                       for(i=0;i<(*ModX).RNum  ;i++)
	                        {
		                       tmp_c=*(*ModX).SDp ;
                               ++(*ModX).SDp ;
                               *TDp++=*(*ModX).SDp ;
		                       *TDp++=tmp_c ;
		                       ++(*ModX).SDp ;
	                         }
	                       (*ModX).SDp -=i ;//reset pointer	
	                       (*ModX).Length=((*ModX).RNum<<1)+5 ; //Set total length of the modbus protocol(in bytes)
                           break ;}
	       case 0x0083 :   ; //exception response
	       case 0x0084 :   ;
	       case 0x0086 :   ;
	       case 0x0088 :   ;
	       case 0x0090 :   ;
	       case 0x0097 :
	                      {/* Exception response */
	                       *TDp++=(*ModX).RegAdd ;//Need exception code saved in RegAdd before excute this command
	                       (*ModX).Length=5 ;//Set total length of the modbus protocol(in bytes)
	                       break ;}
	       default :       (*ModX).Length=0 ; //for not supportted function
         }
        if((*ModX).Length!=0) (*ModX).Sta=0x04 ;//switch to initiate trans status
        else  (*ModX).Sta=0x00 ;//switch to idle status
       }
      if((*ModX).Length>2)
	   {
	     cWord=0xffff ;/**************Begin to calculate CRC value**************/
	     TDp=(*UX).TxBuf ;
	     i=(*ModX).Length-2 ;
	     while(i--)
	      {
            tmp_c = *TDp++ ^ cWord;
            cWord >>= 8;
            cWord  ^= wCRCTable[tmp_c];
	       }
	     *TDp++=cWord ;
	     *TDp=cWord>>8 ;//Pay attention to the CRC value's byte order**********
	    }
    }
 }
if(((*ModX).Sta&0x44)!=0)//begin to start trans
 {
   tmp=(* (*ModX).TXPort) ((*UX).TxBuf,(*ModX).Length) ;//Need Tx_UA() to set limitation when (*ModX).Length=0 or (*ModX).Length>128
   if(tmp==0)
    {
      if(((*ModX).Opc>=0x80)&&((*ModX).Add!=0))   //master command has been issued succesfully
        {                  //in master mode,routine will not allowed to reenter to tran status immediately
          U_Timer[(*UX).TimerID+1].pv=(*UX).RWtime ; //receiving wait timeout
          U_Timer[(*UX).TimerID+1].cv=0 ;
          U_Timer[(*UX).TimerID+1].csr.csword=0x4000 ;//enable U_Timer[(*UX).TimerID+1] to serve as timeout monitor
          (*ModX).Rtry=(*ModX).RTryHold ;
          (*ModX).Sta =0x20 ; //switch wait response status
          (*ModX).Ret |=0x8000 ;
          (*ModX).Err &=0x3fff ;//clear previous trans error recorde
         }
      else//when previous trans is slaver's response or a broadcast trans from master no further response expected
        {
          (*ModX).Sta=0 ;   //switch to idle(slaver receive)status
          (*ModX).Ret |=0x4000 ;
         }
     }
 }

/*****************************************/
 if((*ModX).Sta==0x20)     //in waitting slave response status
 {                       // after get right reponse, reveive process need to perform status switch
                         // and disable monitor timer               ******************************
   if(U_Timer[(*UX).TimerID+1].csr.csbit.q==1)
    {
      if((*ModX).Rtry>0)
       {
         tmp=(*(*ModX).TXPort)((*UX).TxBuf,(*ModX).Length) ;//Need Tx_UA() to set limitation when (*ModX).Length=0 or (*ModX).Length>128
         if(tmp==0)  //Successfully restart retry operation
          {
            U_Timer[(*UX).TimerID+1].csr.csword=0x4000 ;//restart timeout monitor
            U_Timer[(*UX).TimerID+1].cv=0 ;
            (*ModX).Rtry-- ;
           }
        }
      else
       {
         U_Timer[(*UX).TimerID+1].csr.csword=0x0000 ;//disable timeout monitor
         (*ModX).Sta=0x00 ;        //switch to idle status ,desert current mission
         (*ModX).Err|=0x8000 ;  // set no correct slaver response after retry error flag
        }
      (*ModX).Err|=0x4000 ;//set response timeout error has been occured flag
     }
 }
/************* begin to check receive buffer****************/
    tmp_c=(*UX).rxd_sta & 0x77 ;
    if(tmp_c==0)  return  ; //No new data have been received and saved to receive buffer
	if(tmp_c>=0x10)                //Valide data in second receive buffer of channel A
	 {
	   TDp=(*UX).RxBuf1 ;
	   i=(*UX).RxDL1 ;
	   //__disable_interrupt() ;
       __disable_irq();//Disable all interrupt __enable_irq()
       (*UX).rxd_sta &=0x8f ;//set the buffer to empty status
       __enable_irq();
	   //__enable_interrupt() ;
	  }
	else                          //Valide data in first receive buffer of channel A
	 {
	   TDp=(*UX).RxBuf0 ;
	   i=(*UX).RxDL0 ;
	   //__disable_interrupt() ;
       __disable_irq();//Disable all interrupt __enable_irq()
	   (*UX).rxd_sta &=0xf8 ;       //set the buffer to empty status
       __enable_irq();
	   //__enable_interrupt() ;
	  }
    if((*UX).tcs!=0)
      {          //when receiving can be finished and checked before
                 //TX operation switch to idle status((*UX).tcs==00),can use this method to
        return ; //avoid receive data send by myself in halfduplex mode
       }
	RDp=(*ModX).MBuf ;      //Set modbus receive buffer for channel A
	cWord=0xffff ;/**************Begin to calculate CRC value**************/
	RMax=i-2 ;//save this value to ensure >>>>	
	if(RMax>(U_DB_MAX-2)) return ;
	while(i--)
	 {
	   tmp_c =*TDp^cWord ;
	   cWord >>=8 ;
	   cWord ^= wCRCTable[tmp_c] ;
	   *RDp++=*TDp++ ;    //copy received data to modbus receive buffer
	   }
	RDp=(*ModX).MBuf ;   //Set modbus receive buffer for channel A
    if(cWord !=0)        //CRC error
    {
      (*ModX).Err |=0x0100 ; //set corresponing error flag
      return ;
     }
//****************************Begin to deal with received Modbus frame**************
   (*ModX).Err &=0xc000 ;//clear previous receive error recorde
   if(((*ModX).Opc>=0x80)&&((*ModX).Sta==0x20))//in master wait response status
	{
	  if(*RDp++ ==(*ModX).Add)//have correct reponse address
		{
		  tmp_c=*RDp++ ;//
		  if(tmp_c==(*ModX).Fun)//received a normal response function code
		   {
		     switch((*ModX).Fun)     //Master MMMMMMMMMMMMMMMMMMMMM
		      {
		         case 0x03: ;
		         case 0x04: {
		                         i=*RDp++ ;
		                         if((i==((*ModX).DatNum<<1))&&(i==(RMax-3)))//normally RMax-3 always equals to i
		                          {
			                         i >>=1 ; //Get register number in words,thus i must be even number,otherwise have error
			                         TDp=(*ModX).RSDp ; //may be need to set a fix receive buffer*****************************
			                         while(i--)
			                         {
			                           tmp_c= *RDp++ ;
                                       *TDp++=*RDp++ ;
			                           *TDp++=tmp_c ; //assemble input data to word,and save to specified destination
			                          }
		                           }
		                         else  (*ModX).Err |=0x2000 ; //set corresponing error flag
		                         break ;}
		         case 0x06: {
		                         tmp=*RDp++ ;
		                         tmp <<=8 ;
		                         tmp |=*RDp++ ;//get responsed first data
		                         i=*RDp++ ;
		                         i <<=8 ;
		                         i|=*RDp++ ;//get responsed second data
		                         if((tmp !=(*ModX).RegAdd)||(i !=(*ModX).DatNum)||(RMax!=6))
		                            (*ModX).Err |=0x2000 ; //set corresponing error flag
	                                      //just received a single byte write response signal
		                         break ;}
		         case 0x08: {
		                         tmp=*RDp++ ;
		                         tmp <<=8 ;
		                         tmp |=*RDp++ ;//get responsed first data
		                         i=*RDp++ ;
		                         i <<=8 ;
		                         i|=*RDp++ ;//get responsed second data
		                         if((tmp !=*((*ModX).SDp))||(i !=*((*ModX).SDp+1))||(RMax!=6))
		                            (*ModX).Err |=0x2000 ; //set corresponing error flag
	                                      //just received normal loop back response signal
		                         break ;}
		         case 0x10: {
		                         if(RMax!=6)
		                          {
		                           (*ModX).Err |=0x2000 ; //set corresponing error flag
		                            break ;
		                           }
		                         tmp=*RDp++ ;
		                         tmp <<=8 ;
		                         tmp |=*RDp++ ;//get response write register address
		                         if(tmp ==(*ModX).RegAdd)//if equal to master intended,
		                         {
		                            tmp =*RDp++ ;
		                            tmp <<=8 ;
		                            tmp |=*RDp ;
		                            if(tmp!=(*ModX).DatNum) (*ModX).Err |=0x2000 ;
		                           }
		                         else  (*ModX).Err |=0x2000 ; //set corresponing error flag
		                         break ;}
		         case 0x17: {
		                         i=*RDp++ ;
		                         if((i==((*ModX).RNum<<1))&&(i==(RMax-3)))//received rigth bytes data
		                          {
			                         i >>=1 ; //Get register number in words,thus i must be even number,otherwise have error
			                         TDp=(*ModX).RSDp ; //may be need to set a fix receive buffer*****************************
			                         while(i--)
			                         {
			                           tmp_c=*RDp++ ;
                                       *TDp++=*RDp++ ;
			                           *TDp++=tmp_c ; //assemble input data to word,and save to specified destination
			                          }
		                           }
		                         else  (*ModX).Err |=0x2000 ; //received data number is not expected ,set corresponing error flag
		                         break ;}
		         default :  (*ModX).Err |=0x2000 ;//received an response cannot be understand by master ,error occured
		       }
		     (*ModX).Ret |=0x2000 ;//indicate received normal response from slaver
		     (*ModX).Sta=0 ;  //switch to idle status,prevent to do timeout check now
             U_Timer[(*UX).TimerID+1].csr.csword=0x0000 ;//disable timeout monitor
//             U_Timer[(*UX).TimerID+1].cv=0 ;
		    }
		  else if((tmp_c&0x7f)==(*ModX).Fun)//received a correct exception(error code )response
		   {
		     (*ModX).Err &=0xff00 ;
		     (*ModX).Err |=*TDp ;  //set corresponing exception code
		     (*ModX).Sta=0 ;       //reset modbus master to idle status,timeout will not to be checked now
		     (*ModX).Ret|=0x1000 ; //indicate received correc exception reponse
             U_Timer[(*UX).TimerID+1].csr.csword=0x0000 ;//disable timeout monitor
//             U_Timer[(*UX).TimerID+1].cv=0 ;
		    }
	      else //received not expected response from intentted salver(function code not support)
	       {
	         (*ModX).Err |=0x1000 ;   //set corresponing error flag,let timeout process to do status switch
	        }
		 }
	  else //received message not come from intented slaver
	    {
	      (*ModX).Err |=0x0800 ;         //set corresponing error flag,let timeout process to do status switch
	     }
	 }
   if((*ModX).Opc<0x80)//in slaver receive staus
	{
	  tmp_c=*RDp++ ;
	  if(tmp_c == (*ModX).NodeAdd)//received address is my address
	   {
		 tmp_c=*RDp++ ; //get received function code
		 (*ModX).Add=(*ModX).NodeAdd ;//set response slaver address
		 (*ModX).Fun=tmp_c ;//set normal response function code 	
		 switch(tmp_c)  //accordding to received function code to prepar response data
		  {
		    case  0x03 : ;
		    case  0x04 : {  //received a mutipile register read request (function 03)
		                     tmp=*RDp++ ;
		                     tmp <<=8 ;
		                     tmp |=*RDp++ ;//get request read address
		                     i =*RDp++ ;
		                     i <<=8 ;
		                     i |=*RDp++ ;//get request read number(in word)
		                     if((tmp<MREG_4_LIMIT)&&(i<(U_DB_MAX/2-3))&&(RMax==6))
		                     {
		                       if((*ModX).AccTable!=0)
		                       {
		                         if(i==AccessCtrlCheck(ModX,tmp,i,READ_ONLY))
		                         {
                                   if(tmp>=RFBASE_ADD)
                                     (*ModX).SDp=(__IO uint8_t *)(((tmp-RFBASE_ADD)<<1) + MREG_4_RFBASE) ;//set read RF related data address
                                   else  
		                             (*ModX).SDp=(__IO uint8_t *)((tmp<<1) + MREG_4_BASE) ;//set read address
		                           (*ModX).DatNum=i	  ; //set Number of data in register to be send(to master)
		                           (*ModX).Ret |=0x0080 ;//indicate received an request and can be fulfill normally
                                   (* (*ModX).DModNotify) ((*ModX).RegAdd,(*ModX).DatNum) ;//Call Notify function
		                          }
		                         else
		                         {
		                           (*ModX).Fun |=0x80 ;//set exception response funcation code
		                           (*ModX).RegAdd=0x0007 ;   //07 NEGATIVE ACKNOWLEDGMENT exception code
		                           (*ModX).Err |=0x0207 ;//set corresponing error flag
		                           (*ModX).Ret|=0x0040 ;//indicate received a request ,but can not fulfill normally(exception)
		                       	  }
		                        }
		                       else
		                        {
                                   if(tmp>=RFBASE_ADD)
                                     (*ModX).SDp=(__IO uint8_t *)(((tmp-RFBASE_ADD)<<1) + MREG_4_RFBASE) ;//set read RF related data address
                                   else  
		                             (*ModX).SDp=(__IO uint8_t *)((tmp<<1) + MREG_4_BASE) ;//set read address
		                          (*ModX).DatNum=i	  ; //set Number of data in register to be send(to master)
		                          (*ModX).Ret |=0x0080 ;//indicate received an request and can be fulfill normally
                                  (* (*ModX).DModNotify) ((*ModX).RegAdd,(*ModX).DatNum) ;//Call Notify function
		                         }
//-----------------------
                               Timer10[CM_TIMER].csr.csword=0x4000 ;//reset the monitor timer flag
                               Timer10[CM_TIMER].cv=0 ;
                               OpCtrl |=CB_MDS_COM_OK ;
//-----------------------
		                      }
		                     else
		                     {
		                       (*ModX).Fun |=0x80 ;//set exception response funcation code
		                       if(tmp>=MREG_4_LIMIT)
		                       {
		                          (*ModX).RegAdd=0x0002 ;   //02 ILLIGAL DATA ADDRESS exception code
		                          (*ModX).Err |=0x0202 ;//set corresponing error flag
		                        }
		                       else
		                        {
		                          (*ModX).RegAdd=0x0003 ;   //03 ILLIGAL DATA VALUE	 exception code
		                          (*ModX).Err |=0x0203 ;//set corresponing error flag       	
		                         }
		                       (*ModX).Ret|=0x0040 ;//indicate received a request ,but can not fulfill normally(exception)
		                       }
		                     break ; }
		    case  0x06 : {
		                     tmp=*RDp++ ;
		                     tmp <<=8 ;
		                     tmp |=*RDp++ ;//get request write address
		                     i =*RDp++ ;
		                     i <<=8 ;
		                     i |=*RDp++ ;//get request write data(in word)
		                     if((tmp<MREG_4_LIMIT)&&(RMax==6))
		                       {
		                        if((*ModX).AccTable!=0)
		                        {
		                          if(1==AccessCtrlCheck(ModX,tmp,1,WRITE_ONLY))
		                          {
			                       (*ModX).RegAdd=tmp ;
                                   if(tmp>=RFBASE_ADD)
                                     TDp=(__IO uint8_t *)(((tmp-RFBASE_ADD)<<1) + MREG_4_RFBASE) ;//set read RF related data address
                                   else  
		                             TDp=(__IO uint8_t  *)((tmp<<1) + MREG_4_BASE) ;
			                       (*ModX).DatNum=i ;
			                       *TDp++=i ;//write requested address
                                   *TDp++=(i>>8) ;
			                       (*ModX).Ret |=0x0080 ;//indicate received an request and can be fulfill normally
			                       }
			                      else
			                      {
		                           (*ModX).Fun |=0x80 ;//set exception response funcation code
		                           (*ModX).RegAdd=0x0007 ;   //07 NEGATIVE ACKNOWLEDGMENT exception code
		                           (*ModX).Err |=0x0207 ;//set corresponing error flag
		                           (*ModX).Ret|=0x0040 ;//indicate received a request ,but can not fulfill normally(exception)
		                           }
		                         }
			                    else
			                    {
			                       (*ModX).RegAdd=tmp ;
                                   if(tmp>=RFBASE_ADD)
                                     TDp=(__IO uint8_t *)(((tmp-RFBASE_ADD)<<1) + MREG_4_RFBASE) ;//set read RF related data address
                                   else  
		                             TDp=(__IO uint8_t  *)((tmp<<1) + MREG_4_BASE) ;
			                       (*ModX).DatNum=i ;
			                       *TDp++=i ;//write requested address
                                   *TDp++=(i>>8) ;
			                       (*ModX).Ret |=0x0080 ;//indicate received an request and can be fulfill normally
			                      }
			                    }
		                     else
		                       {
		                         (*ModX).Fun |=0x80 ;//set function 10 exception response
		                         if(tmp>=MREG_4_LIMIT)
		                         {
		                           (*ModX).RegAdd=0x0002 ;   //02 ILLIGAL DATA ADDRESS exception code
		                           (*ModX).Err |=0x0202 ;//set corresponing error flag
		                          }
		                         else
		                          {
		                            (*ModX).RegAdd=0x0003 ;   //03 ILLIGAL DATA VALUE	 exception code
		                            (*ModX).Err |=0x0203 ;//set corresponing error flag       	
		                           }
		                         (*ModX).Ret|=0x0040 ;//indicate received a request ,but can not fulfill normally(exception)
		                         }
		                     break ; }
		    case  0x08 : {
		                     if(RMax==6)
		                      {
			                     tmp=*RDp++ ;
			                     tmp <<=8 ;
			                     tmp |=*RDp++ ;//get request write address
			                     i =*RDp++ ;
			                     i <<=8 ;
			                     i |=*RDp++ ;//get request write number(in word)
			                     (*ModX).RegAdd=tmp ;
			                     (*ModX).DatNum=i ;
			                     (*ModX).Ret |=0x0080 ;//indicate received an request and can be fulfill normally
		                       }
		                     else
		                       {
		                        (*ModX).Fun |=0x80  ;//set function 08 exception response
                                (*ModX).RegAdd=0x0003 ;   //03 ILLIGAL DATA VALUE	 exception code
		                        (*ModX).Err |=0x0203  ;//set corresponing error flag       	
		                        (*ModX).Ret|=0x0040 ;//indicate received a request ,but can not fulfill normally(exception)
		                        }
		                     break ; }
		    case  0x10 : {  //received mutipile register write request(function 16)
		                       tmp=*RDp++ ;
		                       tmp <<=8 ;
		                       tmp |=*RDp++ ;//get request write address
		                       i =*RDp++ ;
		                       i <<=8 ;
		                       i |=*RDp++ ;//get request write number(in word)
		                       RMax=(RMax-7)>>1 ;
		                       if((i<=(U_DB_MAX/2-5))&&((i<<1)==*RDp)&&(tmp<MREG_4_LIMIT)&&(i==RMax))//set to write/read data area limit
		                       {                                         //can be write
		                        if((*ModX).AccTable!=0)
		                        {
		                          if(i==AccessCtrlCheck(ModX,tmp,i,WRITE_ONLY))
		                          {
		                            (*ModX).RegAdd=tmp ;
                                    if(tmp>=RFBASE_ADD)
                                      TDp=(__IO uint8_t *)(((tmp-RFBASE_ADD)<<1) + MREG_4_RFBASE) ;//set read RF related data address
                                    else  
		                              TDp=(__IO uint8_t  *)((tmp<<1) + MREG_4_BASE) ;
		                            (*ModX).DatNum=i ;
		                            ++RDp ;//now RDp point to first data value
		                            while(i--)
		                            {
		                              tmp_c=*RDp++ ;
                                      *TDp++=*RDp++ ;
		                              *TDp++=tmp_c ;//write the intended address
		                             }
		                            (*ModX).Ret |=0x0080 ;//indicate received an request and can be fulfill normally
                                    (* (*ModX).DModNotify) ((*ModX).RegAdd,(*ModX).DatNum) ;//Call Notify function
		                           }
		                          else
		                           {
		                             (*ModX).Fun |=0x80 ;//set function 10 exception response
		                             (*ModX).RegAdd=0x0007 ;   //07 NEGATIVE ACKNOWLEDGMENT exception code
		                             (*ModX).Err |=0x0207 ;//set corresponing error flag
		                             (*ModX).Ret|=0x0040 ;//indicate received a request ,but can not fulfill normally(exception)
		                            }
		                          }
		                         else
		                         {
		                            (*ModX).RegAdd=tmp ;
                                    if(tmp>=RFBASE_ADD)
                                      TDp=(__IO uint8_t *)(((tmp-RFBASE_ADD)<<1) + MREG_4_RFBASE) ;//set read RF related data address
                                    else  
		                              TDp=(__IO uint8_t  *)((tmp<<1) + MREG_4_BASE) ;
		                            (*ModX).DatNum=i ;
		                            ++RDp ;//now RDp point to first data value
		                            while(i--)
		                            {
		                              tmp_c=*RDp++ ;
                                      *TDp++=*RDp++ ;
		                              *TDp++=tmp_c ;//write the intended address
		                             }
		                            (*ModX).Ret |=0x0080 ;//indicate received an request and can be fulfill normally
                                    (* (*ModX).DModNotify) ((*ModX).RegAdd,(*ModX).DatNum) ;//Call Notify function
		                          }
//-----------------------
                                Timer10[CM_TIMER].csr.csword=0x4000 ;//reset the monitor timer flag
                                Timer10[CM_TIMER].cv=0 ;
                                OpCtrl |=CB_MDS_COM_OK ;
//-----------------------
		                        }
		                        else
		                        {
                                  if(tmp==COMMAND_ADD)
                                  {
                                    ++RDp ;//now RDp point to first data value
                                    RfSetupTestEntry(RDp) ;//enter test setup entry ,may be never return
                                   }
		                          (*ModX).Fun |=0x80 ;//set function 10 exception response
		                          if(tmp>=MREG_4_LIMIT)
		                          {
		                             (*ModX).RegAdd=0x0002 ;   //02 ILLIGAL DATA ADDRESS exception code
		                             (*ModX).Err |=0x0202 ;//set corresponing error flag
		                           }
		                          else
		                           {
		                             (*ModX).RegAdd=0x0003 ;   //03 ILLIGAL DATA VALUE	 exception code
		                             (*ModX).Err |=0x0203 ;//set corresponing error flag       	
		                            }
		                          (*ModX).Ret|=0x0040 ;//indicate received a request ,but can not fulfill normally(exception)
		                         }
		                     break ; }
		    case  0x17 : {
		                     tmp=*RDp++ ;
		                     tmp <<=8 ;
		                     tmp |=*RDp++ ;//get request read address
		                     i =*RDp++ ;
		                     i <<=8 ;
		                     i |=*RDp++ ;//get request read number(in word)
		                     RMax=(RMax-11)>>1;
		                     if((tmp<MREG_4_LIMIT)&&(i<=(U_DB_MAX/2-3)))
		                      {
		                        if((*ModX).AccTable!=0)
		                        {
		                          if(i==AccessCtrlCheck(ModX,tmp,i,READ_ONLY))
		                          {
			                       (*ModX).RAdd=tmp ;
			                       (*ModX).SDp=(__IO uint8_t *)((tmp<<1) + MREG_4_BASE) ;//set read address
			                       (*ModX).RNum=i	  ; //set Number of data in register to be send(to master)
			                       }
			                      else
			                      {
		                            (*ModX).Err |=0x0090 ;//set corresponing error flag,bit 7,4
			                        }
			                     }
			                    else
			                    {
			                      (*ModX).RAdd=tmp ;
			                      (*ModX).SDp=(__IO uint8_t *)((tmp<<1) + MREG_4_BASE) ;//set read address
			                      (*ModX).RNum=i	  ; //set Number of data in register to be send(to master)
			                     }
		                       }
		                     else
		                      {
		                        if(tmp>=MREG_4_LIMIT)
		                         {
		                           (*ModX).Err |=0x0090 ;//set corresponing error flag,bit 7,4
		                          }
		                        if(i>(U_DB_MAX/2-3))
		                         {
		                           (*ModX).Err |=0x00a0 ;//set corresponing error flag,bit 7,5
		                          }
		                       }
		                       ////////////////////////////////////////////////****************************************
		                     tmp=*RDp++ ;
		                     tmp <<=8 ;
		                     tmp |=*RDp++ ;//get request write address
		                     i =*RDp++ ;
		                     i <<=8 ;
		                     i |=*RDp++ ;//get request write number(in word)
		                     if((i<(U_DB_MAX/2-7))&&((i<<1)==*RDp)&&(tmp<MREG_4_LIMIT)&&(i==RMax))//set to write/read data area limit
		                      {                                         //can be write
		                       if((*ModX).AccTable!=0)
		                        {
		                         if(i==AccessCtrlCheck(ModX,tmp,i,WRITE_ONLY))
		                          {
		                            (*ModX).RegAdd=tmp ;
		                            TDp=(__IO uint8_t *)((tmp<<1) + MREG_4_BASE) ;
		                            (*ModX).RSDp=TDp ;
		                            (*ModX).DatNum=i ;
		                            ++RDp ;//now RDp point to first data value
		                            while(i--)
		                            {
		                              tmp_c=*RDp++ ;
                                      *TDp++=*RDp++ ;
		                              *TDp++=tmp_c ;//write the intended address
		                             }
                                    (* (*ModX).DModNotify) ((*ModX).RegAdd,(*ModX).DatNum) ;//Call Notify function
		                           }
		                         else
		                          {
		                            (*ModX).Err |=0x00d0 ;//set corresponing error flag bit 7,6,4
		                           }
		                        }
		                       else
		                       {
		                         (*ModX).RegAdd=tmp ;
		                         TDp=(__IO uint8_t *)((tmp<<1) + MREG_4_BASE) ;
		                         (*ModX).RSDp=TDp ;
		                         (*ModX).DatNum=i ;
		                         ++RDp ;//now RDp point to first data value
		                         while(i--)
		                           {
		                            tmp_c =*RDp++ ;
                                    *TDp++=*RDp++ ;
		                            *TDp++=tmp_c ;//write the intended address
		                           }
                                  (* (*ModX).DModNotify) ((*ModX).RegAdd,(*ModX).DatNum) ;//Call Notify function
		                         }
		                       }
		                     else
		                      {
		                        if(tmp>=MREG_4_LIMIT)
		                         {
		                           (*ModX).Err |=0x00d0 ;//set corresponing error flag bit 7,6,4
		                          }
	                            if((i>(U_DB_MAX/2-7))||((i<<1)!=*RDp)||(i!=RMax))
	                             {
	                               (*ModX).Err |=0x00e0 ;  //set corresponing error flag bit7,6,5
	                              }
		                       }
		                     if(((*ModX).Err&0x0080)==0)
		                      {
		                        (*ModX).Ret |=0x0080 ;//indicate received an request and can be fulfill normally
//-----------------------
                                Timer10[CM_TIMER].csr.csword=0x4000 ;//reset the monitor timer flag
                                Timer10[CM_TIMER].cv=0 ;
                                OpCtrl |=CB_MDS_COM_OK ;
//-----------------------
		                       }
		                     else
		                      {
		                        (*ModX).Fun |=0x80 ;
		                        if(((*ModX).Err&0x0010)==0)
		                           (*ModX).RegAdd=0x0003 ;//exception code 03 ILLIGAL DATA VALUE
		                        else
		                           (*ModX).RegAdd=0x0002 ;//exception code 02 ILLIGAL DATA ADDRESS		
		                        (*ModX).Ret |=0x0040 ;//indicate received an request and can not be fulfill normally
		                        }
		                     break ; }
		    default  :  {  //function code not supportted exception process
		                    (*ModX).Fun=tmp |0x80 ;
		                    (*ModX).RegAdd=0x0001 ;   //01 exception code
		                    (*ModX).Err |=0x0400 ;    //set corresponing error flag,
		                    (*ModX).Ret|=0x0040  ;//indicate received a request ,but can not fulfill normally(exception)
		                  }
		    }
		   (*ModX).Sta=0x08 ;//switch to slaver trans request status
		  }
	   if(tmp_c==(uint8_t)0x00)//received a broadcast address 0
		 {
		   tmp_c=*RDp++ ; //get received function code
	       if(tmp_c==0x10) //system support function 16 in broadcast mode
	       {
	           tmp=*RDp++ ;
	           tmp <<=8 ;
	           tmp |=*RDp++ ;//Get request write address
	           i =*RDp++ ;
	           i <<=8 ;
	           i |=*RDp++ ;//Get request write number(in word)
	           RMax=(RMax-7)>>1 ;
	           if((i<=(U_DB_MAX/2-5))&&((i<<1)==*RDp)&&(tmp<MREG_4_LIMIT)&&(i==RMax))//set to write/read data area limit
	            {                                         //can be write
	             if(((*ModX).AccTable==0)||(i==AccessCtrlCheck(ModX,tmp,i,WRITE_ONLY)))
		          {
	                TDp=(__IO uint8_t *)((tmp<<1) + MREG_4_BASE) ;
	                ++RDp ;//now RDp point to first data value
	                while(i--)
	                {
	                  tmp_c =*RDp++ ;
                      *TDp++=*RDp++ ;
	                  *TDp++=tmp_c ;//write the intended address
	                 }
		            (*ModX).Ret |=0x0020 ;//indicate received an broadcast request and have been fulfilled normally
                    (* (*ModX).DModNotify) (tmp,(*ModX).DatNum) ;//Call Notify function
		           }
//-----------------------
                  Timer10[CM_TIMER].csr.csword=0x4000 ;//reset the monitor timer flag
                  Timer10[CM_TIMER].cv=0 ;
                  OpCtrl |=CB_MDS_COM_OK ;
//-----------------------
	             }
	            else
	             {
	               (*ModX).Ret|=0x0010 ;//indicate received a broadcast request ,but can not fulfill
	              }
	        }
	      }
     }
}
/*******************************************************************************************/
/*This function return the allowed access length for specfied operation for request address*/
/*******************************************************************************************/
uint16_t  AccessCtrlCheck( ModStruct *ModX, uint16_t DenAdd,uint16_t Length,uint16_t AccType)
{
    int MinLimit,MaxLimit,nonius ;

    if((*ModX).AccTable==0) return 0 ;
    MinLimit=0 ;
    MaxLimit=ACC_CTRL_SEG_NUM-1 ;
    do{
       nonius=(MinLimit+MaxLimit)>>1 ;//divide by 2
       if(DenAdd<(*(*ModX).AccTable).SCtrl[nonius].Lo_Add)
       {
         if(nonius==MinLimit)// out of lowest range
          {						  
            return 0 ;
           }
         else
           MaxLimit=nonius-1 ;
        }
       else if(DenAdd>(*(*ModX).AccTable).SCtrl[nonius].Hi_Add)
       {
         if(nonius==MaxLimit)//out of upper limit
          {
           return 0 ;
          }
         else
           MinLimit=nonius+1 ;
        }
       else
        {
          if((AccType==(*(*ModX).AccTable).SCtrl[nonius].Attr)
              ||((*(*ModX).AccTable).SCtrl[nonius].Attr==READ_WRITE))
		  {
            if((DenAdd+Length)>(*(*ModX).AccTable).SCtrl[nonius].Hi_Add)
				Length=(*(*ModX).AccTable).SCtrl[nonius].Hi_Add-DenAdd ;
		   }
		  else
			  Length=0 ;
		
          return Length ;
         }
    } while(MinLimit<=MaxLimit) ;
    return 0 ;//##############################################
}
//##############################################
