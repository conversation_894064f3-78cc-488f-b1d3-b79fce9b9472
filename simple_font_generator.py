#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版列行式逆向取模汉字取模工具
专门生成指定格式的字库数据
"""

import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox, filedialog
from PIL import Image, ImageDraw, ImageFont
import numpy as np
import os

class SimpleFontGenerator:
    def __init__(self, root):
        self.root = root
        self.root.title("取模工具 29559323854387225959 V1.0版本")
        self.root.geometry("900x700")
        
        self.font_path = None
        self.font_size = 12
        self.create_widgets()
        self.load_default_font()
    
    def create_widgets(self):
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 标题
        title_label = ttk.Label(main_frame, text="取模工具 29559323854387225959 V1.0版本", 
                               font=("SimSun", 14, "bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 15))
        
        # 字体设置
        font_frame = ttk.LabelFrame(main_frame, text="字体设置", padding="10")
        font_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Button(font_frame, text="选择宋体", command=self.select_font).pack(side=tk.LEFT, padx=(0, 10))
        self.font_label = ttk.Label(font_frame, text="使用默认宋体")
        self.font_label.pack(side=tk.LEFT, padx=(0, 15))
        
        ttk.Label(font_frame, text="大小:").pack(side=tk.LEFT, padx=(0, 5))
        self.size_var = tk.StringVar(value="12")
        size_combo = ttk.Combobox(font_frame, textvariable=self.size_var, values=["10", "11", "12", "13", "14"], width=5)
        size_combo.pack(side=tk.LEFT)
        size_combo.bind('<<ComboboxSelected>>', self.on_size_change)
        
        # 输入区域
        input_frame = ttk.LabelFrame(main_frame, text="字符输入", padding="10")
        input_frame.grid(row=2, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 5))
        
        ttk.Label(input_frame, text="输入汉字:").grid(row=0, column=0, sticky=tk.W, pady=(0, 5))
        self.char_entry = ttk.Entry(input_frame, width=12, font=("SimSun", 14))
        self.char_entry.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        self.char_entry.bind('<Return>', lambda e: self.generate_font())
        
        ttk.Button(input_frame, text="生成取模", command=self.generate_font).grid(row=2, column=0, pady=(0, 10))
        ttk.Button(input_frame, text="批量取模", command=self.batch_generate).grid(row=3, column=0, pady=(0, 5))
        
        # 预览区域
        preview_frame = ttk.LabelFrame(input_frame, text="12×12预览", padding="5")
        preview_frame.grid(row=4, column=0, sticky=(tk.W, tk.E), pady=(10, 0))
        
        self.canvas = tk.Canvas(preview_frame, width=240, height=240, bg='white')
        self.canvas.grid(row=0, column=0)
        
        # 输出区域
        output_frame = ttk.LabelFrame(main_frame, text="生成的字库代码", padding="10")
        output_frame.grid(row=2, column=1, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(5, 0))
        
        self.output_text = scrolledtext.ScrolledText(output_frame, width=60, height=35, font=("Consolas", 9))
        self.output_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 控制按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=3, column=0, columnspan=2, pady=(10, 0))
        
        ttk.Button(button_frame, text="复制代码", command=self.copy_code).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="保存文件", command=self.save_to_file).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="清空", command=self.clear_all).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="示例", command=self.load_examples).pack(side=tk.LEFT, padx=(0, 5))
        
        # 配置网格权重
        main_frame.columnconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=2)
        main_frame.rowconfigure(2, weight=1)
        input_frame.columnconfigure(0, weight=1)
        input_frame.rowconfigure(4, weight=1)
        output_frame.columnconfigure(0, weight=1)
        output_frame.rowconfigure(0, weight=1)
    
    def load_default_font(self):
        """加载默认宋体字体"""
        font_paths = [
            "C:/Windows/Fonts/simsun.ttc",
            "C:/Windows/Fonts/simsun.ttf",
            "/System/Library/Fonts/STSong.ttc",
            "/usr/share/fonts/truetype/arphic/uming.ttc",
        ]
        
        for path in font_paths:
            if os.path.exists(path):
                self.font_path = path
                self.font_label.config(text=f"系统宋体: {os.path.basename(path)}")
                return
        
        self.font_label.config(text="使用默认字体")
    
    def select_font(self):
        """选择字体文件"""
        font_path = filedialog.askopenfilename(
            title="选择宋体字体文件",
            filetypes=[("字体文件", "*.ttf *.ttc *.otf"), ("所有文件", "*.*")]
        )
        if font_path:
            self.font_path = font_path
            self.font_label.config(text=f"已选择: {os.path.basename(font_path)}")
    
    def on_size_change(self, event=None):
        """字体大小改变"""
        self.font_size = int(self.size_var.get())
        if self.char_entry.get().strip():
            self.generate_font()
    
    def char_to_bitmap(self, char):
        """将字符转换为12x12位图"""
        try:
            # 创建16x16图像用于渲染
            img = Image.new('L', (16, 16), 255)
            draw = ImageDraw.Draw(img)
            
            # 选择字体
            if self.font_path and os.path.exists(self.font_path):
                font = ImageFont.truetype(self.font_path, self.font_size)
            else:
                font = ImageFont.load_default()
            
            # 获取文字尺寸
            bbox = draw.textbbox((0, 0), char, font=font)
            text_width = bbox[2] - bbox[0]
            text_height = bbox[3] - bbox[1]
            
            # 计算居中位置
            x = (16 - text_width) // 2
            y = (16 - text_height) // 2 - bbox[1]
            
            # 绘制字符
            draw.text((x, y), char, font=font, fill=0)
            
            # 裁剪到12x12
            img = img.crop((2, 2, 14, 14))
            
            # 转换为二值图像
            img_array = np.array(img)
            bitmap = (img_array < 128).astype(np.uint8)
            
            return bitmap
            
        except Exception as e:
            messagebox.showerror("错误", f"生成字符位图失败: {str(e)}")
            return None
    
    def bitmap_to_bytes_column_reverse(self, bitmap):
        """将12x12位图转换为列行式逆向取模字节数组"""
        bytes_data = []
        
        # 前12字节：12列，每列前8行（逆向取模）
        for col in range(12):
            byte_val = 0
            for row in range(8):
                if bitmap[row][col]:
                    byte_val |= (1 << row)  # 逆向：第0行对应bit0
            bytes_data.append(byte_val)
        
        # 后12字节：12列，每列后4行（逆向取模）
        for col in range(12):
            byte_val = 0
            for row in range(8, 12):
                if bitmap[row][col]:
                    byte_val |= (1 << (row - 8))  # 逆向：第8行对应bit0
            bytes_data.append(byte_val)
        
        return bytes_data
    
    def generate_font(self):
        """生成字库数据"""
        char = self.char_entry.get().strip()
        if not char:
            messagebox.showwarning("警告", "请输入汉字")
            return
        
        if len(char) != 1:
            messagebox.showwarning("警告", "请输入单个汉字")
            return
        
        # 生成位图
        bitmap = self.char_to_bitmap(char)
        if bitmap is None:
            return
        
        # 显示预览
        self.show_bitmap_preview(bitmap)
        
        # 转换为字节数组
        bytes_data = self.bitmap_to_bytes_column_reverse(bitmap)
        
        # 生成代码
        unicode_val = ord(char)
        code = self.generate_code(char, unicode_val, bytes_data, bitmap)
        
        # 显示代码
        self.output_text.delete(1.0, tk.END)
        self.output_text.insert(tk.END, code)
    
    def generate_code(self, char, unicode_val, bytes_data, bitmap):
        """生成C代码"""
        code = f"// 字符: {char} (Unicode: 0x{unicode_val:04X}) - 列行式逆向取模\n"
        code += f"// 字体: 宋体 {self.font_size}pt, 12×12点阵\n\n"
        
        # CodePage条目
        code += f"// CodePage条目 (需要插入到CodePage12.c中):\n"
        code += f"{{ 0x{unicode_val:04X}, 0x{unicode_val:04X}, 0xXXXX }}/* Segment XXX, 0x0001 Symbols */\n\n"
        
        # Unicode12数组条目
        code += f"// Unicode12数组条目 (需要插入到Unicode12.c中):\n"
        code += f"{{{{12, 12}},{{\n"
        
        # 前12字节（12列×前8行，逆向取模）
        for i in range(12):
            col_byte = bytes_data[i]
            
            # 生成注释（显示该列前8行的图案）
            pattern = ""
            for row in range(8):
                pattern += "@" if (col_byte & (1 << row)) else "."
            
            if i == 11:
                code += f"0x{col_byte:02X},    /*  {pattern}  */\n\n"
            else:
                code += f"0x{col_byte:02X},    /*  {pattern}  */\n"
        
        # 后12字节（12列×后4行，逆向取模）
        for i in range(12, 24):
            col_byte = bytes_data[i]
            
            # 生成注释（显示该列后4行的图案）
            pattern = ""
            for row in range(4):
                pattern += "@" if (col_byte & (1 << row)) else "."
            pattern += "      "  # 补齐显示
            
            if i == 23:
                code += f"0x{col_byte:02X}     /*  {pattern}  */\n"
            else:
                code += f"0x{col_byte:02X},    /*  {pattern}  */\n"
        
        code += f"}}}}\n\n"
        
        # 添加格式说明
        code += f"// 列行式逆向取模格式说明:\n"
        code += f"// 前12字节: 12列，每列前8行（逆向取模）\n"
        code += f"// 后12字节: 12列，每列后4行（逆向取模）\n"
        code += f"// 逆向取模: 第0行→bit0, 第1行→bit1, ..., 第7行→bit7\n\n"
        
        # 添加完整12x12预览
        code += f"// 完整12×12点阵预览:\n"
        for row in range(12):
            pattern = ""
            for col in range(12):
                pattern += "@" if bitmap[row][col] else "."
            code += f"// 行{row:2d}: {pattern}\n"
        
        return code
    
    def show_bitmap_preview(self, bitmap):
        """显示位图预览"""
        self.canvas.delete("all")
        
        cell_size = 20
        
        # 绘制网格
        for i in range(13):
            self.canvas.create_line(i*cell_size, 0, i*cell_size, 12*cell_size, fill="lightgray")
            self.canvas.create_line(0, i*cell_size, 12*cell_size, i*cell_size, fill="lightgray")
        
        # 绘制像素
        for row in range(12):
            for col in range(12):
                if bitmap[row][col]:
                    x1, y1 = col*cell_size, row*cell_size
                    x2, y2 = x1+cell_size, y1+cell_size
                    self.canvas.create_rectangle(x1, y1, x2, y2, fill="black", outline="gray")
    
    def batch_generate(self):
        """批量生成取模"""
        batch_window = tk.Toplevel(self.root)
        batch_window.title("批量汉字取模")
        batch_window.geometry("700x500")
        
        ttk.Label(batch_window, text="输入多个汉字:").pack(pady=10)
        
        input_text = scrolledtext.ScrolledText(batch_window, width=60, height=6)
        input_text.pack(pady=10, padx=20, fill=tk.BOTH)
        
        result_text = scrolledtext.ScrolledText(batch_window, width=60, height=20)
        result_text.pack(pady=10, padx=20, fill=tk.BOTH, expand=True)
        
        def do_batch_generate():
            input_data = input_text.get(1.0, tk.END).strip()
            if not input_data:
                messagebox.showwarning("警告", "请输入汉字")
                return
            
            # 提取所有汉字
            chars = []
            for line in input_data.split('\n'):
                for char in line.strip():
                    if char and ord(char) > 127:
                        chars.append(char)
            
            if not chars:
                messagebox.showwarning("警告", "没有找到有效的汉字")
                return
            
            results = []
            for char in chars:
                try:
                    bitmap = self.char_to_bitmap(char)
                    if bitmap is not None:
                        bytes_data = self.bitmap_to_bytes_column_reverse(bitmap)
                        unicode_val = ord(char)
                        code = self.generate_code(char, unicode_val, bytes_data, bitmap)
                        results.append(code)
                        results.append("\n" + "="*80 + "\n")
                except Exception as e:
                    results.append(f"// 错误: 字符 '{char}' 生成失败: {str(e)}\n")
            
            result_text.delete(1.0, tk.END)
            result_text.insert(tk.END, '\n'.join(results))
        
        def copy_batch_result():
            result = result_text.get(1.0, tk.END).strip()
            if result:
                batch_window.clipboard_clear()
                batch_window.clipboard_append(result)
                messagebox.showinfo("成功", "批量结果已复制到剪贴板")
        
        button_frame = ttk.Frame(batch_window)
        button_frame.pack(pady=10)
        ttk.Button(button_frame, text="开始生成", command=do_batch_generate).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="复制结果", command=copy_batch_result).pack(side=tk.LEFT)
    
    def load_examples(self):
        """加载示例字符"""
        examples = ["切", "底", "座", "主", "启", "动", "停", "止"]
        example_window = tk.Toplevel(self.root)
        example_window.title("示例字符")
        example_window.geometry("300x200")
        
        ttk.Label(example_window, text="点击字符进行取模:").pack(pady=10)
        
        button_frame = ttk.Frame(example_window)
        button_frame.pack(pady=20)
        
        for i, char in enumerate(examples):
            def load_char(c=char):
                self.char_entry.delete(0, tk.END)
                self.char_entry.insert(0, c)
                self.generate_font()
                example_window.destroy()
            
            ttk.Button(button_frame, text=char, command=load_char, width=4).grid(
                row=i//4, column=i%4, padx=5, pady=5)
    
    def copy_code(self):
        """复制代码到剪贴板"""
        code = self.output_text.get(1.0, tk.END).strip()
        if code:
            self.root.clipboard_clear()
            self.root.clipboard_append(code)
            messagebox.showinfo("成功", "代码已复制到剪贴板")
        else:
            messagebox.showwarning("警告", "没有可复制的代码")
    
    def save_to_file(self):
        """保存到文件"""
        char = self.char_entry.get().strip()
        if not char:
            messagebox.showwarning("警告", "请先生成字符数据")
            return
        
        filename = filedialog.asksaveasfilename(
            title="保存字库数据",
            defaultextension=".c",
            filetypes=[("C文件", "*.c"), ("文本文件", "*.txt"), ("所有文件", "*.*")],
            initialname=f"font_{char}_{ord(char):04X}.c"
        )
        
        if filename:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(self.output_text.get(1.0, tk.END))
                messagebox.showinfo("成功", f"已保存到: {filename}")
            except Exception as e:
                messagebox.showerror("错误", f"保存失败: {str(e)}")
    
    def clear_all(self):
        """清空所有内容"""
        self.char_entry.delete(0, tk.END)
        self.output_text.delete(1.0, tk.END)
        self.canvas.delete("all")

def main():
    root = tk.Tk()
    app = SimpleFontGenerator(root)
    root.mainloop()

if __name__ == "__main__":
    main()
