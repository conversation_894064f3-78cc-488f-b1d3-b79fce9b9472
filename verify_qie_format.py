#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证"切"字的正确格式理解
"""

def verify_qie_format():
    """验证'切'字的字库格式"""
    
    print("验证'切'字的字库格式")
    print("=" * 50)
    
    # "切"字的原始数据
    qie_data = [
        # 前12字节（左8位）
        0x10, 0x10, 0xFF, 0x08, 0x08, 0x02, 0x02, 0xFE, 0x02, 0x02, 0xFE, 0x00,
        # 后12字节（右4位）
        0x00, 0x00, 0x07, 0x02, 0x09, 0x04, 0x03, 0x00, 0x08, 0x08, 0x07, 0x00
    ]
    
    left_bytes = qie_data[:12]   # 前12字节
    right_bytes = qie_data[12:]  # 后12字节
    
    print("原始数据分析:")
    print("前12字节（每行左8位）:", [f"0x{b:02X}" for b in left_bytes])
    print("后12字节（每行右4位）:", [f"0x{b:02X}" for b in right_bytes])
    
    print("\n12x12完整点阵图案:")
    for row in range(12):
        pattern = ""
        
        # 左8位
        left_byte = left_bytes[row]
        for bit in range(8):
            pattern += "█" if (left_byte & (1 << (7-bit))) else "·"
        
        # 右4位（从字节的高4位取）
        right_byte = right_bytes[row]
        for bit in range(4):
            pattern += "█" if (right_byte & (1 << (7-bit))) else "·"
        
        print(f"行{row:2d}: {pattern}")
    
    print("\n标准字库格式输出:")
    print("{{12, 12},{")
    
    # 前12字节，注释显示完整12位
    for i in range(12):
        left_byte = left_bytes[i]
        right_byte = right_bytes[i]
        
        # 生成完整12位注释
        pattern = ""
        for bit in range(8):
            pattern += "@" if (left_byte & (1 << (7-bit))) else "."
        for bit in range(4):
            pattern += "@" if (right_byte & (1 << (7-bit))) else "."
        
        if i == 11:
            print(f"0x{left_byte:02X},    /*  {pattern}  */")
            print()
        else:
            print(f"0x{left_byte:02X},    /*  {pattern}  */")
    
    # 后12字节，注释显示完整12位
    for i in range(12):
        left_byte = left_bytes[i]
        right_byte = right_bytes[i]
        
        # 生成完整12位注释
        pattern = ""
        for bit in range(8):
            pattern += "@" if (left_byte & (1 << (7-bit))) else "."
        for bit in range(4):
            pattern += "@" if (right_byte & (1 << (7-bit))) else "."
        
        if i == 11:
            print(f"0x{right_byte:02X}     /*  {pattern}  */")
        else:
            print(f"0x{right_byte:02X},    /*  {pattern}  */")
    
    print("}}")
    
    print("\n格式说明:")
    print("1. 总共24字节表示一个12x12字符")
    print("2. 前12字节：每行的左8位")
    print("3. 后12字节：每行的右4位（存储在字节的高4位）")
    print("4. 注释：显示完整的12位图案（左8位+右4位）")
    
    print("\n数据验证:")
    print("左8位数据分析:")
    for i, byte_val in enumerate(left_bytes):
        left_pattern = ""
        for bit in range(8):
            left_pattern += "@" if (byte_val & (1 << (7-bit))) else "."
        print(f"  行{i:2d}: 0x{byte_val:02X} = {left_pattern}")
    
    print("\n右4位数据分析:")
    for i, byte_val in enumerate(right_bytes):
        right_pattern = ""
        for bit in range(4):
            right_pattern += "@" if (byte_val & (1 << (7-bit))) else "."
        print(f"  行{i:2d}: 0x{byte_val:02X} = {right_pattern}    (高4位有效)")

def test_bitmap_conversion():
    """测试位图转换功能"""
    print("\n" + "=" * 50)
    print("测试位图转换功能")
    
    # 创建一个简单的测试位图（一个"十"字）
    test_bitmap = []
    for row in range(12):
        row_data = []
        for col in range(12):
            # 创建十字图案：第6行和第6列
            if row == 5 or col == 5:
                row_data.append(1)
            else:
                row_data.append(0)
        test_bitmap.append(row_data)
    
    print("测试位图（十字图案）:")
    for row in range(12):
        pattern = ""
        for col in range(12):
            pattern += "█" if test_bitmap[row][col] else "·"
        print(f"行{row:2d}: {pattern}")
    
    # 转换为字节数组
    left_bytes = []
    right_bytes = []
    
    # 前12字节：每行的左8位
    for row in test_bitmap:
        byte_val = 0
        for col in range(8):
            if row[col]:
                byte_val |= (1 << (7 - col))
        left_bytes.append(byte_val)
    
    # 后12字节：每行的右4位
    for row in test_bitmap:
        byte_val = 0
        for col in range(8, 12):
            if row[col]:
                byte_val |= (1 << (7 - (col - 8)))
        right_bytes.append(byte_val)
    
    print("\n转换后的字库格式:")
    print("{{12, 12},{")
    
    # 前12字节
    for i in range(12):
        left_byte = left_bytes[i]
        right_byte = right_bytes[i]
        
        pattern = ""
        for bit in range(8):
            pattern += "@" if (left_byte & (1 << (7-bit))) else "."
        for bit in range(4):
            pattern += "@" if (right_byte & (1 << (7-bit))) else "."
        
        if i == 11:
            print(f"0x{left_byte:02X},    /*  {pattern}  */")
            print()
        else:
            print(f"0x{left_byte:02X},    /*  {pattern}  */")
    
    # 后12字节
    for i in range(12):
        left_byte = left_bytes[i]
        right_byte = right_bytes[i]
        
        pattern = ""
        for bit in range(8):
            pattern += "@" if (left_byte & (1 << (7-bit))) else "."
        for bit in range(4):
            pattern += "@" if (right_byte & (1 << (7-bit))) else "."
        
        if i == 11:
            print(f"0x{right_byte:02X}     /*  {pattern}  */")
        else:
            print(f"0x{right_byte:02X},    /*  {pattern}  */")
    
    print("}}")

if __name__ == "__main__":
    verify_qie_format()
    test_bitmap_conversion()
