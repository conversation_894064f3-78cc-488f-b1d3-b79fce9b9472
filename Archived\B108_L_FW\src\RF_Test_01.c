//----------------------------------------------------
#include "SysBasic.h"
#include "UartFun_A.h"
#include "RF_Test_01.h"

//------------------------------------------------------------------------
extern  uint8_t  DefPATable[] ;

extern  uint16_t  TestKeyA,TestKeyB ;

extern  volatile  uint16_t  RSSI_STA_L,RSSI_STA_R,RSSI_BufIndex_L,RSSI_BufIndex_R;
extern  int16_t  ch_num ;
//------------------------------------------------------------------------
void  RfTestEntry_01(void)
{
 #ifdef DEV_DEBUG
   GPIOA->ODR     =0x0000bb04 ;
   GPIOA->MODER   =0xa95500a4 ;//--use RS485 for debug
   GPIOA->OTYPER  =0x00000000 ;
   GPIOA->OSPEEDR =0x01550000 ;
   GPIOA->PUPDR   =0x64005409 ;//
   USART_Config() ;//Use RS485 for debug
 #endif
 EnableTIM11() ;//Enable Timer11 for 1mS interrupt
 InitSPI1_01() ;
 //-----------
 KickWatchDog();//kick the watchdog
 TestCtrlW=0x8000 ;
 RSSI_STA_L=0x0002 ;
 RSSI_BufIndex_L=0 ;
 test_p=(uint8_t*) &U_Area.Ultra_BUF[0] ;
 //-------
 SetupTestRegs_01() ;//Reset chip,setup register including PA table
 //-------
 Timer10[OPC_TIMER].pv=10 ;//100mS peroid
 Timer10[OPC_TIMER].cv=0 ;
 Timer10[OPC_TIMER].csr.csword=0x5000 ; //start Timer10[OPC_TIMER] ,set Q=1
 U_Area.FallingKeyRaw.cw=0 ;
 SYS_STA=0x0000  ;
 while(1)
 {
  TimerProc() ;//user defined timer proccess
  ADC12Daemon() ;
  GetKeySta() ;
  switch(SYS_STA)
  {
   case 0x0000 : //RX or TX test function select
	  if(U_Area.FallingKeyRaw.cw==SW5)
	  {//Select TX test
        TestCtrlW=0xc100 ;//Default use 0x55 modulation test
	    SYS_STA=0x0002 ;
		ch_num=APP_PA.RFIC_L_TxChNum ;
        JF01WriteReg(JF01_FREQ2, Tx_01_Ch_p->freq2);      // Frequency control word, high byte.
        JF01WriteReg(JF01_FREQ1, Tx_01_Ch_p->freq1);      // Frequency control word, middle byte.
        JF01WriteReg(JF01_FREQ0, Tx_01_Ch_p->freq0);      // Frequency control word, low byte.
        JF01WriteReg(JF01_IOCFG0,0x02) ;// Set GDO0 to be TX FIFO threshold ALARM signal
        JF01WriteReg(JF01_IOCFG2,0x06) ;// Set GDO2 to packet sent signal
        WriteFIFO_Const_01(0x55,62);//
        EnterTxTestMode_01() ;
        JF01CmdStrobe(JF01_STX);//Enter TX status
	    break ;
	   }
	  if(U_Area.FallingKeyRaw.cw==SW4)
	  {//Select RX test
        TestCtrlW=0x8000 ;//for RX debug
	    SYS_STA=0x0004 ;
		ch_num=APP_PA.RFIC_L_RxChNum ;
        JF01WriteReg(JF01_FREQ2, Rx_01_Ch_p->freq2);      // Frequency control word, high byte.
        JF01WriteReg(JF01_FREQ1, Rx_01_Ch_p->freq1);      // Frequency control word, middle byte.
        JF01WriteReg(JF01_FREQ0, Rx_01_Ch_p->freq0);      // Frequency control word, low byte.
        JF01WriteReg(JF01_IOCFG0,0x00) ;// Set GDO0 to be Asserts when RX FIFO is filled at or above threshold
        JF01WriteReg(JF01_IOCFG2,0x06) ;// Set GDO2 to assert when sync word has been received
        EnterRxTestMode_01() ;
        JF01CmdStrobe(JF01_SRX);//Enter RX status
 	   }
      break ;
   case 0x0002 ://In TX test status
      if(U_Area.FallingKeyRaw.cw==SW11)//SW11 (keycode.12)
      {//Switch test frequecy band
        JF01CmdStrobe(JF01_SIDLE);//Enter IDLE status
		ch_num++ ;
		if(ch_num>5) ch_num=0 ;
        Rx_01_Ch_p=(JF01SwitchReg *)&(APP_PD_p->Rx_Freq_Ch[ch_num]) ;
        Tx_01_Ch_p=(JF01SwitchReg *)&(APP_PD_p->Tx_Freq_Ch[ch_num]) ;
        U_Area.FallingKeyRaw.cw=0 ;
        JF01WriteReg(JF01_FREQ2, Tx_01_Ch_p->freq2);      // Frequency control word, high byte.
        JF01WriteReg(JF01_FREQ1, Tx_01_Ch_p->freq1);      // Frequency control word, middle byte.
        JF01WriteReg(JF01_FREQ0, Tx_01_Ch_p->freq0);      // Frequency control word, low byte.
        JF01WriteReg(JF01_IOCFG0,0x02) ;// Set GDO0 to be TX FIFO threshold ALARM signal
        JF01WriteReg(JF01_IOCFG2,0x06) ;// Set GDO2 to packet sent signal
        WriteFIFO_Const_01(0x55,62);//
        EnterTxTestMode_01() ;
        JF01CmdStrobe(JF01_STX);//Enter TX status
       }
	  if(U_Area.FallingKeyRaw.cw==SW7)
	  {//Select 0x55 modulation TX test
        TestCtrlW=0xc100 ;
	   }
	  if(U_Area.FallingKeyRaw.cw==SW8)
	  {//Select 0x00 modulation test
        TestCtrlW=0xc200 ;
	   }
	  if(U_Area.FallingKeyRaw.cw==SW9)
	  {//Select 0xff modulation test
        TestCtrlW=0xc300 ;
	   }
      TxTestDaemon_01() ;
      break ;
   case 0x0004 ://In RX test status
      if(U_Area.FallingKeyRaw.cw==SW11)//SW11 (keycode.12)
      {//Switch test frequecy band
        JF01CmdStrobe(JF01_SIDLE);//Enter IDLE status
	    ch_num++ ;
		if(ch_num>5) ch_num=0 ;
        Rx_01_Ch_p=(JF01SwitchReg *)&(APP_PD_p->Rx_Freq_Ch[ch_num]) ;
        Tx_01_Ch_p=(JF01SwitchReg *)&(APP_PD_p->Tx_Freq_Ch[ch_num]) ;
        U_Area.FallingKeyRaw.cw=0 ;
        JF01WriteReg(JF01_FREQ2, Rx_01_Ch_p->freq2);      // Frequency control word, high byte.
        JF01WriteReg(JF01_FREQ1, Rx_01_Ch_p->freq1);      // Frequency control word, middle byte.
        JF01WriteReg(JF01_FREQ0, Rx_01_Ch_p->freq0);      // Frequency control word, low byte.
        JF01WriteReg(JF01_IOCFG0,0x00) ;// Set GDO0 to be Asserts when RX FIFO is filled at or above threshold
        JF01WriteReg(JF01_IOCFG2,0x06) ;// Set GDO2 to assert when sync word has been received
        EnterRxTestMode_01() ;
        JF01CmdStrobe(JF01_SRX);//Enter RX status
       }
      RxTestDaemon_01() ;
      RSSI_Test_Filter_01() ;
      if(GFlag&GF_TEST_RX_OK)
      {
        Led_4_on ;//Turn on the OK LED(Keep it ON)
       }
	  else
	    Led_4_off ;//T
      break ;
   default: SYS_STA=0x0000 ;
   }
//-----------------------------------
  if(RfDs.Area21Flag==0x1234)//MD20021,==0x1234
  {
    RfDs.Area21Flag=0x4321 ;
    JF01_B_Read(JF01_IOCFG2,(uint8_t *) &U_Area.Ultra_BUF[0],47) ;//---MD20001
   }
  if(RfDs.Area21Flag==0x2345)//MD20021,==0x2345
  {
    RfDs.Area21Flag=0x5432 ;
    JF01_B_Read(JF01_MARCSTATE,(uint8_t *) &U_Area.Ultra_BUF[48],1) ;//---MD20049HB
   }
//----------------------------------
  U_Area.FallingKeyRaw.cw=0x0000 ;
#ifdef DEV_DEBUG
  ModProcA(&UB,&ModB) ;//Modbus daemon  using channel B	--debug
  UXTxP() ;//--debug
#endif
  if(Timer10[OPC_TIMER].csr.csbit.q==1)
  {
    Timer10[OPC_TIMER].csr.csword=0x4000 ;
    Timer10[OPC_TIMER].cv=0 ;
    if(Led_2_sta)
      Led_2_off ;
    else
      Led_2_on ;
   }
//----------------------------------
  KickWatchDog();//kick the watchdog
 }
}

//-------------------------------------------------------------------------------
void SetupTestRegs_01(void)
{
  JF01Reset();//After this chip enter IDLE status
  JF01CmdStrobe(JF01_SIDLE);//Enter IDLE status
  JF01WriteReg(JF01_FSCTRL1,  APP_PD_p->JF01Def.fsctrl1);    // Frequency synthesizer control.
  JF01WriteReg(JF01_FSCTRL0,  APP_PD_p->JF01Def.fsctrl0);    // Frequency synthesizer control.
  JF01WriteReg(JF01_MDMCFG4,  APP_PD_p->JF01Def.mdmcfg4);    // Modem configuration.
  JF01WriteReg(JF01_MDMCFG3,  APP_PD_p->JF01Def.mdmcfg3);    // Modem configuration.
  JF01WriteReg(JF01_MDMCFG2,  0x00);    // Modem configuration for no preamble/sync word in RX/TX mode(2FSK )
  JF01WriteReg(JF01_MDMCFG1,  APP_PD_p->JF01Def.mdmcfg1);    // Modem configuration.
  JF01WriteReg(JF01_MDMCFG0,  APP_PD_p->JF01Def.mdmcfg0);    // Modem configuration.
  JF01WriteReg(JF01_CHANNR,   APP_PD_p->JF01Def.channr);     // Channel number.
  JF01WriteReg(JF01_DEVIATN,  APP_PD_p->JF01Def.deviatn);    // Modem deviation setting (when FSK modulation is enabled).
  JF01WriteReg(JF01_FREND1,   APP_PD_p->JF01Def.frend1);     // Front end RX configuration.
  JF01WriteReg(JF01_FREND0,   APP_PD_p->JF01Def.frend0);     // Front end RX configuration.
  JF01WriteReg(JF01_MCSM0,    APP_PD_p->JF01Def.mcsm0);      // Main Radio Control State Machine configuration.
  JF01WriteReg(JF01_FOCCFG,   APP_PD_p->JF01Def.foccfg);     // Frequency Offset Compensation Configuration.
  JF01WriteReg(JF01_BSCFG,    APP_PD_p->JF01Def.bscfg);      // Bit synchronization Configuration.
  JF01WriteReg(JF01_AGCCTRL2, APP_PD_p->JF01Def.agcctrl2);   // AGC control.
  JF01WriteReg(JF01_AGCCTRL1, APP_PD_p->JF01Def.agcctrl1);   // AGC control.
  JF01WriteReg(JF01_AGCCTRL0, APP_PD_p->JF01Def.agcctrl0);   // AGC control.
  JF01WriteReg(JF01_FSCAL3,   APP_PD_p->JF01Def.fscal3);     // Frequency synthesizer calibration.
  JF01WriteReg(JF01_FSCAL2,   APP_PD_p->JF01Def.fscal2);     // Frequency synthesizer calibration.
  JF01WriteReg(JF01_FSCAL1,   APP_PD_p->JF01Def.fscal1);     // Frequency synthesizer calibration.
  JF01WriteReg(JF01_FSCAL0,   APP_PD_p->JF01Def.fscal0);     // Frequency synthesizer calibration.
  JF01WriteReg(JF01_FSTEST,   APP_PD_p->JF01Def.fstest);     // Frequency synthesizer calibration.
  JF01WriteReg(JF01_TEST2,    APP_PD_p->JF01Def.test2); //Various test settings ,lost in sleep status.
  JF01WriteReg(JF01_TEST1,    APP_PD_p->JF01Def.test1); //Various test settings ,lost in sleep status.
  JF01WriteReg(JF01_TEST0,    APP_PD_p->JF01Def.test0); //Various test settings ,lost in sleep status.
  JF01WriteReg(JF01_FIFOTHR,  0x47);   //Set TX FIFO threshold 33 Bytes,Bytes in RX FIFO =32 ,Test1=0x35 ,Test2=0x81 from sleep
  JF01WriteReg(JF01_MCSM1,    0x00);   //CCA mode=Always,RXOFF_MODE=IDLE,TXOFF_MODE=IDLE status
  JF01WriteReg(JF01_SYNC1,    0x55);   //0xcc
  JF01WriteReg(JF01_SYNC0,    0x55);   //0x33
  JF01WriteReg(JF01_PKTCTRL1, 0x00);   // Packet automation control.
  JF01WriteReg(JF01_PKTCTRL0, 0x02);   //use infinite packet mode for test.
  JF01WritePATable(DefPATable, PA_TAB_LEN) ;//I will only use DefPATable[0] normally
}

uint8_t WriteFIFO_Const_01(uint8_t Data ,uint8_t Len)
{
  uint8_t i,sta ;
  GPIOE->BSRRH=0x1000 ;//nCS='0'
  while((SPI1->SR&0x0002)==0x0000) ;//Wait the Tx FIFO to empty
  while(SPI1->SR&0x0001)
  { //empty the receive FIFO
    sta=(uint8_t) SPI1->DR ;//
   } 
  while((GPIOE->IDR&0x4000)!=0)  ;//Wait for chip to ready
  __disable_irq();//Disable all interrupt
  SPI1->DR=JF01_TXFIFO+JF01_WRITE_BURST ;//Issue the address byte
  while((SPI1->SR&0x0001)==0)  ;//Wait for the data be output
  sta=SPI1->DR ;//Get returned JF01 sta byte
  for(i=0 ;i<Len ;i++)
  {
    SPI1->DR=Data ;//Write the data
    while((SPI1->SR&0x0001)==0)  ;//Wait for the data be output
    sta=SPI1->DR ;//dummy read
   }
  __enable_irq();
  GPIOE->BSRRL=0x1000;  // Set /nCS to High 
  return sta ;
}
__inline void EnterTxTestMode_01(void)
{
  __disable_irq();//Disable all interrupt//Disable IRQ
  RfDs.RF_State=TX_STATUS ;
  RfDs.RF_TxState=TX_RF_TEST ;
  GPIOB->BSRR=0x00600080 ;//PB.7->Tx_EN='1' Open PA output path, PB.5,6->Rx_EN='0' shut off LNA
  RfDs.RF_RxState=RX_NO_INIT_IDLE ;
  EXTI->RTSR &=0x003ff7ff ;//Disable Rising trigger
  EXTI->FTSR |=0x00000800 ;//generate interrupt on high-to-low transition(TX FIFO blow threshold)
  EXTI->PR   |=0x00000800 ;//clear pending ext interrupt line11# PE.11--GDO0(DCLK)
  EXTI->IMR  |=0x00000800 ;//Allow PE.11 to generate interrupt
  __enable_irq();//Enable IRQ
}
__inline void EnterRxTestMode_01(void)
{
//  GPIOC->BRR=0x00000200 ;//Reset nRx_Tx_EN='0'
  __disable_irq();//Disable all interrupt//Disable IRQ
  RfDs.RF_State=RX_STATUS ;
  RfDs.RF_TxState=TX_IDLE ;
  GPIOB->BSRR=0x00800060 ;//PB.7->Tx_EN='0' Switch off RF output (PA) path,PB.5,6->Rx_EN='1' Switch on RF LNA ,enter receive status
  RfDs.RF_RxState=RX_RF_TEST ;
  RfDs.char_count=0 ;
  test_p=(uint8_t*) &U_Area.Ultra_BUF[0] ;
  EXTI->RTSR |=0x00000800 ;//generate interrupt on low-to-high transition(RX FIFO filled at or above threshold)
  EXTI->FTSR &=0x003fffff ;//Disable Falling trigger
  EXTI->PR   |=0x00000800 ;//clear pending ext interrupt line11# PE.11--GDO0(DCLK)
  EXTI->IMR  |=0x00000800 ;//Allow PE.11 to generate interrupt
  __enable_irq();//Enable IRQ
}
//------------------------------------------
void RxTestDaemon_01(void)
{
 uint8_t i,sta,error_c ;
 if((IsrFlag & IFG_JF01_FIFO_ALARM))//||(GPIOE->IDR&0x0800))//PE.11--GDO0(DCLK) interrupt or stay at high level
 {
   error_c=0 ; 
   __disable_irq();//Disable all interrupt//Disable IRQ
   IsrFlag &=~IFG_JF01_FIFO_ALARM ;//Clear flag
   __enable_irq();//Enable IRQ
   GPIOE->BSRRH=0x1000 ;//reset PE.12 JF01 nCS='0'
   while((SPI1->SR&0x0002)==0x0000) ;//Wait the Tx FIFO to empty
   while(SPI1->SR&0x0001)
   { //empty the receive FIFO
     sta=(uint8_t) SPI1->DR ;//
    } 
   //---------------Begin to read out data in Rx FIFO
   while((GPIOE->IDR&0x4000)!=0)  ;//Wait for chip to ready 
   SPI1->DR=JF01_RXFIFO+JF01_READ_BURST ;//Issue the address byte
   while((SPI1->SR&0x0001)==0)  ;//Wait the SPI1 to idle
   sta=SPI1->DR ;//Get returned JF01 sta byte
   for(i=0 ;i<FIFO_THRESHOLD_BYTES-1 ;i++)
   {
      SPI1->DR=JF01_RXFIFO+JF01_READ_BURST ;//Dummy data
      while ((SPI1->SR&0x0001)==0)  ;//Wait for the data be output
     *test_p=SPI1->DR ;//Get the data ;
     if(((*test_p)!=0x55)&&((*test_p)!=0xaa))
       error_c++ ;
     test_p++ ;
     RfDs.char_count++ ;
     if(RfDs.char_count>=96)
     {
       RfDs.char_count=0 ;
       test_p=(uint8_t*) &U_Area.Ultra_BUF[0] ;
      }
     }
   GPIOE->BSRRL=0x1000;  // Set PE.12 JF01 nCS to High 
   sta=sta ;
   __disable_irq();//Disable IRQ
   IsrFlag|=IFG_RSSI_D_OK_L;//Guide to read RSSI in test mode
   __enable_irq();//Enable IRQ
   if(error_c==0)
     GFlag|=GF_TEST_RX_OK ;
   else
     GFlag&=~GF_TEST_RX_OK ;
   OpCtrl|=CB_JF01_RD_RSSI ;
   Timer10[OPC_TIMER].csr.csword=0x4000 ;//reset the monitor timer
   Timer10[OPC_TIMER].cv=0 ;
   Led_2_on ;
   RfDs.RF_RxPackage++ ;
  }
}
//------------------------------------------
void TxTestDaemon_01(void)
{
 if(IsrFlag & IFG_JF01_FIFO_ALARM)//||(GPIOE->IDR&0x0800))//PE.11--GDO0(DCLK) interrupt or stay at high level
 {
   __disable_irq();//Disable all interrupt//Disable IRQ
   IsrFlag &=~IFG_JF01_FIFO_ALARM ;//Clear flag
   __enable_irq();//Enable IRQ
   if((TestCtrlW&0xff00)==0xc200)
   {//Tx for 0x00 modulation
     WriteFIFO_Const_01(0x00,FIFO_THRESHOLD_BYTES-1);//
    }
   else if((TestCtrlW&0xff00)==0xc300)
   {//Tx for 0xff modulation
     WriteFIFO_Const_01(0xff,FIFO_THRESHOLD_BYTES-1);//
    }
   else
   {//Tx for 0x55 modulation
     WriteFIFO_Const_01(0x55,FIFO_THRESHOLD_BYTES);//
    }
   Timer10[OPC_TIMER].csr.csword=0x4000 ;//reset the monitor timer
   Timer10[OPC_TIMER].cv=0 ;
   Led_2_on ;
   RfDs.RF_TxPackage++ ;
 }
}

//-----------------------------------
void  RSSI_Test_Filter_01(void)
{
 int16_t  i, acc ;
 switch(RSSI_STA_L)
 {
  case 0x0000 :	break ;
  case 0x0002 :
    if(IsrFlag&IFG_RSSI_D_OK_L)
    {
      __disable_irq() ;
      IsrFlag&=~IFG_RSSI_D_OK_L ;
      __enable_irq() ;
	  RfDs.RSSI_D_L=ReadRSSITest_01();
      RfDs.RSSI_BUF_L[RSSI_BufIndex_L++]=RfDs.RSSI_D_L;
      if(RSSI_BufIndex_L>=8)
      {
        RSSI_BufIndex_L=0 ;
        RSSI_STA_L=0x0004 ;
       }
    }
    break ;
  case 0x0004 :
    acc=0 ;
    for(i=0 ;i<8 ;i++)
    {
     acc+=RfDs.RSSI_BUF_L[i] ;
     }
     RfDs.RSSI_L=(acc>>3) ;
	 if(APP_PA.work_mode&LNA_EXIST_01)//parameter control if LNA exist in 38404B board
       RfDs.RSSI_L+=APP_PA.RSSIOffset ;
    if( RfDs.RSSI_L<-128)  RfDs.RSSI_L=-128 ;
    RSSI_STA_L=0x0002 ;
    break ;
  default :
    RSSI_STA_L=0x0000 ;
 }
}
//------------
int16_t ReadRSSITest_01(void)
{
 uint8_t ctmp ;
 int16_t  stmp ;
 ctmp=JF01ReadStaReg(JF01_RSSI) ;
 if(ctmp>128)
 {
   stmp=((uint16_t)ctmp-256)>>1 ;
   stmp-=RSSI_OFFSET ;
  }
 else
 {
   stmp=(ctmp>>1)-RSSI_OFFSET ;
  }
 return stmp ;
}
//------------
