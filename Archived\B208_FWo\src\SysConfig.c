//-----------------
#include "SysConfig.h"
#include "Timer.h"
#include "ThisDevice.h"
#include "RF_BasicFun.h"

//------------------------------
const  uint16_t  SysConfigADef[]={
// .Parameter ID=0x0031 ,LEN=28
   0x0031 , //Px0101,Parameter's identifier
       28 ,//Px0102,configuration data length
   0xffff ,//Px0103, 
   0xffff ,//Px0104,reserved word16   ,+4

   0x0800 ,//Px0105, work mode of this device.=0x0xxx-0x7xxx->for IC021 type receiver  ,=0x8xxx-0xfxxx->for JF01 type receiver
           //--Bit_11 of this parameter to switch if Heart beat signal used(bit11='1' use heart beat signal,='0' not use it
   0x0004 ,//Px0106,Bit0-1[0-3]set this panel  in group id,Bit2-7[0-63] group id ,When the device work as panel transmitter 
   0xffff,0xffff,//Px0107-0108, reserved 2 words,+4

        0 , //Px0109,Left side RF IC Recieve working frequecy channel number[0-5] 
        3 , //Px0110,Left side RF IC Transmitte working frequecy channel number[0-5] 
        4 , //Px0111,Right side RF IC Recieve working frequecy channel number[0-5] 
        3 , //Px0112,Right side RF IC Transmitte working frequecy channel number[0-5]  ,+4  
       
      250 ,//Px0113, AirLinkageTO ,About 250x10mS
	  220 ,//Px0114, OpcKeepTime=150*10 mS 	
      150 ,//Px0115, HeartBeatTim=150x10mS,if used 	
       12 ,//Px0116, RF Tx frame first preamble segment length in bytes(at least 8 bytes for AFC),+4   

             2320 , //Px0117,Synchronization reset value for RF tick timer,will be override use those parameter in APP_PB 
             3275 , //Px0118,Preload counter cycle value for the RF tick timer(determine the RF tick long)
                    //       ,will be override use those parameter in APP_PB	
    0xffff,0xffff , //Px0119-0120,	  ,+4	

               55 , //Px0121,JF01 chip Rx attenuation status monitor time out value --in mS					   
           0xffff , //Px0122,
  (uint16_t)(-17) , //Px0123, RSSI voltage input channel left  
  (uint16_t)(-17) , //Px0124, RSSI voltage input channel right ,+4	

      2060 ,//Px0125,typical temperature coefficient (1.66x4095/3300)*1000 
       263 ,//Px0126,typical temperature offset of on chip temperature sensor @273k(0 celsuis degree)
    0xffff ,//Px0127, Reserved word 3 
    0x0000 //Px0128, crc check value  +4		 
} ;

//-----------------------------------------------------------------------------
const  uint16_t  SysConfigBDef[]={
// .Parameter ID=0x0032 ,LEN=32
   0x0032 ,//Px0101,Parameter's identifier
       32 ,//Px0102,configuration data length
   0xffff ,//Px0103,module Auth code low word16
   0xffff ,//Px0104,module Auth code hight word16   ,+4
//USART 1 port configuration --Maybe for debug connection
  (uint16_t)0x00+((uint16_t)0x05<<8) , //Px0105, work mode of the port(reserved),
                                        //=0x01,Modbus slaver  address
                      (uint16_t)38400 , //Px0106, Communication speed (baud rate,allow:2400 4800,9600,14400,19200,28800,38400,default 19200)
  (uint16_t)0x01+((uint16_t)4<<8) , //Px0107, =0x00 no parity, 01 odd parity (default 01 odd parity),02 even parity
	  			       //=4, in ms,tx delay time of slaver reponse(default 5 ms),minimal value=2ms
  (uint16_t)100+((uint16_t)1<<8) , //Px0108, in ms,when act as master the max wait time(in ms) for slaver respone,minimal value=60ms 
                       //=1, retry number  when there are no response form slaver
 //Lo_Add ,Hi_Add ,   Attr  --For UART 1
        0 ,     7 ,   READ_ONLY , //Px0109-0110, 
        8 ,   127 ,  READ_WRITE , //Px0111-0112,
      128 , 19999 ,   NO_ACCESS , //Px0113-0114,
    20000 , 20255 ,  READ_WRITE , //Px0115-0116,
           
                0 ,//Px0117, number of segment in this control table(=0, no control) 
           0xffff ,//Px0118,
           0xffff ,//Px0119,Left side mode operation key mask code
           0xffff ,//Px0120,Right side mode operation key mask  code,+4

    0x0001 ,//Px0121,Great than 0 to enable the CAN port,=0x00xx->CAN Open Mode,=0x01xx->Free CAN bus meassage mode  
       250 ,//Px0122,CAN bus working speed setting 125kbps,250kbps,500kbps,1Mbps
	0xffff ,//Px0123,  
	0xffff ,//Px0124,  ,+4  
	  
   (uint16_t)0x08+((uint16_t)0x80<<8),//Px0125,CANOpen mode Node ID 1-127(Low byte),CANOpen special setting byte(high byte)
   (uint16_t)0x08+((uint16_t)0x09<<8),//Px0126, CANOpen mode receive PDO Node ID 1(low byte),receive PDO Node ID 2(high byte) ,+4
   	100,//Px0127,CANOpen synchronizing PDO Transmit interval(ms)
	 25,//Px0128,CANOpen data PDO Transmit interval(ms)
   	
//     1305 ,//Px0125,Reset value for RF timer(9600bps) ,when synchronization signal be captured(for Fyf35 use 2320)
//     1638 ,//Px0126,Preload counter cycle value for the RF tick timer(determine the RF tick long)
//           //       for Fyf35 use (9600bps--20.0048Hz,49.98748mS)
     2320 ,//Px0125,Reset value for RF timer ,when synchronization signal be captured(for Fyf35 use 2320)
     3275 ,//Px0126,Preload counter cycle value for the RF tick timer(determine the RF tick long)
           //       for Fyf35 use 3275(10.005Hz,99.945mS)
     1305 ,//Px0127,Reset value for RF timer ,when synchronization signal be captured(not used for Fyf25)
     1638 ,//Px0128,Preload counter cycle value for the RF tick timer(determine the RF tick long)
           //       for Fyf25 use 1638(20.0048Hz,49.98748mS) ,+4

   0xffff ,//Px0129, Reserved 
   0xffff ,//Px0130, Reserved 
   0xffff ,//Px0131, Reserved 
   0xffff  //Px0132, crc check value  +4		 
} ;

//-----------------------------------------------------------------------------
__align(4) const uint32_t  SysConfigCDef[]={
// .Parameter ID=0x0033 ,LEN=86
     0x00560033 ,//Parameter's identifier
//uint16_t  len ;//configuration data length
     0x00000000 ,//Rev_wa[2]
//----------------------------
     0x89560010 ,//R0 ,N Register PLL 8-bit Integer-N 15-Bit Fractional-N,for 315.2872MHz Rx mode
     0x021b7021 ,//R1 ,VCO/Oscillator Register for 315.2872MHz Rx
     0x00b7f882 ,//R2 ,Transmit Modulation register, PA on, for 2FSK,Fdev=2.475kHz
     0x364c80d3 ,//R3 ,Transmit/Receive clock register DEMOD_CLK=4.9152MHz,Data rate=4800
     0x8053d814 ,//R4 ,Demodulator setup register For 18.5 kHz IF BW,Discrim B/W=2x(K value=20),PostDemodulator BW=3.8197kHz 
//     0x0127f882 ,//R2 ,Transmit Modulation register, PA on, for 2FSK,Fdev=4.05kHz
//     0x364c6093 ,//R3 ,Transmit/Receive clock register DEMOD_CLK=4.9152MHz,Data rate=9600
//     0x80732c94 ,//R4 ,Demodulator setup register For 18.5 kHz IF BW,Discrim B/W=2x(K value=11),PostDemodulator BW=8.0214kHz 
     0x000024f5 ,//R5 ,IF filter setup register
     0x0a07cbc6 ,//R6 ,IF fine calibration setup register for stander IF cal    0x05070e06
     0x00000fd8 ,//R8 ,Power down test register,internal Rx/Tx switch control
     0x00a631e9 ,//R9 ,AGC setup register,Manual AGC ,use max LNA&Filter GAIN
//     0x1296473a ,//R10 ,AFC setup register,AFC on,9kHz(-9~+9kHz) AFC range,KP=4,KI=11,AFC scaling factor=569
     0x12d0473a ,//R10 **,AFC setup register,AFC on,9kHz(-9~+9kHz) AFC range,KP=6,KI=8,AFC scaling factor=569
     0x00cc331b ,//R11 ,SYNC word detect register
     0x000008ac ,//R12 ,SWD/Threshold setup register
     0x0000000d ,//R13 ,3FSK Demod register
     0x0000000e ,//R14 ,Test DAC register
     0x0000000f ,//R15 ,Test register
//--Rx frequency channel 0 register setting
     0x8954e440 , //R0 for 314.2657MHz Rx mode,      
     0x021b7021 , //R1 ,VCO/Oscillator Register Rx
//--Rx frequency channel 1 register setting
     0x89550010 , //R0 for 314.3656MHz Rx mode,      
     0x021b7021 , //R1 ,VCO/Oscillator Register Rx
//--Rx frequency channel 2 register setting
     0x895578b0 , //R0 for 314.8000MHz Rx mode,      
     0x021b7021 , //R1 ,VCO/Oscillator Register Rx
//--Rx frequency channel 3 register setting
     0x89560010 , //R0 for 315.2872MHz Rx mode,      
     0x021b7021 , //R1 ,VCO/Oscillator Register Rx
//--Rx frequency channel 4 register setting
     0x89568e70 , //R0 for 315.8000MHz Rx mode,      
     0x021b7021 , //R1 ,VCO/Oscillator Register Rx
//--Rx frequency channel 5 register setting
     0x89570010 , //R0 for 316.2088MHz Rx mode,      
     0x021b7021 , //R1 ,VCO/Oscillator Register Rx
//----------------------------
//--Tx frequency channel 0 register setting
     0x81550010 , //R0 for 314.2656MHz Tx mode
     0x021b7021 , //R1 ,VCO/Oscillator Register Tx
//--Tx frequency channel 1 register setting
     0x81551bc0 , //R0 for 314.3656MHz Tx mode
     0x021b7021 , //R1 ,VCO/Oscillator Register Tx
//--Tx frequency channel 2 register setting
     0x81559470 , //R0 for 314.8000MHz Tx mode
     0x021b7021 , //R1 ,VCO/Oscillator Register Tx
//--Tx frequency channel 3 register setting
     0x81561bc0 , //R0 for 315.2872MHz Tx mode
     0x021b7021 , //R1 ,VCO/Oscillator Register Tx
//--Tx frequency channel 4 register setting
     0x8156aa40 , //R0 for 315.8000MHz Tx mode
     0x021b7021 , //R1 ,VCO/Oscillator Register Tx
//--Tx frequency channel 5 register setting
     0x81571bc0 , //R0 for 316.2088MHz Tx mode
     0x021b7021 , //R1 ,VCO/Oscillator Register Tx
//--------------------------
     0xaaaa5555 , //Module UID
     0x0000ffff  //reserved loc ,Check sum of this basic parameter  block  ,+4
} ;

const uint8_t  SysConfigDDef[]={
// .Parameter ID=0x0034 ,LEN=56
  (uint8_t) (0x0034%256) ,//Parameter's identifier
  (uint8_t) (0x0034>>8) ,//Parameter's identifier
  (uint8_t) (56%256),//configuration data length
  (uint8_t) (56>>8) ,//configuration data length
  (uint8_t) (0x0000%256) ,//Rev_wa[0]
  (uint8_t) (0x0000>>8) ,//
  (uint8_t) (0x0000%256) ,//Rev_wa[1] +4 
  (uint8_t) (0x0000>>8) ,// 
//------------------------------------------------ 
//JF01 Default setting
  (uint8_t)0x29,//00,IOCFG2    GDO2 output pin configuration,CHIP_RDYn
  (uint8_t)0x2E,//01,IOCFG1    GDO1 output pin configuration,High impedance 3state
  (uint8_t)0x06,//02,IOCFG0    GDO0 output pin configuration,TX FIFO  full
  (uint8_t)0x47,//03,FIFOTHR   RX FIFO threshold=32 bytes ,and TX FIFO thresholds=33 bytes
  (uint8_t)0xCC,//04,SYNC1     Sync word, high byte
  (uint8_t)0x33,//05,SYNC0     Sync word, low byte
  (uint8_t)0x08,//06,PKTLEN    Packet length
  (uint8_t)0x84,//07,PKTCTRL1  Packet automation control,PQT,CRC_AUTOFLUSH,APPEND_STATUS,ADR_CHK

  (uint8_t)0x00,//08,PKTCTRL0  Packet automation control,WHITE_DATA,PKT_FORMAT,CRC_EN,LENGTH_CONFIG
  (uint8_t)0x01,//09,ADDR      Device address
  (uint8_t)0x00,//0A,CHANNR    Channel number
  (uint8_t)0x06,//0B,FSCTRL1   Frequency synthesizer control,FREQ_IF
  (uint8_t)0x00,//0C,FSCTRL0   Frequency synthesizer control,FREQOFF
  (uint8_t)0x0C,//0D,FREQ2     Frequency control word, high byte--For Frequency=314.020538MHz
  (uint8_t)0x13,//0E,FREQ1     Frequency control word, middle byte
  (uint8_t)0xE5,//0F,FREQ0     Frequency control word, low byte

  (uint8_t)0xc7,//10,MDMCFG4   Modem configuration,For 102kHz IF BW-CHANBW_E=3,CHANBW_M=0,for 162kHz-BW-CHANBW_E=2,CHANBW_M=1
                             //,For 4.797935kbps-DRATE_E=7(for 9.595871kbps,DRATE_E=8)
  (uint8_t)0x83,//11,MDMCFG3   Modem configuration,For 4.797935kbps-DRATE_M =0x83
                             //(for 9.595871kbps,DRATE_M =0x83)
  (uint8_t)0x02,//12,MDMCFG2   Modem configuration,DEM_DCFILT_OFF,MOD_FORMAT,MANCHESTER_EN,SYNC_MODE
  (uint8_t)0x33,//13,MDMCFG1   Modem configuration,FEC_EN,NUM_PREAMBLE-->6 bytes preamble,CHANSPC_E
  (uint8_t)0xF8,//14,MDMCFG0   Modem configuration,CHANSPC_M
  (uint8_t)0x41,//15,DEVIATN   Modem deviation setting,DEVIATION_E,Fpr deviation=31.738281kHz-DEVIATION_M=0x42
                //(for deviation=25.390625kHz-DEVIATION_M=0x40)(for deviation=28.564453kHz-DEVIATION_M=0x41)
  (uint8_t)0x07,//16,MCSM2     Main Radio Cntrl State Machine config,RX_TIME_RSSI,RX_TIME_QUAL,RX_TIME
  (uint8_t)0x0f,//17,MCSM1     Main Radio Cntrl State Machine config,CCA_MODE,RXOFF_MODE->Stay in RX,TXOFF_MODE->RX mode
  
  (uint8_t)0x19,//18,MCSM0     Main Radio Cntrl State Machine config,FS_AUTOCAL,PO_TIMEOUT,PIN_CTRL_EN,XOSC_FORCE_ON
  (uint8_t)0x16,//19,FOCCFG    Frequency Offset Compensation config,FOC_BS_CS_GATE,FOC_PRE_K,FOC_POST_K,FOC_LIMIT
  (uint8_t)0x6C,//1A,BSCFG     Bit Synchronization configuration,BS_PRE_KI,BS_PRE_KP,BS_POST_KI,BS_POST_KP,BS_LIMIT
  (uint8_t)0x43,//1B,AGCCTRL2  AGC control,MAX_DVGA_GAIN,MAX_LNA_GAIN,MAGN_TARGET
  (uint8_t)0x40,//1C,AGCCTRL1  AGC control,AGC_LNA_PRIORITY,CARRIER_SENSE_REL_THR,CARRIER_SENSE_ABS_THR
  (uint8_t)0x91,//1D,AGCCTRL0  AGC control,HYST_LEVEL,WAIT_TIME,AGC_FREEZE,FILTER_LENGTH
  (uint8_t)0x87,//1E,WOREVT1   High byte Event 0 timeout
  (uint8_t)0x6B,//1F,WOREVT0   Low byte Event 0 timeout
  
  (uint8_t)0xF8,//20,WORCTRL   Wake On Radio control,RC_PD,EVENT1,RC_CAL,WOR_RES
  (uint8_t)0x56,//21,FREND1    Front end RX configuration,LNA_CURRENT,LNA2MIX_CURRENT,LODIV_BUF_CURRENT_RX,MIX_CURRENT
  (uint8_t)0x10,//22,FREND0    Front end TX configuration,LODIV_BUF_CURRENT_TX,PA_POWER
  (uint8_t)0xE9,//23,FSCAL3    Frequency synthesizer calibration,FSCAL3[7:6],CHP_CURR_CAL_EN,FSCAL3[3:0]
  (uint8_t)0x2A,//24,FSCAL2    Frequency synthesizer calibration,VCO_CORE_H_EN,FSCAL2
  (uint8_t)0x00,//25,FSCAL1    Frequency synthesizer calibration,FSCAL1
  (uint8_t)0x1F,//26,FSCAL0    Frequency synthesizer calibration,FSCAL0
  (uint8_t)0x41,//27,RCCTRL1   RC oscillator configuration

  (uint8_t)0x00,//28,RCCTRL0   RC oscillator configuration
  (uint8_t)0x59,//29,FSTEST    Frequency synthesizer cal control,For test only.Do not write to this register
  (uint8_t)0x7F,//2A,PTEST     Production test,Write 0xbf for on-chip temperature in the IDLE state,otherwise use default value 0x7f
  (uint8_t)0x3F,//2B,AGCTEST   AGC test,Do not write to this register
  (uint8_t)0x81,//2C,**TEST2     Various test settings
  (uint8_t)0x35,//2D,**TEST1     Various test settings
  (uint8_t)0x0B,//2E,TEST0     Various test settings,TEST0[7:2],VCO_SEL_CAL_EN,TEST0[0]
  (uint8_t)0xff,//Reserved location

//--Rx frequency channel 0 register setting
  (uint8_t)0x0C,//0D	FREQ2 (x)--For Frequency=314.020538MHz
  (uint8_t)0x13,//0E	FREQ1 (x)
  (uint8_t)0xe5,//0F	FREQ0 (x)
  (uint8_t)0x0B,//2E,TEST0     Various test settings,TEST0[7:2],VCO_SEL_CAL_EN,TEST0[0]
//--Rx frequency channel 1 register setting
  (uint8_t)0x0C,//0D	FREQ2 (x)--For Frequency=315.073853MHz
  (uint8_t)0x1e,//0E	FREQ1 (x)
  (uint8_t)0x44,//0F	FREQ0 (x)
  (uint8_t)0x0B,//2E,TEST0     Various test settings,TEST0[7:2],VCO_SEL_CAL_EN,TEST0[0]
//--Rx frequency channel 2 register setting
  (uint8_t)0x0C,//0D	FREQ2 (x)--For Frequency=316.046123MHz
  (uint8_t)0x27,//0E	FREQ1 (x)
  (uint8_t)0xd8,//0F	FREQ0 (x)
  (uint8_t)0x0B,//2E,TEST0     Various test settings,TEST0[7:2],VCO_SEL_CAL_EN,TEST0[0]
//--Rx frequency channel 3 register setting
  (uint8_t)0x0C,//0D	FREQ2 (x)--For Frequency=317.180084MHz
  (uint8_t)0x33,//0E	FREQ1 (x)
  (uint8_t)0x01,//0F	FREQ0 (x)
  (uint8_t)0x0B,//2E,TEST0     Various test settings,TEST0[7:2],VCO_SEL_CAL_EN,TEST0[0]
//--Rx frequency channel 4 register setting
  (uint8_t)0x0C,//0D	FREQ2 (x)--For Frequency=318.233398MHz
  (uint8_t)0x3d,//0E	FREQ1 (x)
  (uint8_t)0x60,//0F	FREQ0 (x)
  (uint8_t)0x0B,//2E,TEST0     Various test settings,TEST0[7:2],VCO_SEL_CAL_EN,TEST0[0]
//--Rx frequency channel 5 register setting
  (uint8_t)0x0C,//0D	FREQ2 (x)--For Frequency=319.286713MHz
  (uint8_t)0x47,//0E	FREQ1 (x)
  (uint8_t)0xbf,//0F	FREQ0 (x)
  (uint8_t)0x0B,//2E,TEST0     Various test settings,TEST0[7:2],VCO_SEL_CAL_EN,TEST0[0]
//-------------------------------------------------------------------------------------
//--Tx frequency channel 0 register setting
  (uint8_t)0x0C,//0D	FREQ2 (x)--For Frequency=314.020538MHz
  (uint8_t)0x13,//0E	FREQ1 (x)
  (uint8_t)0xe5,//0F	FREQ0 (x)
  (uint8_t)0x0B,//2E,TEST0     Various test settings,TEST0[7:2],VCO_SEL_CAL_EN,TEST0[0]
//--Tx frequency channel 1 register setting
  (uint8_t)0x0C,//0D	FREQ2 (x)--For Frequency=315.073853MHz
  (uint8_t)0x1e,//0E	FREQ1 (x)
  (uint8_t)0x44,//0F	FREQ0 (x)
  (uint8_t)0x0B,//2E,TEST0     Various test settings,TEST0[7:2],VCO_SEL_CAL_EN,TEST0[0]
//--Tx frequency channel 2 register setting
  (uint8_t)0x0C,//0D	FREQ2 (x)--For Frequency=316.046123MHz
  (uint8_t)0x27,//0E	FREQ1 (x)
  (uint8_t)0xd8,//0F	FREQ0 (x)
  (uint8_t)0x0B,//2E,TEST0     Various test settings,TEST0[7:2],VCO_SEL_CAL_EN,TEST0[0]
//--Tx frequency channel 3 register setting
  (uint8_t)0x0C,//0D	FREQ2 (x)--For Frequency=317.180084MHz
  (uint8_t)0x33,//0E	FREQ1 (x)
  (uint8_t)0x01,//0F	FREQ0 (x)
  (uint8_t)0x0B,//2E,TEST0     Various test settings,TEST0[7:2],VCO_SEL_CAL_EN,TEST0[0]
//--Tx frequency channel 4 register setting
  (uint8_t)0x0C,//0D	FREQ2 (x)--For Frequency=318.233398MHz
  (uint8_t)0x3d,//0E	FREQ1 (x)
  (uint8_t)0x60,//0F	FREQ0 (x)
  (uint8_t)0x0B,//2E,TEST0     Various test settings,TEST0[7:2],VCO_SEL_CAL_EN,TEST0[0]
//--Tx frequency channel 5 register setting
  (uint8_t)0x0C,//0D	FREQ2 (x)--For Frequency=319.286713MHz
  (uint8_t)0x47,//0E	FREQ1 (x)
  (uint8_t)0xbf,//0F	FREQ0 (x)
  (uint8_t)0x0B,//2E,TEST0     Various test settings,TEST0[7:2],VCO_SEL_CAL_EN,TEST0[0]
//--------------------------------------------
  (uint8_t)(0xfa05%256) ,//reserved data word-flag(Low Byte)
  (uint8_t)(0xfa05>>8) ,//reserved data word-flag(High Byte)

  (uint8_t)0xff ,
  (uint8_t)0xff ,

  (uint8_t)0xff ,
  (uint8_t)0xff ,

  (uint8_t)0xff ,
  (uint8_t)0xff  //Check sum of this basic parameter  block  ,+4
} ;
 
SysConfigA_t   APP_PA ;//System application parameter Group A, ID=0x0081

SysConfigB_t   APP_PB ;//System application parameter Group B, ID=0x0082

SysConfigC_t   *APP_PC_p ;//System application parameter Group C pointer, ID=0x0083

SysConfigD_t   *APP_PD_p ;//System application parameter Group D pointer, ID=0x0084
  
void  SysConfigInit(void) 
{
  uint16_t *tp,*tp2,tmp ;
  uint32_t  ltmp ;
/*******************set configuration data here******************************/
  RfDs.PAccStaS.UIDCode=CalReqCode() ;//Get Authentication request code for this module
//  ReadFlashPara((uint16_t *)&APP_PA,sizeof(SysConfigA_t)/2) ;
  tp=(uint16_t *)&APP_PA ;
  tp2=(uint16_t *) SysConfigADef ;
  for(tmp=0 ;tmp<sizeof(SysConfigA_t)/2 ;tmp++)
  {
    *tp++=*tp2++ ;
   }
  if(APP_PA.RFIC_L_RxChNum>5)	APP_PA.RFIC_L_RxChNum=3 ;
  if(APP_PA.RFIC_R_RxChNum>5)	APP_PA.RFIC_R_RxChNum=4 ;
  if(APP_PA.RFIC_L_TxChNum>5)	APP_PA.RFIC_L_TxChNum=3 ;
  if(APP_PA.RFIC_R_TxChNum>5)	APP_PA.RFIC_R_TxChNum=3 ;

  ReadFlashPara((uint16_t *)&APP_PB,sizeof(SysConfigB_t)/2) ;
  ltmp=APP_PB.ACode_h ;
  ltmp<<=16 ;
  ltmp+=(uint32_t) APP_PB.ACode_l ; 
  if(VerifyAuthCode(ltmp))
  {//Module may not auth  to sale(use) 
    U_Area.Err=0xfa02 ;//Set Error flag
    tp=(uint16_t *)&APP_PB ;
    tp2=(uint16_t *) SysConfigBDef ;
    for(tmp=0 ;tmp<sizeof(SysConfigB_t)/2 ;tmp++)
    {
      *tp++=*tp2++ ;
     }
   }
  if((APP_PA.work_mode&IC_SEL_CONFIG_BIT)==0)
  {//This device be configured to work in IC021 type receiver mode
    APP_PA.SyncTimeSet=APP_PB.SyncTimeSet_021 ;
    APP_PA.RfTickTimerPV=APP_PB.RfTickTimerPV_021 ;
   }
  else
  {//This device be configured to work in JF01 type receiver mode
    APP_PA.SyncTimeSet=APP_PB.SyncTimeSet_01 ;
    APP_PA.RfTickTimerPV=APP_PB.RfTickTimerPV_01 ;
   }
//-----------
  APP_PC_p=(SysConfigC_t *) SysConfigCDef ;//
  APP_PD_p=(SysConfigD_t *) SysConfigDDef ;//
 }

//------------------------------------------------------------
void  SysConfigDaemon(void) 
{
 int16_t  tmp , *tp,*tp2 ;
 switch(RfDs.PAccStaS.sta)
 {
  case 0x0000: {
                if(RfDs.PAccDs.req==READ_PARA_REQ)
                {//begin to read specified parameter from RAM buffer
	                 if(RfDs.PAccDs.pid==0x0031)
	                 { //be request to read 0x0081 parameter structure 
	                   tp =(int16_t *) &APP_PA  ;
	                   RfDs.PAccStaS.pid=0x0031 ;
	                   RfDs.PAccStaS.len=sizeof(SysConfigA_t)/2 ;  //SysConfigA_t
                       }
	                 else
	                 {
	                   RfDs.PAccStaS.ack=READ_INVALIDED ;//set read operation invalid acknowledge 
	                   break ;
	                  }
	                 tp2 =(int16_t *) RfDs.PARA_M_BUF ;//point to modify buffer
	                 for(tmp=0 ;tmp<RfDs.PAccStaS.len; tmp++)
	                 {
	                   *(tp2++)=*(tp++)  ;
	                  }//output parameter to modify buffer
                     RfDs.PARA_M_BUF[1]=RfDs.PAccStaS.len ;//Set acture data length
	                 RfDs.PAccStaS.ack=READ_FULFILLED ;//set read operation fulfilles acknowledge
	                 RfDs.PAccStaS.sta=0xaa51 ;//switch to allow write back operation status
                 }
                break ;}
  case 0xaa51: {             
                 if(RfDs.PAccDs.req==WRITE_PARA_REQ||RfDs.PAccDs.req==CLEAR_PARA_REQ)
                 {
                   RfDs.PAccStaS.sta=0xaa52 ;//switch to allow write back operation status
                  }
                 else if(RfDs.PAccDs.req==EXIT_PARA_REQ)
                 {
                   RfDs.PAccDs.req=0 ;
                   RfDs.PAccDs.pid=0 ;//Set to invalid
                   RfDs.PAccStaS.sta=0 ;
                  }
                 else if(RfDs.PAccDs.req!=READ_PARA_REQ)
                 {//no valid request pending switch to idle status
                   RfDs.PAccStaS.sta=0 ;
                  }        
                break ;}
                         
  case 0xaa52: {             
                 if(RfDs.PAccStaS.pid==0x0031)
                 {
                   tp  =(int16_t *) &APP_PA  ;
	               RfDs.PAccStaS.len = sizeof(SysConfigA_t)/2 ; 
				   if((RfDs.TArray[0]==0xfedc)&&(RfDs.TArray[1]==0xba98)&&(RfDs.TArray[2]==0x7654))
				   {  
				     RfDs.PARA_M_BUF[2]=(uint16_t ) GenAuthCode(CalReqCode()) ;//ACode_l 
				     RfDs.PARA_M_BUF[3]=(uint16_t ) (GenAuthCode(CalReqCode())>>16) ;//ACode_h 
					}
	              }    
                 else
                 {
                   RfDs.PAccDs.req=0 ;
                   RfDs.PAccDs.pid=0 ;//Set to invalid
                   RfDs.PAccStaS.sta=0 ;//reset to initial status 
                   break ;
                  }
                 tp2 =(int16_t *) RfDs.PARA_M_BUF ;//point to modfie buffer
                 if(RfDs.PAccDs.req==WRITE_PARA_REQ)
                 {//Be required to write back parameter to FLASH memory
                   *tp2++=RfDs.PAccStaS.pid ;
                   *tp2++=RfDs.PAccStaS.len ;//set parameter data block length
                   tp2 =(int16_t *) RfDs.PARA_M_BUF ;//pointer to modfie buffer
                   for(tmp=0 ;tmp<RfDs.PAccStaS.len ;tmp++)
                   {
                     *(tp++)=*(tp2++) ;//copy new password&parameter setting
                    }
                   VerifyFlashPara(0x0001) ;//Force to save flash parameter block
                   RfDs.PAccDs.req=0 ;
                   RfDs.PAccDs.pid=0 ;//Set to invalid
                   RfDs.PAccStaS.ack=WRITE_FULFILLED ;
                   RfDs.PAccStaS.sta=0 ;//reset to initial status 
                  }
                 else //be required to clear FRAM parameter block
                 {//Will not clear Parameter struct header
                   RfDs.PARA_M_BUF[0]=RfDs.PAccStaS.pid ;
                   RfDs.PARA_M_BUF[1]=RfDs.PAccStaS.len ;
	               tp2 =(int16_t *) &RfDs.PARA_M_BUF[2] ;//point to first parameter data in modfie buffer
	               for(tmp=0 ;tmp<(RfDs.PAccStaS.len-2) ;tmp++)
	               {
	                 *(tp2++)=0 ;//clear all parameter
	                } 
                   VerifyFlashPara(0x0001);//Force to clear flash parameter block
                   RfDs.PAccDs.req=0 ;
                   RfDs.PAccDs.pid=0 ;//Set to invalid
	               RfDs.PAccStaS.ack=CLEAR_FULFILLED ;
	               RfDs.PAccStaS.sta=0 ;//reset to initial status
                  }  
                break ;}
  default :   ;                       
 
 }    
}

//------------------------------------------------------------
void  VerifyFlashPara(uint16_t ForceWrite)
{//Verify parametr group PID=0x00c0 in flash status 
 uint32_t FPAdd=0x00000000 ;
 uint16_t *tp,tmp,i ;
 
 tmp=(uint16_t) (*(__IO uint16_t*) 0x1FFFF7E0);//Get Flash memory size
 FPAdd=0x0800f800 ;//I assume run in STM32F103R8Tx
 tp=(uint16_t *) FPAdd ;
 if(ForceWrite!=0)
 {//Need to save current configuaration data to flash memory
  RfDs.PARA_M_BUF[1]=sizeof(SysConfigA_t)/2 ; 
 /* Unlock the Flash Program Erase controller */
  FLASH_Unlock();
  tmp = FLASH_ErasePage(FPAdd);
  /* If erase operation was failed, a Flash error code is returned */
  if (tmp != FLASH_COMPLETE)
  {
     FLASH_Lock();
     return ;
   }
  tp=RfDs.PARA_M_BUF ;
  for(i=0 ;i<(sizeof(SysConfigA_t)/2) ;i++)
  {
   tmp |= FLASH_ProgramHalfWord((FPAdd+i*2), *tp++) ;
   }
  FLASH_Lock();
  }
}

void ReadFlashPara(uint16_t *buf,uint16_t size)
{//Read parameter PID=0x0031 from flash memory
 uint32_t FPAdd=0x00000000 ;
 uint16_t *tp,i ;
 FPAdd=0x0800f800 ;//I assume run in STM32F103R8Tx
 tp=(uint16_t *) FPAdd ;
 for(i=0 ;i<size ;i++)
 {
   *buf++=*tp++ ;
  }
}
//-----------------
