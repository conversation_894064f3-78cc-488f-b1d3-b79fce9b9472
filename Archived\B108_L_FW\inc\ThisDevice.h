#ifndef __THIS_DEVICE_H
#define __THIS_DEVICE_H

#include "stm32l1xx.h"
#include "stdlib.h"
#include "stdio.h"
#include "string.h"
#include "math.h"
#include "stm32l1xx_conf.h"
#include "SysBasic.h"
#include "SysConfig.h"
#include "Display.h"
//----------------------------
/*** Constant Definitions ***/
//#define  TIME_5mS_V     3705   //delay const @CPU=12.00MHz(HSI)
//#define  TIME_2mS_V     1480   //
//#define  TIME_800uS_V   590    //
//#define  TIME_200uS_V   148    //
//#define  TIME_50uS_V    36     //
//#define  TIME_40uS_V    29     //
//#define  TIME_20uS_V    13     //
//--
//#define  TIME_8_2mS_V   4010   //delay const @CPU=8.00MHz(HSE)
//#define  TIME_5mS_V     2470   //
//#define  TIME_2mS_V     980    //
//#define  TIME_1_5mS_V   738    //
//#define  TIME_800uS_V   395    //
//#define  TIME_200uS_V   98     //
//#define  TIME_50uS_V    23     //
//#define  TIME_40uS_V    18     //delay const @CPU=8.00MHz(HSE)
//#define  TIME_20uS_V    8      //
//--
#define  TIME_5mS_V     1240   //delay const @CPU=4.194MHz(MSI)
#define  TIME_1_5mS_V   380    //
#define  TIME_800uS_V   204    //
#define  TIME_200uS_V   50     //Actual value about 208uS
#define  TIME_50uS_V    12     //
#define  TIME_40uS_V    9      //delay const @CPU=4.194MHz(MSI)
#define  TIME_20uS_V    3      //Actual value about 17.2uS
//---------

//#define  USE_OLD_FRAME  1   //Use old 38107 panel control data frame structure in shearer use

//---------
#define  MAX_DIS_ITEM              20    //Define max menu number for display var1,2
#define  MAX_MACH_D_ITEM           20    //Define max menu number for display machine data var1,2

//---------
#define  RF_BUF_LEN_MAX   10	//Define maxim buffer length for normal RF Tx/Rx operation
#define  DEFAULT_POOL     2     //Set default pool number when system turned on
#define  KEY_ON_TPOOL     1     //Set default Token pool for key keeped on

//-----------------------------------------------------------------------------------

#pragma  anon_unions

//---------
//define a big structure for application data
typedef struct TagUserAreaStruct
{
//-----------------MD Address 1-16 (used in FYS30 mode for wired machine data monitor)
  union{
         struct{
                 PANEL_DAT  PaDat_L ;//received valid data from left side wireless transmitter
                 int16_t     RSSI_L ;//used to store RF part RSSI in dBm
                } ;
           uint16_t  PanelArea_L[4] ;//MD1-4
        };
//--------------
  union{
         struct{
                 PANEL_DAT  PaDat_R ;//received valid data from right side  wireless transmitter
                 int16_t     RSSI_R ;//used to store RF part RSSI in dBm
                } ;
           uint16_t  PanelArea_R[4] ;//MD5-8
        } ;
  //--------------------------------MD Address 9-16
  //following two union must be  allocated series in memory for proper output to Host------------
  union 
  {
    TPDOStruct       RF_TPDO ;//MD 9-12,Machine receiver output PDO
    uint16_t    RF_PDODat[4] ;
   } ;
  union 
  {
    TPDO2Struct      OPS_TPDO ;//
    uint16_t    OPS_PDODat[4] ;////MD=13-16,Local connected OPS data,build to PDO from
   } ;
//-----------------MD Address 17-32 (used in FYS30 mode for wired machine data monitor)
  union {
          OPS_TX_DAT      OpsDat_L ;//Data will sent to host machine when act as left wired OPS
          uint16_t    OpsArea_L[4] ;//MD17-20
         } ;
  union {
          OPS_TX_DAT      OpsDat_R ;//Data will sent to host machine when act as right wired OPS
          uint16_t    OpsArea_R[4] ;//MD21-24
         } ;
  //--------------------------------MD Address 25-28
  union {
          TX_TO_OPS_DAT      OpsMachDat ;//Any side OPS received valid data from machine station
          uint16_t       OpsMachArea[4] ;//MD25-28 --use in FYS30 OPS function
         } ;
  //--------------------------------MD Address 29-36
  union {
          TX_TO_OPS_DAT      ToOpsDat_L ;//The data frame in machine station will be output to left side OPS
          uint16_t    Data2OpsArea_L[4] ;//MD29-32 --use in FYS30 OPS function
         } ;
  union {
          TX_TO_OPS_DAT      ToOpsDat_R ;//The data frame in machine station will be output to right side OPS
          uint16_t    Data2OpsArea_R[4] ;//MD33-36 --use in FYS30 OPS function
         } ;
//---------------------------------------------------------------------------------------------------
  uint16_t     RevArray1[4] ;//MD37-40
//---------------------------------------------------------------------
  uint16_t   DisplayDatBuf[32] ;//MD41-72 ,used to store current 32 monitor data
//---------------------------------------------------------------------------------------------------
  uint16_t   RevArray2[10] ;//MD73-82 
  uint16_t    SweepTimeMax ;//MD83,
  uint16_t      SweepTimeC ;//MD84,
  uint16_t     BootCounter ;//MD85,
//------------------------
  uint16_t     WiredComSta ;//MD86,used to record wired com port error,=1 OK ,=0 Connection error or not used (data invalid)
    		                //bit0 - Left side OPS, bit1 -right side OPS
			                //bit2 - N.A., bit3 - N.A.
			                //bit4 - N.A., bit5 - N.A.
			                //bit7 - External receiver station communication OK
			                //bit8 - Can Dev 0(Main HOST) ,bit9-Can dev1 ,bit10-can dev2 ,bit11-can dev3
			                //bit12 - Can Dev 4 ,bit13-Can dev5 ,bit14-can dev6 ,bit15-can dev7
  uint16_t	     KeyComb ;//MD87,combined operation key code for wireless and local input
  uint16_t        DatReq ;//MD88,used to record display request from  any side wireless transmitter
  uint16_t      DatReq_L ;//MD89,used to record display request from left side panel
  uint16_t      DatReq_R ;//MD90------------
  uint16_t     DatReq_CP ;//MD91,Current Output data pointer for one display request
  uint16_t      DatGID_C ;//MD92,Current display data GID
  uint16_t       WcrStaW ;//MD93,
  uint16_t      MachStaW ;//MD94,Received Current machine status word
  uint16_t     Counter10ms ;//MD95
  uint16_t      HW_Version ;//MD96,Device Hardware version version number
  uint16_t      SW_Version ;//MD97,Firmware version number
  uint16_t             Err ;//MD98,Module run error 
  uint16_t       TestCtrlW ;//MD99,Used to control to do RF test
  union {
          uint16_t  TestCommandW ;//MD100,Used to control to do RF test
          uint16_t   AreaFlag100 ;//MD100,used set flag for debug
         } ;
//----------------MD Address 101-132 -------------------------------------------
  PanSetting_t   PSet  ;//MD101 ,Current saved panel setting word(used in wireless control transmitter mode)
  PanStatus_t    PSta  ;//MD102 ,Current saved panel status word(used in wireless control transmitter mode)
  KeyGroup     KeyCode ;//MD103 ,store onboard operation key code(1 word)
  uint16_t     rev_w10 ;//MD104 ,Display ID for key operation comment
//--
  KeyGroup     KeyCodeRaw     ;//MD105 ,store raw onboard operation key code(1 word )
  KeyGroup     RisingKeyRaw   ;//MD106 ,record raw value the rising edge of a key
  KeyGroup     FallingKeyRaw  ;//MD107 ,record raw value the falling edge of a key
  uint16_t     KeyDisID_1st ;//MD108 ,1st operating Key ID for display
  uint16_t     KeyDisID_1st_pre ;//MD109 ,previous 1st operating Key ID for display
  uint16_t     KeyDisID_2nd ;//MD110 ,2nd operating Key ID for display
  uint16_t     KeyDisID_2nd_pre ;//MD111 ,previous 2nd operating Key ID for display

  uint16_t   MachStaChange ;//MD112 ,store feedback command code(1 word)
  uint16_t   FB_CmdRising  ;//MD113 ,record the rising edge of feedback command
  uint16_t   AlarmStatus ;//MD114 ,Current alarm status var

  uint16_t     RevArray4[9]  ;//MD115-123 
//---------------------------------------------------------------------------------------------------
  int16_t      BatVoltage ;//MD124 ,used to store battery voltage in mV
  int16_t         SysTemp ;//MD125 ,used to store current system environment temperature in celsius degree
  int16_t          rev_w1 ;//MD126 ,reserved
  int16_t         ADC_CNT ;//MD127 ,ADC convertion number counter
//---------------------------------------------------------------------------------------------------
  LCDStruct    LcdCtrl        ;//MD128-137, (20 bytes,10 words)used to store LCD operation control or state data
  UNI_PRINT    u_pcb          ;//MD138-143, (12 bytes,6 words),universal print control block for fixed type(ROM string, ANSI ,unicode string)or RAM string display
  uint16_t     uc_str[16][12] ;//MD144-335, 16x12=192 words ,unicode string code to be display,A0-A9 use
  uint8_t      a_str[8][16]   ;//MD336-399, 8x16 bytes=64 words ,ANSCII string for display,A10-A15 use
//----------------------------------------
  uint16_t     AreaFlag400 ;//MD400 , used to set flag for debug
  union{
         PANEL_TX_DAT       PanelDat_RB0 ;//Receive 0 buffer for wireless panel--in WCR mode 
         PANEL_OLD_TX_DAT   PanelOldDat_RB0 ;//Receive 0 buffer for wireless panel--in WCR compatible(legacy) mode 
           SHR_TX_DAT  ShrMachDat_RB0 ;//Receive buffer 0 for shearer machine receiver--in wireless control transmitter mode
		   TUN_TX_DAT  TunMachDat_RB0 ;//Receive buffer 0 for tunneller machine receiver-- in wireless control transmitter mode
		    uint16_t   RfRxBuf0[RF_BUF_LEN_MAX] ;//MD401-410,Universal RF Receive buffer 0
		     uint8_t   RfRxChBuf0[RF_BUF_LEN_MAX*2] ;//MD401-410,Universal RF Receive buffer 0--access in char mode
		 } ;
  union{
         PANEL_TX_DAT       PanelDat_RB1 ;//Receive 1 buffer for wireless panel--in WCR mode 
         PANEL_OLD_TX_DAT   PanelOldDat_RB1 ;//Receive 1 buffer for wireless panel--in WCR compatible(legacy) mode 
           SHR_TX_DAT  ShrMachDat_RB1 ;//Receive buffer 1 for shearer machine receiver--in wireless control transmitter mode
		   TUN_TX_DAT  TunMachDat_RB1 ;//Receive buffer 1 for tunneller machine receiver-- in wireless control transmitter mode 
		     uint16_t  RfRxBuf1[RF_BUF_LEN_MAX] ;//MD411-420,Universal RF Receive buffer 1
		      uint8_t  RfRxChBuf1[RF_BUF_LEN_MAX*2] ;//MD411-420,Universal RF Receive buffer 1--access in char mode
	    } ;
//------------------------------
  union{
         PANEL_TX_DAT      PanelDat_TB0 ;//Transmitter buffer 0 for wireless panel(in wireless control transmitter mode )
         PANEL_OLD_TX_DAT  PanelOldDat_TB0 ;//Transmitter buffer 0 for wireless panel(in wireless control transmitter compatible(legacy) mode )
         SHR_TX_DAT  ShrMachDat_TB0 ;//Transmitter buffer 0 for shearer machine receiver( in WCR mode	)
		   TUN_TX_DAT  TunMachDat_TB0 ;//Transmitter buffer 0 for tunneller machine receiver( in WCR mode ) 
		     uint16_t     RfTxBuf0[RF_BUF_LEN_MAX] ;//MD421-430,Universal RF Transmitter buffer 0
		     uint8_t  RfTxChBuf0[RF_BUF_LEN_MAX*2] ;//MD421-430,Universal RF Transmitter buffer 0--access in char mode
		 } ;
//----
  union{
         PANEL_TX_DAT       PanelDat ;//panel data that will be transmitted(in wireless control transmitter mode )
         PANEL_OLD_TX_DAT   PanelOldDat ;//Compatible(legacy) panel data that will be transmitted(in wireless control transmitter mode )
  	     SHR_TX_DAT       ShrMachDat ;//Received shearer machine data--in panel mode ,or dat that will copy to ShrMachDat_TB0 to transmitte--in WCR mode
		 TUN_TX_DAT       TunMachDat ;//Received  tunneller  machine data--in panel mode 
	     uint16_t        RfDatAsm[RF_BUF_LEN_MAX] ;//MD431-440,Universal RF(Rx/Tx) panel or machine data assemble area
	    } ;
//---------------------------------------------------------------------------------------------------
  uint16_t     AreaFlag441 ;//MD441 , used to set flag for debug
  uint16_t   Ultra_BUF[64] ;//MD442-505
//---------------------------------------------------------------------------------------------------
  uint16_t      RevArray6[5] ;//MD506-510 
  uint16_t      CANOpenID ;//MD=511
  uint16_t      TPDO_Ctrl ;//MD=512
  uint16_t  SYNC_Interval ;//MD=5133,Store CANopen SYNC interval time in ms(default=5ms)
  uint16_t   PDO_Interval ;//MD=514
  uint16_t      NodeIDSim ;//MD=515
  uint16_t   CAN_TCounter ;//MD=516
  //--------------
  CANComStatistics   CAN_Sta ;//MD517-525Used for CAN bus status recorder ,+9
  ComModOpStruct     U2ModOp ;//MD526-531,Uart2 Modbus RTU Master operation recorder object, +6
} UserAreaStruct ;

//------------------------------------------------------------------------------

typedef struct TagSysAreaStruct
{ //total  bytes
 volatile  int16_t  SysActWaitTime ;//Change_able shutoff delay time setting
 volatile  int16_t   BLShutOffTime ;//Change_able LCD black light automatic shutoff delay time setting
 volatile  uint16_t         TPool_p ;//Pointer for current token pool,will be reset to zero by Key event
 volatile  int16_t      RemainToken ;//recorder remain token in current token pool

 volatile  int16_t        RFTickCount ;//used to count down for generate next token when rech to zero
 volatile  int16_t  ActTimeOutMonitor ;
 volatile  int16_t        SyncTimeOut ;
 volatile  int16_t       LCD_BL_Timer ;

 volatile  int16_t  RFConfirmErrorCounter ;//Counting all not confirmed RF TX operation(error)
 volatile  int16_t  LedFlashInterval ;//Current use LED flash interval in RF ticks
 volatile  int16_t   LedFlashCounter ;//Counter used to control LED flash
 volatile  int16_t   LedFunExchTimer ;//used to set led indicator function exchange enduring time 

 uint16_t      RunSetting ;//This device runtime setting
 KeyGroup       PreKeyRaw ;//used to store previous raw operation key
 KeyGroup         KeyMask ;//store mask of the keyboard
 KeyGroup     DeadKeyMask ;//store deak key that found during power on keyboard check process

 uint16_t     PreAlarmSta ;//record the previous alarm data
 uint16_t     AlarmChange ;//indicate which alarm bit be changed
 uint16_t  REFINT_RawResult ;//ADC output internal reference voltage raw value
 uint16_t      TS_RawResult ;//ADC output internal temprature sensor raw value

 uint16_t    PreMachSta ;//used to store  previous feedback machine status words
 uint16_t    PreFB_CmdCode ;//used to store previous feedback operation command code
 uint16_t    PreMachSpeed ;//used to store previous machine speed setting

 uint16_t  CODEPAGE_BUF[16] ;//buffer can be used for unicode string to do codepage converter
 uint16_t   SwallowArea[64] ;//used for output useless data
 
} SysAreaStruct ;

/* Extern variables ----------------------------------------------------------*/
extern  UserAreaStruct  U_Area ;//Extern defined an object for user data

extern  SysAreaStruct   S_Area ;//Extern defined an object for system data --in file ".h"
//-------------------------------------------------------------
void  SystemReset(void) ;

void  InitTimer3_1uS(void) ;//Init TIM for CPU sweep measure

__inline void CpuSweepTime(uint16_t *MaxTp,uint16_t *CurTp) ;//Get the sweep time info

#endif // __THIS_DEVICE_H
/******************* END OF FILE****/
