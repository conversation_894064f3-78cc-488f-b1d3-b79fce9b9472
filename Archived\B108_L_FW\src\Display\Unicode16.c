/*O A 8 16 16 8  CP<Unicode16.cp>  Unicode16.h   LCDIcon FileDescriptor: Do not edit or move */
/* Put Your Comments Here */
#include "Display.h"

const sFont16x16 Unicode16[61]={
{{2, 16},{
0xFF,    /*  @@@@@@@@  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x00,    /*  ........  */

0xFF,    /*  @@@@@@@@  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x00     /*  ........  */
}}
,
{{9, 16},{
0x00,    /*  ........  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0xFE,    /*  @@@@@@@.  */
0x04,    /*  .....@..  */
0x08,    /*  ....@...  */
0x10,    /*  ...@....  */
0xE0,    /*  @@@.....  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x00,    /*  ........  */

0x00,    /*  ........  */
0x38,    /*  ..@@@...  */
0x3C,    /*  ..@@@@..  */
0x1F,    /*  ...@@@@@  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x02,    /*  ......@.  */
0x01,    /*  .......@  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x00     /*  ........  */
}}
,
{{9, 16},{
0x00,    /*  ........  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0xF8,    /*  @@@@@...  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x00,    /*  ........  */

0x00,    /*  ........  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x33,    /*  ..@@..@@  */
0x30,    /*  ..@@....  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x00     /*  ........  */
}}
,
{{16, 16},{
0x40,    /*  .@......  */
0x40,    /*  .@......  */
0xC0,    /*  @@......  */
0xC0,    /*  @@......  */
0xC0,    /*  @@......  */
0xE0,    /*  @@@.....  */
0xFC,    /*  @@@@@@..  */
0xFF,    /*  @@@@@@@@  */
0xFC,    /*  @@@@@@..  */
0xE0,    /*  @@@.....  */
0xC0,    /*  @@......  */
0xC0,    /*  @@......  */
0xC0,    /*  @@......  */
0x40,    /*  .@......  */
0x40,    /*  .@......  */
0x00,    /*  ........  */

0x00,    /*  ........  */
0x00,    /*  ........  */
0x40,    /*  .@......  */
0x39,    /*  ..@@@..@  */
0x3F,    /*  ..@@@@@@  */
0x1F,    /*  ...@@@@@  */
0x1F,    /*  ...@@@@@  */
0x0F,    /*  ....@@@@  */
0x1F,    /*  ...@@@@@  */
0x1F,    /*  ...@@@@@  */
0x3F,    /*  ..@@@@@@  */
0x39,    /*  ..@@@..@  */
0x40,    /*  .@......  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x00     /*  ........  */
}}
,
{{16, 16},{
0x00,    /*  ........  */
0x00,    /*  ........  */
0x80,    /*  @.......  */
0x40,    /*  .@......  */
0x30,    /*  ..@@....  */
0x0C,    /*  ....@@..  */
0x0B,    /*  ....@.@@  */
0x08,    /*  ....@...  */
0xC8,    /*  @@..@...  */
0xBC,    /*  @.@@@@..  */
0x08,    /*  ....@...  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x00,    /*  ........  */

0x40,    /*  .@......  */
0x41,    /*  .@.....@  */
0x20,    /*  ..@.....  */
0x20,    /*  ..@.....  */
0x10,    /*  ...@....  */
0x08,    /*  ....@...  */
0x04,    /*  .....@..  */
0x03,    /*  ......@@  */
0x00,    /*  ........  */
0x01,    /*  .......@  */
0x06,    /*  .....@@.  */
0x18,    /*  ...@@...  */
0x30,    /*  ..@@....  */
0x60,    /*  .@@.....  */
0x20,    /*  ..@.....  */
0x00     /*  ........  */
}}
,
{{16, 16},{
0x80,    /*  @.......  */
0x40,    /*  .@......  */
0x20,    /*  ..@.....  */
0xF8,    /*  @@@@@...  */
0x07,    /*  .....@@@  */
0x22,    /*  ..@...@.  */
0x18,    /*  ...@@...  */
0x0C,    /*  ....@@..  */
0xFB,    /*  @@@@@.@@  */
0x48,    /*  .@..@...  */
0x48,    /*  .@..@...  */
0x48,    /*  .@..@...  */
0x68,    /*  .@@.@...  */
0x48,    /*  .@..@...  */
0x08,    /*  ....@...  */
0x00,    /*  ........  */

0x00,    /*  ........  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0xFF,    /*  @@@@@@@@  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0xFF,    /*  @@@@@@@@  */
0x04,    /*  .....@..  */
0x04,    /*  .....@..  */
0x04,    /*  .....@..  */
0x04,    /*  .....@..  */
0x06,    /*  .....@@.  */
0x04,    /*  .....@..  */
0x00     /*  ........  */
}}
,
{{16, 16},{
0x40,    /*  .@......  */
0x20,    /*  ..@.....  */
0xF0,    /*  @@@@....  */
0x1C,    /*  ...@@@..  */
0x07,    /*  .....@@@  */
0xF2,    /*  @@@@..@.  */
0x94,    /*  @..@.@..  */
0x94,    /*  @..@.@..  */
0x94,    /*  @..@.@..  */
0xFF,    /*  @@@@@@@@  */
0x94,    /*  @..@.@..  */
0x94,    /*  @..@.@..  */
0x94,    /*  @..@.@..  */
0xF4,    /*  @@@@.@..  */
0x04,    /*  .....@..  */
0x00,    /*  ........  */

0x00,    /*  ........  */
0x00,    /*  ........  */
0x7F,    /*  .@@@@@@@  */
0x00,    /*  ........  */
0x40,    /*  .@......  */
0x41,    /*  .@.....@  */
0x22,    /*  ..@...@.  */
0x14,    /*  ...@.@..  */
0x0C,    /*  ....@@..  */
0x13,    /*  ...@..@@  */
0x10,    /*  ...@....  */
0x30,    /*  ..@@....  */
0x20,    /*  ..@.....  */
0x61,    /*  .@@....@  */
0x20,    /*  ..@.....  */
0x00     /*  ........  */
}}
,
{{16, 16},{
0x00,    /*  ........  */
0x00,    /*  ........  */
0x80,    /*  @.......  */
0x40,    /*  .@......  */
0x30,    /*  ..@@....  */
0x0E,    /*  ....@@@.  */
0x84,    /*  @....@..  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x0E,    /*  ....@@@.  */
0x10,    /*  ...@....  */
0x60,    /*  .@@.....  */
0xC0,    /*  @@......  */
0x80,    /*  @.......  */
0x80,    /*  @.......  */
0x00,    /*  ........  */

0x00,    /*  ........  */
0x01,    /*  .......@  */
0x20,    /*  ..@.....  */
0x70,    /*  .@@@....  */
0x28,    /*  ..@.@...  */
0x24,    /*  ..@..@..  */
0x23,    /*  ..@...@@  */
0x31,    /*  ..@@...@  */
0x10,    /*  ...@....  */
0x10,    /*  ...@....  */
0x14,    /*  ...@.@..  */
0x78,    /*  .@@@@...  */
0x30,    /*  ..@@....  */
0x01,    /*  .......@  */
0x00,    /*  ........  */
0x00     /*  ........  */
}}
,
{{16, 16},{
0x00,    /*  ........  */
0x10,    /*  ...@....  */
0x10,    /*  ...@....  */
0x10,    /*  ...@....  */
0x11,    /*  ...@...@  */
0x1E,    /*  ...@@@@.  */
0x14,    /*  ...@.@..  */
0xF0,    /*  @@@@....  */
0x10,    /*  ...@....  */
0x18,    /*  ...@@...  */
0x17,    /*  ...@.@@@  */
0x12,    /*  ...@..@.  */
0x18,    /*  ...@@...  */
0x10,    /*  ...@....  */
0x00,    /*  ........  */
0x00,    /*  ........  */

0x01,    /*  .......@  */
0x81,    /*  @......@  */
0x41,    /*  .@.....@  */
0x21,    /*  ..@....@  */
0x11,    /*  ...@...@  */
0x09,    /*  ....@..@  */
0x05,    /*  .....@.@  */
0x03,    /*  ......@@  */
0x05,    /*  .....@.@  */
0x09,    /*  ....@..@  */
0x31,    /*  ..@@...@  */
0x61,    /*  .@@....@  */
0xC1,    /*  @@.....@  */
0x41,    /*  .@.....@  */
0x01,    /*  .......@  */
0x00     /*  ........  */
}}
,
{{16, 16},{
0x00,    /*  ........  */
0x50,    /*  .@.@....  */
0x4F,    /*  .@..@@@@  */
0x4A,    /*  .@..@.@.  */
0x48,    /*  .@..@...  */
0xFF,    /*  @@@@@@@@  */
0x48,    /*  .@..@...  */
0x48,    /*  .@..@...  */
0x48,    /*  .@..@...  */
0x00,    /*  ........  */
0xFC,    /*  @@@@@@..  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0xFF,    /*  @@@@@@@@  */
0x00,    /*  ........  */
0x00,    /*  ........  */

0x00,    /*  ........  */
0x00,    /*  ........  */
0x3F,    /*  ..@@@@@@  */
0x01,    /*  .......@  */
0x01,    /*  .......@  */
0xFF,    /*  @@@@@@@@  */
0x21,    /*  ..@....@  */
0x61,    /*  .@@....@  */
0x3F,    /*  ..@@@@@@  */
0x00,    /*  ........  */
0x0F,    /*  ....@@@@  */
0x40,    /*  .@......  */
0x80,    /*  @.......  */
0x7F,    /*  .@@@@@@@  */
0x00,    /*  ........  */
0x00     /*  ........  */
}}
,
{{16, 16},{
0x20,    /*  ..@.....  */
0x24,    /*  ..@..@..  */
0x24,    /*  ..@..@..  */
0xE4,    /*  @@@..@..  */
0x24,    /*  ..@..@..  */
0x24,    /*  ..@..@..  */
0x24,    /*  ..@..@..  */
0x20,    /*  ..@.....  */
0x10,    /*  ...@....  */
0x10,    /*  ...@....  */
0xFF,    /*  @@@@@@@@  */
0x10,    /*  ...@....  */
0x10,    /*  ...@....  */
0xF0,    /*  @@@@....  */
0x00,    /*  ........  */
0x00,    /*  ........  */

0x08,    /*  ....@...  */
0x1C,    /*  ...@@@..  */
0x0B,    /*  ....@.@@  */
0x08,    /*  ....@...  */
0x0C,    /*  ....@@..  */
0x05,    /*  .....@.@  */
0x4E,    /*  .@..@@@.  */
0x24,    /*  ..@..@..  */
0x10,    /*  ...@....  */
0x0C,    /*  ....@@..  */
0x03,    /*  ......@@  */
0x20,    /*  ..@.....  */
0x40,    /*  .@......  */
0x3F,    /*  ..@@@@@@  */
0x00,    /*  ........  */
0x00     /*  ........  */
}}
,
{{16, 16},{
0x00,    /*  ........  */
0x00,    /*  ........  */
0xFE,    /*  @@@@@@@.  */
0x92,    /*  @..@..@.  */
0x92,    /*  @..@..@.  */
0x92,    /*  @..@..@.  */
0xFE,    /*  @@@@@@@.  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0xFE,    /*  @@@@@@@.  */
0x02,    /*  ......@.  */
0x02,    /*  ......@.  */
0x02,    /*  ......@.  */
0xFE,    /*  @@@@@@@.  */
0x00,    /*  ........  */
0x00,    /*  ........  */

0x00,    /*  ........  */
0x10,    /*  ...@....  */
0x3F,    /*  ..@@@@@@  */
0x10,    /*  ...@....  */
0x10,    /*  ...@....  */
0x0A,    /*  ....@.@.  */
0x1D,    /*  ...@@@.@  */
0x08,    /*  ....@...  */
0x00,    /*  ........  */
0xFF,    /*  @@@@@@@@  */
0x00,    /*  ........  */
0x04,    /*  .....@..  */
0x08,    /*  ....@...  */
0x07,    /*  .....@@@  */
0x00,    /*  ........  */
0x00     /*  ........  */
}}
,
{{16, 16},{
0x00,    /*  ........  */
0x10,    /*  ...@....  */
0x3E,    /*  ..@@@@@.  */
0x10,    /*  ...@....  */
0x10,    /*  ...@....  */
0xF0,    /*  @@@@....  */
0x9F,    /*  @..@@@@@  */
0x90,    /*  @..@....  */
0x90,    /*  @..@....  */
0x92,    /*  @..@..@.  */
0x94,    /*  @..@.@..  */
0x1C,    /*  ...@@@..  */
0x10,    /*  ...@....  */
0x10,    /*  ...@....  */
0x10,    /*  ...@....  */
0x00,    /*  ........  */

0x40,    /*  .@......  */
0x20,    /*  ..@.....  */
0x10,    /*  ...@....  */
0x88,    /*  @...@...  */
0x87,    /*  @....@@@  */
0x41,    /*  .@.....@  */
0x46,    /*  .@...@@.  */
0x28,    /*  ..@.@...  */
0x10,    /*  ...@....  */
0x28,    /*  ..@.@...  */
0x27,    /*  ..@..@@@  */
0x40,    /*  .@......  */
0xC0,    /*  @@......  */
0x40,    /*  .@......  */
0x00,    /*  ........  */
0x00     /*  ........  */
}}
,
{{16, 16},{
0x00,    /*  ........  */
0x02,    /*  ......@.  */
0x02,    /*  ......@.  */
0xF2,    /*  @@@@..@.  */
0x12,    /*  ...@..@.  */
0x12,    /*  ...@..@.  */
0x12,    /*  ...@..@.  */
0x12,    /*  ...@..@.  */
0xF2,    /*  @@@@..@.  */
0x02,    /*  ......@.  */
0x02,    /*  ......@.  */
0x02,    /*  ......@.  */
0xFE,    /*  @@@@@@@.  */
0x02,    /*  ......@.  */
0x02,    /*  ......@.  */
0x00,    /*  ........  */

0x00,    /*  ........  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x07,    /*  .....@@@  */
0x02,    /*  ......@.  */
0x02,    /*  ......@.  */
0x02,    /*  ......@.  */
0x02,    /*  ......@.  */
0x07,    /*  .....@@@  */
0x10,    /*  ...@....  */
0x20,    /*  ..@.....  */
0x40,    /*  .@......  */
0x3F,    /*  ..@@@@@@  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x00     /*  ........  */
}}
,
{{16, 16},{
0x00,    /*  ........  */
0x10,    /*  ...@....  */
0x92,    /*  @..@..@.  */
0x92,    /*  @..@..@.  */
0x92,    /*  @..@..@.  */
0x92,    /*  @..@..@.  */
0x92,    /*  @..@..@.  */
0x92,    /*  @..@..@.  */
0x92,    /*  @..@..@.  */
0x92,    /*  @..@..@.  */
0x12,    /*  ...@..@.  */
0x02,    /*  ......@.  */
0x02,    /*  ......@.  */
0xFE,    /*  @@@@@@@.  */
0x00,    /*  ........  */
0x00,    /*  ........  */

0x00,    /*  ........  */
0x00,    /*  ........  */
0x1F,    /*  ...@@@@@  */
0x04,    /*  .....@..  */
0x04,    /*  .....@..  */
0x04,    /*  .....@..  */
0x04,    /*  .....@..  */
0x04,    /*  .....@..  */
0x04,    /*  .....@..  */
0x0F,    /*  ....@@@@  */
0x00,    /*  ........  */
0x20,    /*  ..@.....  */
0x40,    /*  .@......  */
0x3F,    /*  ..@@@@@@  */
0x00,    /*  ........  */
0x00     /*  ........  */
}}
,
{{16, 16},{
0x40,    /*  .@......  */
0x40,    /*  .@......  */
0x4F,    /*  .@..@@@@  */
0x49,    /*  .@..@..@  */
0x49,    /*  .@..@..@  */
0xC9,    /*  @@..@..@  */
0xCF,    /*  @@..@@@@  */
0x70,    /*  .@@@....  */
0xC0,    /*  @@......  */
0xCF,    /*  @@..@@@@  */
0x49,    /*  .@..@..@  */
0x59,    /*  .@.@@..@  */
0x69,    /*  .@@.@..@  */
0x4F,    /*  .@..@@@@  */
0x00,    /*  ........  */
0x00,    /*  ........  */

0x02,    /*  ......@.  */
0x02,    /*  ......@.  */
0x7E,    /*  .@@@@@@.  */
0x45,    /*  .@...@.@  */
0x45,    /*  .@...@.@  */
0x44,    /*  .@...@..  */
0x7C,    /*  .@@@@@..  */
0x00,    /*  ........  */
0x7C,    /*  .@@@@@..  */
0x44,    /*  .@...@..  */
0x45,    /*  .@...@.@  */
0x45,    /*  .@...@.@  */
0x7E,    /*  .@@@@@@.  */
0x06,    /*  .....@@.  */
0x02,    /*  ......@.  */
0x00     /*  ........  */
}}
,
{{16, 16},{
0x00,    /*  ........  */
0xFE,    /*  @@@@@@@.  */
0x02,    /*  ......@.  */
0x12,    /*  ...@..@.  */
0x92,    /*  @..@..@.  */
0x92,    /*  @..@..@.  */
0x92,    /*  @..@..@.  */
0xFE,    /*  @@@@@@@.  */
0x92,    /*  @..@..@.  */
0x92,    /*  @..@..@.  */
0x92,    /*  @..@..@.  */
0x12,    /*  ...@..@.  */
0x02,    /*  ......@.  */
0xFE,    /*  @@@@@@@.  */
0x00,    /*  ........  */
0x00,    /*  ........  */

0x00,    /*  ........  */
0xFF,    /*  @@@@@@@@  */
0x40,    /*  .@......  */
0x40,    /*  .@......  */
0x5F,    /*  .@.@@@@@  */
0x48,    /*  .@..@...  */
0x48,    /*  .@..@...  */
0x48,    /*  .@..@...  */
0x48,    /*  .@..@...  */
0x48,    /*  .@..@...  */
0x5F,    /*  .@.@@@@@  */
0x40,    /*  .@......  */
0x40,    /*  .@......  */
0xFF,    /*  @@@@@@@@  */
0x00,    /*  ........  */
0x00     /*  ........  */
}}
,
{{16, 16},{
0x00,    /*  ........  */
0x10,    /*  ...@....  */
0x0C,    /*  ....@@..  */
0xA4,    /*  @.@..@..  */
0x24,    /*  ..@..@..  */
0x24,    /*  ..@..@..  */
0x25,    /*  ..@..@.@  */
0xE6,    /*  @@@..@@.  */
0x24,    /*  ..@..@..  */
0x24,    /*  ..@..@..  */
0x24,    /*  ..@..@..  */
0x24,    /*  ..@..@..  */
0x14,    /*  ...@.@..  */
0x0C,    /*  ....@@..  */
0x04,    /*  .....@..  */
0x00,    /*  ........  */

0x40,    /*  .@......  */
0x20,    /*  ..@.....  */
0x18,    /*  ...@@...  */
0x07,    /*  .....@@@  */
0x08,    /*  ....@...  */
0x10,    /*  ...@....  */
0x20,    /*  ..@.....  */
0x7F,    /*  .@@@@@@@  */
0x42,    /*  .@....@.  */
0x42,    /*  .@....@.  */
0x42,    /*  .@....@.  */
0x42,    /*  .@....@.  */
0x40,    /*  .@......  */
0x40,    /*  .@......  */
0x00,    /*  ........  */
0x00     /*  ........  */
}}
,
{{16, 16},{
0x00,    /*  ........  */
0x00,    /*  ........  */
0xFC,    /*  @@@@@@..  */
0x56,    /*  .@.@.@@.  */
0x55,    /*  .@.@.@.@  */
0x54,    /*  .@.@.@..  */
0x54,    /*  .@.@.@..  */
0xFC,    /*  @@@@@@..  */
0x10,    /*  ...@....  */
0x90,    /*  @..@....  */
0x10,    /*  ...@....  */
0x10,    /*  ...@....  */
0xFF,    /*  @@@@@@@@  */
0x10,    /*  ...@....  */
0x10,    /*  ...@....  */
0x00,    /*  ........  */

0x00,    /*  ........  */
0x42,    /*  .@....@.  */
0x23,    /*  ..@...@@  */
0x12,    /*  ...@..@.  */
0x0A,    /*  ....@.@.  */
0x46,    /*  .@...@@.  */
0x82,    /*  @.....@.  */
0x7F,    /*  .@@@@@@@  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x47,    /*  .@...@@@  */
0x80,    /*  @.......  */
0x7F,    /*  .@@@@@@@  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x00     /*  ........  */
}}
,
{{16, 16},{
0x08,    /*  ....@...  */
0x30,    /*  ..@@....  */
0x10,    /*  ...@....  */
0x00,    /*  ........  */
0xFF,    /*  @@@@@@@@  */
0x10,    /*  ...@....  */
0x88,    /*  @...@...  */
0x8C,    /*  @...@@..  */
0x53,    /*  .@.@..@@  */
0x42,    /*  .@....@.  */
0x22,    /*  ..@...@.  */
0x12,    /*  ...@..@.  */
0xCE,    /*  @@..@@@.  */
0x02,    /*  ......@.  */
0x00,    /*  ........  */
0x00,    /*  ........  */

0x02,    /*  ......@.  */
0x06,    /*  .....@@.  */
0x02,    /*  ......@.  */
0x01,    /*  .......@  */
0xFF,    /*  @@@@@@@@  */
0x01,    /*  .......@  */
0x01,    /*  .......@  */
0x05,    /*  .....@.@  */
0x39,    /*  ..@@@..@  */
0x11,    /*  ...@...@  */
0x41,    /*  .@.....@  */
0x81,    /*  @......@  */
0x7F,    /*  .@@@@@@@  */
0x01,    /*  .......@  */
0x01,    /*  .......@  */
0x00     /*  ........  */
}}
,
{{16, 16},{
0x00,    /*  ........  */
0x00,    /*  ........  */
0xE0,    /*  @@@.....  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0xFF,    /*  @@@@@@@@  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0xE0,    /*  @@@.....  */
0x00,    /*  ........  */
0x00,    /*  ........  */

0x00,    /*  ........  */
0x20,    /*  ..@.....  */
0x7F,    /*  .@@@@@@@  */
0x20,    /*  ..@.....  */
0x20,    /*  ..@.....  */
0x20,    /*  ..@.....  */
0x20,    /*  ..@.....  */
0x3F,    /*  ..@@@@@@  */
0x20,    /*  ..@.....  */
0x20,    /*  ..@.....  */
0x20,    /*  ..@.....  */
0x20,    /*  ..@.....  */
0x20,    /*  ..@.....  */
0x7F,    /*  .@@@@@@@  */
0x00,    /*  ........  */
0x00     /*  ........  */
}}
,
{{16, 16},{
0x00,    /*  ........  */
0x04,    /*  .....@..  */
0x04,    /*  .....@..  */
0x04,    /*  .....@..  */
0x84,    /*  @....@..  */
0x44,    /*  .@...@..  */
0x34,    /*  ..@@.@..  */
0x4F,    /*  .@..@@@@  */
0x94,    /*  @..@.@..  */
0x24,    /*  ..@..@..  */
0x44,    /*  .@...@..  */
0x84,    /*  @....@..  */
0x84,    /*  @....@..  */
0x04,    /*  .....@..  */
0x00,    /*  ........  */
0x00,    /*  ........  */

0x00,    /*  ........  */
0x60,    /*  .@@.....  */
0x39,    /*  ..@@@..@  */
0x01,    /*  .......@  */
0x00,    /*  ........  */
0x3C,    /*  ..@@@@..  */
0x40,    /*  .@......  */
0x42,    /*  .@....@.  */
0x4C,    /*  .@..@@..  */
0x40,    /*  .@......  */
0x40,    /*  .@......  */
0x70,    /*  .@@@....  */
0x04,    /*  .....@..  */
0x09,    /*  ....@..@  */
0x31,    /*  ..@@...@  */
0x00     /*  ........  */
}}
,
{{16, 16},{
0x08,    /*  ....@...  */
0x08,    /*  ....@...  */
0xFF,    /*  @@@@@@@@  */
0x88,    /*  @...@...  */
0x48,    /*  .@..@...  */
0x22,    /*  ..@...@.  */
0xAA,    /*  @.@.@.@.  */
0xAA,    /*  @.@.@.@.  */
0xAA,    /*  @.@.@.@.  */
0xFF,    /*  @@@@@@@@  */
0xAA,    /*  @.@.@.@.  */
0xAA,    /*  @.@.@.@.  */
0xFA,    /*  @@@@@.@.  */
0x22,    /*  ..@...@.  */
0x22,    /*  ..@...@.  */
0x00,    /*  ........  */

0x42,    /*  .@....@.  */
0x81,    /*  @......@  */
0x7F,    /*  .@@@@@@@  */
0x00,    /*  ........  */
0x80,    /*  @.......  */
0x60,    /*  .@@.....  */
0x1C,    /*  ...@@@..  */
0x10,    /*  ...@....  */
0x20,    /*  ..@.....  */
0x3F,    /*  ..@@@@@@  */
0x44,    /*  .@...@..  */
0x44,    /*  .@...@..  */
0x44,    /*  .@...@..  */
0x44,    /*  .@...@..  */
0x40,    /*  .@......  */
0x00     /*  ........  */
}}
,
{{16, 16},{
0x08,    /*  ....@...  */
0x88,    /*  @...@...  */
0xFF,    /*  @@@@@@@@  */
0x48,    /*  .@..@...  */
0x28,    /*  ..@.@...  */
0xFF,    /*  @@@@@@@@  */
0x09,    /*  ....@..@  */
0xC9,    /*  @@..@..@  */
0x09,    /*  ....@..@  */
0x09,    /*  ....@..@  */
0xE9,    /*  @@@.@..@  */
0x09,    /*  ....@..@  */
0x09,    /*  ....@..@  */
0xCF,    /*  @@..@@@@  */
0x00,    /*  ........  */
0x00,    /*  ........  */

0x41,    /*  .@.....@  */
0x80,    /*  @.......  */
0x7F,    /*  .@@@@@@@  */
0x40,    /*  .@......  */
0x30,    /*  ..@@....  */
0x0F,    /*  ....@@@@  */
0x7C,    /*  .@@@@@..  */
0x21,    /*  ..@....@  */
0x21,    /*  ..@....@  */
0x21,    /*  ..@....@  */
0x3F,    /*  ..@@@@@@  */
0x21,    /*  ..@....@  */
0x21,    /*  ..@....@  */
0x21,    /*  ..@....@  */
0x7C,    /*  .@@@@@..  */
0x00     /*  ........  */
}}
,
{{16, 16},{
0x08,    /*  ....@...  */
0x08,    /*  ....@...  */
0x08,    /*  ....@...  */
0xFF,    /*  @@@@@@@@  */
0x88,    /*  @...@...  */
0x68,    /*  .@@.@...  */
0x24,    /*  ..@..@..  */
0x2C,    /*  ..@.@@..  */
0xB4,    /*  @.@@.@..  */
0x25,    /*  ..@..@.@  */
0x26,    /*  ..@..@@.  */
0x34,    /*  ..@@.@..  */
0x2C,    /*  ..@.@@..  */
0x24,    /*  ..@..@..  */
0x20,    /*  ..@.....  */
0x00,    /*  ........  */

0x02,    /*  ......@.  */
0x42,    /*  .@....@.  */
0x81,    /*  @......@  */
0x7F,    /*  .@@@@@@@  */
0x02,    /*  ......@.  */
0x82,    /*  @.....@.  */
0x8A,    /*  @...@.@.  */
0x4E,    /*  .@..@@@.  */
0x53,    /*  .@.@..@@  */
0x32,    /*  ..@@..@.  */
0x12,    /*  ...@..@.  */
0x2E,    /*  ..@.@@@.  */
0x42,    /*  .@....@.  */
0xC2,    /*  @@....@.  */
0x02,    /*  ......@.  */
0x00     /*  ........  */
}}
,
{{16, 16},{
0x08,    /*  ....@...  */
0x08,    /*  ....@...  */
0x08,    /*  ....@...  */
0xFF,    /*  @@@@@@@@  */
0x88,    /*  @...@...  */
0x48,    /*  .@..@...  */
0x00,    /*  ........  */
0x98,    /*  @..@@...  */
0x48,    /*  .@..@...  */
0x28,    /*  ..@.@...  */
0x0A,    /*  ....@.@.  */
0x2C,    /*  ..@.@@..  */
0x48,    /*  .@..@...  */
0xD8,    /*  @@.@@...  */
0x08,    /*  ....@...  */
0x00,    /*  ........  */

0x02,    /*  ......@.  */
0x42,    /*  .@....@.  */
0x81,    /*  @......@  */
0x7F,    /*  .@@@@@@@  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x40,    /*  .@......  */
0x42,    /*  .@....@.  */
0x42,    /*  .@....@.  */
0x42,    /*  .@....@.  */
0x7E,    /*  .@@@@@@.  */
0x42,    /*  .@....@.  */
0x42,    /*  .@....@.  */
0x42,    /*  .@....@.  */
0x40,    /*  .@......  */
0x00     /*  ........  */
}}
,
{{16, 16},{
0x08,    /*  ....@...  */
0x08,    /*  ....@...  */
0xFF,    /*  @@@@@@@@  */
0x88,    /*  @...@...  */
0x48,    /*  .@..@...  */
0x00,    /*  ........  */
0xF0,    /*  @@@@....  */
0x97,    /*  @..@.@@@  */
0xF5,    /*  @@@@.@.@  */
0x05,    /*  .....@.@  */
0xF5,    /*  @@@@.@.@  */
0x95,    /*  @..@.@.@  */
0x97,    /*  @..@.@@@  */
0xF0,    /*  @@@@....  */
0x00,    /*  ........  */
0x00,    /*  ........  */

0x42,    /*  .@....@.  */
0x81,    /*  @......@  */
0x7F,    /*  .@@@@@@@  */
0x00,    /*  ........  */
0x40,    /*  .@......  */
0x42,    /*  .@....@.  */
0x22,    /*  ..@...@.  */
0x12,    /*  ...@..@.  */
0x0A,    /*  ....@.@.  */
0xFF,    /*  @@@@@@@@  */
0x06,    /*  .....@@.  */
0x0A,    /*  ....@.@.  */
0x32,    /*  ..@@..@.  */
0x62,    /*  .@@...@.  */
0x22,    /*  ..@...@.  */
0x00     /*  ........  */
}}
,
{{16, 16},{
0x00,    /*  ........  */
0xF8,    /*  @@@@@...  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0xFF,    /*  @@@@@@@@  */
0x00,    /*  ........  */
0x40,    /*  .@......  */
0x30,    /*  ..@@....  */
0xDF,    /*  @@.@@@@@  */
0x10,    /*  ...@....  */
0x10,    /*  ...@....  */
0x10,    /*  ...@....  */
0xF0,    /*  @@@@....  */
0x10,    /*  ...@....  */
0x10,    /*  ...@....  */
0x00,    /*  ........  */

0x00,    /*  ........  */
0x0F,    /*  ....@@@@  */
0x04,    /*  .....@..  */
0x02,    /*  ......@.  */
0xFF,    /*  @@@@@@@@  */
0x40,    /*  .@......  */
0x40,    /*  .@......  */
0x20,    /*  ..@.....  */
0x21,    /*  ..@....@  */
0x16,    /*  ...@.@@.  */
0x08,    /*  ....@...  */
0x16,    /*  ...@.@@.  */
0x21,    /*  ..@....@  */
0x60,    /*  .@@.....  */
0x20,    /*  ..@.....  */
0x00     /*  ........  */
}}
,
{{16, 16},{
0x00,    /*  ........  */
0x40,    /*  .@......  */
0x42,    /*  .@....@.  */
0x42,    /*  .@....@.  */
0x42,    /*  .@....@.  */
0x42,    /*  .@....@.  */
0x42,    /*  .@....@.  */
0xFE,    /*  @@@@@@@.  */
0xC2,    /*  @@....@.  */
0x42,    /*  .@....@.  */
0x42,    /*  .@....@.  */
0x42,    /*  .@....@.  */
0x42,    /*  .@....@.  */
0x42,    /*  .@....@.  */
0x40,    /*  .@......  */
0x00,    /*  ........  */

0x40,    /*  .@......  */
0x40,    /*  .@......  */
0x20,    /*  ..@.....  */
0x10,    /*  ...@....  */
0x08,    /*  ....@...  */
0x04,    /*  .....@..  */
0x03,    /*  ......@@  */
0x00,    /*  ........  */
0x3F,    /*  ..@@@@@@  */
0x40,    /*  .@......  */
0x40,    /*  .@......  */
0x40,    /*  .@......  */
0x40,    /*  .@......  */
0x40,    /*  .@......  */
0x70,    /*  .@@@....  */
0x00     /*  ........  */
}}
,
{{16, 16},{
0x00,    /*  ........  */
0x00,    /*  ........  */
0xC0,    /*  @@......  */
0x3E,    /*  ..@@@@@.  */
0x2A,    /*  ..@.@.@.  */
0x2A,    /*  ..@.@.@.  */
0x2A,    /*  ..@.@.@.  */
0x2A,    /*  ..@.@.@.  */
0x2A,    /*  ..@.@.@.  */
0xEA,    /*  @@@.@.@.  */
0x2A,    /*  ..@.@.@.  */
0x3E,    /*  ..@@@@@.  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x00,    /*  ........  */

0x00,    /*  ........  */
0x00,    /*  ........  */
0x7F,    /*  .@@@@@@@  */
0x22,    /*  ..@...@.  */
0x22,    /*  ..@...@.  */
0x12,    /*  ...@..@.  */
0x12,    /*  ...@..@.  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x3F,    /*  ..@@@@@@  */
0x44,    /*  .@...@..  */
0x44,    /*  .@...@..  */
0x42,    /*  .@....@.  */
0x43,    /*  .@....@@  */
0x70,    /*  .@@@....  */
0x00     /*  ........  */
}}
,
{{16, 16},{
0x00,    /*  ........  */
0x04,    /*  .....@..  */
0x84,    /*  @....@..  */
0x44,    /*  .@...@..  */
0xE4,    /*  @@@..@..  */
0x34,    /*  ..@@.@..  */
0x2C,    /*  ..@.@@..  */
0x27,    /*  ..@..@@@  */
0x24,    /*  ..@..@..  */
0x24,    /*  ..@..@..  */
0x24,    /*  ..@..@..  */
0xE4,    /*  @@@..@..  */
0x04,    /*  .....@..  */
0x04,    /*  .....@..  */
0x04,    /*  .....@..  */
0x00,    /*  ........  */

0x02,    /*  ......@.  */
0x01,    /*  .......@  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0xFF,    /*  @@@@@@@@  */
0x09,    /*  ....@..@  */
0x09,    /*  ....@..@  */
0x09,    /*  ....@..@  */
0x29,    /*  ..@.@..@  */
0x49,    /*  .@..@..@  */
0xC9,    /*  @@..@..@  */
0x7F,    /*  .@@@@@@@  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x00     /*  ........  */
}}
,
{{16, 16},{
0x08,    /*  ....@...  */
0x08,    /*  ....@...  */
0xC8,    /*  @@..@...  */
0xFF,    /*  @@@@@@@@  */
0x48,    /*  .@..@...  */
0x88,    /*  @...@...  */
0x08,    /*  ....@...  */
0x00,    /*  ........  */
0xFE,    /*  @@@@@@@.  */
0x02,    /*  ......@.  */
0x02,    /*  ......@.  */
0x02,    /*  ......@.  */
0xFE,    /*  @@@@@@@.  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x00,    /*  ........  */

0x04,    /*  .....@..  */
0x03,    /*  ......@@  */
0x00,    /*  ........  */
0xFF,    /*  @@@@@@@@  */
0x00,    /*  ........  */
0x41,    /*  .@.....@  */
0x30,    /*  ..@@....  */
0x0C,    /*  ....@@..  */
0x03,    /*  ......@@  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x3F,    /*  ..@@@@@@  */
0x40,    /*  .@......  */
0x78,    /*  .@@@@...  */
0x00     /*  ........  */
}}
,
{{16, 16},{
0x08,    /*  ....@...  */
0x08,    /*  ....@...  */
0xE8,    /*  @@@.@...  */
0xFF,    /*  @@@@@@@@  */
0x48,    /*  .@..@...  */
0x88,    /*  @...@...  */
0x40,    /*  .@......  */
0x42,    /*  .@....@.  */
0x42,    /*  .@....@.  */
0x42,    /*  .@....@.  */
0xFE,    /*  @@@@@@@.  */
0x42,    /*  .@....@.  */
0x42,    /*  .@....@.  */
0x42,    /*  .@....@.  */
0x40,    /*  .@......  */
0x00,    /*  ........  */

0x04,    /*  .....@..  */
0x03,    /*  ......@@  */
0x00,    /*  ........  */
0xFF,    /*  @@@@@@@@  */
0x00,    /*  ........  */
0x01,    /*  .......@  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0xFF,    /*  @@@@@@@@  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x00     /*  ........  */
}}
,
{{16, 16},{
0x14,    /*  ...@.@..  */
0x24,    /*  ..@..@..  */
0x44,    /*  .@...@..  */
0x84,    /*  @....@..  */
0x64,    /*  .@@..@..  */
0x1C,    /*  ...@@@..  */
0x20,    /*  ..@.....  */
0x18,    /*  ...@@...  */
0x0F,    /*  ....@@@@  */
0xE8,    /*  @@@.@...  */
0x08,    /*  ....@...  */
0x08,    /*  ....@...  */
0x28,    /*  ..@.@...  */
0x18,    /*  ...@@...  */
0x08,    /*  ....@...  */
0x00,    /*  ........  */

0x20,    /*  ..@.....  */
0x10,    /*  ...@....  */
0x4C,    /*  .@..@@..  */
0x43,    /*  .@....@@  */
0x43,    /*  .@....@@  */
0x2C,    /*  ..@.@@..  */
0x20,    /*  ..@.....  */
0x10,    /*  ...@....  */
0x0C,    /*  ....@@..  */
0x03,    /*  ......@@  */
0x06,    /*  .....@@.  */
0x18,    /*  ...@@...  */
0x30,    /*  ..@@....  */
0x60,    /*  .@@.....  */
0x20,    /*  ..@.....  */
0x00     /*  ........  */
}}
,
{{16, 16},{
0x00,    /*  ........  */
0x20,    /*  ..@.....  */
0x10,    /*  ...@....  */
0x8C,    /*  @...@@..  */
0xA7,    /*  @.@..@@@  */
0xA4,    /*  @.@..@..  */
0xA4,    /*  @.@..@..  */
0xA4,    /*  @.@..@..  */
0xA4,    /*  @.@..@..  */
0xA4,    /*  @.@..@..  */
0xA4,    /*  @.@..@..  */
0xA4,    /*  @.@..@..  */
0x24,    /*  ..@..@..  */
0x04,    /*  .....@..  */
0x04,    /*  .....@..  */
0x00,    /*  ........  */

0x00,    /*  ........  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x0F,    /*  ....@@@@  */
0x30,    /*  ..@@....  */
0x40,    /*  .@......  */
0xF0,    /*  @@@@....  */
0x00     /*  ........  */
}}
,
{{16, 16},{
0x00,    /*  ........  */
0x40,    /*  .@......  */
0x40,    /*  .@......  */
0x48,    /*  .@..@...  */
0x48,    /*  .@..@...  */
0xC8,    /*  @@..@...  */
0x09,    /*  ....@..@  */
0xFA,    /*  @@@@@.@.  */
0x46,    /*  .@...@@.  */
0x80,    /*  @.......  */
0x40,    /*  .@......  */
0x20,    /*  ..@.....  */
0x30,    /*  ..@@....  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x00,    /*  ........  */

0x20,    /*  ..@.....  */
0x20,    /*  ..@.....  */
0x10,    /*  ...@....  */
0x0C,    /*  ....@@..  */
0x03,    /*  ......@@  */
0x40,    /*  .@......  */
0x80,    /*  @.......  */
0x7F,    /*  .@@@@@@@  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x03,    /*  ......@@  */
0x04,    /*  .....@..  */
0x08,    /*  ....@...  */
0x10,    /*  ...@....  */
0x10,    /*  ...@....  */
0x00     /*  ........  */
}}
,
{{16, 16},{
0x10,    /*  ...@....  */
0x21,    /*  ..@....@  */
0x86,    /*  @....@@.  */
0x60,    /*  .@@.....  */
0x00,    /*  ........  */
0x0C,    /*  ....@@..  */
0xD4,    /*  @@.@.@..  */
0x54,    /*  .@.@.@..  */
0x55,    /*  .@.@.@.@  */
0xF6,    /*  @@@@.@@.  */
0x54,    /*  .@.@.@..  */
0x54,    /*  .@.@.@..  */
0xD4,    /*  @@.@.@..  */
0x0C,    /*  ....@@..  */
0x04,    /*  .....@..  */
0x00,    /*  ........  */

0x04,    /*  .....@..  */
0xFC,    /*  @@@@@@..  */
0x03,    /*  ......@@  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x8F,    /*  @...@@@@  */
0x69,    /*  .@@.@..@  */
0x09,    /*  ....@..@  */
0x0F,    /*  ....@@@@  */
0x09,    /*  ....@..@  */
0x29,    /*  ..@.@..@  */
0xCF,    /*  @@..@@@@  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x00     /*  ........  */
}}
,
{{16, 16},{
0x80,    /*  @.......  */
0x70,    /*  .@@@....  */
0x00,    /*  ........  */
0xFF,    /*  @@@@@@@@  */
0x10,    /*  ...@....  */
0x0C,    /*  ....@@..  */
0x04,    /*  .....@..  */
0x7F,    /*  .@@@@@@@  */
0x54,    /*  .@.@.@..  */
0xD4,    /*  @@.@.@..  */
0x54,    /*  .@.@.@..  */
0x7F,    /*  .@@@@@@@  */
0x04,    /*  .....@..  */
0x04,    /*  .....@..  */
0x04,    /*  .....@..  */
0x00,    /*  ........  */

0x40,    /*  .@......  */
0x30,    /*  ..@@....  */
0x4C,    /*  .@..@@..  */
0x43,    /*  .@....@@  */
0x2C,    /*  ..@.@@..  */
0x22,    /*  ..@...@.  */
0x12,    /*  ...@..@.  */
0x0A,    /*  ....@.@.  */
0x06,    /*  .....@@.  */
0xFF,    /*  @@@@@@@@  */
0x06,    /*  .....@@.  */
0x0A,    /*  ....@.@.  */
0x12,    /*  ...@..@.  */
0x22,    /*  ..@...@.  */
0x62,    /*  .@@...@.  */
0x00     /*  ........  */
}}
,
{{16, 16},{
0x08,    /*  ....@...  */
0x30,    /*  ..@@....  */
0x00,    /*  ........  */
0xFF,    /*  @@@@@@@@  */
0x20,    /*  ..@.....  */
0x20,    /*  ..@.....  */
0x20,    /*  ..@.....  */
0x20,    /*  ..@.....  */
0xFF,    /*  @@@@@@@@  */
0x20,    /*  ..@.....  */
0xE1,    /*  @@@....@  */
0x26,    /*  ..@..@@.  */
0x2C,    /*  ..@.@@..  */
0x20,    /*  ..@.....  */
0x20,    /*  ..@.....  */
0x00,    /*  ........  */

0x04,    /*  .....@..  */
0x02,    /*  ......@.  */
0x01,    /*  .......@  */
0xFF,    /*  @@@@@@@@  */
0x40,    /*  .@......  */
0x20,    /*  ..@.....  */
0x18,    /*  ...@@...  */
0x07,    /*  .....@@@  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x03,    /*  ......@@  */
0x0C,    /*  ....@@..  */
0x30,    /*  ..@@....  */
0x60,    /*  .@@.....  */
0x20,    /*  ..@.....  */
0x00     /*  ........  */
}}
,
{{16, 16},{
0x00,    /*  ........  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0xFE,    /*  @@@@@@@.  */
0x22,    /*  ..@...@.  */
0x22,    /*  ..@...@.  */
0x22,    /*  ..@...@.  */
0x22,    /*  ..@...@.  */
0xFE,    /*  @@@@@@@.  */
0x22,    /*  ..@...@.  */
0x22,    /*  ..@...@.  */
0x22,    /*  ..@...@.  */
0x22,    /*  ..@...@.  */
0xFE,    /*  @@@@@@@.  */
0x00,    /*  ........  */
0x00,    /*  ........  */

0x80,    /*  @.......  */
0x40,    /*  .@......  */
0x30,    /*  ..@@....  */
0x0F,    /*  ....@@@@  */
0x02,    /*  ......@.  */
0x02,    /*  ......@.  */
0x02,    /*  ......@.  */
0x02,    /*  ......@.  */
0xFF,    /*  @@@@@@@@  */
0x02,    /*  ......@.  */
0x02,    /*  ......@.  */
0x42,    /*  .@....@.  */
0x82,    /*  @.....@.  */
0x7F,    /*  .@@@@@@@  */
0x00,    /*  ........  */
0x00     /*  ........  */
}}
,
{{16, 16},{
0x00,    /*  ........  */
0x00,    /*  ........  */
0xF8,    /*  @@@@@...  */
0x48,    /*  .@..@...  */
0x48,    /*  .@..@...  */
0x48,    /*  .@..@...  */
0x48,    /*  .@..@...  */
0xFF,    /*  @@@@@@@@  */
0x48,    /*  .@..@...  */
0x48,    /*  .@..@...  */
0x48,    /*  .@..@...  */
0x48,    /*  .@..@...  */
0xF8,    /*  @@@@@...  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x00,    /*  ........  */

0x00,    /*  ........  */
0x00,    /*  ........  */
0x0F,    /*  ....@@@@  */
0x04,    /*  .....@..  */
0x04,    /*  .....@..  */
0x04,    /*  .....@..  */
0x04,    /*  .....@..  */
0x3F,    /*  ..@@@@@@  */
0x44,    /*  .@...@..  */
0x44,    /*  .@...@..  */
0x44,    /*  .@...@..  */
0x44,    /*  .@...@..  */
0x4F,    /*  .@..@@@@  */
0x40,    /*  .@......  */
0x70,    /*  .@@@....  */
0x00     /*  ........  */
}}
,
{{16, 16},{
0x20,    /*  ..@.....  */
0x20,    /*  ..@.....  */
0xA0,    /*  @.@.....  */
0x7C,    /*  .@@@@@..  */
0x24,    /*  ..@..@..  */
0x26,    /*  ..@..@@.  */
0x6D,    /*  .@@.@@.@  */
0xB4,    /*  @.@@.@..  */
0x24,    /*  ..@..@..  */
0x24,    /*  ..@..@..  */
0x24,    /*  ..@..@..  */
0xFC,    /*  @@@@@@..  */
0x20,    /*  ..@.....  */
0x20,    /*  ..@.....  */
0x20,    /*  ..@.....  */
0x00,    /*  ........  */

0x44,    /*  .@...@..  */
0x42,    /*  .@....@.  */
0x41,    /*  .@.....@  */
0x7C,    /*  .@@@@@..  */
0x44,    /*  .@...@..  */
0x44,    /*  .@...@..  */
0x7C,    /*  .@@@@@..  */
0x45,    /*  .@...@.@  */
0x44,    /*  .@...@..  */
0x7D,    /*  .@@@@@.@  */
0x46,    /*  .@...@@.  */
0x45,    /*  .@...@.@  */
0x7C,    /*  .@@@@@..  */
0x40,    /*  .@......  */
0x40,    /*  .@......  */
0x00     /*  ........  */
}}
,
{{16, 16},{
0x00,    /*  ........  */
0x20,    /*  ..@.....  */
0x20,    /*  ..@.....  */
0x22,    /*  ..@...@.  */
0x22,    /*  ..@...@.  */
0x22,    /*  ..@...@.  */
0x22,    /*  ..@...@.  */
0xE2,    /*  @@@...@.  */
0x22,    /*  ..@...@.  */
0x22,    /*  ..@...@.  */
0x22,    /*  ..@...@.  */
0x22,    /*  ..@...@.  */
0x22,    /*  ..@...@.  */
0x20,    /*  ..@.....  */
0x20,    /*  ..@.....  */
0x00,    /*  ........  */

0x10,    /*  ...@....  */
0x08,    /*  ....@...  */
0x04,    /*  .....@..  */
0x03,    /*  ......@@  */
0x00,    /*  ........  */
0x40,    /*  .@......  */
0x80,    /*  @.......  */
0x7F,    /*  .@@@@@@@  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x01,    /*  .......@  */
0x02,    /*  ......@.  */
0x0C,    /*  ....@@..  */
0x18,    /*  ...@@...  */
0x00,    /*  ........  */
0x00     /*  ........  */
}}
,
{{16, 16},{
0x40,    /*  .@......  */
0x60,    /*  .@@.....  */
0x58,    /*  .@.@@...  */
0xC7,    /*  @@...@@@  */
0x62,    /*  .@@...@.  */
0x00,    /*  ........  */
0x90,    /*  @..@....  */
0x90,    /*  @..@....  */
0x90,    /*  @..@....  */
0xFF,    /*  @@@@@@@@  */
0x90,    /*  @..@....  */
0x92,    /*  @..@..@.  */
0x9C,    /*  @..@@@..  */
0x94,    /*  @..@.@..  */
0x80,    /*  @.......  */
0x00,    /*  ........  */

0x20,    /*  ..@.....  */
0x22,    /*  ..@...@.  */
0x23,    /*  ..@...@@  */
0x12,    /*  ...@..@.  */
0x12,    /*  ...@..@.  */
0x12,    /*  ...@..@.  */
0x20,    /*  ..@.....  */
0x20,    /*  ..@.....  */
0x10,    /*  ...@....  */
0x13,    /*  ...@..@@  */
0x0C,    /*  ....@@..  */
0x14,    /*  ...@.@..  */
0x22,    /*  ..@...@.  */
0x40,    /*  .@......  */
0xF8,    /*  @@@@@...  */
0x00     /*  ........  */
}}
,
{{16, 16},{
0x20,    /*  ..@.....  */
0x30,    /*  ..@@....  */
0xA8,    /*  @.@.@...  */
0x67,    /*  .@@..@@@  */
0x12,    /*  ...@..@.  */
0x00,    /*  ........  */
0x90,    /*  @..@....  */
0x34,    /*  ..@@.@..  */
0xD4,    /*  @@.@.@..  */
0x1F,    /*  ...@@@@@  */
0xF4,    /*  @@@@.@..  */
0x14,    /*  ...@.@..  */
0x54,    /*  .@.@.@..  */
0x34,    /*  ..@@.@..  */
0x10,    /*  ...@....  */
0x00,    /*  ........  */

0x22,    /*  ..@...@.  */
0x23,    /*  ..@...@@  */
0x22,    /*  ..@...@.  */
0x12,    /*  ...@..@.  */
0x10,    /*  ...@....  */
0x02,    /*  ......@.  */
0x02,    /*  ......@.  */
0x43,    /*  .@....@@  */
0x22,    /*  ..@...@.  */
0x1A,    /*  ...@@.@.  */
0x07,    /*  .....@@@  */
0x0A,    /*  ....@.@.  */
0x12,    /*  ...@..@.  */
0x62,    /*  .@@...@.  */
0x02,    /*  ......@.  */
0x00     /*  ........  */
}}
,
{{16, 16},{
0x00,    /*  ........  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0xF8,    /*  @@@@@...  */
0x48,    /*  .@..@...  */
0x48,    /*  .@..@...  */
0x4C,    /*  .@..@@..  */
0x4B,    /*  .@..@.@@  */
0x4A,    /*  .@..@.@.  */
0x48,    /*  .@..@...  */
0x48,    /*  .@..@...  */
0x48,    /*  .@..@...  */
0xF8,    /*  @@@@@...  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x00,    /*  ........  */

0x00,    /*  ........  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0xFF,    /*  @@@@@@@@  */
0x44,    /*  .@...@..  */
0x44,    /*  .@...@..  */
0x44,    /*  .@...@..  */
0x44,    /*  .@...@..  */
0x44,    /*  .@...@..  */
0x44,    /*  .@...@..  */
0x44,    /*  .@...@..  */
0x44,    /*  .@...@..  */
0xFF,    /*  @@@@@@@@  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x00     /*  ........  */
}}
,
{{16, 16},{
0x08,    /*  ....@...  */
0xF4,    /*  @@@@.@..  */
0x57,    /*  .@.@.@@@  */
0x54,    /*  .@.@.@..  */
0xFC,    /*  @@@@@@..  */
0x54,    /*  .@.@.@..  */
0xF0,    /*  @@@@....  */
0x42,    /*  .@....@.  */
0xA2,    /*  @.@...@.  */
0x1E,    /*  ...@@@@.  */
0x02,    /*  ......@.  */
0xA2,    /*  @.@...@.  */
0x62,    /*  .@@...@.  */
0x3E,    /*  ..@@@@@.  */
0x00,    /*  ........  */
0x00,    /*  ........  */

0x80,    /*  @.......  */
0x7F,    /*  .@@@@@@@  */
0x02,    /*  ......@.  */
0x02,    /*  ......@.  */
0x5F,    /*  .@.@@@@@  */
0x82,    /*  @.....@.  */
0x7F,    /*  .@@@@@@@  */
0x0A,    /*  ....@.@.  */
0x09,    /*  ....@..@  */
0x09,    /*  ....@..@  */
0x09,    /*  ....@..@  */
0xFF,    /*  @@@@@@@@  */
0x09,    /*  ....@..@  */
0x09,    /*  ....@..@  */
0x09,    /*  ....@..@  */
0x00     /*  ........  */
}}
,
{{16, 16},{
0x40,    /*  .@......  */
0x41,    /*  .@.....@  */
0x4E,    /*  .@..@@@.  */
0xC4,    /*  @@...@..  */
0x00,    /*  ........  */
0x40,    /*  .@......  */
0x30,    /*  ..@@....  */
0x0F,    /*  ....@@@@  */
0x08,    /*  ....@...  */
0x08,    /*  ....@...  */
0xF8,    /*  @@@@@...  */
0x08,    /*  ....@...  */
0x08,    /*  ....@...  */
0x08,    /*  ....@...  */
0x00,    /*  ........  */
0x00,    /*  ........  */

0x00,    /*  ........  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x7F,    /*  .@@@@@@@  */
0x20,    /*  ..@.....  */
0x11,    /*  ...@...@  */
0x01,    /*  .......@  */
0x01,    /*  .......@  */
0x01,    /*  .......@  */
0x01,    /*  .......@  */
0xFF,    /*  @@@@@@@@  */
0x01,    /*  .......@  */
0x01,    /*  .......@  */
0x01,    /*  .......@  */
0x01,    /*  .......@  */
0x00     /*  ........  */
}}
,
{{16, 16},{
0x40,    /*  .@......  */
0x42,    /*  .@....@.  */
0xDC,    /*  @@.@@@..  */
0x08,    /*  ....@...  */
0x00,    /*  ........  */
0x04,    /*  .....@..  */
0xC4,    /*  @@...@..  */
0x04,    /*  .....@..  */
0x04,    /*  .....@..  */
0x04,    /*  .....@..  */
0xFC,    /*  @@@@@@..  */
0x04,    /*  .....@..  */
0x04,    /*  .....@..  */
0x04,    /*  .....@..  */
0x04,    /*  .....@..  */
0x00,    /*  ........  */

0x00,    /*  ........  */
0x00,    /*  ........  */
0x7F,    /*  .@@@@@@@  */
0x20,    /*  ..@.....  */
0x50,    /*  .@.@....  */
0x40,    /*  .@......  */
0x7F,    /*  .@@@@@@@  */
0x40,    /*  .@......  */
0x40,    /*  .@......  */
0x40,    /*  .@......  */
0x7F,    /*  .@@@@@@@  */
0x41,    /*  .@.....@  */
0x41,    /*  .@.....@  */
0x41,    /*  .@.....@  */
0x40,    /*  .@......  */
0x00     /*  ........  */
}}
,
{{16, 16},{
0x40,    /*  .@......  */
0x41,    /*  .@.....@  */
0xCE,    /*  @@..@@@.  */
0x04,    /*  .....@..  */
0x00,    /*  ........  */
0xFC,    /*  @@@@@@..  */
0x04,    /*  .....@..  */
0x02,    /*  ......@.  */
0x02,    /*  ......@.  */
0xFC,    /*  @@@@@@..  */
0x04,    /*  .....@..  */
0x04,    /*  .....@..  */
0x04,    /*  .....@..  */
0xFC,    /*  @@@@@@..  */
0x00,    /*  ........  */
0x00,    /*  ........  */

0x40,    /*  .@......  */
0x20,    /*  ..@.....  */
0x1F,    /*  ...@@@@@  */
0x20,    /*  ..@.....  */
0x40,    /*  .@......  */
0x47,    /*  .@...@@@  */
0x42,    /*  .@....@.  */
0x41,    /*  .@.....@  */
0x40,    /*  .@......  */
0x5F,    /*  .@.@@@@@  */
0x40,    /*  .@......  */
0x42,    /*  .@....@.  */
0x44,    /*  .@...@..  */
0x43,    /*  .@....@@  */
0x40,    /*  .@......  */
0x00     /*  ........  */
}}
,
{{16, 16},{
0x80,    /*  @.......  */
0x82,    /*  @.....@.  */
0x9C,    /*  @..@@@..  */
0x88,    /*  @...@...  */
0x00,    /*  ........  */
0x88,    /*  @...@...  */
0x88,    /*  @...@...  */
0xFF,    /*  @@@@@@@@  */
0x88,    /*  @...@...  */
0x88,    /*  @...@...  */
0x88,    /*  @...@...  */
0xFF,    /*  @@@@@@@@  */
0x88,    /*  @...@...  */
0x88,    /*  @...@...  */
0x80,    /*  @.......  */
0x00,    /*  ........  */

0x00,    /*  ........  */
0x40,    /*  .@......  */
0x20,    /*  ..@.....  */
0x1F,    /*  ...@@@@@  */
0x20,    /*  ..@.....  */
0x50,    /*  .@.@....  */
0x4C,    /*  .@..@@..  */
0x43,    /*  .@....@@  */
0x40,    /*  .@......  */
0x40,    /*  .@......  */
0x40,    /*  .@......  */
0x5F,    /*  .@.@@@@@  */
0x40,    /*  .@......  */
0x40,    /*  .@......  */
0x40,    /*  .@......  */
0x00     /*  ........  */
}}
,
{{16, 16},{
0x40,    /*  .@......  */
0x41,    /*  .@.....@  */
0x4E,    /*  .@..@@@.  */
0xC4,    /*  @@...@..  */
0x00,    /*  ........  */
0x44,    /*  .@...@..  */
0xE4,    /*  @@@..@..  */
0x5C,    /*  .@.@@@..  */
0x47,    /*  .@...@@@  */
0xF4,    /*  @@@@.@..  */
0x44,    /*  .@...@..  */
0x44,    /*  .@...@..  */
0x44,    /*  .@...@..  */
0x04,    /*  .....@..  */
0x00,    /*  ........  */
0x00,    /*  ........  */

0x00,    /*  ........  */
0x40,    /*  .@......  */
0x20,    /*  ..@.....  */
0x1F,    /*  ...@@@@@  */
0x22,    /*  ..@...@.  */
0x42,    /*  .@....@.  */
0x42,    /*  .@....@.  */
0x42,    /*  .@....@.  */
0x42,    /*  .@....@.  */
0x5F,    /*  .@.@@@@@  */
0x42,    /*  .@....@.  */
0x42,    /*  .@....@.  */
0x42,    /*  .@....@.  */
0x42,    /*  .@....@.  */
0x42,    /*  .@....@.  */
0x00     /*  ........  */
}}
,
{{16, 16},{
0x40,    /*  .@......  */
0x41,    /*  .@.....@  */
0xC6,    /*  @@...@@.  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0xF2,    /*  @@@@..@.  */
0x52,    /*  .@.@..@.  */
0x52,    /*  .@.@..@.  */
0x56,    /*  .@.@.@@.  */
0xFA,    /*  @@@@@.@.  */
0x5A,    /*  .@.@@.@.  */
0x56,    /*  .@.@.@@.  */
0xF2,    /*  @@@@..@.  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x00,    /*  ........  */

0x40,    /*  .@......  */
0x20,    /*  ..@.....  */
0x1F,    /*  ...@@@@@  */
0x20,    /*  ..@.....  */
0x40,    /*  .@......  */
0x5F,    /*  .@.@@@@@  */
0x42,    /*  .@....@.  */
0x42,    /*  .@....@.  */
0x42,    /*  .@....@.  */
0x5F,    /*  .@.@@@@@  */
0x4A,    /*  .@..@.@.  */
0x52,    /*  .@.@..@.  */
0x4F,    /*  .@..@@@@  */
0x40,    /*  .@......  */
0x40,    /*  .@......  */
0x00     /*  ........  */
}}
,
{{16, 16},{
0x40,    /*  .@......  */
0x42,    /*  .@....@.  */
0xCC,    /*  @@..@@..  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x4A,    /*  .@..@.@.  */
0x32,    /*  ..@@..@.  */
0x26,    /*  ..@..@@.  */
0x2A,    /*  ..@.@.@.  */
0xE1,    /*  @@@....@  */
0x31,    /*  ..@@...@  */
0x29,    /*  ..@.@..@  */
0x25,    /*  ..@..@.@  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x00,    /*  ........  */

0x40,    /*  .@......  */
0x20,    /*  ..@.....  */
0x1F,    /*  ...@@@@@  */
0x20,    /*  ..@.....  */
0x40,    /*  .@......  */
0x41,    /*  .@.....@  */
0x5D,    /*  .@.@@@.@  */
0x51,    /*  .@.@...@  */
0x51,    /*  .@.@...@  */
0x5F,    /*  .@.@@@@@  */
0x51,    /*  .@.@...@  */
0x51,    /*  .@.@...@  */
0x5D,    /*  .@.@@@.@  */
0x41,    /*  .@.....@  */
0x40,    /*  .@......  */
0x00     /*  ........  */
}}
,
{{16, 16},{
0x00,    /*  ........  */
0x84,    /*  @....@..  */
0x84,    /*  @....@..  */
0x94,    /*  @..@.@..  */
0xA4,    /*  @.@..@..  */
0x84,    /*  @....@..  */
0x84,    /*  @....@..  */
0xD2,    /*  @@.@..@.  */
0xA2,    /*  @.@...@.  */
0x82,    /*  @.....@.  */
0xA2,    /*  @.@...@.  */
0x9A,    /*  @..@@.@.  */
0x82,    /*  @.....@.  */
0x80,    /*  @.......  */
0x80,    /*  @.......  */
0x00,    /*  ........  */

0x20,    /*  ..@.....  */
0x20,    /*  ..@.....  */
0x10,    /*  ...@....  */
0x10,    /*  ...@....  */
0x08,    /*  ....@...  */
0x04,    /*  .....@..  */
0x02,    /*  ......@.  */
0xFF,    /*  @@@@@@@@  */
0x01,    /*  .......@  */
0x02,    /*  ......@.  */
0x04,    /*  .....@..  */
0x08,    /*  ....@...  */
0x18,    /*  ...@@...  */
0x30,    /*  ..@@....  */
0x10,    /*  ...@....  */
0x00     /*  ........  */
}}
,
{{16, 16},{
0x40,    /*  .@......  */
0x30,    /*  ..@@....  */
0x2C,    /*  ..@.@@..  */
0xE7,    /*  @@@..@@@  */
0x24,    /*  ..@..@..  */
0x24,    /*  ..@..@..  */
0x02,    /*  ......@.  */
0xF4,    /*  @@@@.@..  */
0x18,    /*  ...@@...  */
0x10,    /*  ...@....  */
0x9F,    /*  @..@@@@@  */
0x10,    /*  ...@....  */
0x18,    /*  ...@@...  */
0xF4,    /*  @@@@.@..  */
0x02,    /*  ......@.  */
0x00,    /*  ........  */

0x01,    /*  .......@  */
0x01,    /*  .......@  */
0x01,    /*  .......@  */
0x7F,    /*  .@@@@@@@  */
0x21,    /*  ..@....@  */
0x11,    /*  ...@...@  */
0x40,    /*  .@......  */
0x4F,    /*  .@..@@@@  */
0x20,    /*  ..@.....  */
0x18,    /*  ...@@...  */
0x07,    /*  .....@@@  */
0x10,    /*  ...@....  */
0x20,    /*  ..@.....  */
0x4F,    /*  .@..@@@@  */
0x40,    /*  .@......  */
0x00     /*  ........  */
}}
,
{{16, 16},{
0x40,    /*  .@......  */
0x30,    /*  ..@@....  */
0x2F,    /*  ..@.@@@@  */
0xE8,    /*  @@@.@...  */
0x28,    /*  ..@.@...  */
0x28,    /*  ..@.@...  */
0x04,    /*  .....@..  */
0xC4,    /*  @@...@..  */
0x5F,    /*  .@.@@@@@  */
0x44,    /*  .@...@..  */
0xC4,    /*  @@...@..  */
0x5F,    /*  .@.@@@@@  */
0x44,    /*  .@...@..  */
0xC4,    /*  @@...@..  */
0x04,    /*  .....@..  */
0x00,    /*  ........  */

0x01,    /*  .......@  */
0x01,    /*  .......@  */
0x01,    /*  .......@  */
0x7F,    /*  .@@@@@@@  */
0x21,    /*  ..@....@  */
0x11,    /*  ...@...@  */
0x00,    /*  ........  */
0x7F,    /*  .@@@@@@@  */
0x22,    /*  ..@...@.  */
0x22,    /*  ..@...@.  */
0x3F,    /*  ..@@@@@@  */
0x22,    /*  ..@...@.  */
0x22,    /*  ..@...@.  */
0x7F,    /*  .@@@@@@@  */
0x00,    /*  ........  */
0x00     /*  ........  */
}}
,
{{16, 16},{
0x40,    /*  .@......  */
0x30,    /*  ..@@....  */
0xEF,    /*  @@@.@@@@  */
0x28,    /*  ..@.@...  */
0x28,    /*  ..@.@...  */
0x44,    /*  .@...@..  */
0x64,    /*  .@@..@..  */
0xDC,    /*  @@.@@@..  */
0x10,    /*  ...@....  */
0x54,    /*  .@.@.@..  */
0xFF,    /*  @@@@@@@@  */
0x54,    /*  .@.@.@..  */
0x54,    /*  .@.@.@..  */
0x7C,    /*  .@@@@@..  */
0x10,    /*  ...@....  */
0x00,    /*  ........  */

0x01,    /*  .......@  */
0x01,    /*  .......@  */
0x7F,    /*  .@@@@@@@  */
0x21,    /*  ..@....@  */
0x51,    /*  .@.@...@  */
0x22,    /*  ..@...@.  */
0x14,    /*  ...@.@..  */
0x0F,    /*  ....@@@@  */
0x14,    /*  ...@.@..  */
0x25,    /*  ..@..@.@  */
0x3F,    /*  ..@@@@@@  */
0x45,    /*  .@...@.@  */
0x45,    /*  .@...@.@  */
0x45,    /*  .@...@.@  */
0x44,    /*  .@...@..  */
0x00     /*  ........  */
}}
,
{{16, 16},{
0xFE,    /*  @@@@@@@.  */
0x02,    /*  ......@.  */
0x32,    /*  ..@@..@.  */
0x4E,    /*  .@..@@@.  */
0x82,    /*  @.....@.  */
0x00,    /*  ........  */
0xFE,    /*  @@@@@@@.  */
0x4A,    /*  .@..@.@.  */
0xCA,    /*  @@..@.@.  */
0x4A,    /*  .@..@.@.  */
0x4A,    /*  .@..@.@.  */
0x4A,    /*  .@..@.@.  */
0x7E,    /*  .@@@@@@.  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x00,    /*  ........  */

0xFF,    /*  @@@@@@@@  */
0x00,    /*  ........  */
0x02,    /*  ......@.  */
0x04,    /*  .....@..  */
0x03,    /*  ......@@  */
0x00,    /*  ........  */
0xFF,    /*  @@@@@@@@  */
0x40,    /*  .@......  */
0x20,    /*  ..@.....  */
0x03,    /*  ......@@  */
0x0C,    /*  ....@@..  */
0x12,    /*  ...@..@.  */
0x21,    /*  ..@....@  */
0x60,    /*  .@@.....  */
0x20,    /*  ..@.....  */
0x00     /*  ........  */
}}
,
{{16, 16},{
0xFE,    /*  @@@@@@@.  */
0x02,    /*  ......@.  */
0x32,    /*  ..@@..@.  */
0xCA,    /*  @@..@.@.  */
0x46,    /*  .@...@@.  */
0x20,    /*  ..@.....  */
0x30,    /*  ..@@....  */
0x2C,    /*  ..@.@@..  */
0xE3,    /*  @@@...@@  */
0x24,    /*  ..@..@..  */
0x28,    /*  ..@.@...  */
0x10,    /*  ...@....  */
0x20,    /*  ..@.....  */
0x20,    /*  ..@.....  */
0x00,    /*  ........  */
0x00,    /*  ........  */

0xFF,    /*  @@@@@@@@  */
0x02,    /*  ......@.  */
0x04,    /*  .....@..  */
0x43,    /*  .@....@@  */
0x20,    /*  ..@.....  */
0x11,    /*  ...@...@  */
0x4D,    /*  .@..@@.@  */
0x81,    /*  @......@  */
0x7F,    /*  .@@@@@@@  */
0x01,    /*  .......@  */
0x05,    /*  .....@.@  */
0x09,    /*  ....@..@  */
0x11,    /*  ...@...@  */
0x30,    /*  ..@@....  */
0x00,    /*  ........  */
0x00     /*  ........  */
}}
,
{{16, 16},{
0x00,    /*  ........  */
0xFE,    /*  @@@@@@@.  */
0x02,    /*  ......@.  */
0x02,    /*  ......@.  */
0x02,    /*  ......@.  */
0x02,    /*  ......@.  */
0x02,    /*  ......@.  */
0x02,    /*  ......@.  */
0x02,    /*  ......@.  */
0x02,    /*  ......@.  */
0x02,    /*  ......@.  */
0x02,    /*  ......@.  */
0x02,    /*  ......@.  */
0xFE,    /*  @@@@@@@.  */
0x00,    /*  ........  */
0x00,    /*  ........  */

0x00,    /*  ........  */
0x3F,    /*  ..@@@@@@  */
0x20,    /*  ..@.....  */
0x20,    /*  ..@.....  */
0x20,    /*  ..@.....  */
0x20,    /*  ..@.....  */
0x20,    /*  ..@.....  */
0x20,    /*  ..@.....  */
0x20,    /*  ..@.....  */
0x20,    /*  ..@.....  */
0x20,    /*  ..@.....  */
0x20,    /*  ..@.....  */
0x20,    /*  ..@.....  */
0x3F,    /*  ..@@@@@@  */
0x00,    /*  ........  */
0x00     /*  ........  */
}}
};


