#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
字库点阵显示工具
输入字库数据，显示对应的12×12汉字点阵
"""

import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import re

class FontDisplayTool:
    def __init__(self, root):
        self.root = root
        self.root.title("字库点阵显示工具 V1.0")
        self.root.geometry("1000x700")
        
        self.create_widgets()
        self.load_example_data()
    
    def create_widgets(self):
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 标题
        title_label = ttk.Label(main_frame, text="字库点阵显示工具", 
                               font=("SimSun", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # 输入区域
        input_frame = ttk.LabelFrame(main_frame, text="字库数据输入", padding="10")
        input_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 5))
        
        ttk.Label(input_frame, text="粘贴字库数据:").grid(row=0, column=0, sticky=tk.W, pady=(0, 5))
        
        self.input_text = scrolledtext.ScrolledText(input_frame, width=50, height=25, font=("Consolas", 9))
        self.input_text.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        
        button_frame = ttk.Frame(input_frame)
        button_frame.grid(row=2, column=0, sticky=(tk.W, tk.E), pady=(5, 0))
        
        ttk.Button(button_frame, text="解析显示", command=self.parse_and_display).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="清空", command=self.clear_input).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="示例数据", command=self.load_example_data).pack(side=tk.LEFT, padx=(0, 5))
        
        # 显示区域
        display_frame = ttk.LabelFrame(main_frame, text="点阵显示", padding="10")
        display_frame.grid(row=1, column=1, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(5, 5))
        
        # Canvas用于显示点阵
        self.canvas = tk.Canvas(display_frame, width=400, height=400, bg='white')
        self.canvas.grid(row=0, column=0, pady=(0, 10))
        
        # 显示信息
        info_frame = ttk.Frame(display_frame)
        info_frame.grid(row=1, column=0, sticky=(tk.W, tk.E))
        
        self.info_label = ttk.Label(info_frame, text="等待输入数据...", font=("SimSun", 12))
        self.info_label.grid(row=0, column=0, pady=(0, 5))
        
        self.size_label = ttk.Label(info_frame, text="", font=("SimSun", 10))
        self.size_label.grid(row=1, column=0)
        
        # 分析区域
        analysis_frame = ttk.LabelFrame(main_frame, text="数据分析", padding="10")
        analysis_frame.grid(row=1, column=2, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(5, 0))
        
        self.analysis_text = scrolledtext.ScrolledText(analysis_frame, width=40, height=25, font=("Consolas", 9))
        self.analysis_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 控制按钮
        control_frame = ttk.Frame(main_frame)
        control_frame.grid(row=2, column=0, columnspan=3, pady=(10, 0))
        
        ttk.Button(control_frame, text="复制分析", command=self.copy_analysis).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(control_frame, text="保存图片", command=self.save_image).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(control_frame, text="放大显示", command=self.zoom_display).pack(side=tk.LEFT, padx=(0, 5))
        
        # 配置网格权重
        main_frame.columnconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.columnconfigure(2, weight=1)
        main_frame.rowconfigure(1, weight=1)
        input_frame.columnconfigure(0, weight=1)
        input_frame.rowconfigure(1, weight=1)
        analysis_frame.columnconfigure(0, weight=1)
        analysis_frame.rowconfigure(0, weight=1)
    
    def parse_font_data(self, text):
        """解析字库数据"""
        try:
            # 查找所有的十六进制数值
            hex_pattern = r'0x([0-9A-Fa-f]{2})'
            matches = re.findall(hex_pattern, text)
            
            if len(matches) < 24:
                return None, f"数据不足，找到{len(matches)}个字节，需要24个字节"
            
            # 转换为字节数组
            bytes_data = [int(match, 16) for match in matches[:24]]
            
            # 转换为12x12位图（列行式逆向取模）
            bitmap = [[0 for _ in range(12)] for _ in range(12)]
            
            # 前12字节：12列，每列前8行（逆向取模）
            for col in range(12):
                col_byte = bytes_data[col]
                for row in range(8):
                    if col_byte & (1 << row):  # 逆向取模：bit0对应第0行
                        bitmap[row][col] = 1
            
            # 后12字节：12列，每列后4行（逆向取模）
            for col in range(12):
                col_byte = bytes_data[col + 12]
                for row in range(8, 12):
                    if col_byte & (1 << (row - 8)):  # 逆向取模：bit0对应第8行
                        bitmap[row][col] = 1
            
            return bitmap, bytes_data
            
        except Exception as e:
            return None, f"解析错误: {str(e)}"
    
    def parse_and_display(self):
        """解析并显示字库数据"""
        input_data = self.input_text.get(1.0, tk.END).strip()
        if not input_data:
            messagebox.showwarning("警告", "请输入字库数据")
            return
        
        result = self.parse_font_data(input_data)
        if result[0] is None:
            messagebox.showerror("错误", result[1])
            return
        
        bitmap, bytes_data = result
        
        # 显示点阵
        self.display_bitmap(bitmap)
        
        # 显示信息
        self.info_label.config(text="字库数据解析成功")
        self.size_label.config(text="12×12点阵 (列行式逆向取模)")
        
        # 生成分析
        analysis = self.generate_analysis(bitmap, bytes_data)
        self.analysis_text.delete(1.0, tk.END)
        self.analysis_text.insert(tk.END, analysis)
    
    def display_bitmap(self, bitmap):
        """显示位图"""
        self.canvas.delete("all")
        
        cell_size = 30
        
        # 绘制网格
        for i in range(13):
            self.canvas.create_line(i*cell_size, 0, i*cell_size, 12*cell_size, fill="lightgray")
            self.canvas.create_line(0, i*cell_size, 12*cell_size, i*cell_size, fill="lightgray")
        
        # 绘制像素
        for row in range(12):
            for col in range(12):
                if bitmap[row][col]:
                    x1, y1 = col*cell_size, row*cell_size
                    x2, y2 = x1+cell_size, y1+cell_size
                    self.canvas.create_rectangle(x1, y1, x2, y2, fill="black", outline="gray")
        
        # 添加行列标号
        for i in range(12):
            # 列标号
            self.canvas.create_text(i*cell_size + cell_size//2, -15, text=str(i), font=("Arial", 10), fill="blue")
            # 行标号
            self.canvas.create_text(-20, i*cell_size + cell_size//2, text=str(i), font=("Arial", 10), fill="red")
    
    def generate_analysis(self, bitmap, bytes_data):
        """生成数据分析"""
        analysis = "字库数据分析报告\n"
        analysis += "=" * 50 + "\n\n"
        
        # 基本信息
        analysis += "基本信息:\n"
        analysis += f"点阵大小: 12×12\n"
        analysis += f"数据格式: 列行式逆向取模\n"
        analysis += f"数据字节: 24字节\n"
        analysis += f"有效像素: {sum(sum(row) for row in bitmap)}个\n\n"
        
        # 前12字节分析
        analysis += "前12字节分析 (12列×前8行):\n"
        for col in range(12):
            byte_val = bytes_data[col]
            col_pattern = ""
            for row in range(8):
                col_pattern += "█" if bitmap[row][col] else "·"
            analysis += f"列{col:2d}: 0x{byte_val:02X} = {col_pattern}\n"
        
        analysis += "\n后12字节分析 (12列×后4行):\n"
        for col in range(12):
            byte_val = bytes_data[col + 12]
            col_pattern = ""
            for row in range(8, 12):
                col_pattern += "█" if bitmap[row][col] else "·"
            col_pattern += "    "  # 补齐显示
            analysis += f"列{col:2d}: 0x{byte_val:02X} = {col_pattern}\n"
        
        # 完整点阵
        analysis += "\n完整12×12点阵:\n"
        for row in range(12):
            pattern = ""
            for col in range(12):
                pattern += "█" if bitmap[row][col] else "·"
            analysis += f"行{row:2d}: {pattern}\n"
        
        # 格式说明
        analysis += "\n格式说明:\n"
        analysis += "- 列行式存储：按列存储，每列从上到下\n"
        analysis += "- 逆向取模：第0行→bit0, 第1行→bit1, ..., 第7行→bit7\n"
        analysis += "- 前12字节：12列的前8行数据\n"
        analysis += "- 后12字节：12列的后4行数据\n"
        
        return analysis
    
    def load_example_data(self):
        """加载示例数据"""
        example_data = """{{12, 12},{
0x10,    /*  ....@...  */
0x10,    /*  ....@...  */
0xFF,    /*  @@@@@@@@  */
0x08,    /*  ...@....  */
0x08,    /*  ...@....  */
0x02,    /*  .@......  */
0x02,    /*  .@......  */
0xFE,    /*  .@@@@@@@  */
0x02,    /*  .@......  */
0x02,    /*  .@......  */
0xFE,    /*  .@@@@@@@  */
0x00,    /*  ........  */

0x00,    /*  ....      */
0x00,    /*  ....      */
0x07,    /*  @@@.      */
0x02,    /*  .@..      */
0x09,    /*  @..@      */
0x04,    /*  ..@.      */
0x03,    /*  @@..      */
0x00,    /*  ....      */
0x08,    /*  ...@      */
0x08,    /*  ...@      */
0x07,    /*  @@@.      */
0x00     /*  ....      */
}}"""
        
        self.input_text.delete(1.0, tk.END)
        self.input_text.insert(tk.END, example_data)
        
        # 自动解析显示
        self.parse_and_display()
    
    def clear_input(self):
        """清空输入"""
        self.input_text.delete(1.0, tk.END)
        self.analysis_text.delete(1.0, tk.END)
        self.canvas.delete("all")
        self.info_label.config(text="等待输入数据...")
        self.size_label.config(text="")
    
    def copy_analysis(self):
        """复制分析结果"""
        analysis = self.analysis_text.get(1.0, tk.END).strip()
        if analysis:
            self.root.clipboard_clear()
            self.root.clipboard_append(analysis)
            messagebox.showinfo("成功", "分析结果已复制到剪贴板")
        else:
            messagebox.showwarning("警告", "没有可复制的分析结果")
    
    def save_image(self):
        """保存点阵图片"""
        try:
            from tkinter import filedialog
            from PIL import Image, ImageDraw
            
            # 获取当前显示的位图数据
            input_data = self.input_text.get(1.0, tk.END).strip()
            if not input_data:
                messagebox.showwarning("警告", "没有可保存的图片")
                return
            
            result = self.parse_font_data(input_data)
            if result[0] is None:
                messagebox.showerror("错误", "数据解析失败")
                return
            
            bitmap, _ = result
            
            # 创建图片
            cell_size = 20
            img_size = 12 * cell_size
            img = Image.new('RGB', (img_size, img_size), 'white')
            draw = ImageDraw.Draw(img)
            
            # 绘制点阵
            for row in range(12):
                for col in range(12):
                    if bitmap[row][col]:
                        x1, y1 = col * cell_size, row * cell_size
                        x2, y2 = x1 + cell_size, y1 + cell_size
                        draw.rectangle([x1, y1, x2, y2], fill='black')
            
            # 保存文件
            filename = filedialog.asksaveasfilename(
                title="保存点阵图片",
                defaultextension=".png",
                filetypes=[("PNG图片", "*.png"), ("JPEG图片", "*.jpg"), ("所有文件", "*.*")]
            )
            
            if filename:
                img.save(filename)
                messagebox.showinfo("成功", f"图片已保存到: {filename}")
                
        except ImportError:
            messagebox.showerror("错误", "需要安装PIL库: pip install pillow")
        except Exception as e:
            messagebox.showerror("错误", f"保存失败: {str(e)}")
    
    def zoom_display(self):
        """放大显示"""
        input_data = self.input_text.get(1.0, tk.END).strip()
        if not input_data:
            messagebox.showwarning("警告", "没有可显示的数据")
            return
        
        result = self.parse_font_data(input_data)
        if result[0] is None:
            messagebox.showerror("错误", "数据解析失败")
            return
        
        bitmap, _ = result
        
        # 创建放大显示窗口
        zoom_window = tk.Toplevel(self.root)
        zoom_window.title("放大显示")
        zoom_window.geometry("600x600")
        
        zoom_canvas = tk.Canvas(zoom_window, width=600, height=600, bg='white')
        zoom_canvas.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        cell_size = 45
        
        # 绘制放大的点阵
        for i in range(13):
            zoom_canvas.create_line(i*cell_size, 0, i*cell_size, 12*cell_size, fill="lightgray")
            zoom_canvas.create_line(0, i*cell_size, 12*cell_size, i*cell_size, fill="lightgray")
        
        for row in range(12):
            for col in range(12):
                if bitmap[row][col]:
                    x1, y1 = col*cell_size, row*cell_size
                    x2, y2 = x1+cell_size, y1+cell_size
                    zoom_canvas.create_rectangle(x1, y1, x2, y2, fill="black", outline="gray")
        
        # 添加标号
        for i in range(12):
            zoom_canvas.create_text(i*cell_size + cell_size//2, -20, text=str(i), font=("Arial", 12), fill="blue")
            zoom_canvas.create_text(-25, i*cell_size + cell_size//2, text=str(i), font=("Arial", 12), fill="red")

def main():
    root = tk.Tk()
    app = FontDisplayTool(root)
    root.mainloop()

if __name__ == "__main__":
    main()
