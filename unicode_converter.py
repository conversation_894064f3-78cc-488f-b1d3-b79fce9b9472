#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Unicode码值与字库点阵数据互转工具
支持0x1154 ↔ 字库点阵数据的双向转换
"""

import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import re

class UnicodeConverter:
    def __init__(self, root):
        self.root = root
        self.root.title("Unicode码值与字库点阵互转工具 v1.0")
        self.root.geometry("900x700")
        
        # 创建界面
        self.create_widgets()
    
    def create_widgets(self):
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 标题
        title_label = ttk.Label(main_frame, text="Unicode码值与字库点阵数据互转工具", 
                               font=("SimSun", 14, "bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))
        
        # Unicode码值输入区域
        unicode_frame = ttk.LabelFrame(main_frame, text="Unicode码值输入", padding="10")
        unicode_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 5))
        
        ttk.Label(unicode_frame, text="输入Unicode码值 (如: 0x1154):").grid(row=0, column=0, sticky=tk.W, pady=(0, 5))
        self.unicode_entry = ttk.Entry(unicode_frame, width=20, font=("Consolas", 12))
        self.unicode_entry.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        self.unicode_entry.bind('<Return>', lambda e: self.unicode_to_char())
        
        ttk.Button(unicode_frame, text="转换为汉字", command=self.unicode_to_char).grid(row=2, column=0, pady=(0, 10))
        
        # 显示对应汉字
        self.char_display = ttk.Label(unicode_frame, text="汉字: ", font=("SimSun", 16))
        self.char_display.grid(row=3, column=0, sticky=tk.W, pady=(0, 10))
        
        # 查找字库按钮
        ttk.Button(unicode_frame, text="查找字库数据", command=self.find_font_data).grid(row=4, column=0, pady=(0, 5))
        
        # 字库点阵输入区域
        font_frame = ttk.LabelFrame(main_frame, text="字库点阵数据输入", padding="10")
        font_frame.grid(row=1, column=1, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(5, 0))
        
        ttk.Label(font_frame, text="粘贴字库点阵数据:").grid(row=0, column=0, sticky=tk.W, pady=(0, 5))
        
        self.font_text = scrolledtext.ScrolledText(font_frame, width=40, height=15, font=("Consolas", 9))
        self.font_text.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        
        ttk.Button(font_frame, text="解析点阵数据", command=self.parse_font_data).grid(row=2, column=0, pady=(0, 5))
        
        # 预览区域
        preview_frame = ttk.LabelFrame(main_frame, text="点阵预览", padding="10")
        preview_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(10, 0))
        
        # 创建Canvas用于显示点阵
        canvas_frame = ttk.Frame(preview_frame)
        canvas_frame.grid(row=0, column=0, sticky=(tk.W, tk.E))
        
        self.canvas = tk.Canvas(canvas_frame, width=300, height=300, bg='white')
        self.canvas.grid(row=0, column=0, padx=(0, 20))
        
        # 信息显示区域
        info_frame = ttk.Frame(preview_frame)
        info_frame.grid(row=0, column=1, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        self.info_text = scrolledtext.ScrolledText(info_frame, width=50, height=12, font=("Consolas", 9))
        self.info_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 控制按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=3, column=0, columnspan=2, pady=(10, 0))
        
        ttk.Button(button_frame, text="复制信息", command=self.copy_info).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="生成C代码", command=self.generate_c_code).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="批量转换", command=self.batch_convert).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="清空", command=self.clear_all).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="示例数据", command=self.load_example).pack(side=tk.LEFT, padx=(0, 5))
        
        # 配置网格权重
        main_frame.columnconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(1, weight=1)
        main_frame.rowconfigure(2, weight=1)
        unicode_frame.columnconfigure(0, weight=1)
        font_frame.columnconfigure(0, weight=1)
        font_frame.rowconfigure(1, weight=1)
        preview_frame.columnconfigure(1, weight=1)
        preview_frame.rowconfigure(0, weight=1)
        info_frame.columnconfigure(0, weight=1)
        info_frame.rowconfigure(0, weight=1)
    
    def unicode_to_char(self):
        """将Unicode码值转换为汉字"""
        unicode_str = self.unicode_entry.get().strip()
        if not unicode_str:
            messagebox.showwarning("警告", "请输入Unicode码值")
            return
        
        try:
            # 解析Unicode码值
            if unicode_str.startswith('0x') or unicode_str.startswith('0X'):
                unicode_val = int(unicode_str, 16)
            else:
                unicode_val = int(unicode_str, 16)
            
            # 转换为汉字
            char = chr(unicode_val)
            self.char_display.config(text=f"汉字: {char} (Unicode: 0x{unicode_val:04X})")
            
            # 显示信息
            info = f"Unicode码值: 0x{unicode_val:04X}\n"
            info += f"十进制: {unicode_val}\n"
            info += f"对应汉字: {char}\n"
            info += f"UTF-8编码: {char.encode('utf-8').hex()}\n"
            
            self.info_text.delete(1.0, tk.END)
            self.info_text.insert(tk.END, info)
            
        except ValueError:
            messagebox.showerror("错误", "无效的Unicode码值格式")
        except Exception as e:
            messagebox.showerror("错误", f"转换失败: {str(e)}")
    
    def find_font_data(self):
        """查找字库数据（模拟功能）"""
        unicode_str = self.unicode_entry.get().strip()
        if not unicode_str:
            messagebox.showwarning("警告", "请先输入Unicode码值")
            return
        
        try:
            if unicode_str.startswith('0x') or unicode_str.startswith('0X'):
                unicode_val = int(unicode_str, 16)
            else:
                unicode_val = int(unicode_str, 16)
            
            char = chr(unicode_val)
            
            # 模拟查找结果
            info = f"查找字库数据结果:\n"
            info += f"Unicode: 0x{unicode_val:04X} ({char})\n"
            info += f"需要在以下文件中查找:\n"
            info += f"1. CodePage12.c - 查找码值映射\n"
            info += f"2. Unicode12.c - 查找点阵数据\n\n"
            info += f"在CodePage12.c中查找:\n"
            info += f"{{ 0x{unicode_val:04X}, 0x{unicode_val:04X}, 0xXXXX }}\n\n"
            info += f"然后在Unicode12.c中找到对应索引的点阵数据"
            
            self.info_text.delete(1.0, tk.END)
            self.info_text.insert(tk.END, info)
            
        except Exception as e:
            messagebox.showerror("错误", f"查找失败: {str(e)}")
    
    def parse_font_data(self):
        """解析字库点阵数据"""
        font_data = self.font_text.get(1.0, tk.END).strip()
        if not font_data:
            messagebox.showwarning("警告", "请输入字库点阵数据")
            return
        
        try:
            # 解析点阵数据
            bitmap, info = self.extract_bitmap_from_text(font_data)
            if bitmap is None:
                messagebox.showerror("错误", "无法解析字库数据格式")
                return
            
            # 显示预览
            self.show_bitmap_preview(bitmap)
            
            # 显示信息
            self.info_text.delete(1.0, tk.END)
            self.info_text.insert(tk.END, info)
            
        except Exception as e:
            messagebox.showerror("错误", f"解析失败: {str(e)}")
    
    def extract_bitmap_from_text(self, text):
        """从文本中提取位图数据"""
        try:
            # 查找所有的十六进制数值
            hex_pattern = r'0x([0-9A-Fa-f]{2})'
            matches = re.findall(hex_pattern, text)
            
            if len(matches) < 24:  # 12x12需要24字节
                return None, "数据不足，需要24个字节"
            
            # 转换为字节数组
            bytes_data = [int(match, 16) for match in matches[:24]]
            
            # 转换为12x12位图
            # 前12字节：每行的前8位
            # 后12字节：每行的后4位
            bitmap = []
            for row in range(12):
                row_data = []

                # 前8位（来自前12字节）
                front_byte = bytes_data[row]
                for bit in range(8):
                    row_data.append(1 if (front_byte & (1 << (7-bit))) else 0)

                # 后4位（来自后12字节）
                back_byte = bytes_data[row + 12]
                for bit in range(4):
                    row_data.append(1 if (back_byte & (1 << (7-bit))) else 0)

                bitmap.append(row_data)
            
            # 生成信息
            info = f"解析结果:\n"
            info += f"数据字节数: {len(bytes_data)}\n"
            info += f"位图大小: 12x12\n\n"
            info += f"十六进制数据:\n"
            for i in range(0, len(bytes_data), 2):
                info += f"0x{bytes_data[i]:02X}, 0x{bytes_data[i+1]:02X}\n"
            
            info += f"\n点阵图案:\n"
            for row in bitmap:
                line = ""
                for pixel in row:
                    line += "█" if pixel else "·"
                info += line + "\n"
            
            return bitmap, info
            
        except Exception as e:
            return None, f"解析错误: {str(e)}"
    
    def show_bitmap_preview(self, bitmap):
        """显示位图预览"""
        self.canvas.delete("all")
        
        # 计算缩放比例
        cell_size = 20
        
        # 绘制网格
        for i in range(13):
            self.canvas.create_line(i*cell_size, 0, i*cell_size, 12*cell_size, fill="lightgray")
            self.canvas.create_line(0, i*cell_size, 12*cell_size, i*cell_size, fill="lightgray")
        
        # 绘制像素
        for row in range(12):
            for col in range(12):
                if bitmap[row][col]:
                    x1, y1 = col*cell_size, row*cell_size
                    x2, y2 = x1+cell_size, y1+cell_size
                    self.canvas.create_rectangle(x1, y1, x2, y2, fill="black", outline="gray")
    
    def copy_info(self):
        """复制信息到剪贴板"""
        info = self.info_text.get(1.0, tk.END).strip()
        if info:
            self.root.clipboard_clear()
            self.root.clipboard_append(info)
            messagebox.showinfo("成功", "信息已复制到剪贴板")
        else:
            messagebox.showwarning("警告", "没有可复制的信息")
    
    def clear_all(self):
        """清空所有内容"""
        self.unicode_entry.delete(0, tk.END)
        self.font_text.delete(1.0, tk.END)
        self.info_text.delete(1.0, tk.END)
        self.char_display.config(text="汉字: ")
        self.canvas.delete("all")
    
    def load_example(self):
        """加载示例数据"""
        # 加载示例Unicode码值
        self.unicode_entry.delete(0, tk.END)
        self.unicode_entry.insert(0, "0x1154")
        
        # 加载示例字库数据
        example_data = """{{12, 12},{
0x00,    /*  ........  */
0x0C,    /*  ....@@..  */
0x0E,    /*  ....@@@.  */
0xE1,    /*  @@@....@  */
0xF1,    /*  @@@@...@  */
0x1E,    /*  ...@@@@.  */
0x0C,    /*  ....@@..  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x00,    /*  ........  */

0x00,    /*  ........  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x06,    /*  .....@@.  */
0x06,    /*  .....@@.  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x00,    /*  ........  */
0x00     /*  ........  */
}}"""
        
        self.font_text.delete(1.0, tk.END)
        self.font_text.insert(tk.END, example_data)
        
        # 自动转换
        self.unicode_to_char()
        self.parse_font_data()

    def generate_c_code(self):
        """生成完整的C代码"""
        unicode_str = self.unicode_entry.get().strip()
        font_data = self.font_text.get(1.0, tk.END).strip()

        if not unicode_str or not font_data:
            messagebox.showwarning("警告", "请输入Unicode码值和字库数据")
            return

        try:
            # 解析Unicode
            if unicode_str.startswith('0x') or unicode_str.startswith('0X'):
                unicode_val = int(unicode_str, 16)
            else:
                unicode_val = int(unicode_str, 16)

            char = chr(unicode_val)

            # 解析字库数据
            bitmap, _ = self.extract_bitmap_from_text(font_data)
            if bitmap is None:
                messagebox.showerror("错误", "无法解析字库数据")
                return

            # 生成C代码
            c_code = f"// 字符: {char} (Unicode: 0x{unicode_val:04X})\n"
            c_code += f"// CodePage12.c 条目:\n"
            c_code += f"{{ 0x{unicode_val:04X}, 0x{unicode_val:04X}, 0xXXXX }},/* Segment XXX, 0x0001 Symbols */\n\n"
            c_code += f"// Unicode12.c 条目:\n"
            c_code += font_data

            # 显示在新窗口
            self.show_code_window(c_code, f"字符 {char} 的C代码")

        except Exception as e:
            messagebox.showerror("错误", f"生成失败: {str(e)}")

    def batch_convert(self):
        """批量转换Unicode码值"""
        batch_window = tk.Toplevel(self.root)
        batch_window.title("批量Unicode转换")
        batch_window.geometry("600x500")

        ttk.Label(batch_window, text="输入Unicode码值列表 (每行一个，如: 0x1154):").pack(pady=10)

        input_text = scrolledtext.ScrolledText(batch_window, width=60, height=10)
        input_text.pack(pady=10, padx=20, fill=tk.BOTH, expand=True)

        result_text = scrolledtext.ScrolledText(batch_window, width=60, height=15)
        result_text.pack(pady=10, padx=20, fill=tk.BOTH, expand=True)

        def do_batch_convert():
            input_data = input_text.get(1.0, tk.END).strip()
            if not input_data:
                messagebox.showwarning("警告", "请输入Unicode码值")
                return

            lines = input_data.split('\n')
            results = []

            for line in lines:
                line = line.strip()
                if not line:
                    continue

                try:
                    if line.startswith('0x') or line.startswith('0X'):
                        unicode_val = int(line, 16)
                    else:
                        unicode_val = int(line, 16)

                    char = chr(unicode_val)
                    results.append(f"0x{unicode_val:04X} → {char}")

                except Exception as e:
                    results.append(f"{line} → 错误: {str(e)}")

            result_text.delete(1.0, tk.END)
            result_text.insert(tk.END, '\n'.join(results))

        ttk.Button(batch_window, text="转换", command=do_batch_convert).pack(pady=10)

    def show_code_window(self, code, title):
        """显示代码窗口"""
        code_window = tk.Toplevel(self.root)
        code_window.title(title)
        code_window.geometry("700x600")

        code_text = scrolledtext.ScrolledText(code_window, font=("Consolas", 10))
        code_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        code_text.insert(tk.END, code)

        def copy_code():
            code_window.clipboard_clear()
            code_window.clipboard_append(code)
            messagebox.showinfo("成功", "代码已复制到剪贴板")

        ttk.Button(code_window, text="复制代码", command=copy_code).pack(pady=10)

    def bitmap_to_unicode_guess(self, bitmap):
        """根据位图猜测可能的Unicode字符（简单实现）"""
        # 这是一个简化的实现，实际应用中需要更复杂的字符识别算法
        # 计算位图的一些特征
        total_pixels = sum(sum(row) for row in bitmap)

        # 根据像素数量和分布猜测字符类型
        if total_pixels < 10:
            return "可能是标点符号或空白字符"
        elif total_pixels < 30:
            return "可能是简单汉字或字母"
        else:
            return "可能是复杂汉字"

def main():
    root = tk.Tk()
    app = UnicodeConverter(root)
    root.mainloop()

if __name__ == "__main__":
    main()
