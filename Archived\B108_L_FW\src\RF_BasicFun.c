#include  "RF_BasicFun.h"
//----------------------------------------------------------------------------------------
tim_count  RfcTimer[RFC_TIMER_NUM]  ;//system ISR process

tim_count  RfcTimer10[RFC_TIMER10_NUM]  ;//user level timer(1 tick=10ms),RFC_TIMER10_NUM==2

__IO uint16_t  RandomS,PRandom ;


//---------------------------------------------
uint8_t   *test_p ;//pointer used to store current testoutput poistion of RX data

uint8_t   *RfDat_rp  ;//pointer used for RF receiveing  data
uint8_t   *RfDat_tp  ;//pointer used for RF Transmitte data 

volatile  uint16_t  RSSI_STA_L,RSSI_STA_R,RSSI_BufIndex_L,RSSI_BufIndex_R;
uint16_t  RssiDisSta ;
volatile  int16_t   RSSI_Sum ;


IC021RxSwitchReg     *Rx_021_Ch_p ;//A pointer to Rx work channel register of IC021
IC021TxSwitchReg     *Tx_021_Ch_p ;//A pointer to Tx work channel register of IC021

JF01SwitchReg         *Rx_01_Ch_p ;//A pointer to Rx work channel register of JF01
JF01SwitchReg         *Tx_01_Ch_p ;//A pointer to Tx work channel register of JF01

RFC_WorkDS_t   RfDs ;//Main data struct used for RF related function

volatile uint8_t   ShiftReg ;
volatile uint8_t   BitCounter;
volatile uint8_t   PreambleCount;
volatile uint8_t   PreambleError;
volatile uint8_t   ByteCounter;

uint16_t  TestKeyA,TestKeyB ;


LongWord  IC021Reg ;

//RSSI Gain correction table for IC021
const uint8_t gain_correction[] =
    { 86, 0, 0, 0, 58, 38, 24, 0,
	   0, 0, 0, 0, 0, 0, 0, 0
     }; //For AGC and LAN 021

//---------------------

uint8_t  DefPATable[PA_TAB_LEN]={//@315MHz
  0xc2 , //10dBm ,26.9mA
  0xcb , //7dBm  ,22.1mA
  0x85 , //5dBm  ,18.3mA
  0x51 , //0dBm  ,15.0mA
  0x0d , //-20dBm ,11.4mA @315MHz
//  0x69 , //-5dBm ,12.8mA
//  0x34 , //-10dBm ,13.5mA
//  0x1c , //-15dBm ,12.0mA
//  0x0d , //-20dBm ,11.4mA @315MHz
  } ;

//-----------
void InitRfcTimer10(void)
{
  unsigned i ;
  for(i=0;i<RFC_TIMER10_NUM ;i++)
  {
    RfcTimer10[i].pv=RfcTimer10[i].cv=RfcTimer10[i].csr.csword=0 ;
    }
}

void InitRfcTimer(void)
{
  RfcTimer[0].pv=RfcTimer[0].cv=RfcTimer[0].csr.csword=0 ;
  RfcTimer[1].pv=RfcTimer[1].cv=RfcTimer[1].csr.csword=0 ;
}

void  RfcTimer10Fun(void)
{
  unsigned i ;
  for(i=0;i<RFC_TIMER10_NUM;i++)
   {
     if(RfcTimer10[i].csr.csbit.en==1)
     {
       if(RfcTimer10[i].cv<65535) ++(RfcTimer10[i].cv) ;
       if(RfcTimer10[i].cv>=RfcTimer10[i].pv)
       {
         RfcTimer10[i].csr.csbit.q=1 ;
        }
      }
   }
}

void RfcTimerFun(void) //This function must be called in system tick process ISR
{
  if(RfcTimer[0].csr.csbit.en==1)
  {
    if(RfcTimer[0].cv<65535) ++(RfcTimer[0].cv) ;
    if(RfcTimer[0].cv>=RfcTimer[0].pv)
    {
      RfcTimer[0].csr.csbit.q=1 ;
     }
   }
  if(RfcTimer[1].csr.csbit.en==1)
  {
    if(RfcTimer[1].cv<65535) ++(RfcTimer[1].cv) ;
    if(RfcTimer[1].cv>=RfcTimer[1].pv)
    {
      RfcTimer[1].csr.csbit.q=1 ;
     }
   }
}
//--------------------------------------------------------------
void WaitSomeTime(uint16_t delay)
{//Do not change for time critical--@ HCLK=12MHz,8MHz,4.194
  uint16_t tmp ;
  for(tmp=0 ;tmp <delay ;tmp++)
  {
   RandomS +=(SysTick->VAL<<5) ;
   RandomS +=(SysTick->VAL<<7) ;
  }
  PRandom &=(~ENC_MASK) ;
  PRandom |=(RandomS&ENC_MASK) ;
}
//--------------------------------------------------------------

void  InitRfEnvironment(void)
{
//-------------------------------------------------------
  ClrObj16((uint16_t *)&RfDs,sizeof(RFC_WorkDS_t)>>1);
  RfDs.PAccStaS.UIDCode=CalReqCode() ;//Get Authentication request code for this module
  if((APP_PA.work_mode&DEVICE_MODE_MASK)==FYF25_MODE)
  {//This device be configured to work in FYF25 mode
	RfDs.TxPackageLen = sizeof(PANEL_TX_DAT) ;
	RfDs.RxPackageLen = sizeof(SHR_TX_DAT) ;
   }
  else if((APP_PA.work_mode&DEVICE_MODE_MASK)==FYF35_SHR_MODE)
  {//This device be configured to work in FYF35 shearer mode
#ifdef USE_OLD_FRAME
	RfDs.TxPackageLen = sizeof(PANEL_OLD_TX_DAT) ;
#else	  
	RfDs.TxPackageLen = sizeof(PANEL_TX_DAT) ;
#endif
	RfDs.RxPackageLen = sizeof(SHR_TX_DAT) ;
   }
  else if((APP_PA.work_mode&DEVICE_MODE_MASK)==FYF35_TUN_MODE)
  {//This device be configured to work in FYF35 Tunneller mode
#ifdef USE_OLD_FRAME
	RfDs.TxPackageLen = sizeof(PANEL_OLD_TX_DAT) ;
#else	  
	RfDs.TxPackageLen = sizeof(PANEL_TX_DAT) ;
#endif
	RfDs.RxPackageLen = sizeof(TUN_TX_DAT) ;
   }
  else if((APP_PA.work_mode&DEVICE_MODE_MASK)==FYS30_021_MODE)
  {//This device be configured to work in FYS30 RF021 shearer mode
	RfDs.TxPackageLen = sizeof(SHR_TX_DAT) ;
	RfDs.RxPackageLen = sizeof(PANEL_TX_DAT) ;
   }
  else if((APP_PA.work_mode&DEVICE_MODE_MASK)==FYS30_01_MODE)
  {//This device be configured to work in FYS30 RF01 shearer mode
	RfDs.TxPackageLen = sizeof(SHR_TX_DAT) ;
	RfDs.RxPackageLen = sizeof(PANEL_TX_DAT) ;
   }
  else
  {//This device be forced to work in Test mode--DEVICE_TEST_MODE
	RfDs.TxPackageLen = sizeof(PANEL_TX_DAT) ;
	RfDs.RxPackageLen = sizeof(SHR_TX_DAT) ;
   }
//----
  U_Area.PSta.StaW=0x0000 ;
  U_Area.PSta.StaW|=(APP_PA.GroupId_InGroupId&0x00ff) ;
//-------
  S_Area.SysActWaitTime=APP_PA.SysActWaitTime;
  if(SysSetting.ExtendPan_AS_T)
    S_Area.SysActWaitTime=APP_PA.SysActWaitTime<<1 ;//Delay Time*2
  S_Area.BLShutOffTime=APP_PA.BLShutOffTime;
  if(SysSetting.ExtendBL_AS_T)
    S_Area.BLShutOffTime=APP_PA.BLShutOffTime<<1 ;//Delay Time*2
  if(SysSetting.UnidirOpCtrl)
    U_Area.PSet.UniDirCtrl=0x0001 ;//PanSettingW.UniDirCtrl =1--Bit5
  S_Area.ActTimeOutMonitor=S_Area.SysActWaitTime ;//Begin and refresh active monitor
  S_Area.KeyMask.cw=APP_PB.KeyMask ;//Get permanent mask,postive mask ='1' enable the key
  S_Area.LedFlashInterval=APP_PA.LedFlashInteval ;
  S_Area.LedFlashCounter=S_Area.LedFlashInterval ;
  //----
  OpCtrl=0 ;
  GFlag_Pre=~GFlag ;//
  M_W_Bit_pIndex=0 ;
  if(SysSetting.AllowOthersideCtrl==0)
  {//Both side operation not allowed
    if(Def_CRC_Seed==RIGHT_CRC_SEED)
      S_Area.KeyMask.cw&=APP_PB.RightKeyMask ;
    else
      S_Area.KeyMask.cw&=APP_PB.LeftKeyMask ;
   }
  //------------
  UserSetting1=0xffff ;
  UserSetting2=0x0000 ;
  Display_p1=1 ;//Machine haulage speed 
  if(Def_CRC_Seed==RIGHT_CRC_SEED)
    Display_p2=3*256 ;//Right arm height
  else
    Display_p2=2*256 ;//Left arm height
  DisplayPointer2=Display_p2>>8 ;
  DisPage_p=0x0001 ;
  //--------
  S_Area.TPool_p=DEFAULT_POOL ;//Set default Token pool
  S_Area.RemainToken=RfTT_p->TokenPool[S_Area.TPool_p].TokenNum ;
  TxToken=0 ;
  //---------------------------
  RfcTimer[RF_TIMER].pv=APP_PA.JF01_RxAttenMonTO ;//--55 mS,JF01 Rx signal anttenuation setting restore monitor,IC021 routine will reinit the default value
  RfcTimer[RF_TIMER].cv=0 ;
  RfcTimer[RF_TIMER].csr.csword=0x0000 ; //disable RfcTimer[0]

  RfcTimer10[RF_OP_TIMER_L].pv=APP_PA.OpcKeepTime ;
  RfcTimer10[RF_OP_TIMER_L].csr.csword=0x4000 ;//reset and enable the monitor timer
  RfcTimer10[RF_OP_TIMER_L].cv=0 ;
  RfcTimer10[RF_OP_TIMER_R].pv=APP_PA.OpcKeepTime ;
  RfcTimer10[RF_OP_TIMER_R].csr.csword=0x4000 ;//reset and enable the monitor timer
  RfcTimer10[RF_OP_TIMER_R].cv=0 ;

  RfcTimer10[AIR_TIMER_L].pv=APP_PA.AirLinkageTO ;
  RfcTimer10[AIR_TIMER_L].csr.csword=0x4000 ;//reset and enable the monitor timer
  RfcTimer10[AIR_TIMER_L].cv=0 ;
  RfcTimer10[AIR_TIMER_R].pv=APP_PA.AirLinkageTO ;
  RfcTimer10[AIR_TIMER_R].csr.csword=0x4000 ;//reset and enable the monitor timer
  RfcTimer10[AIR_TIMER_R].cv=0 ;
//------------------------
  RSSI_STA_L=0x0002 ;
  RSSI_STA_R=0x0002 ;
  RSSI_BufIndex_L=0 ;
  RSSI_BufIndex_R=0 ;
//------------------------
  test_p=(uint8_t *) U_Area.Ultra_BUF ;
  RfDs.Area21Flag=0xf021 ;
  RfDs.FixAdd100_Flag=0xf100 ;//Fixd MD 20100 address flag
  RfDs.DClkCntChk=480 ;
}

//-------------------------------------------------------
void  RfSetupTestEntry(__IO uint8_t *RDp)
{//only allow three wrods command--this function direct import from MSP430 program and may not work in ARM system
  TestKeyA=(uint16_t)(*RDp++) ;
  TestKeyA<<=8 ;
  TestKeyA|=(uint16_t)*RDp++ ;//get the hole word

  TestKeyB=(uint16_t)(*RDp++) ;
  TestKeyB<<=8 ;
  TestKeyB|=(uint16_t)*RDp++ ;//get the hole word

  TestCtrlW=(uint16_t)(*RDp++) ;
  TestCtrlW<<=8 ;
  TestCtrlW|=(uint16_t)*RDp++ ;//get the hole word
  if((TestKeyA==TEST_KEY_ID_A)&&(TestKeyB==TEST_KEY_ID_B))
  {
    SCB->AIRCR = 0x05fa0001;//do (system &) core reset,see Cortex-M3 Technical Reference Manaul page 8_23
   }
}
//--------------------------------------
void  RSSI_Filter_021(void)
{
 int16_t i,acc ;
 switch(RSSI_STA_L)
 {
  case 0x0000 :
      break ;
  case 0x0002 :
    if(IsrFlag&IFG_RSSI_D_OK_L)
    {
      __disable_irq() ;
      IsrFlag&=~IFG_RSSI_D_OK_L ;
      __enable_irq() ;
      RfDs.RSSI_BUF_L[RSSI_BufIndex_L++]=RfDs.RSSI_D_L;//RfDs.RSSI_D_L be set in RxDaemon
      if(RSSI_BufIndex_L>=8)
      {
        RSSI_BufIndex_L=0 ;
        RSSI_STA_L=0x0004 ;
       }
    }
    break ;
  case 0x0004 :
    acc=0 ;
    for(i=0 ;i<8 ;i++)
    {
     acc+=RfDs.RSSI_BUF_L[i] ;
     }
    RfDs.RSSI_L=(acc>>3) ;
    RfDs.RSSI_L+=APP_PA.RSSIOffset ;
	if(RfDs.RSSI_L<-128) RfDs.RSSI_L=-128 ; 
    RSSI_STA_L=0x0002 ;
    break ;
  default :
    RSSI_STA_L=0x0000 ;
 }
 switch(RSSI_STA_R)
 {
  case 0x0000 :

    break ;
  case 0x0002 :
    if(IsrFlag&IFG_RSSI_D_OK_R)
    {
      __disable_irq() ;
      IsrFlag&=~IFG_RSSI_D_OK_R ;
      __enable_irq() ;
      RfDs.RSSI_BUF_R[RSSI_BufIndex_R++]=RfDs.RSSI_D_R;//RfDs.RSSI_D_L be set in RxDaemon
     if(RSSI_BufIndex_R>=8)
     {
      RSSI_BufIndex_R=0 ;
      RSSI_STA_R=0x0004 ;
      }
    }
    break ;
  case 0x0004 :
    acc=0 ;
    for(i=0 ;i<8 ;i++)
    {
     acc+=RfDs.RSSI_BUF_R[i] ;
     }
    RfDs.RSSI_R=(acc>>3) ;
    RfDs.RSSI_R+=APP_PA.RSSIOffset ;
	if(RfDs.RSSI_R<-128) RfDs.RSSI_R=-128 ; 
    RSSI_STA_R=0x0002 ;
    break ;
  default :
    RSSI_STA_R=0x0000 ;
 }
}
 
//-----------------------------------
void  RSSI_Filter_01(void)
{
 int16_t  i, acc ;
 switch(RSSI_STA_L)
 {
  case 0x0000 :

      break ;
  case 0x0002 :
    if(IsrFlag&IFG_RSSI_D_OK_L)
    {
      __disable_irq() ;
      IsrFlag&=~IFG_RSSI_D_OK_L ;
      __enable_irq() ;
      RfDs.RSSI_BUF_L[RSSI_BufIndex_L++]=RfDs.RSSI_D_L;//RfDs.RSSI_D_L be set in Rx Daemon
      if(RSSI_BufIndex_L>=8)
      {
        RSSI_BufIndex_L=0 ;
        RSSI_STA_L=0x0004 ;
       }
    }
    break ;
  case 0x0004 :
    acc=0 ;
    for(i=0 ;i<8 ;i++)
    {
     acc+=RfDs.RSSI_BUF_L[i] ;
     }
    RfDs.RSSI_L=(acc>>3) ;
	 
    if(APP_PA.work_mode&LNA_EXIST_01)//parameter control if LNA exist in 38404B board
      RfDs.RSSI_L+=APP_PA.RSSIOffset ;
    if( RfDs.RSSI_L<-128)  RfDs.RSSI_L=-128 ;
    RSSI_STA_L=0x0002 ;
    break ;
  default :
    RSSI_STA_L=0x0000 ;
 }
 switch(RSSI_STA_R)
 {
  case 0x0000 :

    break ;
  case 0x0002 :
    if(IsrFlag&IFG_RSSI_D_OK_R)
    {
      __disable_irq() ;
      IsrFlag&=~IFG_RSSI_D_OK_R ;
      __enable_irq() ;
      RfDs.RSSI_BUF_R[RSSI_BufIndex_R++]=RfDs.RSSI_D_R;//RfDs.RSSI_D_L be set in Rx Daemon
      if(RSSI_BufIndex_R>=8)
      {
        RSSI_BufIndex_R=0 ;
        RSSI_STA_R=0x0004 ;
       }
    }
    break ;
  case 0x0004 :
    acc=0 ;
    for(i=0 ;i<8 ;i++)
    {
     acc+=RfDs.RSSI_BUF_R[i] ;
     }
    RfDs.RSSI_R=(acc>>3) ;
    if(APP_PA.work_mode&LNA_EXIST_01)//parameter control if LNA exist in 38404B board
      RfDs.RSSI_R+=APP_PA.RSSIOffset ;
    if( RfDs.RSSI_R<-128)  RfDs.RSSI_R=-128 ;
    RSSI_STA_R=0x0002 ;
    break ;
  default :
    RSSI_STA_R=0x0000 ;
 }
}

//-----------------------------------------------------------------------------
