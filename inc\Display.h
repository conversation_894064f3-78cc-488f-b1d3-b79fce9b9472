#ifndef __DISPLAY_H
#define __DISPLAY_H
/*LCD Display  defintion and function*/

#include  "stm32l1xx.h"
#include  "stdlib.h"
#include  "string.h"
#include  "stdio.h"

#pragma  anon_unions

#define DEF_LCD_P_OUT      0x0100    //Set default output to LCD  when enter lower status

//#define DRAW_OVER_BACKGROUND
//--------------------------------------------------------------------------------------------------
// GPIOA pinout for LCD control.
//--------------------------------------------------------------------------------------------------
#define LCD_CS1_PIN  (uint16_t)0x1000 // PA.12
#define LCD_RST_PIN  (uint16_t)0x0800 // PA.11
#define LCD_A0_PIN   (uint16_t)0x0400 // PA.10
#define LCD_WR_PIN   (uint16_t)0x0200 // PA.9
#define LCD_RD_PIN   (uint16_t)0x0100 // PA.8
//--------------------------------------------------------------------------------------------------
#define LCD_ND_TIME     1  //LCD set up normal delay time 10mS
#define LCD_VC_TIME     2  //Delay time 20mS for  LCD VC command(Booster circuit ON) to take effect
#define LCD_VR_TIME     2  //Delay time 20mS for  LCD VR command(Voltage regulator ON) to take effect
#define LCD_POW_ON_TIME 60 //Delay 600mS ,wait for smoothing capacitor
#define LCD_REF_ON_TIME 5  //Delay 50mS ,wait for internal voltage reference setting take effect
#define LCD_VOL_ON_Time 5  //Delay 50mS ,wait for electronic volume setting take effect
#define LCDMAX_WIDTH    127
#define LCDMAX_HIGTH    63
//--------------------------------------------------------------------------------------------------
// Background variable
//--------------------------------------------------------------------------------------------------
#ifdef DRAW_OVER_BACKGROUND

static const uint8_t *Background;

typedef enum
{
  DRAW_OR = 0,
  DRAW_XOR = 1,
  DRAW_CLEAR = 2
} draw_type;
#endif // DRAW_OVER_BACKGROUND
//--------------------------------------------------------------------------------------------------
/* System define 3 different ANSI font file,6x8,8x8 and 8x16
   System also provide 2 differenct size unicode font in adequate range,12x12 and 16x16 size
   The char or string write function,support ANSI code draw and different size
   Special unicode draw function(string) support two size(12x12,16x16)with dynmic width adjust capable
*/
#define NULL_CHAR   0x00   //define null ANSI character
#define SPACE_CHAR  0x20   //define ANSI space character
#define ZERO_CHAR   0x30   //define number zero
#define DIGIT_CHAR  0x2e   //define arithmetic point character
#define SIZE6x8      6     //memory size need by 6x8 font in bytes
#define SIZE8x8      8     //memory size need by 8x8 font in bytes
#define SIZE8x16     16    //memory size need by 8x16 font in bytes
#define SIZE12x12    24    //memory size need by 12x12 font in bytes
#define SIZE16x16    32    //memory size need by 16x16 font in bytes
#define ANSI_NUM     256   //each ANSI font file have 256 character symbol
//---------------------------------------------------------------------------------------------
#define UNICODE12_NUM  599   //each 12x12 Unicode font file have (change as need) character symbol
#define UNICODE12_PAGE 434   //item in 12x12 unicode page file
#define UNICODE16_NUM  61   //each 16x16 Unicode font file have (change as need) character symbol
#define UNICODE16_PAGE 61   //item in 16x16 unicode page file
//---------------------------------------------------------------------------------------------
#define FONT_ANSI6x8    0     //default print font ,ANSI character in 6x8 form
#define FONT_ANSI8x8    1     //print in font ,ANSI 8x6 form
#define FONT_ANSI8x16   2     //print in font ,ANSI 8x16 form
#define FONT_UNICODE12  0     //print in unicode font 12x12 form
#define FONT_UNICODE16  1     //print in unicode font 16x16 form
//---------------------------------------------------------------------------------------------
/*
 Following define the print control byte according to print information structure
   union{
   uint16_t   b ;//control byte
   struct {
            uint8_t font       : 2 ;//user specified font type(symbol file ,this also specified the codepage file,if it is a unicode string)
            uint8_t digits     : 2 ;//used to denote how many decimal digits in printed integer data,=0 none,1,2,3

            uint8_t lf_en      : 1 ;//allow print function to do auto line advance operation
            uint8_t reverse    : 1 ;//=1 force to do reverse(white<->dark)print the string,=0 normal display
            uint8_t unicode    : 1 ;//=0 the string contain only ANSI(ASCII) character,=1 the string contain unicode character
            uint8_t right      : 1 ;//=0 string print in X direction to left align mode(default),=1 right align mdoe
           } ;
        } ctrl ;

*/
#define REV_DIS   0x20   //used to set/reset reverse display mode
#define A_6_0D    0x00  //=0000 0000 print control data for ANSI string use 6x8 font,no decimal,left align(default)
#define A_6_1D    0x04  //=0000 0100 ANSI string use 6x8 font,1 decimal,left align
#define A_6_2D    0x08  //=0000 1000 ANSI string use 6x8 font,2 decimal,left align
#define A_6_3D    0x0c  //=0000 1100 ANSI string use 6x8 font,3 decimal,left align
#define A_6_0D_R  0x80  //=1000 0000 ANSI string use 6x8 font,no decimal,right align
#define A_6_1D_R  0x84  //=1000 0100 ANSI string use 6x8 font,1 decimal,right align
#define A_6_2D_R  0x88  //=1000 1000 ANSI string use 6x8 font,2 decimal,right align
#define A_6_3D_R  0x8c  //=1000 1100 ANSI string use 6x8 font,3 decimal,right align

#define A_8_0D    0x01  //=0000 0001 ANSI string use 8x8 font,no decimal,left align
#define A_8_1D    0x05  //=0000 0101 ANSI string use 8x8 font,1 decimal,left align
#define A_8_2D    0x09  //=0000 1001 ANSI string use 8x8 font,2 decimal,left align
#define A_8_3D    0x0d  //=0000 1101 ANSI string use 8x8 font,3 decimal,left align
#define A_8_0D_R  0x81  //=1000 0001 ANSI string use 8x8 font,no decimal,right align
#define A_8_1D_R  0x85  //=1000 0101 ANSI string use 8x8 font,1 decimal,right align
#define A_8_2D_R  0x89  //=1000 1001 ANSI string use 8x8 font,2 decimal,right align
#define A_8_3D_R  0x8d  //=1000 1101 ANSI string use 8x8 font,3 decimal,right align

#define A_16_0D    0x02  //=0000 0010 ANSI string use 8x16 font,no decimal,left align
#define A_16_1D    0x06  //=0000 0110 ANSI string use 8x16 font,1 decimal,left align
#define A_16_2D    0x0a  //=0000 1010 ANSI string use 8x16 font,2 decimal,left align
#define A_16_3D    0x0e  //=0000 1110 ANSI string use 8x16 font,3 decimal,left align
#define A_16_0D_R  0x82  //=1000 0010 ANSI string use 8x16 font,no decimal,right align
#define A_16_1D_R  0x86  //=1000 0110 ANSI string use 8x16 font,1 decimal,right align
#define A_16_2D_R  0x8a  //=1000 1010 ANSI string use 8x16 font,2 decimal,right align
#define A_16_3D_R  0x8e  //=1000 1110 ANSI string use 8x16 font,3 decimal,right align

#define UINCODE_12    0x40  //=0100 00 00 Unicode string use 12x12 font ,left align
#define UINCODE_12_R  0xc0  //=1100 00 00 Unicode string use 12x12 font ,Right align

#define UINCODE_16    0x41  //=0100 00 01 Unicode string use 16x16 font ,left align
#define UINCODE_16_R  0xc1  //=1100 00 01 Unicode string use 16x16 font ,right align
//---------------------------------------------------------------------------------------------
typedef struct
 {//Head structure for font symbol
    int16_t cxPix;
    int16_t cyPix;
  } sHeader;

typedef struct
 {//Data structure used for 6x8 ANSI font
    sHeader sH;
    uint8_t b[SIZE6x8];
  } sFont6x8;
typedef struct
 {//Data structure used for 8x8 ANSI font
    sHeader sH;
    uint8_t b[SIZE8x8];
  } sFont8x8;
typedef struct
 {//Data structure used for 8x16 ANSI font
    sHeader sH;
    uint8_t b[SIZE8x16];
 } sFont8x16;
typedef struct
 {//Data structure used for 12x12 unicode font
    sHeader sH;
    uint8_t b[SIZE12x12];
  } sFont12x12;

typedef struct
 {//Data structure used for 16x16 unicode font
    sHeader sH;
    uint8_t b[SIZE16x16];
  } sFont16x16;

typedef struct
 {////Data structure used for 128x64 LCD backgroud image setting
    sHeader sH;
    uint8_t b[1024];
  } sBack;

//--------------------------------------------------------------------------------------------------
/*Codepage related data structure defintion*/
typedef struct
 {//Head of a codepage table file
    uint16_t num_cp_ranges;        /* Number of GCODEPAGE_RANGE elements in cp_range */
    uint16_t deault_symbol;        /* Symbol used if not found in cp_range's. This symbol must be in a range */
  } GCODEPAGE_HDR;

typedef struct
 {//Codepage table range index structure
    uint16_t min;           /* First value in range */
    uint16_t max;           /* Last value in range */

    uint16_t index;         /* Symbol index in font.sym file */
  } GCODEPAGE_RANGE;
typedef struct
 {//Code page data structure used for 12x12 unicode font file
    GCODEPAGE_HDR cph;
    GCODEPAGE_RANGE cpr[UNICODE12_PAGE]; /* Dynamic length array of 12x12 font symbol*/
  } GCODEPAGE12;

typedef struct
 {//Code page data structure used for 16x16 unicode font file
    GCODEPAGE_HDR cph;
    GCODEPAGE_RANGE cpr[UNICODE16_PAGE]; /* Dynamic length arrayof 12x12 font symbol*/
 } GCODEPAGE16;

//-----------------------------------------------------------------------------
typedef struct
 {
   int8_t x    ;//algin point position in the X axis(for left or right align)
   int8_t y    ;//bottom align position in the Y axis(always bottom align )
   uint8_t width ;//define the print area window width in pixs(must be positive number )
   uint8_t height ;//define the print area window's height in pixs(must be positive number )
   int8_t len  ;//length of the string ,if=0 output routine will search for null character to determin the length
                     //print routine will use font information to calculate the upperlimit for the string length
                     //and will stop print string when a overflow occure at give X direction,fill a ellipsis(...)
                     //because no auto line advance function implemented for some type print operation.
   union{
   uint8_t  b ;//control byte
   struct {
            uint8_t font       : 2 ;//user specified font type(symbol file ,this also specified the codepage file,if it is a unicode string)
            uint8_t digits     : 2 ;//used to denote how many decimal digits in printed integer data,=0 none,1,2,3

            uint8_t lf_en      : 1 ;//allow print function to do auto line advance operation
            uint8_t reverse    : 1 ;//=1 force to do reverse(white<->dark)print the string,=0 normal display
            uint8_t unicode    : 1 ;//=0 the string contain only ANSI(ASCII) character,=1 the string contain unicode character
            uint8_t right      : 1 ;//=0 string print in X direction to left align mode(default),=1 right align mdoe
           } ;
        } ctrl ;
 } STR_INFO  ;//information structure for a string(to print on LCD) 6 bytes

typedef struct
{
  STR_INFO         info;
  union
  {
   uint8_t           *ch_p;//ANSI character string pointer
   const uint16_t    *uc_p;//unicode string pointer
   } str ;
} UNI_PRINT ;//universal print control structure,total 8 bytes

//define control structure for LCD setup
typedef struct TagLCDStruct
{
  volatile union{
          uint8_t Flag ;
          struct {
                  uint8_t ModuleInitOk  : 1 ;//The LCD module have been initialized,ready for display
                  uint8_t DisplayOn     : 1 ;//LCD module in display ON status
                  uint8_t SleepMode     : 1 ;//LCD Module in Sleep mode
                  uint8_t BackgroundOk  : 1 ;//Backgroud data issued to LCD internal RAM ok
                  uint8_t rev_bits      : 3 ;
                  uint8_t DynamicFlag   : 1 ;//Dynamic flag used to perform LCD Module display operation
                  } ;
                }Check ;
  uint8_t   InitSta ;//used to record init state
  uint8_t   LcdPage   ;  //current page address of the LCD controller
  uint8_t   LcdColumn ; //current column address of the LCD controller
  uint16_t  LcdRefreshTime ;//used to control LCD refresh
  volatile union{
          uint16_t  Req ;
          struct {
                  uint16_t  Print_A0   : 1 ;//Request to print LCD Area 0,
                  uint16_t  Print_A1   : 1 ;//
                  uint16_t  Print_A2   : 1 ;//
                  uint16_t  Print_A3   : 1 ;//
                  uint16_t  Print_A4   : 1 ;//
                  uint16_t  Print_A5   : 1 ;//
                  uint16_t  Print_A6   : 1 ;//
                  uint16_t  Print_A7   : 1 ;//
                  uint16_t  Print_A8   : 1 ;//
                  uint16_t  Print_A9   : 1 ;//
                  uint16_t  Print_A10  : 1 ;//
                  uint16_t  Print_A11  : 1 ;//
                  uint16_t  Print_A12  : 1 ;//
                  uint16_t  Print_A13  : 1 ;//
                  uint16_t  Print_A14  : 1 ;//
                  uint16_t  Print_A15  : 1 ;//
                  } ;
                  } Set ;//Set request to display variables strings or fixed type symbol on the screen
  volatile union{
          uint16_t  Req ;
          struct {
                  uint16_t  Print_A0   : 1 ;//Request to clear previous printed LCD Area 0,
                  uint16_t  Print_A1   : 1 ;//
                  uint16_t  Print_A2   : 1 ;//
                  uint16_t  Print_A3   : 1 ;//
                  uint16_t  Print_A4   : 1 ;//
                  uint16_t  Print_A5   : 1 ;//
                  uint16_t  Print_A6   : 1 ;//
                  uint16_t  Print_A7   : 1 ;//
                  uint16_t  Print_A8   : 1 ;//
                  uint16_t  Print_A9   : 1 ;//
                  uint16_t  Print_A10  : 1 ;//
                  uint16_t  Print_A11  : 1 ;//
                  uint16_t  Print_A12  : 1 ;//
                  uint16_t  Print_A13  : 1 ;//
                  uint16_t  Print_A14  : 1 ;//
                  uint16_t  Print_A15  : 1 ;//
                  } ;
                  } C_Set ;//Set request to clear display variables strings or fixed type symbol on the screen
  volatile union{
          uint16_t  Flag ;
          struct {

                  uint16_t  LCD_A0_OK   : 1 ;//Fixed disply area 0 on LCD be printed ok
                  uint16_t  LCD_A1_OK   : 1 ;//
                  uint16_t  LCD_A2_OK   : 1 ;//
                  uint16_t  LCD_A3_OK   : 1 ;//
                  uint16_t  LCD_A4_OK   : 1 ;//
                  uint16_t  LCD_A5_OK   : 1 ;//
                  uint16_t  LCD_A6_OK   : 1 ;//
                  uint16_t  LCD_A7_OK   : 1 ;//
                  uint16_t  LCD_A8_OK   : 1 ;//
                  uint16_t  LCD_A9_OK   : 1 ;//
                  uint16_t  LCD_A10_OK  : 1 ;//
                  uint16_t  LCD_A11_OK  : 1 ;//
                  uint16_t  LCD_A12_OK  : 1 ;//
                  uint16_t  LCD_A13_OK  : 1 ;//
                  uint16_t  LCD_A14_OK  : 1 ;//
                  uint16_t  LCD_A15_OK  : 1 ;//
                  } ;
                  } Test;//Test the flag to determine the request display operation complete yes or not
  uint16_t     PRINT_STAGE  ;
  UNI_PRINT    *pcb_p  ;//define a prirnt control structure pointer,to support mutistage print operation
} LCDStruct ;

//-------------------------------------------------------
typedef struct {
                union{
                       uint16_t DTypeUnit ;//
                       struct {
                                uint16_t  DType      : 4 ;//=0x0 unsigned ,=0x1 signed ,=0x2 16 bit Hex
                                uint16_t  Unit       : 4 ;//=0x0 (��),=0x1 'A' ,=0x2 'C' ,=0x3 'V'
                                                          //=0x4 'm'
                                uint16_t  rev_bits   : 8 ;
                                } ;
                       };
                uint16_t ItemTag[5] ;//Normal display item unicode str
                uint16_t MenuItem[8] ;//setup menu item unicode str
                } MachDPStruct ;

//--------------------------------------------------------------------------------------------------
// Public function prototypes
//--------------------------------------------------------------------------------------------------
void LcdClear (void);
void LcdContrast (uint8_t contrast);
void LcdGoto (uint8_t x, uint8_t y);
void LcdImage (uint8_t x, uint8_t y, uint8_t xsize, uint8_t ysize, const uint8_t * dataPtr);

void LcdEnterSleep(void) ;
void LcdWakeup(void);

void LcdSendData (uint8_t data);
void LcdSendCommand (uint8_t command);
uint8_t LcdReadData(void) ;
uint8_t LcdReadStatus(void) ;
__inline void LcdSetPageAdd(uint8_t page);
__inline void LcdSetColumnAdd(uint8_t col) ;
void LcdSetBackground (const uint8_t * dataPtr);
void LcdModuleInitDaemon(void);
void UnicodeToIndex12(const uint16_t *unicode,uint16_t *index,int16_t length);
void UnicodeToIndex16(const uint16_t *unicode,uint16_t *index,int16_t length);
void LcdPrintStr(UNI_PRINT *pcb_lp);
void ClrPrintedStr(UNI_PRINT * pcb_lp);

void SetLayoutOnLCD(STR_INFO *area_p);
void LcdWelcomPrint(void) ;
void LcdSaftyHintPrint(void) ;

void LcdPrintDaemon(void) ;

void  Shr_LCD_print(void) ;//LCD screen print function for general shearer
void  S260T1_LCD_print(void) ;//LCD screen print function for S260 tunneller (T1) 
void  S260T2_LCD_print(void) ;//LCD screen print function for S260 tunneller (T2) 


/*
#ifdef DRAW_OVER_BACKGROUND
void LcdInit (const uint8_t * dataPtr);
void LcdSetBackground (const uint8_t * dataPtr);
void LcdVBargraph (uint8_t x, uint8_t ystart, uint8_t yend, uint8_t yposition, draw_type dt);
void LcdHBargraph (uint8_t y, uint8_t xstart, uint8_t xend, uint8_t xposition, draw_type dt);
void LcdLine (uint8_t xb, uint8_t yb, uint8_t xe, uint8_t ye, draw_type dt);

#else  // DRAW_OVER_BACKGROUND
void LcdInit (void);
void LcdVBargraph (uint8_t x, uint8_t ystart, uint8_t yend, uint8_t yposition);
void LcdHBargraph (uint8_t y, uint8_t xstart, uint8_t xend, uint8_t xposition);
void LcdLine (uint8_t xb, uint8_t yb, uint8_t xe, uint8_t ye);

#endif // DRAW_OVER_BACKGROUND
*/
void  MonitorDataDraw(void) ;

void  MachineStatusAlarmDraw(void) ;

void  ZkAlarmDeamon(void) ;

void  ClrAlarmHintArea(void) ;

void  AHAreaPrintDeamon(void) ;

#endif // __DISPLAY_H
