 /**
  ******************************************************************************
  * @file    Project/STM32L1xx_StdPeriph_Template/stm32l1xx_it.h 
  * <AUTHOR> Application Team
  * @version V1.0.0
  * @date    Apri-2011
  * @brief   This file contains the headers of the interrupt handlers.
  ******************************************************************************
  * @copy
  *
  * THE PRESENT FIRMWARE WHICH IS FOR GUIDANCE ONLY AIMS AT PROVIDING CUSTOMERS
  * WITH CODING INFORMATION REGARDING THEIR PRODUCTS IN ORDER FOR THEM TO SAVE
  * TIME. AS A RESULT, STMICR<PERSON>LECTRONICS SHALL NOT BE HELD LIABLE FOR ANY
  * DIRECT, INDIRECT OR CONSEQUENTIAL DAMAGES WITH RESPECT TO ANY CLAIMS ARISING
  * FROM THE CONTENT OF SUCH FIRMWARE AND/OR THE USE MADE BY CUSTOMERS OF THE
  * CODING INFORMATION CONTAINED HEREIN IN CONNECTION WITH THEIR PRODUCTS.
  *
  * <h2><center>&copy; COPYRIGHT 2010 STMicroelectronics</center></h2>
  */ 

/* Define to prevent recursive inclusion -------------------------------------*/
#ifndef __STM32L1xx_IT_H
#define __STM32L1xx_IT_H

#ifdef __cplusplus
 extern "C" {
#endif 

/* Includes ------------------------------------------------------------------*/
#include "stm32l1xx.h"

/* Exported types ------------------------------------------------------------*/
/* Exported constants --------------------------------------------------------*/
/* Exported macro ------------------------------------------------------------*/
/* Exported functions ------------------------------------------------------- */

void NMI_Handler(void);
void HardFault_Handler(void);
void MemManage_Handler(void);
void BusFault_Handler(void);
void UsageFault_Handler(void);
void SVC_Handler(void);
void DebugMon_Handler(void);
void PendSV_Handler(void);
void SysTick_Handler(void);

void WWDG_IRQHandler(void);// Window Watchdog
void PVD_IRQHandler(void);// PVD through EXTI Line detect
void TAMPER_STAMP_IRQHandler(void);// Tamper and Time Stamp
void RTC_WKUP_IRQHandler(void);// RTC Wakeup
void FLASH_IRQHandler(void);// FLASH
void RCC_IRQHandler(void);// RCC
void EXTI0_IRQHandler(void);// EXTI Line 0
void EXTI1_IRQHandler(void);// EXTI Line 1
void EXTI2_IRQHandler(void);// EXTI Line 2
void EXTI3_IRQHandler(void);// EXTI Line 3
void EXTI4_IRQHandler(void);// EXTI Line 4
void DMA1_Channel1_IRQHandler(void);// DMA1 Channel 1
void DMA1_Channel2_IRQHandler(void);// DMA1 Channel 2
void DMA1_Channel3_IRQHandler(void);// DMA1 Channel 3
void DMA1_Channel4_IRQHandler(void);// DMA1 Channel 4
void DMA1_Channel5_IRQHandler(void);// DMA1 Channel 5
void DMA1_Channel6_IRQHandler(void);// DMA1 Channel 6
void DMA1_Channel7_IRQHandler(void);// DMA1 Channel 7
void ADC1_IRQHandler(void);// ADC1
void USB_HP_IRQHandler(void);// USB High Priority
void USB_LP_IRQHandler(void);// USB Low  Priority
void DAC_IRQHandler(void);// DAC
void COMP_IRQHandler(void);// COMP through EXTI Line
void EXTI9_5_IRQHandler(void);// EXTI Line 9..5
void LCD_IRQHandler(void);// LCD
void TIM9_IRQHandler(void);// TIM9
void TIM10_IRQHandler(void);// TIM10
void TIM11_IRQHandler(void);// TIM11
void TIM2_IRQHandler(void);// TIM2
void TIM3_IRQHandler(void);// TIM3
void TIM4_IRQHandler(void);// TIM4
void I2C1_EV_IRQHandler(void);// I2C1 Event
void I2C1_ER_IRQHandler(void);// I2C1 Error
void I2C2_EV_IRQHandler(void);// I2C2 Event
void I2C2_ER_IRQHandler(void);// I2C2 Error
void SPI1_IRQHandler(void);// SPI1
void SPI2_IRQHandler(void);// SPI2
void USART1_IRQHandler(void);// USART1
void USART2_IRQHandler(void);// USART2
void USART3_IRQHandler(void);// USART3
void EXTI15_10_IRQHandler(void);// EXTI Line 15..10
void RTC_Alarm_IRQHandler(void);// RTC Alarm through EXTI Line
void USB_FS_WKUP_IRQHandler(void);// USB FS Wakeup from suspend
void TIM6_IRQHandler(void);// TIM6
void TIM7_IRQHandler(void);// TIM7

#ifdef __cplusplus
}
#endif

#endif /* __STM32L1xx_IT_H */

/******************* (C) COPYRIGHT 2010 STMicroelectronics *****END OF FILE****/
