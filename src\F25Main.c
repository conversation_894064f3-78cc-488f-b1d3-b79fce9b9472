#include "F25Main.h"
//------------------------------------

/*******************************************************************************
* Function Name  : __F25Main
* Description    : This is main function entry for work in  Fyf25 mode
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void  __F25Main(void)
{
 __disable_irq();//Disable all interrupt
 //-------------------------------------------------------------------------------
 if((BootDats->f_key==F_KEY_FLAG)&&(BootDats->s_key==S_KEY_FLAG))
 { //system is hot boot
   BootDats->rst_count++ ;
   BootDats->bt_flag&=0x00ff ;
   BootDats->bt_flag|=HOTBOOT_FLAG ;//|=0x5a00
  }
 else //system is cool boot
 {
   BootDats->f_key=F_KEY_FLAG ;//set cool boot flag
   BootDats->s_key=S_KEY_FLAG ;
   BootDats->rst_count=0 ;//reset boot counter
   BootDats->bt_flag=0x0000 ;
  }
 //-----
 if(BootDats->switch_id==SWITCHED_ON_ID)
 {//Prevoius set flag is SWITCHED_ON_ID--this boot to enter shut off mode
   BootDats->switch_id = SWITCH_OFF_ID ;
  }
 else
 {//Prevoius set flag is SWITCH_OFF_ID or random data--this boot to run normal active mode
   BootDats->switch_id = SWITCHED_ON_ID;//Set switched ON flag,then enter switch ON operation
  }
 //-------------------------------------------------------------------------------
 PWR->CR|=0x00000100 ;//Set DBP--enable to access RTC backup register and RCC CSR 
 RCC->CSR=0x01000100 ;//LSE set to ON,Remove reset flag
 PWR->CR&=0xfffffeff ;//Reset DBP--disable to access RTC backup register and RCC CSR
 //---------------------------------
 InitSysTimer()   ;//Initialize timers in kernel mode
 InitTimer() ;//Initialize  timer in user mode
 Init108_F25Mode() ;//Initialize memory,peripherals--GPIO ,set environment varivable 
 InitSTM32Lxx() ;//Initialize System clock variable and NVIC 
 __enable_irq();
/*--------  
 GetKeySta() ;//Repeat twice
 if(Def_CRC_Seed==RIGHT_CRC_SEED)
 {
   if(U_Area.KeyCodeRaw.cw==(SW4+SW7))
	 Def_CRC_Seed=LEFT_CRC_SEED ;//Swap Right mode to Leftt mode 
   }
 else
 {
   if(U_Area.KeyCodeRaw.cw==(SW6+SW9))
	 Def_CRC_Seed=RIGHT_CRC_SEED ;//Swap Left mode to Right mode 
  }	 
*/  
 InitRfEnvironment() ;//Initialize or setup the RF operation working environment
 //------------------------
 if(Def_CRC_Seed==RIGHT_CRC_SEED)
 {
   Rx_021_Ch_p=(IC021RxSwitchReg *)&(APP_PC_p->Rx_Freq_Ch[APP_PA.RFIC_R_RxChNum]) ;
   Tx_021_Ch_p=(IC021TxSwitchReg *)&(APP_PC_p->Tx_Freq_Ch[APP_PA.RFIC_R_TxChNum]) ;
   Rx_01_Ch_p=(JF01SwitchReg *)&(APP_PD_p->Rx_Freq_Ch[APP_PA.RFIC_R_RxChNum]) ;
   Tx_01_Ch_p=(JF01SwitchReg *)&(APP_PD_p->Tx_Freq_Ch[APP_PA.RFIC_R_TxChNum]) ;
   U_Area.uc_str[5][0]=0xF353;//0x5200 ;//'R'
   S_Area.LedFunExchTimer=10 ;
  }
 else
 {
   Rx_021_Ch_p=(IC021RxSwitchReg *)&(APP_PC_p->Rx_Freq_Ch[APP_PA.RFIC_L_RxChNum]) ;
   Tx_021_Ch_p=(IC021TxSwitchReg *)&(APP_PC_p->Tx_Freq_Ch[APP_PA.RFIC_L_TxChNum]) ;
   Rx_01_Ch_p=(JF01SwitchReg *)&(APP_PD_p->Rx_Freq_Ch[APP_PA.RFIC_L_RxChNum]) ;
   Tx_01_Ch_p=(JF01SwitchReg *)&(APP_PD_p->Tx_Freq_Ch[APP_PA.RFIC_L_TxChNum]) ;
   U_Area.uc_str[5][0]=0xE65D;//0x4C00 ;//'L"
  }
 //--
 InitSPI1_01() ;//Init SPI for JF01 access
 ResetRfTxEnPin ;//TxEn pin to low
 ResetRfRxEnPin ;//Switch off Rx LNA
 JF01CmdStrobe(JF01_SPWD) ;//Put the JF101 to sleep status
 JF01Reset();//After this chip enter IDLE status
 InitJF01Regiters() ;//Init configuration register to default value
 JF01WritePATable(DefPATable,PA_TAB_LEN) ;//I will only use DefPATable[0] normally
 JF01WriteReg(JF01_FREQ2,Tx_01_Ch_p->freq2); // Frequency control word, high byte.
 JF01WriteReg(JF01_FREQ1,Tx_01_Ch_p->freq1); // Frequency control word, middle byte.
 JF01WriteReg(JF01_FREQ0,Tx_01_Ch_p->freq0); // Frequency control word, low byte.
 JF01WriteReg(JF01_IOCFG0,0x02) ;// Set GDO0 to be TX FIFO threshold ALARM signal
 JF01WriteReg(JF01_IOCFG2,0x06) ;// Set GDO2 to packet sent signal
 //Prepare the PowerOn or Shutoff Notify Tx data frame
 U_Area.PanelDat.Payload.PSet.SettingW=0x22e1;//(Enc_2,Enc_1,FixedBit,UniDirCtrl)=1,DatGID_2=2,DatGID_1=1
 U_Area.PanelDat.Payload.PSta.StaW=0x0000 ;
 U_Area.PanelDat.Payload.PSta.StaW|=(APP_PA.GroupId_InGroupId&0x00ff) ;
 U_Area.PanelDat.Payload.PSta.InquiryPackage=1 ;
 if(BootDats->switch_id==SWITCH_OFF_ID)
 {
   U_Area.PanelDat.Payload.PSta.ToBeShutoff=1 ;
  }
 //--
 U_Area.PanelDat.Payload.PSta.ToBeShutoff=1 ;//Always send shutoff flag for SD_TH --2017.07.05
 //--
 U_Area.PanelDat.Payload.KeyCode  =0x0000 ;//
 U_Area.PanelDat.CRC_app  = CRC16WithSeed(Def_CRC_Seed,(const uint16_t *)&U_Area.PanelDat,(sizeof(PANEL_TX_DAT)/2-1));
 //-----
 U_Area.PanelDat_TB0.Payload.PSet.SettingW=U_Area.PanelDat.Payload.PSet.SettingW^0xaaaa ;
 U_Area.PanelDat_TB0.Payload.PSta.StaW=U_Area.PanelDat.Payload.PSta.StaW^0xaaaa ;
 U_Area.PanelDat_TB0.Payload.KeyCode=U_Area.PanelDat.Payload.KeyCode^0xaaaa ;
 U_Area.PanelDat_TB0.CRC_app=U_Area.PanelDat.CRC_app^0xaaaa ;
 //--   
 InitRfTickTimer();//Init a hardware RF tick timer(TIM10) 
//------
 S_Area.TPool_p=0 ;//Reset Token pool pointer to First pool
 S_Area.RemainToken= RfTT_p->TokenPool[0].TokenNum ;
 S_Area.RFTickCount=RfTT_p->TokenPool[0].Interval-1;
 __disable_irq();
 IsrFlag |= IFG_KB_CHANGED ;
 __enable_irq();
 SYS_STA=LP_WAIT_STA ;
 do
 {//
   if(IsrFlag&IFG_GTOKEN_EN )
   {
     __disable_irq();
     IsrFlag&=~IFG_GTOKEN_EN ;
     __enable_irq();
     TxToken=1 ;
     S_Area.RemainToken-- ;
     if(S_Area.RemainToken<=0)
     {
       GFlag|=GF_SYS_NOTIFY_OK;
      }
    }
   switch(SYS_STA)
   {
    case LP_WAIT_STA:
	     if(TxToken>0)
	     {
	       SYS_STA=SETUP_TX_STA ;
	      }
	    break ;
    case SETUP_TX_STA:
		 RfDs.JF01Init.OpReq=0x0000 ;
		 //Check chip status
		 JF01CmdStrobe(JF01_SIDLE);//Put the chip to IDLE status
		 JF01CmdStrobe(JF01_SFTX);//Flush TX FIFO
		 JF01WriteReg(JF01_PKTLEN,RfDs.TxPackageLen); // Set TX Packet length.
		 JF01WriteFIFO(U_Area.RfTxChBuf0,RfDs.TxPackageLen) ;
		 RfDs.JF01Init.OpSta=IN_TX_STA ;
		 EnterTxMode_01() ;
		 JF01CmdStrobe(JF01_STX);//
		 Led_2x1_on() ;//--Indicator ON
		 SYS_STA=DO_TX_STA ;
		 break ;
    case DO_TX_STA:
		 if(RfDs.RF_TxState==TX_FINISHED)
		 {
		   RfDs.RF_TxState=TX_IDLE ;
		   TxToken-- ;//Use one token
		   if(TxToken==0)//Request to work in unidirection mode
		   {
		     Led_2x1_off() ;//--Indicator switch to off after package send
		     SYS_STA=LP_WAIT_STA ;
		    }
		  }
		 break ;
     default:	;
    }
  } while(((GFlag&GF_SYS_NOTIFY_OK)==0)||(TxToken==1));
//--
 DisableRfTickTimer();
 SetupJF01PD() ;//Power down RF part--need to wait chip enter IDLE status(and oscillator on)before reuse it
 if(BootDats->switch_id==SWITCHED_ON_ID)
 {
   SYS_STA=POWER_ON_INIT_STA;
  }
 else
 {
   SYS_STA=PREPARE_PD_STA;
  }
 //--
 #ifdef DEV_DEBUG
   USART_Config() ;//Use RS485 for debug
 #endif
 InitOnchipADC() ;
 while(1)
 {
  TimerProc() ;//user defined timer proccess
  GetKeySta() ;
  (*TxToken_fp)() ;
  switch(SYS_STA)
  {
   case LP_WAIT_STA:
	    if(IsrFlag&IFG_TO_BE_SHUTOFF) SCB->AIRCR = 0x05fa0001;//do system & core reset,this will lead to device shut off
	    if(TxToken)
	    {
		  JF01CmdStrobe(JF01_SIDLE);//Put the chip to IDLE status--need to wait JF01 oscillator start up
		  WaitSomeTime(TIME_200uS_V);//For JF01 crystal oscillator start up
	      ExitLowSpeedRunMode() ;
	      EnableTIM11() ;//Enable Timer11 for 1mS interrupt
	      SYS_STA=SETUP_TX_STA ;
	     }
	    break ;
   case SETUP_TX_STA:
	    SYS_STA=DO_TX_STA ;
	    FastSetupTx_01() ;//After this call ,the TX operation begin
		if(U_Area.PSta.InquiryPackage==0)
	      Led_2x1_on() ;//--Indicator ON(switch to on when U_Area.PSta.InquiryPackage=0 )
   case DO_TX_STA:
	    if(RfDs.RF_TxState==TX_FINISHED)
	    {
	      RfDs.RF_TxState=TX_IDLE ;
		  __disable_irq();
	      TxToken-- ;//Use one token
	      __enable_irq();	
	      if(TxToken==0)//Request to work in unidirection mode
	      {
	        Led_2x1_off() ;//--Indicator switch to off after package send
	        //S_Area.LedFlashCounter=S_Area.LedFlashInterval ; //Reload the counter
	        SYS_STA=SETUP_LP_STA ;
	       }
	      else//Normal Bidirection mode
	      {
	        SYS_STA=SETUP_TX_STA ;
	       }
	     }
	    break ;
   case SETUP_LP_STA:
		#ifndef NO_STANDBY
		  DisableTIM11() ;//Disable 1024Hz system operation tick interrupt
		#endif
		SetupJF01PD() ;//shut down the RF part
		SetupLowSpeedRunMode() ;
		SYS_STA=LP_WAIT_STA ;
	    break ;
   case POWER_ON_INIT_STA:
	    //--Check to condition for enter RF test mode or Not
	    GetKeySta() ;//Repeat twice
	    if(U_Area.KeyCodeRaw.cw==(SW13+SW14+SW15))
	    {//SW11,SW13,SW14,SW15 be pressed at same time(simultanously)
	      RfTestEntry_01() ;//To enter RF test mode ,Never return
	     }
	    else if(U_Area.KeyCodeRaw.cw)
	    {
	      GFlag|=GF_KB_ERROR ;//Set keyboard error flag
	      S_Area.DeadKeyMask.cw=~U_Area.KeyCodeRaw.cw ;
	      S_Area.DeadKeyMask.cw|=APP_PB.EStopCode ;//disable mask for emergnece stop key
	      S_Area.KeyMask.cw&=S_Area.DeadKeyMask.cw ;
	      U_Area.PSta.KeyBoardError=0x0001 ;//Send key board error flag to machine receiver
	     }
	    //---------------
	    S_Area.TPool_p=DEFAULT_POOL ;//Set default Token pool
	    S_Area.RemainToken=RfTT_p->TokenPool[S_Area.TPool_p].TokenNum ;
	    TxToken=0 ;
	    GFlag |=GF_SYS_INIT_OK ;
	    U_Area.PSta.InquiryPackage=0x0001 ;//InquiryPackage=1
	    InitTIM9_F25(); //Init hardware timer TIM9--Pd=1.000S
	    InitRfTickTimer();//Init a hardware RF tick timer(TIM10) 
	   #ifndef NO_STANDBY
	    DisableTIM11() ;//Disable 1024Hz system operation tick interrupt
	   #else
	    InitTIM11_F25();//Init hardware timer TIM11--Pd=0.9765mS
	   #endif
	    SetupLowSpeedRunMode() ;
	    SYS_STA=LP_WAIT_STA ;
	    break ; 
   case PREPARE_PD_STA:
	    Led_1_off ;
	    Led_2_off ;
	    Led_3_off ;
	    Led_4_off ;
        Led_BL_off ;
	    JF01WriteReg(JF01_IOCFG2,0x2f) ;
	    JF01WriteReg(JF01_IOCFG1,0x2f) ;
	    JF01WriteReg(JF01_IOCFG0,0x2f) ;
		SetupJF01PD() ;//shut down the RF part
		SYS_STA=POWER_DOWN_STA ;
   case POWER_DOWN_STA:
	    __disable_irq();
		EnStandby_NoLCD_01() ;
	    break ;
   default:  SCB->AIRCR = 0x05fa0001;//do system & core reset
  }
  //--
  ADC12Daemon() ;
  if(GFlag&GF_BATV_REFRESH)
  {
    GFlag&=~GF_BATV_REFRESH;
    if(U_Area.BatVoltage<APP_PA.BatLowLimit0)
    {//less than 3.025V
	  S_Area.LedFlashInterval=APP_PA.LedFlashInteval<<2 ;//Extened to 4 times of original value
      GFlag|=GF_BAT_ALARM ;//Set battery low alarm flag
     }
    else if(U_Area.BatVoltage<APP_PA.BatLowLimit)
    {//less than 3.18V
	  S_Area.LedFlashInterval=APP_PA.LedFlashInteval<<1 ;//Extend 1 time
      GFlag|=GF_BAT_ALARM ;//Set battery low alarm flag
     }
    else if(GFlag&GF_BAT_ALARM)
    {//Battery in normal status
      GFlag&=~GF_BAT_ALARM ;//Clear battery low alarm flag
     }
   }
  if(IsrFlag&IFG_FLASH_EN)
  {
    if(GFlag&GF_BAT_ALARM)
	  Led_4_on ;//Battery low voltage indicator ON
	if(GFlag&GF_KB_ERROR)
	  Led_3_on ;//System error indicator ON (Keyboard error)
	if(U_Area.KeyCode.cw==0)
	{
	  if(((Led_3_sta)==0)&&((Led_4_sta)==0))
	    Led_1x2_on() ;//Power on indicator ON 
	 }
   }
  else
  {
	Led_1x2_off() ;//Power on indicator OFF
	Led_3_off ;//System error indicator OFF
	Led_4_off ;//Battery low voltage indicator OFF
   }
#ifdef DEV_DEBUG
  ModProcA(&UB,&ModB) ;//Modbus daemon  using channel B	--debug
  UXTxP() ;//--debug
#endif
 } 
}
//---------
void Proc10mS_F25(void) //Timed process at frequency=100Hz
{
  ;
}

void Proc100mS_F25(void)
{
  ;
}

void Proc1S_F25(void) //Timed process at frequency=1Hz
{
  ;
}

void Proc1H_F25(void) //Timed process at interval 1 hour
{
  ;
}

