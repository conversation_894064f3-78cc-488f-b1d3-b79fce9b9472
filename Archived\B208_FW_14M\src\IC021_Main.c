#include "IC021_Main.h"
//------------------------------------

/*******************************************************************************
* Function Name  : __IC021_Main
* Description    : This is main function entry for use IC021 type receiver
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void  __IC021_Main(void)
{
 InitSPI1_021() ;//Init the SPI1 for  IC021  operation
 InitRfEnvironment();//
 USART_Config() ;//Initialize the uart and modbus RTU function
 //----------------
 Timer10[RUN_IND_TIMER].pv=95 ;
 Timer10[RUN_IND_TIMER].cv=0 ;
 Timer10[RUN_IND_TIMER].csr.csword=0x4000 ; //start Timer10[RUN_IND_TIMER]
 Timer10[CM_TIMER].pv=APP_PB.U1Config.rx_t_out ;//Setup modbus slaver mode communication monitor
 Timer10[CM_TIMER].cv=0 ;
 Timer10[CM_TIMER].csr.csword=0x4000 ; //start Timer10[CM_TIMER]
 //----------------
#ifndef M_DEBUG
 InitWatchDog() ;//Time out value about 10ms
#endif	
 //------------------------
 InitTimer3_1uS() ;//Init TIM3 for CPU sweep measure(1=1uS)
 U_Area.HW_Version=ReadVersion_021_L() ;
 U_Area.HW_Version|=ReadVersion_021_R() ;
 U_Area.AreaFlag100=0xF100 ;//MD100
 SYS_STA=POWER_ON_INIT_STA ;
 while(1)
 {
  TimerProc() ;//user defined timer proccess
  //----------------------------------
  switch(SYS_STA)
  {
  case DUPLEX_INIT_STA :
	RfDs.IC021Init_L.OpReq =SET_CODE+SET_UP_RX ;//Request to do Rx setup operation
	RfDs.IC021Init_R.OpReq =SET_CODE+SET_UP_RX ;//Request to do Rx setup operation
    RfDs.IC021Init_L.OpSta=NOT_INIT_STA;//
    RfDs.IC021Init_R.OpSta=NOT_INIT_STA;//
    SYS_STA=DUPLEX_RX_STA ;
    break ;
  case DUPLEX_RX_STA :
    if((IsrFlag&IFG_GTOKEN_EN))
    {
	  if(RfDs.RF_TxState_R==TX_IDLE)
	  {
        __disable_irq();//Disable IRQ
        IsrFlag &=~IFG_GTOKEN_EN ;
        RfDs.RF_State_R=RF_IDLE_STATUS ;//Set to Idle state
        __enable_irq();//Enable IRQ
		B_IO1_on ;//--debug
        if(RfDs.IC021Init_R.OpSta!=IN_RX_STA)
        {
          RfDs.IC021Init_R.OpSta=NOT_INIT_STA ;
          RfDs.IC021Init_R.OpReq=SET_CODE+SET_UP_TX ;
         }
        else
          RfDs.IC021Init_R.OpReq=SET_UP_TX ;//
        SYS_STA=DUPLEX_TX_STA ;
	   }
     }
	 /*
	if((RfDs.IC021Init_L.OpSta==IN_RX_STA)&&(RfDs.DClkCntChk_L==0))
	{//The RF chip may be in wrong status
	   RfDs.DClkCntChk_L=480;
       RfDs.IC021Init_L.OpSta=NOT_INIT_STA ;
       RfDs.IC021Init_L.OpReq=SET_CODE+SET_UP_RX ;
	 }
	if((RfDs.IC021Init_R.OpSta==IN_RX_STA)&&(RfDs.DClkCntChk_R==0))
	{//The RF chip may be in wrong status
	   RfDs.DClkCntChk_R=480 ;
       RfDs.IC021Init_R.OpSta=NOT_INIT_STA ;
       RfDs.IC021Init_R.OpReq=SET_CODE+SET_UP_RX ;
	 }
	 */
    break ;
  case DUPLEX_TX_STA :
    if(RfDs.RF_TxState_R==TX_FINISHED)
    {
	   B_IO1_off ;//--debug
       RfDs.RF_State_R=RF_IDLE_STATUS ;//Set to Idle state
       RfDs.RF_TxState_R=TX_IDLE ;
       if(RfDs.IC021Init_R.OpSta!=IN_TX_STA)
       {
         RfDs.IC021Init_R.OpSta=NOT_INIT_STA ;
         RfDs.IC021Init_R.OpReq=SET_CODE+SET_UP_RX ;//
        }
       else
         RfDs.IC021Init_R.OpReq=SET_UP_RX ;//
       SYS_STA=DUPLEX_RX_STA ;
     }
	 /*
	if((RfDs.IC021Init_R.OpSta==IN_TX_STA)&&(RfDs.DClkCntChk_R==0))
	{//The RF chip may be in wrong status
       RfDs.IC021Init_R.OpSta=NOT_INIT_STA ;
       RfDs.IC021Init_R.OpReq=SET_CODE+SET_UP_TX ;
	 }
	 */
    break ;
  case UNI_INIT_STA :
    Timer10[RUN_IND_TIMER].pv=95 ;
    Timer10[RUN_IND_TIMER].cv=0 ;
    Timer10[RUN_IND_TIMER].csr.csword=0x4000 ; //start Timer10[RUN_IND_TIMER]
    InitSPI1_021() ;//Init the SPI1 for  IC021  operation
	RfDs.IC021Init_L.OpReq =SET_CODE+SET_UP_RX ;//Request to do Rx setup operation
	RfDs.IC021Init_R.OpReq =SET_CODE+SET_UP_RX ;//Request to do Rx setup operation
    RfDs.IC021Init_L.OpSta=NOT_INIT_STA;//
    RfDs.IC021Init_R.OpSta=NOT_INIT_STA;//
    SYS_STA=UNI_RX_STA ;
    break ;
  case UNI_RX_STA :
	if((RfDs.IC021Init_L.OpSta==IN_RX_STA)&&(RfDs.DClkCntChk_L==0))
	{//The RF chip may be in wrong status
       RfDs.IC021Init_L.OpSta=NOT_INIT_STA ;
       RfDs.IC021Init_L.OpReq=SET_CODE+SET_UP_RX ;
	 }
	if((RfDs.IC021Init_R.OpSta==IN_RX_STA)&&(RfDs.DClkCntChk_R==0))
	{//The RF chip may be in wrong status
       RfDs.IC021Init_R.OpSta=NOT_INIT_STA ;
       RfDs.IC021Init_R.OpReq=SET_CODE+SET_UP_RX ;
	 }
    break ;
  case POWER_ON_INIT_STA :
    if((TestKeyA==TEST_KEY_ID_A)&&(TestKeyB==TEST_KEY_ID_B))
      RfTestEntry_021() ;//This call never return
    //--------------------------------------
    SYS_STA=UNI_INIT_STA;//DUPLEX_INIT_STA ;//Enter Duplex RF init status
    //-----------
    break ;
  case WAIT_RFIC_READY_STA:

    break ;
  default :
    SYS_STA=POWER_ON_INIT_STA ;
  }
//--------------------------------------
  ModProcA(&UA,&ModA) ;//Modbus daemon  using channel A
  UXTxP() ;//
  RfDaemon_021_L() ;
  RfDaemon_021_R() ;
  RfRxDaemon_021_L() ;
  RfRxDaemon_021_R() ;

  if(Timer10[CM_TIMER].csr.csbit.q==1)
  {
    OpCtrl&=~CB_MDS_COM_OK ;//Wired communication lost error
   }
  if((U_Area.PaDat_L.KeyCode!=0)||(U_Area.PaDat_R.KeyCode!=0))
  {
    Led_1_on ;//if PA.0='0',set it to '1'
    Timer10[RUN_IND_TIMER].csr.csbit.q=1 ;
   }
  else if(Timer10[RUN_IND_TIMER].csr.csbit.q==1)
  {
    if(Led_1_sta)
    {
      if(OpCtrl&(CB_MDM_COM_OK+CB_MDS_COM_OK))//Modbus Master or Slaver communication OK
        Timer10[RUN_IND_TIMER].pv=5 ;
      else
        Timer10[RUN_IND_TIMER].pv=95 ;
      Led_1_off ;//if PA.0='1',reset it to '0'
     }
    else
    {
      Timer10[RUN_IND_TIMER].pv=5 ;
      Led_1_on ;//if PA.0='0',set it to '1'
     } 
    Timer10[RUN_IND_TIMER].cv=0 ;
    Timer10[RUN_IND_TIMER].csr.csword=0x4000 ; //start Timer10[RUN_IND_TIMER]
   }
   
  RSSI_Filter_021() ;//Get and calculate the RSSI of IC021
 
  ActiveAntenna() ;//For SC_GN shearer
   
  if(U_Area.TestCtrlW==0x5a5f)//MD186
  {
    U_Area.TestCtrlW=0x5a50 ;
    TestCtrlW=U_Area.TestCommandW ;	//MD187
	RfTestEntry_021() ;//nerver return
   }
//----------------------------------------
  SysConfigDaemon() ;//For parameter change IAP
#ifndef M_DEBUG
  KickWatchDog();//kick the watchdog
#endif   
  CpuSweepTime(&U_Area.SweepTimeMax,&U_Area.SweepTimeC);
  if((CK_HSI_Flag==0)&&(RCC->CR&0x00020000)==0)//system init set to use HSE,but HSE not ready
  {
   SCB->AIRCR = 0x05fa0001;//do system & core reset
   }
 }
}

//---------
//---------
void Proc10mS_IC021(void) //Timed process at frequency=100Hz
{
  CopyObj16((uint16_t *)U_Area.RevArray0,(uint16_t *)U_Area.DisplayDatBuf,26);
}

void Proc100mS_IC021(void)
{
  RfDs.DClkCntChk_L=RfDs.DClkCnt_L ;
  RfDs.DClkCnt_L=0;  
  if((RfDs.DClkCntChk_L<420)||(RfDs.DClkCntChk_L>520))
    RfDs.DClkCntChk_L=0 ;//Set to invalid

  RfDs.DClkCntChk_R=RfDs.DClkCnt_R ;
  RfDs.DClkCnt_R=0;  
  if((RfDs.DClkCntChk_R<420)||(RfDs.DClkCntChk_R>520))
    RfDs.DClkCntChk_R=0 ;//Set to invalid
}

void Proc1S_IC021(void) //Timed process at frequency=1Hz
{
  ;
}

void Proc1H_IC021(void) //Timed process at interval 1 hour
{
  ;
}

