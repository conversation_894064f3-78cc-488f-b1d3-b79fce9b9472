/*This file define Analog input funtion using STM32L15x ADC12 module*/
#include  "AD_Input.h"

uint16_t  ADC_INPUT_BUF[ADC_CH_NUM*4] ;

uint32_t  ADC_STA ;

AIProcPara  *ain_cp ;

extern SysConfigA_t   APP_PA ;//System application parameter Group A, ID=0x0081

extern  UserAreaStruct  U_Area ;//Extern defined an object for user data

extern  SysAreaStruct  S_Area ;//Extern defined an object for user data

extern  volatile  uint32_t   OpCtrl ;
extern  volatile  uint32_t    GFlag ;



void InitOnchipADC(void)
{
  ADC1->CR2=0x00000000 ;//Power down the ADC1 to reduce power consume
  ADC_STA=ADC_POWER_DOWN_STA;
}

/*And associate analog input data process function                   */
void CalBatVoltage(uint16_t rawv)
{//calculate output voltage of the system battary
  int32_t ltemp ;
  ltemp=(int32_t)(1224*4095)/rawv ;//Fixed or typical parameter for STM32L15x
  ltemp*=APP_PA.BatMeAdjCoeff ;
  ltemp/=1000 ;
  ltemp+=APP_PA.BatMeOffset ;
  U_Area.BatVoltage=(int16_t)ltemp ;//post measured battery output voltage
}
void CalSysTemp(uint16_t rawv)
{//calculate system temperature value
  int32_t ltemp ;
  ltemp=(int32_t)rawv;
  ltemp*=1000;
  ltemp/=APP_PA.TCoeff ;
  ltemp-=(int32_t)APP_PA.VTemp0 ;
  U_Area.SysTemp=(int16_t)ltemp ;//post measured system environment temperature
}


void ADC12Daemon(void)
{
 uint32_t sum32 ; 
 switch(ADC_STA)
 {
 case ADC_POWER_DOWN_STA:
   ADC_STA=ADC_POWER_ON_INIT ;
   break ;
 case ADC_POWER_ON_INIT:

   ADC1->CR2=0x00000710 ;//External trigger disabled,Right alignment, Enable DMA ,ADC in OFF status
   ADC1->CR1=0x04030100 ;//Overfun interrupt enbled,PDI=1,PDD=1,enable Scan mode ,no EOC interrupt
   for(sum32=0 ;sum32<ADC_CH_NUM*4 ;sum32++)
     ADC_INPUT_BUF[sum32]=0x0000 ;

   ADC1->SMPR3=0x24924924 ;//Channel 0-9 use 48 cycles sample time
   ADC1->SMPR2=0x24924924 ;//Channel 10-19 use 48 cycles sample time
   ADC1->SMPR1=0x00024924 ;//Channel 20-25 use 48 cycles sample time
   ADC1->SQR5 =0x21184611 ;//Regular sequence 1-6 --17(REFINT),16(Temprature Sensor),17(REFINT),16(Temprature Sensor),17(REFINT),16(Temprature Sensor),
   ADC1->SQR4 =0x00000211 ;//Regular sequence 7-12--17(REFINT),16(Temprature Sensor), other to AIN0
   ADC1->SQR3 =0x00000000 ;//Regular sequence 13-18--
   ADC1->SQR2 =0x00000000 ;//Regular sequence,19-24--
   ADC1->SQR1 =0x00700000 ;//Regular sequency length=8,25-27-- 
   //Begin to init the DMA1 channel 1 for ADC regular convertion
   DMA1_Channel1->CCR=0x00000000 ;//disable the DMA channel
   DMA1_Channel1->CPAR=(uint32_t) &ADC1->DR ;//Set the peripheral register address as source area
   DMA1_Channel1->CMAR=(uint32_t) ADC_INPUT_BUF ;//Set begin of output buffer area target address
   DMA1_Channel1->CNDTR=ADC_CH_NUM*4 ;//Max tranfer number
   DMA1_Channel1->CCR=0x000015aa ;//Set Medium priority,16 bit size,P->M,increase memory,Circular mode,enable Transfer complete and error interrupt
   DMA1_Channel1->CCR=0x000015ab ;//enable the DMA channel
   /* Enable the DMA1  Channel 1 Interrupt--may be done in boot sequency */
   ADC->CCR=0x00820000 ;//bit23-TSVREFE=1 Temperature sensor and Vrefint channel enalbed,ADC clock=HSI/4 ;
   ADC_STA=ADC_WAIT_FOR_START ;
   break ;
 case ADC_WAIT_FOR_START:
   if(StartAdcFlag)// GPIOC->ODR's bit0 use as ADC convertion request flag
  {//Be request to do AD convertion--
	 ClearStartAdcFlag ;//Reset the do AD convertion request flag
     RCC->CR |=RCC_CR_HSION ;//enable HSI
	 RCC->APB2ENR|=0x00000200 ;//enable ADC1
     ADC1->CR2=0x00000711 ;//External trigger disabled,Right alignment,EOCS=1,DDS=1enable DMA ,ADC in OFF status
     ADC_STA=ADC_CONVERTING ;
	 while((ADC1->SR&0x00000040)==0) ;//Wait the ADC is ready to convert
     ADC1->CR2=0x40000711 ;//software trigger ,Right alignment,enable DMA ,ADC in ON status
    }
   break ;
 case ADC_CONVERTING:
   break ;//Wait DMA interrupt to switch ADC_STA to ADC_GET_RESULT
 case ADC_GET_RESULT:
   if(ADC1->SR&0x00000040)//ADC still in ON status
     ADC1->CR2=0x00000710 ;//ADC in OFF status
   RCC->CR &=~RCC_CR_HSION ;//disable HSI
   RCC->APB2ENR&=0xfffffdff ;//disable ADC1
   sum32=ADC_INPUT_BUF[0]+ADC_INPUT_BUF[2]+ADC_INPUT_BUF[4]+ADC_INPUT_BUF[6] ;
   S_Area.REFINT_RawResult=(uint16_t) sum32>>2 ;
   CalBatVoltage(S_Area.REFINT_RawResult) ;
   GFlag|=GF_BATV_REFRESH ;
   sum32=ADC_INPUT_BUF[1]+ADC_INPUT_BUF[3]+ADC_INPUT_BUF[5]+ADC_INPUT_BUF[7] ;
   S_Area.TS_RawResult=(uint16_t) sum32>>2 ;
   CalSysTemp(S_Area.TS_RawResult) ;
   U_Area.ADC_CNT++ ;
   ADC_STA=ADC_WAIT_FOR_START ;
   break ;
 default:
   ADC1->CR2=0x00000000 ;//Power down the ADC1 to reduce power consume
   ADC_STA=ADC_POWER_DOWN_STA ;
 }
}

void ADC_DMA_ISR(void)
{
  if(DMA1->ISR&0x00000002)//DMA1 channel 1 global & transfer complete interrupt
  {
    ADC_STA=ADC_GET_RESULT ;
//    ADC1->CR2=0x00000000 ;//Power down the ADC1 to reduce power consume
//    GPIOA->BRR=0x00000002 ;//disable Vcc_mid function
   }
  if(DMA1->ISR&0x00000008)
  {//Transfer error interrupt occur
    U_Area.ADC_CNT=0xffff ;
	ADC_STA=ADC_POWER_ON_INIT ;//Request to re_init ADC and re config DMA1 channel1
   }
  DMA1->IFCR=0x0000000f;//clear the channel 1 interrupt
  DMA1->IFCR=0x00000000;//reset the channel 1 interrupt
}
/*********************************************************/
